<div id="page-header-parent">
	<div class="title">
		<div class="wrapper">
			<?php //$parent_title = get_the_title($post->post_parent); echo "<h1>". $parent_title ."</h1>"; ?>
			<h1 class="title-page">
				<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
					Error 404 - Sidan finns inte
				<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
					Error 404 - Page not found
				<?php endif;?>
			</h1>

		</div>
	</div>
	<div id="search_form">
		<div class="wrapper">
			<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
				<form action="<?= get_site_url();?>" method="get" id="search-form" accept-charset="UTF-8" autocomplete="false">
			<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
				<form action="https://www.swedac.se/?lang=en" method="get" id="search-form" accept-charset="UTF-8" autocomplete="false">
			<?php endif;?>
				<div class="main_serach">
					<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
						<input pattern=".{3,}" required title="En sökning bör innehålla minst 3 tecken" type="search" class="search-field" placeholder="Sök …" value="" name="s" id="search-page-input">
						<input type="submit" class="search-submit" value="Sök">
					<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
						<input pattern=".{3,}" required title="A search should contain at least 3 characters" type="search" class="search-field" placeholder="Search …" value="" name="s" id="search-page-input">
						<input type="submit" class="search-submit" value="Search">
					<?php endif;?>
				</div>
				
				<div class="adv_search">
					<ul id="mainmenu">
						<li>
						
						<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
							<span class="toggle-adv">Avancerad sökning <i class="fa fa-angle-down" aria-hidden="true"></i></span>
						<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
							<span class="toggle-adv">Advanced search<i class="fa fa-angle-down" aria-hidden="true"></i></span>
						<?php endif;?>
							<ul id="submenu">
								<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
									<li><a href="https://www.swedac.se/lag-ratt/swedacs-foreskrifter/sok-foreskrifter-och-dokument/" target="_blank">Föreskrifter & Dokument</a></li>
									<li><a href="https://swedac.se/tjanster/reglerad-matteknik/sok-certifikat-och-godkannanden/" target="_blank"><?php _e('Certifikat och Godkännande', 'certificates');?></a></li>
									<li><a href="http://search.swedac.se/sv/ackrediteringar" target="_blank">Ackrediterade organ</a></li>
									<li><a href="http://search.swedac.se/sv/namnstamplar" target="_blank">Namnstämpelregistret</a></li>
								<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>

									<li><a href="https://www.swedac.se/law-order/swedacs-regulations/search-regulations-documents/?lang=en" target="_blank">Regulations & Document</a></li>
									<li><a href="http://search.swedac.se/sv/ackrediteringar" target="_blank">Accreditation register</a></li>
									<li><a href="http://search.swedac.se/sv/namnstamplar" target="_blank">Stamps register</a></li>
								<?php endif;?>
							</ul>
						</li>
					</ul>
				</div> 
				<div id="sokkallor">
					
					<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
						<label for="alla_sokkallor" class="alt">
							<input class="alla_sokkallor" type="checkbox" name="alla_sokkallor" id="alla_sokkallor" checked> 
							Alla sökkällor
						</label>
						<label for="swedac_se" class="alt">
							<input class="search_alt" type="checkbox" name="swedac_se" id="swedac_se" checked> 
							<span>Swedac.se</span>
						</label>
						<label for="foreskrifter_dokument" class="alt">
							<input class="search_alt" type="checkbox" name="foreskrifter_dokument" id="foreskrifter_dokument" checked> 
							<span>Föreskrifter & Dokument</span>
						</label>
						<label for="namnstampelregistret" class="alt">
							<input class="search_alt" type="checkbox" name="namnstampelregistret" id="namnstampelregistret" checked> 
							<span>Namnstämpelregistret</span>
						</label>
						<label for="ackrediterande_organ" class="alt">
							<input class="search_alt" type="checkbox" name="ackrediterande_organ" id="ackrediterande_organ" checked> 
							<span>Ackrediterade organ</span>
						</label>
						<label for="certifikat_godkannanden" class="alt">
							<input class="search_alt" type="checkbox" name="certifikat_godkannanden" id="certifikat_godkannanden" checked>
							<span><?php _e('Certifikat och Godkännanden', 'certificates');?></span>
						</label>
					<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
						<label for="alla_sokkallor" class="alt">
							<input class="alla_sokkallor" type="checkbox" name="alla_sokkallor" id="alla_sokkallor" checked> 
							All sources
						</label>
						<label for="swedac_se" class="alt">
							<input class="search_alt" type="checkbox" name="swedac_se" id="swedac_se" checked> 
							<span>Swedac.se</span>
						</label>
						<label for="foreskrifter_dokument" class="alt">
							<input class="search_alt" type="checkbox" name="foreskrifter_dokument" id="foreskrifter_dokument" checked> 
							<span>Regulations & Documents</span>
						</label>
						<label for="namnstampelregistret" class="alt">
							<input class="search_alt" type="checkbox" name="namnstampelregistret" id="namnstampelregistret" checked> 
							<span>Stamps register</span>
						</label>
						<label for="ackrediterande_organ" class="alt">
							<input class="search_alt" type="checkbox" name="ackrediterande_organ" id="ackrediterande_organ" checked> 
							<span>Accreditation register</span>
						</label>
					<?php endif;?>
				</div>
				
				
				<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
					<input type="hidden" name="lang" value="sv">
					<input class="intern_sok_empty" type="hidden" value="empty" name="post_type" id="post_type" disabled/>

					<input class="intern_sok" type="hidden" value="post" name="post_type_post" id="post_type_post" />
					<input class="intern_sok" type="hidden" value="amnesomraden" name="post_type_amnesomraden" id="post_type_amnesomraden" />
					<input class="intern_sok" type="hidden" value="kalender" name="post_type_kalender" id="post_type_kalender" />
					<input class="intern_sok" type="hidden" value="kurser" name="post_type_kurser" id="post_type_kurser" />
					<input class="intern_sok" type="hidden" value="swedac_magasin" name="post_type_swedac_magasin" id="post_type_swedac_magasin" />
					<input class="intern_sok" type="hidden" value="page" name="post_type_page" id="post_type_page" />

					<input class="intern_sok_dokument" type="hidden" value="dokument" name="post_type_dokument" id="post_type_dokument" />
					<input class="intern_sok_certifikat" type="hidden" value="certifikat" name="post_type_certifikat" id="post_type_certifikat" />

					<input class="intern_sok_certifikat_only" type="hidden" value="certifikat" name="post_type" id="post_type_certifikat_only" disabled/>
					<input class="intern_sok_dokument_only" type="hidden" value="dokument" name="post_type" id="post_type_dokument_only" disabled/>
				<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
					<input type="hidden" name="lang" value="en">
					<input class="intern_sok_empty" type="hidden" value="empty" name="post_type" id="post_type" disabled/>

					<input class="intern_sok" type="hidden" value="post" name="post_type_post" id="post_type_post" />
					<input class="intern_sok" type="hidden" value="amnesomraden" name="post_type_amnesomraden" id="post_type_amnesomraden" />
					<input class="intern_sok" type="hidden" value="kalender" name="post_type_kalender" id="post_type_kalender" />
					<input class="intern_sok" type="hidden" value="kurser" name="post_type_kurser" id="post_type_kurser" />
					<input class="intern_sok" type="hidden" value="swedac_magasin" name="post_type_swedac_magasin" id="post_type_swedac_magasin" />
					<input class="intern_sok" type="hidden" value="page" name="post_type_page" id="post_type_page" />
					<input class="intern_sok_certifikat" type="hidden" value="certifikat" name="post_type_certifikat" id="post_type_certifikat" />

					<input class="intern_sok_dokument" type="hidden" value="dokument" name="post_type_dokument" id="post_type_dokument" />
					
					<input class="intern_sok_certifikat_only" type="hidden" value="certifikat" name="post_type" id="post_type_certifikat_only" disabled/>
					<input class="intern_sok_dokument_only" type="hidden" value="dockument_eng" name="post_type" id="post_type_dokument_only" disabled/>
				<?php endif;?>

			</form>
		</div>
	</div>
</div>
<div class="wrapper">
	<div class="alert alert-warning">
		<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
			<p>Sidan du s&#246;ker kan ha blivit borttagen, &#228;ndrat namn eller flyttats. <br/>Var god prova dessa alternativ:</p>
			<ul>
				<li>Kontrollera stavningen.</li>
				<li>Kontrollera s&#229; att url:en ser korrekt ut.</li>
				<li>G&#229; tillbaka till <a href="<?php echo home_url(); ?>" title="Gå till startsidan">startsidan</a>.</li>
				<li>Testa att anv&#228;nda s&#246;kfunktionen.</li>
			</ul>
		<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
			<p>The page you are looking for might have been removed , changed their name or moved. <br/> Please following options:</p>
			<ul>
				<li>Check your spelling.</li>
				<li>Check that the URL looks correct.</li>
				<li>Go back to <a href="<?php echo home_url(); ?>" title="Go back to start">start</a>.</li>
				<li>Try using the search function</li>
			</ul>
		<?php endif;?>
	</div>
</div>

<?php //get_search_form(); ?>