<?php

/**
 * Template Name: Swedac <PERSON>gasin - 2020
 */

while (have_posts()) : the_post(); ?>
<?php get_template_part('templates/page', 'header-utvalda');
    
endwhile; ?>

<?php

    // NYA LÖSNING PÅ ATT VISA POSTER VIA WPML (på riktigt denna gången)

    // $active_languages = apply_filters( 'wpml_active_languages', false );
    // $current_language = apply_filters( 'wpml_current_language', NULL );

    // if ( $active_languages ) {

    //     $posts = array();

    //     foreach ( $active_languages as $code => $data ) {
    //         do_action( 'wpml_switch_language', $code );

    //         $args = array(
    //                 'suppress_filters' => false,
    //                 // Add some arguments
    //         );

    //         $posts[ $code ] = get_posts( $args );
    //     }

    //     do_action( 'wpml_switch_language', $current_language );

    //     // At this point we should have some posts objects in $posts['en'], $post['fr'], ...
    //     // Now all the options are available to merge this...
    // } 

?>

<?php 

$paged = (get_query_var('paged')) ? get_query_var('paged') : false;

// Checks if you're at the first page
if ($paged === false) :
    $utvalda = new WP_Query( array( 
        'post_type'      => 'swedac_utvalda',
        'orderby'		 => 'date', 
        'order'          => 'DESC', 
        'posts_per_page' => 1
        ) 
    ); 
    // Stores duplicates
    $duplicates = array();

    while( $utvalda->have_posts() ) : $utvalda->the_post();

        if( have_rows('utvalda_repeater') ) :
            $segment = 0;
            while( have_rows('utvalda_repeater') ) : the_row();
                $hero = get_sub_field('utvalda_hero');
                $hero_rubrik = get_sub_field('utvalda_hero_rubrik');
                $subtitle = get_sub_field('utvalda_underrubrik');
                $text = get_sub_field('utvalda_text'); 
                $video = get_sub_field('utvalda_video');
                $video_text = get_sub_field('utvalda_video_text'); 
                $video_bild = get_sub_field('utvalda_video_bild'); 
                $video_rubrik = get_sub_field('utvalda_video_rubrik'); 

                $segment++;
                ?>
                    <?php if ($hero) : ?>       
                        <div class="container-fluid p-0">
                            <div class="row m-0">
                                <div class="col-12 text-center responsive-background d-flex justify-content-center align-items-center h-500 p-0 mt-15" style="background-image: url('<?php echo $hero['url'] ?>');">
                                    <h1 class="text-white text-large"><?php echo $hero_rubrik ?></h1>
                                </div>
                            </div>
                        </div> 
                    <?php endif; ?>

                <div class="wrapper p-0 mb-5">
                    <div class="container-fluid p-0">
                        <div class="row mx-auto">
                            <?php if ($subtitle) : ?>
                                <div class="col-12 col-md-8 my-5 mx-auto">
                                    <h2 class="text-center mt-5 mb-3"><?php echo $subtitle ?></h2>
                                    <p class="mb-5"><?php echo $text ?></p>
                                </div>
                            <?php else: ?>
                                <div class="col-12 col-md-8 mx-auto my-4"></div>
                            <?php endif; ?>

                            <?php if ($video) : ?>
                                <div class="col-12 p-0 px-md-4">
                                    <div class="responsive-background position-relative video-height imageDiv<?=$segment ?>" style=" background-image: url('<?php echo $video_bild['url'] ?>');">
                                        <svg class="playIcon videoPlay" onclick="showVideo(<?=$segment ?>);" height="85" width="85" viewBox="0 0 30 30">
                                            <defs>
                                                <style>.cls-1{fill:#fff;}</style>
                                            </defs>
                                            <circle class="cls-1" cx="15" cy="15" r="15"/>
                                            <path d="M20,14.4,13.8,9.9a.75.75,0,0,0-.8-.1.67.67,0,0,0-.4.7v9.1a1,1,0,0,0,.4.7.37.37,0,0,0,.3.1c.2,0,.3,0,.4-.1l6.2-4.5a.71.71,0,0,0,.3-.6A.72.72,0,0,0,20,14.4Z"/>
                                        </svg>
                                        <div class="overlay">
                                            <h2 class="text-center text-white mt-3 text-overlay"><?php echo $video_rubrik; ?></h2>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 hide videoDiv<?=$segment ?>"> <?php echo $video; ?> </div>
                                <?php if ($video_text) : ?>     
                                    <div class="col-12 col-md-8 my-5 mx-auto"><p class="my-5"><?php echo $video_text; ?></p></div>
                                <?php else: ?>
                                    <div class="col-12 col-md-8 mx-auto"><p></p></div>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php
                            if( have_rows('utvalda_repeater_inlagg') ) :
                                $count = 0;
                                $rows = 0; 
                                
                                while ( have_rows('utvalda_repeater_inlagg') ) : the_row();
                                    $rows++;
                                endwhile;

                                while( have_rows('utvalda_repeater_inlagg') ) : the_row();
                                    $utvalda_inlagg = get_sub_field('utvalda_inlagg');
                                    $utvaldID = $utvalda_inlagg->ID;
                                    $titel = get_field('swedac_magasin_rubrik', $utvaldID);

                                    $img_featured = wp_get_attachment_image_src( get_post_thumbnail_id($utvaldID), 'swedac_magasin' );
                                    $img_kvadrat = get_field('swedac_magasin_kvadratisk', $utvaldID);
                        
                                    if ($img_kvadrat) {
                                        $existingImg = $img_kvadrat['url'];
                                    } elseif ($img_featured) {
                                        $existingImg = $img_featured[0];
                                    }

                                    if ($rows == 4) {
                                        $widthClass = 'col-md-6 col-12';
                                        $imgSize = 'h-250';
                                    }

                                    if ($rows == 5) {
                                        if ($count >= 2) {
                                            $widthClass = 'col-md-4 col-12';
                                            $imgSize = 'h-250';
                                        } else {
                                            $widthClass = 'col-md-6 col-12';
                                            $imgSize = 'h-250';
                                        }
                                    }

                                    if ($rows == 6) {
                                        if ($count > 2) {
                                            $widthClass = 'col-md-4 col-12';
                                            $imgSize = 'h-250';
                                        } elseif ($count == 2) {
                                            $widthClass = 'col-md-12 col-12';
                                            $imgSize = 'h-500';
                                        } else {
                                            $widthClass = 'col-md-6 col-12';
                                            $imgSize = 'h-250';
                                        }
                                    }
                        
                                    $imgClass = $existingImg;
                                    
                                    //Stores duplicates if there are some from "utvalda" that appears in the "Swedacskolan".
                                    $duplicates[] = $utvaldID;
                                    ?>
                                    <div class="grow py-3 py-md-4 px-0 px-md-4 <?php echo $widthClass ?>">
                                        <div class="content">
                                            <a class="text-decoration-none" href="<?php the_permalink($utvaldID); ?>">
                                                <div class="responsive-background position-relative <?php echo $imgSize ?>" style="<?php if ($imgClass): ?> background-image: url('<?php echo $imgClass ?>'); <?php else: ?> background: green; <?php endif; ?>">
                                                    <div class="overlay">
                                                        <h2 class="text-center text-white mt-3 text-overlay"><?php echo $titel; ?></h2>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                    <?php 
                                    $count++;
                                endwhile;
                            endif; ?>
                        </div> 
                    </div>
                </div> 
                <?php
            endwhile;
        else: ?>
        <div class="wrapper">
            <div class="container">
                <div class="row mx-auto">
                    <div class="mt-5">
                    <p>Du har inte lagt till några utvalda inlägg...</p>
                    </div> 
                </div> 
            </div> 
        </div>  
        <?php
        endif;
    endwhile; ?>
    <?php
endif; ?>

<!----------------- SWEDACSKOLAN ----------------->

<?php

if(ICL_LANGUAGE_CODE=='sv'):
    $args = array(
        'post_type' => 'swedac_magasin',
        'posts_per_page' => 11,
        'orderby' => 'date',
        'paged' => $paged,
        'post__not_in' => $duplicates
    );
elseif(ICL_LANGUAGE_CODE=='en'):
    $args = array(
        'post_type' => 'swedac_magasin_en',
        'posts_per_page' => 11,
        'orderby' => 'date',
        'paged' => $paged,
        'post__not_in' => $duplicates
    );
endif;
$query = new WP_Query($args); 

?>

<div class="wrapper p-0">
    <div class="container-fluid p-0">
        <div class="row mx-auto">
            <div class="col-12 mt-5">
                <h1>Swedacskolan</h1>
            </div> 
            <?php if($query->have_posts()): ?>
                <?php $count = 0; ?>
                <?php while($query->have_posts() ) : $query->the_post();
                    $img_featured = wp_get_attachment_image_src( get_post_thumbnail_id($post->ID), 'swedac_magasin' );
                    $img_rektangular = get_field('swedac_magasin_rektangular');
                    $img_kvadrat = get_field('swedac_magasin_kvadratisk');

                    if ($img_kvadrat) {
                        $existingImg = $img_kvadrat['url'];
                    } elseif ($img_featured) {
                        $existingImg = $img_featured[0];
                    }

                    $widthClass = ( $count % 7 == 0 ) ? 'col-md-12 col-12' : 'col-md-4 col-12';
                    $imgClass = ( $count % 7 == 0 ) ? $img_rektangular['url'] : $existingImg;
                    $imgSize = ( $count % 7 == 0 ) ? 'h-500' : 'h-250';
                ?>
                <div class="grow py-3 py-md-4 px-0 px-md-4 <?php echo $widthClass ?>">
                    <div class="content">
                        <a class="text-decoration-none" href="<?php the_permalink(); ?>">
                            <div class="responsive-background <?php echo $imgSize ?>"
                                style="<?php if ($imgClass): ?> background-image: url('<?php echo $imgClass ?>'); <?php else: ?> background: green; <?php endif; ?>">
                            </div>
                            <div class="info pt-3 pt-md-0 mx-4 mx-md-auto">
                                <h2 class="mt-3"><?php the_title(); ?></h2>
                                <div class="text-black excerpt">
                                    <?php the_excerpt(); ?>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>

                <?php $count++;
                    
                endwhile; ?>

                <div class="col-12 my-4 mx-2 mx-md-auto">
                    <?php 
                        echo paginate_links( array(
                            'total' => $query->max_num_pages
                        )); 
                    ?>
                </div>

            <?php endif;
            wp_reset_postdata(); ?>
        </div>
    </div>
</div>