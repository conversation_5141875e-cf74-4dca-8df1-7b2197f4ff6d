<?php
include("wp-load.php");

$mysql_server = "db";
$mysql_user = "root";
// $mysql_password = "m74kYv~1";
$mysql_password = "swedac_wp";
$mysql_database = "swedac_wp";

$conn = mysqli_connect($mysql_server, $mysql_user, $mysql_password, $mysql_database);

$sql = "SELECT * FROM `wp_posts` WHERE post_type = 'ansvarigperson' AND post_status = 'publish'";
$result = mysqli_query($conn, $sql);

while($row = mysqli_fetch_assoc($result))
{
	$sidor = "";

	$sql2 = "SELECT meta_value FROM `wp_postmeta` WHERE post_id = '".$row['ID']."' AND meta_key = 'ansvarig_for_sida'";
	$result2 = mysqli_query($conn, $sql2);
	while($row2 = mysqli_fetch_assoc($result2))
	{

		$parts = explode('"',$row2['meta_value']);
		$i = 1;
		$sidor = "";
		foreach($parts as $part)
		{
			$sidor = $part;
			if($i%2 == 0)
			{
				$page_id = $sidor;
				var_dump($part);
				//Sidans namn, länk till sidan frontend, ny rad
				echo $page_id .'<br/>';

				$title 	= get_the_title($page_id);
				$url 	= get_permalink($page_id);
				if(!empty($title))
				{
					$correct_url .= '<li><a href="'.$url.'" title="'.$title.'">'.$title.'</a></li>';
				}
			}
			$i++;
		}
	}

	//Hämta epost på personen
	$sql3 = "SELECT meta_value FROM `wp_postmeta` WHERE post_id = '".$row['ID']."' AND meta_key = 'e_post_ansvarig'";
	$result3 = mysqli_query($conn, $sql3);
	$row3 = mysqli_fetch_assoc($result3);
	$mail = $row3['meta_value'];

	// Hämnta namn på personen
	$sql4 = "SELECT meta_value FROM `wp_postmeta` WHERE post_id = '".$row['ID']."' AND meta_key = 'namn_ansvarig'";
	$result4 = mysqli_query($conn, $sql4);
	$row4 = mysqli_fetch_assoc($result4);

	$namn = $row4['meta_value'];


	//Skapar mailet
	//********************************************************

	$site_url = '/ansvarigsida.php';
	//BOUNDARY (DON'T TOUCH)
	$boundary_ansvarig = uniqid('np');


	if ( $site_url ) {
		//-----------------------------------------
		//  Mail till ansvarig
		//-----------------------------------------
		//SET AN ID FOR FORM

		//TITLE IN MAIL
		$sub_ansvarig = "Kontrollera sidor | swedac.se";

		//SHORT EXCERPT VIEWED IN EG. GMAIL
		$excerpt = 'Dags att kontrollera innehåll på swedac.se';

		//MAIN MESSAGE IN MAIL
		$message = '
			<p>Hej! <br/>
			Nu är det dags att gå igenom innehållet på följande sidor och kontrollera att allt stämmer.</p>

			<b>Du ansvarar för följande sidor:</b>
			<ul style="margin:5px 0 15px 25px;">
			'.$correct_url.'
			</ul>

			<p>Om något behöver uppdateras så kontaktar du <a href="mailto:<EMAIL>"><EMAIL></a><br>
			Återkoppla till <a href="mailto:<EMAIL>"><EMAIL></a> oavsett om innehållet är korrekt eller behöver uppdateras.</p>

			<small>Om dina sidor har en engelsk version ska denna kontrolleras också.</small>

			<small>Du får detta meddelande eftersom du är satt som ansvarig för dessa sidor.</small>
		';

		//SENDERS EMAIL
		$from = '<EMAIL>';

		//SENDERS NAME
		$from_name = 'Swedac';

		//Till ansvariga
		wp_mail($mail, subject_ansvarig($sub_ansvarig), html_mail_ansvarig($sub_ansvarig,$message,$excerpt), headers_ansvarig($from, $from_name));
	}

}

function subject_ansvarig($sub_ansvarig){

	$subject_ansvarig = mb_encode_mimeheader(utf8_decode($sub_ansvarig), 'UTF-8', 'B');

	return $subject_ansvarig;
}


function headers_ansvarig($from, $from_name){

	global $boundary_ansvarig;

	$headers_ansvarig = "MIME-Version: 1.0\r\n";
	$headers_ansvarig .= "From: ".$from_name." <" . $from . ">\r\n";
	// $headers_ansvarig .= "Errors-To: ". $from . "\r\n";
	// $headers_ansvarig .= "Return-Path: ". $from . "\r\n";
	// $headers_ansvarig .= "Message-ID: <" . time() . "." . $from . ">\r\n";
  // $headers_ansvarig .= "Content-Type: multipart/alternative;boundary=" . $boundary_ansvarig . "\r\n";
  // $headers_ansvarig .= "This is a multi-part message in MIME format.\r\n";

	return $headers_ansvarig;
}


function html_mail_ansvarig($sub_ansvarig,$message,$excerpt) {

	global $boundary_ansvarig;

	$plain_text = strip_tags($message);

	$html_mail_ansvarig = "\r\n\r\n--" . $boundary_ansvarig . "\r\n";
	$html_mail_ansvarig .= "Content-type: text/plain;charset=utf-8\r\n\r\n";

	$html_mail_ansvarig .= $plain_text;
	$html_mail_ansvarig .= "\r\n\r\n--" . $boundary_ansvarig . "\r\n";

	$html_mail_ansvarig .= "Content-type:text/html;charset=UTF-8" . "\r\n";

	$html_mail_ansvarig .= '<!DOCTYPE html>
	<html lang="en">
	<head>
	  <meta charset="utf-8"> <!-- utf-8 works for most cases -->
		<meta name="viewport" content="width=device-width"> <!-- Forcing initial-scale shouldnt be necessary -->
		<meta http-equiv="X-UA-Compatible" content="IE=edge"> <!-- Use the latest (edge) version of IE rendering engine -->
	  <title>'.$sub.'</title> <!-- the <title> tag shows on email notifications on Android 4.4. -->
	</head>
	<body leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" bgcolor="#f4f4f4" style="margin:0; padding:0; -webkit-text-size-adjust:none; -ms-text-size-adjust:none;">
	<table cellpadding="0" cellspacing="0" border="0" height="100%" width="100%" bgcolor="#f4f4f4" id="bodyTable" style="border-collapse: collapse;table-layout: fixed;margin:0 auto;"><tr><td>

		<!-- Hidden Preheader Text : BEGIN -->
		<div style="display:none; visibility:hidden; opacity:0; color:transparent; height:0; width:0;line-height:0; overflow:hidden;mso-hide: all;">
			'.$excerpt.'
		</div>
		<!-- Hidden Preheader Text : END -->

	  <!-- Outlook and Lotus Notes dont support max-width but are always on desktop, so we can enforce a wide, fixed width view. -->
	  <!-- Beginning of Outlook-specific wrapper : BEGIN -->
		<!--[if (gte mso 9)|(IE)]>
	  <table width="600" align="center" cellpadding="0" cellspacing="0" border="0">
	    <tr>
	      <td>
	  <![endif]-->
	  <!-- Beginning of Outlook-specific wrapper : END -->

	  <!-- Email wrapper : BEGIN -->
	  <table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="max-width: 600px;margin: auto;" class="email-container">
	  	<tr>
	      <td style="text-align: center;padding: 40px 0;font-family: sans-serif; font-size: 16px; line-height: 24px;color: #888888;">
	        <strong>'.$sub.'</strong><br>
	      </td>
	    </tr>
	    <tr>
	    	<td>

	        <table border="0" width="100%" cellpadding="0" cellspacing="0" bgcolor="#ffffff">

	          <!-- Full Width, Fluid Column : BEGIN -->
	          <tr>
	            <td style="padding: 40px; font-family: sans-serif; font-size: 16px; line-height: 24px; color: #666666;">
	              '.$message.'
	            </td>
	          </tr>


				</td>
	      	</tr>
	          <!-- Full Width, Fluid Column : END -->

	        </table>
	      </td>
			</tr>

	    <!-- Footer : BEGIN -->
	    <tr>
	      <td style="text-align: center;padding: 40px 0;font-family: sans-serif; font-size: 11px; line-height: 18px;color: #888888;">
	        <img style="width:200px; height:37px;" src="https://www.swedac.se/wp-content/themes/swedac_theme/assets/images/swedac-logotype.png" alt="Swedac Logotyp" />


	      </td>
	    </tr>
	    <!-- Footer : END -->

	  </table>
	  <!-- Email wrapper : END -->

	  <!-- End of Outlook-specific wrapper : BEGIN -->
		<!--[if (gte mso 9)|(IE)]>
	      </td>
	    </tr>
	  </table>
	  <![endif]-->
	  <!-- End of Outlook-specific wrapper : END -->

	</td></tr></table>
	</body>
	</html>';

	$html_mail_ansvarig .= "\r\n\r\n--" . $boundary_ansvarig . "--";

	return $html_mail_ansvarig;

}

?>
