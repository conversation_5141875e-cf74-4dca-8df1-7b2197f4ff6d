<?php

/**
 * Template Name: Template 3 - Ämnesområden
 */
?>

<?php while (have_posts()) : the_post(); ?>
    <?php get_template_part('templates/page', 'header-custom'); ?>
    <div class="wrapper">
        <?php //get_template_part('templates/content', 'page'); ?>
    </div>
<?php endwhile; ?>

<div class="wrapper">
    <div id="snabbval_amnesomrodan">
        <?php if (ICL_LANGUAGE_CODE == 'sv') : ?>
            <?php
            $taxonomy  = 'amnesomraden_categories';

            //Hämta alla överkategorier
            $parentterms = get_terms(array('taxonomy' => $taxonomy, 'parent' => 0, 'hide_empty' => true));

            if ($parentterms) :
                $i = 2;
                $c = 1;
                ?>
                <div class="category_section first">
                    <div class="head">
                        <h2>Alla ämnesområden</h2>
                    </div>
                    <div class="content">
                        <?php $all_omraden = array(
                            'post_type'         => 'amnesomraden',
                            'posts_per_page'    => -1
                        );

                        $query_all = new WP_Query($all_omraden);
                        if ($query_all->have_posts()) : ?>
                            <form name="all_amnesomraden" id="all_amnesomraden_form">
                                <label class="label_select" for="all_amnesomraden_select">Ämnesområde</label>

                                <select id="all_amnesomraden_select" name="omraden">
                                    <option selected disabled>Ämnesområden A-Ö</option>
                                    <?php while ($query_all->have_posts()) : $query_all->the_post(); ?>
                                        <option value="<?php the_permalink(); ?>"><?php the_title(); ?></option>
                                    <?php endwhile; ?>
                                </select>
                                <button type="button" class="submit-button" data-form="all_amnesomraden_form" aria-label="Sök">Sök</button>
                                <div class="loading-icon amnesomraden-loading-icon">
                                    <i class="fa fa-spinner fa-spin" aria-hidden="true"></i>
                                </div>
                            </form>
                        <?php endif;
                        wp_reset_query(); ?>

                        <?php
                        $all_kontrollformer = new WP_Query(array(
                            'post_type'         => 'amnesomraden',
                            'posts_per_page'    => -1,
                            'tax_query'         => array(
                                array(
                                    'taxonomy'  => $taxonomy,
                                    'field'     => 'slug',
                                    'terms'     => 'kontrollformer',
                                )
                            )
                        ));

                        if ($all_kontrollformer->have_posts()) : ?>
                            <form name="all_kontrollformer" id="all_kontrollformer_form">
                                <label class="label_select" for="all_kontrollformer_select">Kontrollform</label>

                                <select id="all_kontrollformer_select" name="kontrollformer">
                                    <option selected disabled>Kontrollformer</option>
                                    <?php while ($all_kontrollformer->have_posts()) : $all_kontrollformer->the_post(); ?>
                                        <option value="<?php the_permalink(); ?>"><?php the_title(); ?></option>
                                    <?php endwhile; ?>
                                </select>
                                <button type="button" class="submit-button" data-form="all_kontrollformer_form" aria-label="Sök">Sök</button>
                                <div class="loading-icon kontrollformer-loading-icon">
                                    <i class="fa fa-spinner fa-spin" aria-hidden="true"></i>
                                </div>
                            </form>
                        <?php endif;
                        wp_reset_query(); ?>
                    </div>
                </div>
                <?php
                foreach ($parentterms as $parentterm) :
                    //Hämta alla underkategorier
                    $childterms = get_terms(array('taxonomy' => $taxonomy, 'parent' => $parentterm->term_id, 'hide_empty' => true));
                    ?>

                    <?php if ($childterms) : ?>
                        <div class="row mx-0 pb-4">
                            <h2><strong><?php echo $parentterm->name; ?></strong></h2>
                        </div>
                        <div class="row">
                            <?php
                            foreach ($childterms as $childterm) :
                                $term_slug = $childterm->slug;

                                $args = array(
                                    'post_type'             => 'amnesomraden',
                                    'order'                 => 'ASC',
                                    'orderby'               => 'title',
                                    'ignore_custom_sort'    => true,
                                    'posts_per_page'        => -1,
                                    'tax_query'             => array(
                                        array(
                                            'taxonomy'  => $taxonomy,
                                            'field'     => 'slug',
                                            'terms'     => $term_slug
                                        )
                                    ),
                                );

                                $my_query = new WP_Query($args);
                                ?>

                                <?php if ($my_query->have_posts()) :
                                    $c_post = 0; ?>
                                    <div class="col-12 col-sm-12 col-md-6 col-lg-4 category_section px-3" data-paragraph="<?php echo $c; ?>">
                                        <div class="head">
                                            <h2 class="title-cuter"><?php echo $childterm->name; ?></h2>
                                        </div>
                                        <div class="content">
                                            <ul class="omraden omraden_<?php echo $c; ?>" data-more="<?php echo $c; ?>">
                                                <?php while ($my_query->have_posts()) : $my_query->the_post();
                                                    $c_post++; ?>
                                                    <li <?php post_class(); ?> data-count="<?php echo $c_post; ?>">
                                                        <a href="<?php the_permalink(); ?>" title=""><?php the_title(); ?></a>
                                                    </li>
                                                <?php endwhile; ?>

                                                <span class="show_more show_more_<?php echo $c; ?>" data-toggle="<?php echo $c; ?>">Visa fler</span>
                                                <span class="show_less show_less_<?php echo $c; ?>" data-toggle-less="<?php echo $c; ?>">Visa färre</span>
                                            </ul>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <?php wp_reset_query();
                                $i++;
                                $c++;
                            endforeach; ?>
                        </div>
                    <?php endif; ?>
                <?php endforeach;
            endif; ?>

            <div class="amnesomraden_puff">
                <?php if (get_field('amnesomraden_puff_rep')) : ?>
                    <ul>
                        <?php $i = 2; ?>
                        <?php while (has_sub_field('amnesomraden_puff_rep')) : ?>
                            <li <?php if ($i % 3 == 0) {
                                    echo 'class="middle"';
                                } ?>>
                                <div class="rubrik">
                                    <h2>
                                        <a href="<?php echo get_sub_field('lankmal'); ?>" title="<?php echo get_sub_field('lanknamn'); ?>"><?php echo get_sub_field('lanknamn'); ?></a>
                                    </h2>
                                </div>
                            </li>
                        <?php $i++;
                        endwhile;  ?>
                    </ul>
                <?php endif; ?>
            </div>

        <?php elseif (ICL_LANGUAGE_CODE == 'en') : ?>
            <?php
            $taxonomy = 'amnesomraden_categories';
            $term_args = array(
                //Tar bort kategorin Ledningssystem på sidan ämnesområden
                'exclude'   => array(42, 80)
            );
            $terms = get_terms($taxonomy, $term_args);

            if ($terms) :
                $i = 2;
                $c = 1; ?>
                <div id="grid">
                    <div class="category_section first">
                        <div class="head">
                            <h2>All Working areas</h2>
                        </div>
                        <div class="content">
                            <span>Choose your working area from the menu below, or search among our categories.</span>

                            <?php $all_omraden = array(
                                'post_type'         => 'amnesomraden',
                                'posts_per_page'    => -1
                            );

                            $query_all = new WP_Query($all_omraden);
                            if ($query_all->have_posts()) : ?>
                                <form name="all_amnesomraden">
                                    <label class="label_select" for="all_amnesomraden_select">Working area</label>

                                    <select id="all_amnesomraden_select" name="omraden" OnChange="location.href=all_amnesomraden.omraden.options[selectedIndex].value">
                                        <option selected disabled>Choose working area</option>
                                        <?php while ($query_all->have_posts()) : $query_all->the_post(); ?>
                                            <option value="<?php the_permalink(); ?>"><?php the_title(); ?></option>
                                        <?php endwhile; ?>
                                    </select>
                                </form>
                            <?php endif;
                            wp_reset_query(); ?>
                        </div>
                    </div>

                    <?php foreach ($terms as $term) :
                        $term_slug = $term->slug;

                        $args = array(
                            'post_type'     => 'amnesomraden',
                            $taxonomy       => $term_slug,
                        );

                        $my_query = new WP_Query($args); ?>

                        <?php if ($my_query->have_posts()) : 
                                    $c_post = 0; ?>
                            <div class="col-12 col-sm-12 col-md-6 col-lg-4 category_section px-3" data-paragraph="<?php echo $c; ?>"> 
                                <!-- category_section <?php //if ($i % 3 == 0) { echo 'middle '; } ?> -->
                                <div class="head">
                                    <h2 class="title-cuter"><?php echo $term->name; ?></h2>
                                </div>
                                <div class="content">
                                    <ul class="omraden omraden_<?php echo $c; ?>" data-more="<?php echo $c; ?>">
                                        <?php while ($my_query->have_posts()) : $my_query->the_post();
                                            $c_post++; ?>
                                            <li <?php post_class(); ?> data-count="<?php echo $c_post; ?>">
                                                <a href="<?php the_permalink(); ?>" title=""><?php the_title(); ?></a>
                                            </li>
                                        <?php endwhile; ?>

                                        <span class="show_more show_more_<?php echo $c; ?>" data-toggle="<?php echo $c; ?>">Visa fler</span>
                                        <span class="show_less show_less_<?php echo $c; ?>" data-toggle-less="<?php echo $c; ?>">Visa färre</span>
                                    </ul>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php wp_reset_query();
                        $i++;
                        $c++;
                    endforeach; ?>
                    <?php if ((get_field('amnesomrade_gemensamma_termer_rubrik_en')) && (get_field('amnesomrade_gemensamma_termer_rep_en'))) : ?>
                        <div class="category_section last">
                            <div class="head">
                                <h2><?php echo get_field('amnesomrade_gemensamma_termer_rubrik_en'); ?></h2>
                            </div>
                            <div class="content">
                                <?php if (get_field('amnesomrade_gemensamma_termer_rep_en')) : ?>
                                    <ul>
                                        <?php while (has_sub_field('amnesomrade_gemensamma_termer_rep_en')) : ?>
                                            <li>
                                                <a href="<?php echo get_sub_field('lankmal'); ?>" title="<?php echo get_sub_field('lanknamn'); ?>"><?php echo get_sub_field('lanknamn'); ?></a>
                                            </li>
                                        <?php endwhile; ?>
                                    </ul>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                </div> <!-- // End of #grid -->
            <?php endif;
            wp_reset_query();  // Restore global post data stomped by the_post().
            ?>

            <?php if (get_field('amnesomraden_puff_rep_en')) : ?>
                <div class="amnesomraden_puff">
                    <?php if (get_field('amnesomraden_puff_rep_en')) : ?>
                        <ul>
                            <?php $i = 2; ?>
                            <?php while (has_sub_field('amnesomraden_puff_rep_en')) : ?>
                                <li <?php if ($i % 3 == 0) :
                                        echo 'class="middle"';
                                    endif; ?>>
                                    <div class="rubrik">
                                        <h2>
                                            <a href="<?php echo get_sub_field('lankmal'); ?>" title="<?php echo get_sub_field('lanknamn'); ?>"><?php echo get_sub_field('lanknamn'); ?></a>
                                        </h2>
                                    </div>
                                </li>
                                <?php $i++;
                            endwhile;  ?>
                        </ul>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>
