<?php

require_once "src/ellipticcurve.php";


$success = 0;
$failure = 0;

function assertEqual($a, $b) {
    if ($a != $b) {
        global $failure;
        $failure ++;
        echo "\n      FAIL: '" . $a . "' != '" . $b . "'";
    } else {
        global $success;
        $success ++;
        echo "\n      success";
    }
}

function printHeader($text) {
    echo "\n  " . $text . " test:";
}

function printSubHeader($text) {
    echo "\n    " . $text . ":";
}


echo "\n\nRunning ECDSA tests:";

printHeader("ECDSA");

printSubHeader("testVerifyRightMessage");
$privateKey = new EllipticCurve\PrivateKey;
$publicKey = $privateKey->publicKey();
$message = "This is the right message";
$signature = EllipticCurve\Ecdsa::sign($message, $privateKey);
assertEqual(EllipticCurve\Ecdsa::verify($message, $signature, $publicKey), true);

printSubHeader("testVerifyWrongMessage");
$privateKey = new EllipticCurve\PrivateKey;
$publicKey = $privateKey->publicKey();
$message1 = "This is the right message";
$message2 = "This is the wrong message";
$signature = EllipticCurve\Ecdsa::sign($message1, $privateKey);
assertEqual(EllipticCurve\Ecdsa::verify($message2, $signature, $publicKey), false);


printHeader("openSSL");

printSubHeader("testAssign");
// Generated by: openssl ecparam -name secp256k1 -genkey -out privateKey.pem
$privateKeyPem = EllipticCurve\Utils\File::read("tests/privateKey.pem");
$privateKey = EllipticCurve\PrivateKey::fromPem($privateKeyPem);
$message = EllipticCurve\Utils\File::read("tests/message.txt");
$signature = EllipticCurve\Ecdsa::sign($message, $privateKey);
$publicKey = $privateKey->publicKey();
assertEqual(EllipticCurve\Ecdsa::verify($message, $signature, $publicKey), true);

printSubHeader("testVerifySignature");
// openssl ec -in privateKey.pem -pubout -out publicKey.pem
$publicKeyPem = EllipticCurve\Utils\File::read("tests/publicKey.pem");
// openssl dgst -sha256 -sign privateKey.pem -out signature.binary message.txt
$signatureDer = EllipticCurve\Utils\File::read("tests/signatureDer.txt");
$message = EllipticCurve\Utils\File::read("tests/message.txt");
$publicKey = EllipticCurve\PublicKey::fromPem($publicKeyPem);
$signature = EllipticCurve\Signature::fromDer($signatureDer);
assertEqual(EllipticCurve\Ecdsa::verify($message, $signature, $publicKey), true);


printHeader("PrivateKey");

printSubHeader("testPemConversion");
$privateKey1 = new EllipticCurve\PrivateKey;
$pem = $privateKey1->toPem();
$privateKey2 = EllipticCurve\PrivateKey::fromPem($pem);
assertEqual($privateKey1->toPem(), $privateKey2->toPem());

printSubHeader("testDerConversion");
$privateKey1 = new EllipticCurve\PrivateKey;
$der = $privateKey1->toDer();
$privateKey2 = EllipticCurve\PrivateKey::fromDer($der);
assertEqual($privateKey1->toPem(), $privateKey2->toPem());

printSubHeader("testStringConversion");
$privateKey1 = new EllipticCurve\PrivateKey;
$str = $privateKey1->toString();
$privateKey2 = EllipticCurve\PrivateKey::fromString($str);
assertEqual($privateKey1->toPem(), $privateKey2->toPem());


printHeader("PublicKey");

printSubHeader("testPemConversion");
$privateKey = new EllipticCurve\PrivateKey;
$publicKey1 = $privateKey->publicKey();
$pem = $publicKey1->toPem();
$publicKey2 = EllipticCurve\PublicKey::fromPem($pem);
assertEqual($publicKey1->toPem(), $publicKey2->toPem());

printSubHeader("testDerConversion");
$privateKey = new EllipticCurve\PrivateKey;
$publicKey1 = $privateKey->publicKey();
$der = $publicKey1->toDer();
$publicKey2 = EllipticCurve\PublicKey::fromDer($der);
assertEqual($publicKey1->toPem(), $publicKey2->toPem());


printSubHeader("testStringConversion");
$privateKey = new EllipticCurve\PrivateKey;
$publicKey1 = $privateKey->publicKey();
$str = $publicKey1->toString();
$publicKey2 = EllipticCurve\PublicKey::fromString($str);
assertEqual($publicKey1->toPem(), $publicKey2->toPem());


printHeader("Signature");

printSubHeader("testDerConversion");
$privateKey = new EllipticCurve\PrivateKey;
$message = "This is a text message";
$signature1 = EllipticCurve\Ecdsa::sign($message, $privateKey);
$der = $signature1->toDer();
$signature2 = EllipticCurve\Signature::fromDer($der);

printSubHeader("testBase64Conversion");
$privateKey = new EllipticCurve\PrivateKey;
$message = "This is a text message";
$signature1 = EllipticCurve\Ecdsa::sign($message, $privateKey);
$base64 = $signature1->toBase64();
$signature2 = EllipticCurve\Signature::fromBase64($base64);


if ($failure == 0) {
    echo "\n\nALL " . $success . " TESTS SUCCESSFUL\n\n";
} else {
    echo "\n\n" . $failure . "/" . ($failure + $success) . " FAILURES OCCURRED\n\n";
}

?>
