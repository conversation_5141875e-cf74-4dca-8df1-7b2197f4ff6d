<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => 'dev-main',
        'version' => 'dev-main',
        'reference' => 'a5e992cdfcadd3d983f899e622db09eb750f27e2',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => 'a5e992cdfcadd3d983f899e622db09eb750f27e2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sendgrid/php-http-client' => array(
            'pretty_version' => '3.11.1',
            'version' => '3.11.1.0',
            'reference' => 'b7e45dc6734357253217d63bf0d661fe18013e48',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sendgrid/php-http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sendgrid/sendgrid' => array(
            'pretty_version' => '7.8.3',
            'version' => '7.8.3.0',
            'reference' => '3883392ceb5066be4607c47de7fda9c5e7dbcfa4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sendgrid/sendgrid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sendgrid/sendgrid-php' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'smalot/pdfparser' => array(
            'pretty_version' => 'v2.11.0',
            'version' => '2.11.0.0',
            'reference' => 'ac8e6678b0940e4b2ccd5caadd3fb18e68093be6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../smalot/pdfparser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'starkbank/ecdsa' => array(
            'pretty_version' => '0.0.4',
            'version' => '0.0.4.0',
            'reference' => '9369d35ed9019321adb4eb9fd3be21357d527c74',
            'type' => 'library',
            'install_path' => __DIR__ . '/../starkbank/ecdsa',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '85181ba99b2345b0ef10ce42ecac37612d9fd341',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
