{"packages": [{"name": "sendgrid/php-http-client", "version": "3.11.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/sendgrid/php-http-client.git", "reference": "b7e45dc6734357253217d63bf0d661fe18013e48"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sendgrid/php-http-client/zipball/b7e45dc6734357253217d63bf0d661fe18013e48", "reference": "b7e45dc6734357253217d63bf0d661fe18013e48", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "php": ">=5.6"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "phpunit/phpunit": "^5.7 || ^6.5", "sebastian/version": "^1.0.6", "squizlabs/php_codesniffer": "~2.0"}, "time": "2020-08-19T19:09:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"SendGrid\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "HTTP REST client, simplified for PHP", "homepage": "http://github.com/sendgrid/php-http-client", "keywords": ["api", "fluent", "http", "rest", "sendgrid"], "install-path": "../sendgrid/php-http-client"}, {"name": "sendgrid/sendgrid", "version": "7.8.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sendgrid/sendgrid-php.git", "reference": "3883392ceb5066be4607c47de7fda9c5e7dbcfa4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sendgrid/sendgrid-php/zipball/3883392ceb5066be4607c47de7fda9c5e7dbcfa4", "reference": "3883392ceb5066be4607c47de7fda9c5e7dbcfa4", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "php": ">=5.6", "sendgrid/php-http-client": "~3.10", "starkbank/ecdsa": "0.*"}, "replace": {"sendgrid/sendgrid-php": "*"}, "require-dev": {"phpunit/phpunit": "^5.7.9 || ^6.4.3", "squizlabs/php_codesniffer": "3.*", "swaggest/json-diff": "^3.4"}, "time": "2020-09-02T19:58:01+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"SendGrid\\Contacts\\": "lib/contacts/", "SendGrid\\EventWebhook\\": "lib/eventwebhook/", "SendGrid\\Helper\\": "lib/helper/", "SendGrid\\Mail\\": "lib/mail/", "SendGrid\\Stats\\": "lib/stats/"}, "classmap": ["lib/BaseSendGridClientInterface.php", "lib/SendGrid.php", "lib/TwilioEmail.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "This library allows you to quickly and easily send emails through Twilio SendGrid using PHP.", "homepage": "http://github.com/sendgrid/sendgrid-php", "keywords": ["email", "grid", "send", "sendgrid", "twi<PERSON>"], "install-path": "../sendgrid/sendgrid"}, {"name": "smalot/pdfparser", "version": "v2.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/smalot/pdfparser.git", "reference": "ac8e6678b0940e4b2ccd5caadd3fb18e68093be6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/smalot/pdfparser/zipball/ac8e6678b0940e4b2ccd5caadd3fb18e68093be6", "reference": "ac8e6678b0940e4b2ccd5caadd3fb18e68093be6", "shasum": ""}, "require": {"ext-iconv": "*", "ext-zlib": "*", "php": ">=7.1", "symfony/polyfill-mbstring": "^1.18"}, "time": "2024-08-16T06:48:03+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"Smalot\\PdfParser\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Pdf parser library. Can read and extract information from pdf file.", "homepage": "https://www.pdfparser.org", "keywords": ["extract", "parse", "parser", "pdf", "text"], "support": {"issues": "https://github.com/smalot/pdfparser/issues", "source": "https://github.com/smalot/pdfparser/tree/v2.11.0"}, "install-path": "../smalot/pdfparser"}, {"name": "starkbank/ecdsa", "version": "0.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/starkbank/ecdsa-php.git", "reference": "9369d35ed9019321adb4eb9fd3be21357d527c74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/starkbank/ecdsa-php/zipball/9369d35ed9019321adb4eb9fd3be21357d527c74", "reference": "9369d35ed9019321adb4eb9fd3be21357d527c74", "shasum": ""}, "require": {"php": ">=5.5"}, "time": "2020-05-15T15:46:20+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/ellipticcurve.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "StarkBank", "email": "<EMAIL>", "homepage": "https://starkbank.com", "role": "Developer"}], "description": "fast openSSL-compatible implementation of the Elliptic Curve Digital Signature Algorithm (ECDSA)", "homepage": "https://github.com/starkbank/ecdsa-php", "install-path": "../starkbank/ecdsa"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}], "dev": true, "dev-package-names": []}