{"name": "smalot/pdfparser", "description": "Pdf parser library. Can read and extract information from pdf file.", "keywords": ["PDF", "text", "parser", "parse", "extract"], "type": "library", "license": "LGPL-3.0", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/smalot/pdfparser/issues"}, "homepage": "https://www.pdfparser.org", "require": {"php": ">=7.1", "symfony/polyfill-mbstring": "^1.18", "ext-zlib": "*", "ext-iconv": "*"}, "autoload": {"psr-0": {"Smalot\\PdfParser\\": "src/"}}, "autoload-dev": {"psr-4": {"PerformanceTests\\": "tests/Performance/", "PHPUnitTests\\": "tests/PHPUnit/"}}, "config": {"process-timeout": 1200}}