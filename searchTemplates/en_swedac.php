<?php 
global $wpdb;

$search_term = get_search_query(); ?>
<div id="swedacResults" class="searchResultSection">
    <div class="wrapper">
        <?php
        $searchQuery = new WP_Query(
            wp_parse_args(
                $wp_query->query,
                array(
                    'posts_per_page' => -1,
                    'post_type' => array(
                        'page', 'post', 'amnesomraden',
                        'kalender', 'bloggen', 'swedac_magasin',
                        'kurser', 'kravspecifikationer',
                    )
                )
            )
        );
        
        $count_result = $searchQuery->post_count;

        if ($count_result == 0) {
            // Hämta alla sökresultat från databasen som gav en träff
            $sql = "SELECT * FROM `wp_serach_log_en` WHERE `traffar` > 0";
            $result = $wpdb->get_results($sql, 'ARRAY_A');

            if ($result != null) {
                $i = 0;

                $similar_results = [];

                foreach ($result as $index => $row) {
                    // Kolla hur likt det nuvarande sökordet är med det från databasen
                    similar_text(trim($search_term), $row['term'], $perc);                   

                    if ($perc >= 80) {
                        $similar_text[] = [
                            'match' => $perc,
                            'term' => $row['term'],
                            'url' => $row['url'],
                        ];
                    }
                }

                if (!empty($similar_text)) {
                    rsort($similar_text);

                    echo '<ul class="did_you_mean_suggestion_list en">';
                    echo '<h3>Did you mean:</h3>'; 
                    
                    foreach ($similar_text as $index => $text) {
                        if ($index <= 4) {
                            echo '<li><a href="' . $text['url'] . '" target="_blank" rel="noopener noreferrer">' . $text['term'] . '</a></li>';
                        }
                    }

                    echo '</ul>';
                }
            }
        }

        if ($searchQuery->have_posts()) : ?>
            <div class="search_result">
                 <h2>Swedac.se</h2>
                <?php while (have_posts()) : the_post(); ?>
                    <article <?php post_class('search_post'); ?>>
                        <?php $get_post_image = wp_get_attachment_image_src( get_post_thumbnail_id($post->ID), 'thumbnail' );
                        $image_url = $get_post_image['0']; ?>
                        <div class="left">
                            <div class="meta">
                                <?php get_template_part('templates/entry-meta') ?>
                            </div>
                            <h3><a href="<?php the_permalink(); ?>" alt="<?php the_title(); ?>"><?php the_title(); ?></a></h3>
                            <div class="excerpt">
                                <?php the_excerpt(); ?>
                            </div>
                        </div>
                        <div class="right">
                            <?php if ( has_post_thumbnail() ) { ?>
                                <a href="<?php the_permalink(); ?>" alt="<?php the_title(); ?>">
                                    <img data-src="<?php echo $image_url; ?>" alt="<?php the_title(); ?>" />
                                </a>
                            <?php } ?>
                        </div>
                    </article>
                <?php endwhile; ?>

                <div class="search_result_buttons">
                    <?php if($count_result < 10) { ?>
                        <small class="small_post_page">Showing 1-<?php echo $count_result; ?> of <?php echo $count_result; ?> <?php if($count_result == 1) {echo " result";} else {echo " results";} ?></small>
                    <?php } else { ?>
                        <small class="small_post_page">Showing 1-10 of <?php echo $count_result; ?> results</small>
                    <?php } ?>
                    <input type="hidden" id="pagePostCountResults" value="<?php echo $count_result; ?><?php if($count_result == 1) {echo " result";} else {echo " results";} ?>">
                    <small class="small_post_page_total">Showing all <?php echo $count_result; ?> results</small>

                    <?php if($count_result > 10) { ?>
                        <span id="loadMorePosts" class="visa_fler_traffar"><i class="fa fa-spinner fa-spin loading-icon" aria-hidden="true"></i> Show all</span>
                    <?php } ?>
                </div>
            </div>
        <?php else: ?>

            <input type="hidden" id="pagePostCountResults" value="0 results">
            <div class="alert alert-warning">
                <h2>Swedac.se</h2>
                <strong>Nothing found</strong>
                <em><?php echo sprintf( __( 'The keyword '), $searchQuery->found_posts ); echo '<span class="term">' . get_search_query() .'</span> gave unfortunately no hit in the search source swedac.se.'; ?></em>
            </div>

        <?php endif; wp_reset_postdata(); ?>
        <?php

        global $wpdb;

        $search_term = utf8_decode($search_term);
        $url = utf8_decode($url);

        $sql_get = $wpdb->prepare("SELECT `term` FROM `wp_serach_log_en` WHERE `term` = %s", $search_term);
        $result = $wpdb->get_results($sql_get);

        if ($wpdb->num_rows > 0) {
            $sql = $wpdb->prepare(
                "UPDATE `wp_serach_log_en` SET `antal_sokningar` = `antal_sokningar` + 1 WHERE `term` = %s",
                $search_term
            );
        } else {
            $sql = $wpdb->prepare(
                "INSERT INTO `wp_serach_log_en` (`term`, `antal_sokningar`, `url`, `traffar`) VALUES (%s, '1', %s, %s)",
                $search_term, $url, $count_result
            );
        }

        $wpdb->query($sql);

        ?>
        <br class="clear" />
    </div>
</div>
