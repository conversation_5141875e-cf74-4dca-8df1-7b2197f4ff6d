<div id="documentResults" class="searchResultSection">
    <div class="wrapper">
        <div class="search_result_dokument">
            <div class="senaste_dokument_result">
                <?php
                // Get data from URL into variables
                $fritext = $_GET['s'] != '' ? $_GET['s'] : '';
                $array = array();
                $pt_doc_for_args = array(
                    'posts_per_page' 	=> -1,
                    'post_type'     =>  'document_eng',
                    's'             =>  $get_search_query,
                    'meta_query' => array(
                            'relation' => 'OR',
                            array(
                                'key'     => 'crm_doc_title_en',
                                'value'   => $fritext,
                                'compare' => 'LIKE',
                            ),
                            array(
                                'key'     => 'crm_doc_dokumentbeteckning_en',
                                'value'   => $fritext,
                                'compare' => 'LIKE',
                            ),
                            array(
                                'key'     => 'crm_doc_type_en',
                                'value'   => $fritext,
                                'compare' => 'LIKE',
                            ),
                            array(
                                'key'     => 'crm_doc_text_content_en',
                                'value'   => $fritext,
                                'compare' => 'LIKE',
                            ),
                        )
                    );

                    $array = array();
                    $array1 = array();
                    $array2 = array();
                    $array3 = array();

                    $posts = get_posts($pt_doc_for_args);
                    foreach ($posts as $post){
                        $fileUrl = get_field('crm_doc_doc_en', $post->ID);
                        if(!empty($fileUrl)) {
                            //Hämtar fält
                            $subject = get_field('crm_doc_dokumentbeteckning_en',$post->ID);
                            $subject2 = get_field('crm_doc_dokumentbeteckning_en',$post->ID);

                            // Explodar efter STAFS och : samt hämtar ut årdet

                            if(strpos($subject2, 'STAFS ') !== false) {
                                $field = end(explode('STAFS ', $subject2));

                                $field = substr($field, 0, 4);
                                $result = end(explode(':', $subject));

                                //Lägger till 0 innan nummer som är lägre än 10
                                if($result < 10) {
                                    $result = '0'.$result;
                                }

                                //Slår ihop variabler
                                $result = $field . $result;

                                //Lägger in allt i en array med värden ovan
                                array_push( $array, array(
                                    "title" 	=> get_the_title($post->ID),
                                    "id" 		=> get_the_id($post->ID),
                                    "link" 		=> get_permalink($post->ID),
                                    "crm_doc_dokumentbeteckning_en" => $result,
                                    "crm_doc_doc" 	=> get_field('crm_doc_doc_en', $post->ID),
                                    "crm_doc_type" 	=> get_field('crm_doc_type_en', $post->ID)
                                ));

                            } elseif (strpos($subject2, 'SWEDAC DOC ') !== false) {

                                $field = end(explode('SWEDAC DOC ', $subject2));

                                $field = substr($field, 0, 2); // hämtar första 2

                                $result = end(explode(':', $subject));
                                $result = current(explode("0", $field));
                                //Lägger till 0 innan nummer som är lägre än 10
                                if($result < 10) {
                                    $result = '0'.$result;
                                }
                                if($field < 10) {
                                    // $field = current(explode("0", $field));
                                }

                                //Slår ihop variabler
                                $result = $field . $result;

                                //Lägger in allt i en array med värden ovan
                                array_push( $array1, array(
                                    "title" 	=> get_the_title($post->ID),
                                    "id" 		=> get_the_id($post->ID),
                                    "link" 		=> get_permalink($post->ID),
                                    "crm_doc_dokumentbeteckning_en" => $result,
                                    "crm_doc_doc" 	=> get_field('crm_doc_doc_en', $post->ID),
                                    "crm_doc_type" 	=> get_field('crm_doc_type_en', $post->ID)
                                ));

                            } elseif (strpos($subject2, 'REP ') !== false) {
                                $field = end(explode('REP ', $subject2));

                                $field = substr($field, 0, 2); // hämtar första 2

                                $result = end(explode(':', $subject));
                                $result = current(explode("0", $field));
                                //Lägger till 0 innan nummer som är lägre än 10
                                if($result < 10) {
                                    $result = '0'.$result;
                                }
                                if($field < 10) {
                                    // $field = current(explode("0", $field));
                                }

                                //Slår ihop variabler
                                $result = $field . $result;

                                //Lägger in allt i en array med värden ovan
                                array_push( $array2, array(
                                    "title" 	=> get_the_title($post->ID),
                                    "id" 		=> get_the_id($post->ID),
                                    "link" 		=> get_permalink($post->ID),
                                    "crm_doc_dokumentbeteckning_en" => $result,
                                    "crm_doc_doc" 	=> get_field('crm_doc_doc_en', $post->ID),
                                    "crm_doc_type" 	=> get_field('crm_doc_type_en', $post->ID)
                                ));


                            } elseif (strpos($subject2, 'INFO ') !== false) {
                                $field = end(explode('INFO ', $subject2));
                                $field = substr($field, 0, 2); // hämtar första 2

                                $result = end(explode(':', $subject));
                                $result = current(explode("0", $field));
                                //Lägger till 0 innan nummer som är lägre än 10
                                if($result < 10) {
                                    $result = '0'.$result;
                                }
                                if($field < 10) {
                                    // $field = current(explode("0", $field));
                                }

                                //Slår ihop variabler
                                $result = $field . $result;

                                //Lägger in allt i en array med värden ovan
                                array_push( $array3, array(
                                    "title" 	=> get_the_title($post->ID),
                                    "id" 		=> get_the_id($post->ID),
                                    "link" 		=> get_permalink($post->ID),
                                    "crm_doc_dokumentbeteckning_en" => $result,
                                    "crm_doc_doc" 	=> get_field('crm_doc_doc_en', $post->ID),
                                    "crm_doc_type" 	=> get_field('crm_doc_type_en', $post->ID)
                                ));
                            }
                        }
                    }



                    //Sorterar på Störst > Lägst ex. 1993-2017
                    usort($array, 'sortById');
                    usort($array1, 'sortById');
                    usort($array2, 'sortById');
                    usort($array3, 'sortById');

                    $array_first = array_merge($array, $array1);
                    $array_second = array_merge($array2, $array3);

                    $array = array_merge($array_first, $array_second); ?>

                    <h2>Regulations & documents</h2>
                    <div class="table_wrapper">
                        <table id="docs_result">
                            <thead>
                                <tr>
                                    <th>DOC.NR</th>
                                    <th>TITLE</th>
                                    <th>TYPE OF DOCUMENT</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($array as $item) {

                                    $title 		= $item['title'];
                                    $id 		= $item['id'];
                                    $url 		= $item['link'];
                                    $doc 		= $item['crm_doc_doc'];
                                    $doc_type 	= $item['crm_doc_type'];

                                    $doc_bet = get_field('crm_doc_dokumentbeteckning_en', $id);
                                    $doc_bet_2 = substr($doc_bet, 6); // returns "de"
                                ?>
                                    <?php if($doc) { ?>
                                        <tr>
                                            <td><?php echo $doc_bet; ?></td>
                                            <td class="docTitle"><a href="<?php echo $url; ?>" title="<?php echo $title; ?>"><?php echo $title; ?></a></td>
                                            <td><?php echo $doc_type; ?></td>
                                        </tr>
                                    <?php } ?>
                                <?php } ?>
                            </tbody>
                        </table>

                        <?php if(empty($array)) { ?>
                            <div class="alert alert-warning">
                                <strong>Nothing found</strong>
                                <em><?php echo sprintf( __( 'The keyword '), $wp_query->found_posts ); echo '<span class="term">' . get_search_query() .'</span> gave unfortunately no hit in the search source Regulations & documents.'; ?></em>
                            </div>
                            <input type="hidden" id="documentCountResults" value="0 results">

                        <?php } else { ?>
                            <div class="search_result_buttons">
                                <?php $docCount = count($array); ?>
                                <?php if($docCount < 10) { ?>
                                    <small class="smallDocs">Shows 1-<?php echo $docCount; ?> of <?php echo $docCount; ?> <?php if($docCount == 1) {echo " result";} else {echo " results";} ?></small>
                                <?php } else { ?>
                                    <small class="smallDocs">Shows 1-10 of <?php echo $docCount; ?> results</small>
                                <?php } ?>
                                <input type="hidden" id="documentCountResults" value="<?php echo $docCount; ?><?php if($docCount == 1) {echo " result";} else {echo " results";} ?>">
                                <small class="smallDocs_total">Showing all <?php echo $docCount; ?> results</small>

                                <?php if($docCount > 10) { ?>
                                    <span id="loadMoreDocs" class="visa_fler_traffar"><i class="fa fa-spinner fa-spin loading-icon" aria-hidden="true"></i> Show all</span>
                                <?php } ?>

                                <a href="https://www.swedac.se/law-order/swedacs-regulations/search-regulations-documents/?lang=en">
                                    <span class="avancerad_sokning">
                                        <i class="fa fa-external-link" aria-hidden="true"></i> Advanced search
                                    </span>
                                </a>
                            </div>
                        <?php } ?>
                    </div>
            </div>
        </div>
        <br class="clear" />
    </div>
</div>
