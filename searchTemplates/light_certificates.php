<div id="certificateResult" class="searchResultSection">
    <div class="wrapper">
        <div class="search_result_certifikat">
        <div class="senaste_certifikat_result">
            <?php
            // Get data from URL into variables
            $fritext = $_GET['s'] != '' ? $_GET['s'] : '';
            $array = array();
            $pt_doc_for_args = array(
                'posts_per_page' 	=> -1,
                'post_type'     =>  'certifikat',
                's'             =>  get_search_query(),
                'meta_query' => array(
                        'relation' => 'OR',
                        array(
                            'key'     => 'crm_cert_title',
                            'value'   => $fritext,
                            'compare' => 'LIKE',
                        ),
                        array(
                            'key'     => 'crm_cert_dokumentbeteckning',
                            'value'   => $fritext,
                            'compare' => 'LIKE',
                        ),
                        array(
                            'key'     => 'crm_cert_type',
                            'value'   => $fritext,
                            'compare' => 'LIKE',
                        ),
                        array(
                            'key'     => 'crm_cert_description',
                            'value'   => $fritext,
                            'compare' => 'LIKE',
                        ),
                    )
                );

                $array = array();
                $array1 = array();
                $array2 = array();
                $array3 = array();

                $posts = get_posts($pt_doc_for_args);
                foreach ($posts as $post){

                    $fileUrl = get_field('crm_cert_doc', $post->ID);
                    if(!empty($fileUrl)) {
                        //Hämtar fält
                        $subject = get_field('crm_cert_type',$post->ID);

                        if($subject == 'cottt'){
                            //Lägger in allt i en array med värden ovan
                            array_push( $array, array(
                                "title" 	=> get_the_title($post->ID),
                                "id" 		=> get_the_id($post->ID),
                                "link" 		=> get_permalink($post->ID),
                                "crm_cert_dokumentbeteckning" => __('Certifikat om tillsatsanordningar till taxametrar', 'certificates'),
                                "crm_cert_doc" 	=> get_field('crm_cert_doc', $post->ID),
                                "crm_cert_type" 	=> get_field('crm_cert_type', $post->ID)
                            ));

                        } elseif ($subject == 'cumtawg') {
                            //Lägger in allt i en array med värden ovan
                            array_push( $array1, array(
                                "title" 	=> get_the_title($post->ID),
                                "id" 		=> get_the_id($post->ID),
                                "link" 		=> get_permalink($post->ID),
                                "crm_cert_dokumentbeteckning" => __('Certifikat utfärdade med stöd av WELMEC Guide 8.8', 'certificates'),
                                "crm_cert_doc" 	=> get_field('crm_cert_doc', $post->ID),
                                "crm_cert_type" 	=> get_field('crm_cert_type', $post->ID)
                            ));

                        } elseif ($subject == 'eoemk') {

                            //Lägger in allt i en array med värden ovan
                            array_push( $array2, array(
                                "title" 	=> get_the_title($post->ID),
                                "id" 		=> get_the_id($post->ID),
                                "link" 		=> get_permalink($post->ID),
                                "crm_cert_dokumentbeteckning" => __('EU-typintyg och EU-intyg om konstruktionskontroll (MID 2014/32/EU)', 'certificates'),
                                "crm_cert_doc" 	=> get_field('crm_cert_doc', $post->ID),
                                "crm_cert_type" 	=> get_field('crm_cert_type', $post->ID)
                            ));


                        } elseif ($subject == 'gakmd') {

                            //Lägger in allt i en array med värden ovan
                            array_push( $array2, array(
                                "title" 	=> get_the_title($post->ID),
                                "id" 		=> get_the_id($post->ID),
                                "link" 		=> get_permalink($post->ID),
                                "crm_cert_dokumentbeteckning" => __('Godkännanden av kvalitetssystem, modul D', 'certificates'),
                                "crm_cert_doc" 	=> get_field('crm_cert_doc', $post->ID),
                                "crm_cert_type" 	=> get_field('crm_cert_type', $post->ID)
                            ));


                        }
                    }

                }

                usort($array, 'sortById');
                usort($array1, 'sortById');
                usort($array2, 'sortById');
                usort($array3, 'sortById');

                $array_first = array_merge($array, $array1);
                $array_second = array_merge($array2, $array3);

                $array = array_merge($array_first, $array_second); ?>

                <h2><?php _e('Certifikat och Godkännanden', 'certificates')?></h2>
                <div class="table_wrapper">
                    <table id="cert_result">
                        <thead>
                            <tr>
                                <th><?php _e('CERT.NR', 'certificates')?></th>
                                <th><?php _e('RUBRIK', 'certificates')?></th>
                                <th><?php _e('TYP AV CERTIFIKAT', 'certificates')?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($array as $item) {

                                $title 		= $item['title'];
                                $id 		= $item['id'];
                                $url 		= $item['link'];
                                $doc 		= $item['crm_cert_doc'];
                                switch ($item['crm_cert_type']) {
                                    case 'cottt':
                                        $doc_type = 'Certifikat om tillsatsanordningar till taxametrar';
                                        break;
                                    
                                    case 'cumtawg':
                                        $doc_type = 'Certifikat utfärdade med stöd av WELMEC Guide 8.8';
                                        break;
                                    
                                    case 'eoemk':
                                        $doc_type = 'EU-typintyg och EU-intyg om konstruktionskontroll (MID 2014/32/EU)';
                                        break;

                                    case 'gakmd':
                                        $doc_type = 'Godkännanden av kvalitetssystem, modul D';
                                        break;
                                    
                                    default:
                                        $doc_type = 'Certifikat om tillsatsanordningar till taxametrar';
                                        break;
                                }
                                

                                $doc_bet = get_field('crm_cert_dokumentbeteckning', $id);
                                $doc_bet_2 = substr($doc_bet, 6); // returns "de"
                            ?>
                                <?php if($doc) { ?>
                                    <tr>
                                        <td><?php echo $doc_bet; ?></td>
                                        <td class="certTitle"><a href="<?php echo $url; ?>"><?php echo __($title, 'certificates'); ?></a></td>
                                        <td><?php echo $doc_type; ?></td>
                                    </tr>
                                <?php } ?>
                            <?php } ?>
                        </tbody>
                    </table>
                    <?php if(empty($array)) { ?>
                        <div class="alert alert-warning">
                            <strong><?php _e('Inget hittades', 'certificates')?></strong>
                            <em><?php echo  __( 'Sökordet ')  ; echo '<span class="term">' . get_search_query() .'</span>'. __(' gav dessvärre ingen träff i sökkällan Certifikat och Godkännanden', 'certificates');?></em>
                            <!-- <em><?php echo sprintf( __( 'Sökordet '), $wp_query->found_posts ); echo '<span class="term">' . get_search_query() .'</span> gav dessvärre ingen träff i sökkällan Certifikat och Godkännanden.'; ?></em> -->

                        </div>
                        <input type="hidden" id="certificateCountResult" value="0 träffar">
                    <?php } else { ?>
                        <div class="search_result_buttons">
                            <?php $docCount = count($array); ?>
                            <?php if($docCount < 10) { ?>
                                <small class="smallCerts"><?php _e('Visar 1- ', 'certificates');?><?php echo $docCount; ?> <?php _e('av', 'certificates')?> <?php echo $docCount; ?> <?php if($docCount == 1) {echo " träff";} else {echo " träffar";} ?></small>
                            <?php } else { ?>
                                <small class="smallCerts"><?php _e('Visar 1-10 av', 'certificates');?> <?php echo $docCount; ?><?php _e(' träffar', 'certificates');?></small>
                            <?php } ?>
                            <input type="hidden" id="certificateCountResult" value="<?php if(empty($docCount)) {echo '0';} else {echo $docCount;} ?><?php if($docCount == 1) {echo _e(' träffar', 'certificates');} else {echo _e(' träffar', 'certificates');} ?>">

                            <small class="smallCerts_total"><?php _e('Visar alla ', 'certificates');?><?php echo $docCount; ?><?php _e('träffar', 'certificates');?> </small>

                            <?php if($docCount > 10) { ?>
                                <span id="loadMoreCerts" class="visa_fler_traffar"><i class="fa fa-spinner fa-spin loading-icon" aria-hidden="true"></i><?php _e('Visa alla', 'certificates');?></span>
                            <?php } ?>
                        </div>
                    <?php } ?>
                </div>

        </div>
    </div>
        <br class="clear" />
    </div>
</div>
