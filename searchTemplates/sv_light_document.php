<div id="documentResults" class="searchResultSection">
    <div class="wrapper">
        <div class="search_result_dokument">
        <div class="senaste_dokument_result">
            <?php
            // Get data from URL into variables
           
            if(ICL_LANGUAGE_CODE=='sv') {

               // filter
               function my_posts_where( $where ) {
              global $wpdb;
                  $where = str_replace("meta_key = 'crm_doc_area_%", "meta_key LIKE 'crm_doc_area_%", $wpdb->remove_placeholder_escape($where));
                  return $where;
               }
               add_filter('posts_where', 'my_posts_where');

               if(empty($doc_type)) {

                   $doc_type = "stafs, rep, doc, info";

                   $fritext = $_GET['fritext'];
                   $doc_nr = $_GET['s'];
                   $omraden = $_GET['omraden'];
                   $doc_type = $_GET['document_type'];

                   $metaDataOver = array();
                   $metaDataUnder = array();

                   if(!empty($_GET["s"])){
                       $metaDataOver["beteckning"] = array(
                           'key'     => 'crm_doc_dokumentbeteckning',
                           'value'   => $doc_nr,
                           'compare' => 'LIKE',
                       );
                   }
                   if(isset($_GET["fritext"])){
                       if(!empty($_GET['fritext'])) {
                           if(empty($_GET["s"])) {
                               $doc_nr = $_GET["fritext"];
                               $metaDataUnder["beteckning"] = array(
                                   'key'     => 'crm_doc_dokumentbeteckning',
                                   'value'   => $doc_nr,
                                   'compare' => 'LIKE',
                               );

                           }
                           $metaDataUnder["fritext"] = array(
                               'key'     => 'crm_doc_title',
                               'value'   => $fritext,
                               'compare' => 'LIKE',
                           );
                           $metaDataUnder['innehall'] = array(
                               'key' => 'crm_doc_text_content',
                               'value' => $fritext,
                               'compare' => 'LIKE',
                           );
                       }
                   }
                   if(isset($_GET["document_type"])){
                       $metaDataOver["document_type"] =    array(
                           'key'     => 'crm_doc_type',
                           'value'   => array('stafs', 'rep', 'doc', 'info'),
                           'compare' => 'IN',
                       );
                   }

                   if(isset($_GET['omraden'])) {
                       $metaDataOver["omraden"] =    array(
                           'key'     => 'crm_doc_area_%_omradesnamn',
                           'value'   => $omraden,
                           'compare' => 'LIKE',
                       );
                   }


                   $metaDataUnder = array_merge(array('relation' => 'OR'), $metaDataUnder);

                   $metaquery = array( 'meta_query' => array(
                       'relation' => 'AND',
                       $metaDataOver,
                       $metaDataUnder,
                   ));


                   $pt_doc_for_args = array(
                       'posts_per_page' 	=> -1,
                       'post_type'     	=> 'dokument',
                       's'					=> array($fritext, $doc_nr),
                       'meta_query' => $metaquery,
                   );
               } else {

                   $fritext = $_GET['fritext'];
                   $doc_nr = $_GET['s'];
                   $omraden = $_GET['omraden'];
                   $doc_type = $_GET['document_type'];

                   if($doc_type == "stafs-upphavd") {
                       $stafs_upphavd = true;
                   } else {
                       $stafs_upphavd = false;
                   }

                   $metaDataOver = array();
                   $metaDataUnder = array();

                   if(!empty($_GET["s"])){
                       $metaDataOver["beteckning"] = array(
                           'key'     => 'crm_doc_dokumentbeteckning',
                           'value'   => $doc_nr,
                           'compare' => 'LIKE',
                       );
                   }
                   if(isset($_GET["fritext"])){
                       if(!empty($_GET['fritext'])) {
                           if(empty($_GET["s"])) {
                               $doc_nr = $_GET["fritext"];
                               $metaDataUnder["beteckning"] = array(
                                   'key'     => 'crm_doc_dokumentbeteckning',
                                   'value'   => $doc_nr,
                                   'compare' => 'LIKE',
                               );

                           }
                           $metaDataUnder["fritext"] = array(
                               'key'     => 'crm_doc_title',
                               'value'   => $fritext,
                               'compare' => 'LIKE',
                           );
                       }



                   }
                   if(isset($_GET["document_type"])){
                       if($doc_type == "stafs-upphavd") {
                           $metaDataOver["document_type"] =    array(
                               'key'     => 'crm_doc_type',
                               'value'   => 'stafs',
                               'compare' => '=',
                           );
                           $metaDataOver["stafs_upphavd"] =    array(
                               'key'     => 'crm_doc_repealed',
                               'value'   => '1',
                               'compare' => '=',
                           );
                       } else {
                           $metaDataOver["document_type"] =    array(
                               'key'     => 'crm_doc_type',
                               'value'   => $doc_type,
                               'compare' => '=',
                           );
                           $metaDataOver["stafs_upphavd"] =    array(
                               'key'     => 'crm_doc_repealed',
                               'value'   => '1',
                               'compare' => '!=',
                           );
                       }

                   }

                   if(isset($_GET['omraden'])) {
                       $metaDataOver["omraden"] =    array(
                           'key'     => 'crm_doc_area_%_omradesnamn',
                           'value'   => $omraden,
                           'compare' => 'LIKE',
                       );
                   }
                   if(!empty($metaDataUnder)){
                       $metaDataUnder = array_merge(array('relation' => 'OR'), $metaDataUnder);
                   }
                   $metaDataOver = array_merge(array('relation' => 'AND'), $metaDataOver);

                   if(!empty($metaDataUnder)){
                       $metaquery = array( 'meta_query' => array(
                           $metaDataOver,
                           $metaDataUnder,
                       ));
                       
                   }else{
                       $metaquery = array( 'meta_query' => array(
                           $metaDataOver
                       ));

                   }

                   $pt_doc_for_args = array(
                       'posts_per_page' 	=> -1,
                       'post_type'     	=> 'dokument',
                       's'					=> array($fritext, $doc_nr),
                       'meta_query' => $metaquery,
                   );

               }

           } elseif(ICL_LANGUAGE_CODE=='en') {
               // filter
               function my_posts_where( $where ) {
              global $wpdb;
                  $where = str_replace("meta_key = 'crm_doc_area_en%", "meta_key LIKE 'crm_doc_area_en%", $wpdb->remove_placeholder_escape($where));
                  return $where;
               }
               add_filter('posts_where', 'my_posts_where');
               if(empty($doc_type)) {

                   $doc_type = "stafs, rep, doc, info";

                   $fritext = $_GET['fritext'];
                   $doc_nr = $_GET['s'];
                   $omraden = $_GET['omraden'];
                   $doc_type = $_GET['document_type'];

                   $metaDataOver = array();
                   $metaDataUnder = array();

                   if(!empty($_GET["s"])){
                       $metaDataOver["beteckning"] = array(
                           'key'     => 'crm_doc_dokumentbeteckning_en',
                           'value'   => $doc_nr,
                           'compare' => 'LIKE',
                       );
                   }
                   if(isset($_GET["fritext"])){
                       if(!empty($_GET['fritext'])) {
                           if(empty($_GET["s"])) {
                               $doc_nr = $_GET["fritext"];
                               $metaDataUnder["beteckning"] = array(
                                   'key'     => 'crm_doc_dokumentbeteckning_en',
                                   'value'   => $doc_nr,
                                   'compare' => 'LIKE',
                               );

                           }
                           $metaDataUnder["fritext"] = array(
                               'key'     => 'crm_doc_title_en',
                               'value'   => $fritext,
                               'compare' => 'LIKE',
                           );
                           $metaDataUnder['innehall'] = array(
                               'key' => 'crm_doc_text_content_en',
                               'value' => $fritext,
                               'compare' => 'LIKE',
                           );
                       }
                   }
                   if(isset($_GET["document_type"])){
                       $metaDataOver["document_type"] =    array(
                           'key'     => 'crm_doc_type_en',
                           'value'   => array('stafs', 'rep', 'doc', 'info'),
                           'compare' => 'IN',
                       );
                   }

                   if(isset($_GET['omraden'])) {
                       $metaDataOver["omraden"] =    array(
                           'key'     => 'crm_doc_area_en%_omradesnamn',
                           'value'   => $omraden,
                           'compare' => 'LIKE',
                       );
                   }

                   $metaDataUnder = array_merge(array('relation' => 'OR'), $metaDataUnder);

                   $metaquery = array( 'meta_query' => array(
                       'relation' => 'AND',
                       $metaDataOver,
                       $metaDataUnder,
                   ));


                   $pt_doc_for_args = array(
                       'posts_per_page' 	=> -1,
                       'post_type'     	=> 'document_eng',
                       's'					=> array($fritext, $doc_nr),
                       'meta_query' => $metaquery,
                   );


               } else {

                   $fritext = $_GET['fritext'];
                   $doc_nr = $_GET['s'];
                   $omraden = $_GET['omraden'];
                   $doc_type = $_GET['document_type'];

                   $metaDataOver = array();
                   $metaDataUnder = array();

                   if(!empty($_GET["s"])){
                       $metaDataOver["beteckning"] = array(
                           'key'     => 'crm_doc_dokumentbeteckning_en',
                           'value'   => $doc_nr,
                           'compare' => 'LIKE',
                       );
                   }
                   if(isset($_GET["fritext"])){
                       if(!empty($_GET['fritext'])) {
                           if(empty($_GET["s"])) {
                               $doc_nr = $_GET["fritext"];
                               $metaDataUnder["beteckning"] = array(
                                   'key'     => 'crm_doc_dokumentbeteckning_en',
                                   'value'   => $doc_nr,
                                   'compare' => 'LIKE',
                               );

                           }
                           $metaDataUnder["fritext"] = array(
                               'key'     => 'crm_doc_title_en',
                               'value'   => $fritext,
                               'compare' => 'LIKE',
                           );
                       }



                   }
                   if(isset($_GET["document_type"])){
                       $metaDataOver["document_type"] =    array(
                           'key'     => 'crm_doc_type_en',
                           'value'   => $doc_type,
                           'compare' => '=',
                       );
                   }

                   if(isset($_GET['omraden'])) {
                       $metaDataOver["omraden"] =    array(
                           'key'     => 'crm_doc_area_%_omradesnamn',
                           'value'   => $omraden,
                           'compare' => 'LIKE',
                       );
                   }

                   $metaDataUnder = array_merge(array('relation' => 'OR'), $metaDataUnder);
                   $metaDataOver = array_merge(array('relation' => 'AND'), $metaDataOver);

                   $metaquery = array( 'meta_query' => array(
                       $metaDataOver,
                       $metaDataUnder,
                   ));

                   $pt_doc_for_args = array(
                       'posts_per_page' 	=> -1,
                       'post_type'     	=> 'document_eng',
                       's'					=> array($fritext, $doc_nr),
                       'meta_query' => $metaquery,
                   );
               }


           }

           // $pt_doc_for_query = new WP_Query( $pt_doc_for_args );
           $array = array();
           $array1 = array();
           $array2 = array();
           $array3 = array();

               $posts = get_posts($pt_doc_for_args);
               foreach ($posts as $post) {
                   $fileUrl = get_field('crm_doc_doc', $post->ID);
                   if(!empty($fileUrl)) {
                       //Hämtar fält
                       $subject = get_field('crm_doc_dokumentbeteckning',$post->ID);
                       $subject2 = get_field('crm_doc_dokumentbeteckning',$post->ID);

                       // Explodar efter STAFS och : samt hämtar ut årdet

                       if(strpos($subject2, 'STAFS ') !== false) {
                           $field = end(explode('STAFS ', $subject2));

                           $field = substr($field, 0, 4);
                           $result = end(explode(':', $subject));

                           //Lägger till 0 innan nummer som är lägre än 10
                           if($result < 10) {
                               $result = '0'.$result;
                           }

                           //Slår ihop variabler
                           $result = $field . $result;

                           //Lägger in allt i en array med värden ovan
                           array_push( $array, array(
                               "title" 	=> get_the_title($post->ID),
                               "id" 		=> get_the_id($post->ID),
                               "link" 		=> get_permalink($post->ID),
                               "crm_doc_dokumentbeteckning" => $result,
                               "crm_doc_doc" 	=> get_field('crm_doc_doc', $post->ID),
                               "crm_doc_type" 	=> get_field('crm_doc_type', $post->ID)
                           ));

                       } elseif (strpos($subject2, 'SWEDAC DOC ') !== false) {

                           $field = end(explode('SWEDAC DOC ', $subject2));

                           $field = substr($field, 0, 2); // hämtar första 2

                           $result = end(explode(':', $subject));
                           $result = current(explode("0", $field));
                           //Lägger till 0 innan nummer som är lägre än 10
                           if($result < 10) {
                               $result = '0'.$result;
                           }
                           if($field < 10) {
                               // $field = current(explode("0", $field));
                           }

                           //Slår ihop variabler
                           $result = $field . $result;

                           //Lägger in allt i en array med värden ovan
                           array_push( $array1, array(
                               "title" 	=> get_the_title($post->ID),
                               "id" 		=> get_the_id($post->ID),
                               "link" 		=> get_permalink($post->ID),
                               "crm_doc_dokumentbeteckning" => $result,
                               "crm_doc_doc" 	=> get_field('crm_doc_doc', $post->ID),
                               "crm_doc_type" 	=> get_field('crm_doc_type', $post->ID)
                           ));

                       } elseif (strpos($subject2, 'REP ') !== false) {
                           $field = end(explode('REP ', $subject2));

                           $field = substr($field, 0, 2); // hämtar första 2

                           $result = end(explode(':', $subject));
                           $result = current(explode("0", $field));
                           //Lägger till 0 innan nummer som är lägre än 10
                           if($result < 10) {
                               $result = '0'.$result;
                           }
                           if($field < 10) {
                               // $field = current(explode("0", $field));
                           }

                           //Slår ihop variabler
                           $result = $field . $result;

                           //Lägger in allt i en array med värden ovan
                           array_push( $array2, array(
                               "title" 	=> get_the_title($post->ID),
                               "id" 		=> get_the_id($post->ID),
                               "link" 		=> get_permalink($post->ID),
                               "crm_doc_dokumentbeteckning" => $result,
                               "crm_doc_doc" 	=> get_field('crm_doc_doc', $post->ID),
                               "crm_doc_type" 	=> get_field('crm_doc_type', $post->ID)
                           ));


                       } elseif (strpos($subject2, 'INFO ') !== false) {
                           $field = end(explode('INFO ', $subject2));
                           $field = substr($field, 0, 2); // hämtar första 2

                           $result = end(explode(':', $subject));
                           $result = current(explode("0", $field));
                           //Lägger till 0 innan nummer som är lägre än 10
                           if($result < 10) {
                               $result = '0'.$result;
                           }
                           if($field < 10) {
                               // $field = current(explode("0", $field));
                           }

                           //Slår ihop variabler
                           $result = $field . $result;

                           //Lägger in allt i en array med värden ovan
                           array_push( $array3, array(
                               "title" 	=> get_the_title($post->ID),
                               "id" 		=> get_the_id($post->ID),
                               "link" 		=> get_permalink($post->ID),
                               "crm_doc_dokumentbeteckning" => $result,
                               "crm_doc_doc" 	=> get_field('crm_doc_doc', $post->ID),
                               "crm_doc_type" 	=> get_field('crm_doc_type', $post->ID)
                           ));
                       }
                   }
               }



               //Sorterar på Störst > Lägst ex. 1993-2017

               usort($array, 'sortById');
               usort($array1, 'sortById');
               usort($array2, 'sortById');
               usort($array3, 'sortById');

               $array_first = array_merge($array, $array1);
               $array_second = array_merge($array2, $array3);

               $array = array_merge($array_first, $array_second);
               
               

                ?>
                <h2>Föreskrifter & Dokument färre resultat</h2>
                <div class="table_wrapper">
                    <table id="docs_result">
                        <thead>
                            <tr>
                                <th>DOK.NR</th>
                                <th>RUBRIK</th>
                                <th>TYP AV DOKUMENT</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($array as $item) {

                                $title 		= $item['title'];
                                $id 		= $item['id'];
                                $url 		= $item['link'];
                                $doc 		= $item['crm_doc_doc'];
                                $doc_type 	= $item['crm_doc_type'];

                                $doc_bet = get_field('crm_doc_dokumentbeteckning', $id);
                                $doc_bet_2 = substr($doc_bet, 6); // returns "de"
                            ?>
                                <?php if($doc) { ?>
                                    <tr>
                                        <td><?php echo $doc_bet; ?></td>
                                        <td class="docTitle"><a href="<?php echo $url; ?>" title="<?php echo $title; ?>"><?php echo $title; ?></a></td>
                                        <td><?php echo $doc_type; ?></td>
                                    </tr>
                                <?php } ?>
                            <?php } ?>
                        </tbody>
                    </table>
                    <?php if(empty($array)) { ?>
                        <div class="alert alert-warning">
                            <strong>Inget hittades</strong>
                            <em><?php echo sprintf( __( 'Sökordet '), $wp_query->found_posts ); echo '<span class="term">' . get_search_query() .'</span> gav dessvärre ingen träff i sökkällan Föreskrifter & Dokument'; ?></em>
                        </div>
                        <input type="hidden" id="documentCountResults" value="0 träffar">
                    <?php } else { ?>
                        <div class="search_result_buttons">
                            <?php $docCount = count($array); ?>
                            <?php if($docCount < 10) { ?>
                                <small class="smallDocs">Visar 1-<?php echo $docCount; ?> av <?php echo $docCount; ?> <?php if($docCount == 1) {echo " träff";} else {echo " träffar";} ?></small>
                            <?php } else { ?>
                                <small class="smallDocs">Visar 1-10 av <?php echo $docCount; ?> träffar</small>
                            <?php } ?>
                            <input type="hidden" id="documentCountResults" value="<?php if(empty($docCount)) {echo '0';} else {echo $docCount;} ?><?php if($docCount == 1) {echo " träff";} else {echo " träffar";} ?>">

                            <small class="smallDocs_total">Visar alla <?php echo $docCount; ?> träffar</small>

                            <?php if($docCount > 10) { ?>
                                <span id="loadMoreDocs" class="visa_fler_traffar"><i class="fa fa-spinner fa-spin loading-icon" aria-hidden="true"></i> Visa alla</span>
                            <?php } ?>
                        </div>
                    <?php } ?>
                </div>

        </div>
    </div>
        <br class="clear" />
    </div>
</div>
