on:
  push:
    branches:
      - uat
name: 🚀 Deploy website to UAT on push
jobs:
  web-deploy:
    name: 🎉 Deploy to UAT
    runs-on: ubuntu-latest
    env:
      AWS_REGION: eu-north-1
      AWS_INSTANCE_SECURITY_GROUP_ID: sg-03a3088ac963fd9b7
      AWS_REMOTE_HOST: ec2-13-60-136-136.eu-north-1.compute.amazonaws.com
      AWS_REMOTE_USER: ubuntu
      SERVER_PATH: /var/www/staging.swedac.se/web/wp-content/themes/swedac_theme/
      SERVER_USER_AND_GROUP: web5:client1
    steps:
      - name: 🔨 Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET }}
          aws-region: ${{ env.AWS_REGION }}

      - name: 🌎 Get Runner IP address
        id: ip
        uses: haythem/public-ip@v1.3

      - name: 🌎 Whitelist Runner IP address
        run: |
          aws ec2 authorize-security-group-ingress \
            --group-id $AWS_INSTANCE_SECURITY_GROUP_ID \
            --protocol tcp \
            --port 22 \
            --cidr ${{ steps.ip.outputs.ipv4 }}/32

      - name: 🚚 Get latest code
        uses: actions/checkout@v4

      - name: 📦 Install PHP
        uses: "shivammathur/setup-php@v2"
        with:
          php-version: "8.1"

      - name: 📦 Install Composer
        uses: "ramsey/composer-install@v3"

      - name: 📦 Install Composer dependencies
        run: composer install

      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: 📦 Install Node.js dependencies
        run: npm install

      - name: 🔨 Build Project
        run: npm run build

      - name: 📂 Deploy to Server
        uses: easingthemes/ssh-deploy@main
        with:
          SSH_PRIVATE_KEY: ${{ secrets.SSH_KEY }}
          ARGS: '-avzr --rsync-path="sudo rsync" --delete'
          REMOTE_HOST: ${{ env.AWS_REMOTE_HOST }}
          REMOTE_USER: ${{ env.AWS_REMOTE_USER }}
          TARGET: ${{ env.SERVER_PATH }}
          EXCLUDE: >-
            /node_modules/,
            /.git/,
            /.github/,
            /.env,
            /.env.example,
            /composer.json,
            /composer.lock,
            /package.json,
            /package-lock.json,
            /gulpfile.js,
            /readme.md,
            /.gitignore
          SCRIPT_AFTER: |
            sudo chown -R ${{ env.SERVER_USER_AND_GROUP }} ${{ env.SERVER_PATH }}

      - name: 🌎 Revoke Runner IP address
        run: |
          aws ec2 revoke-security-group-ingress \
            --group-id $AWS_INSTANCE_SECURITY_GROUP_ID \
            --protocol tcp \
            --port 22 \
            --cidr ${{ steps.ip.outputs.ipv4 }}/32
