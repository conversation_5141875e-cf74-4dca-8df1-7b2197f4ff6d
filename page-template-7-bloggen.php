<?php
/**
 * Template Name: Template 7 - GD Bloggen
 */
?>

<div class="wrapper">
	<div id="page-bloggen-content">
		<?php 

		$paged = get_query_var( 'paged' ) ? get_query_var( 'paged' ) : 1;
		
		if(ICL_LANGUAGE_CODE=='sv'): 
			$args = array(
				'post_type'   		=> 'bloggen',
				'posts_per_page'  	=> 3,
				'orderby' 			=> 'date', 
				'paged'   			=> $paged,
			);
		elseif(ICL_LANGUAGE_CODE=='en'): 
			$args = array(
				'post_type'   		=> 'gd-blog',
				'posts_per_page'  	=> 3,
				'orderby' 			=> 'date', 
				'paged'   			=> $paged,
			);
		endif;

		$query = new WP_Query($args);
		if($query->have_posts()): ?>
			<?php while($query->have_posts()):$query->the_post(); global $post; ?>
				<article <?php post_class('blogg-post'); ?> aria-label="<?php echo get_the_title(); ?>">
					<header class="post-header">
						<?php if ( has_post_thumbnail()) : ?>
							<?php $get_post_image = wp_get_attachment_image_src( get_post_thumbnail_id($post->ID), 'large' );
							$image_url = $get_post_image['0'];
							$img_id = get_post_thumbnail_id(get_the_ID());
							$alt_text = get_post_meta($img_id , '_wp_attachment_image_alt', true); ?>
	
							<div class="page_thumbnail">
								<img data-src="<?php echo $image_url; ?>" alt="<?php echo $alt_text; ?>" />
							</div>
						<?php endif; ?>

						<h2 id="<?php echo $post->post_name; ?>"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
					</header>
					<div class="post-content">
						<?php the_content(); ?>
					</div>
					<footer class="post-footer">
						<div class="post_settings">
							
							<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
								<?php if((get_field('gd_blogg_skribent_namn')) && (get_field('gd_blogg_skribent_epost'))) { ?>
									<div class="author">
										<span><a href="mailto:<?php echo antispambot(get_field('gd_blogg_skribent_epost')); ?>" title="Skicka ett mail till <?php echo get_field('gd_blogg_skribent_namn'); ?>"><?php echo get_field('gd_blogg_skribent_namn'); ?></a></span>
									</div>
								<?php } elseif (get_field('gd_blogg_skribent_namn')) { ?>
									<div class="author">
										<span><?php echo get_field('gd_blogg_skribent_namn'); ?></span>
									</div>
								<?php } else { ?>

								<?php } ?>
							<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
								<?php if((get_field('gd_blogg_skribent_namn_en')) && (get_field('gd_blogg_skribent_epost'))) { ?>
									<div class="author">
										<span><a href="mailto:<?php echo antispambot(get_field('gd_blogg_skribent_epost')); ?>" title="Send an email to <?php echo get_field('gd_blogg_skribent_namn_en'); ?>"><?php echo get_field('gd_blogg_skribent_namn_en'); ?></a></span>
									</div>
								<?php } elseif (get_field('gd_blogg_skribent_namn_en')) { ?>
									<div class="author">
										<span><?php echo get_field('gd_blogg_skribent_namn_en'); ?></span>
									</div>
								<?php } else { ?>
 
								<?php } ?>
							<?php endif;?>
							
							<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
								<div class="publish_change">
									<span>Publicerad: <?php echo get_the_date('j F Y'); ?>, kl. <?php the_time('H:i') ?></span>  
									<span>Senast uppdaterad: <?php the_modified_date('j F Y'); ?>, kl. <?php the_modified_time('H:i') ?></span>
								</div>
								<div class="share_post">
									<!-- FACEBOOK -->
									<a class="share facebook" onclick="window.open('http://www.facebook.com/sharer/sharer.php?u=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>', 'shareFacebook', 'width=650, height=270, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
										<i class="fa fa-facebook"></i> <span>Dela på Facebook</span>
									</a>
									<!-- FACEBOOK END -->

									<!-- LINKEDIN -->
										<a class="share linkedin" onclick="window.open('http://www.linkedin.com/shareArticle?mini=true&url=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>&source=[SOURCE/DOMAIN]', 'shareLinkedIn', 'width=610, height=480, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
											<i class="fa fa-linkedin-square"></i> <span>Dela på LinkedIn</span>
										</a>
									<!-- LINKEIND END -->

									<!-- MAIL -->
									<a id="<?php echo get_the_ID(); ?>" aria-labelledby="<?php echo $post->post_name; ?>" class="share mail" href="mailto:?subject=<?php the_title(); ?>&body=Hej, jag tror att du skulle gilla detta. Kolla in detta <?php echo get_permalink(); ?>">
										<i class="fa fa-envelope" aria-hidden="true"></i> <span>Skicka som e-post</span>
									</a>
									<!-- MAIL END -->
								</div>
							<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>

								<div class="publish_change">
									<span>Published: <?php echo get_the_date('j F Y'); ?>, kl. <?php the_time('H:i') ?></span>  
									<span>Last updated: <?php the_modified_date('j F Y'); ?>, kl. <?php the_modified_time('H:i') ?></span>
								</div>
								<div class="share_post">
									<!-- FACEBOOK -->
									<a class="share facebook" onclick="window.open('http://www.facebook.com/sharer/sharer.php?u=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>', 'shareFacebook', 'width=650, height=270, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
										<i class="fa fa-facebook"></i> <span>Share on Facebook</span>
									</a>
									<!-- FACEBOOK END -->

									<!-- LINKEDIN -->
										<a class="share linkedin" onclick="window.open('http://www.linkedin.com/shareArticle?mini=true&url=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>&source=[SOURCE/DOMAIN]', 'shareLinkedIn', 'width=610, height=480, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
											<i class="fa fa-linkedin-square"></i> <span>Share on LinkedIn</span>
										</a>
									<!-- LINKEIND END -->


									<!-- MAIL -->
									<a  id="<?php echo get_the_ID(); ?>" aria-labelledby="<?php echo $post->post_name; ?>" class="share mail" href="mailto:?subject=<?php the_title(); ?>&body=Hej, jag tror att du skulle gilla detta. Kolla in detta <?php echo get_permalink(); ?>">
										<i class="fa fa-envelope" aria-hidden="true"></i> <span>Send as email</span>
									</a>
									<!-- MAIL END -->
								</div>
							<?php endif;?>
						</div>
					</footer>
				</article>
			<?php endwhile; ?>

			<?php echo easy_wp_pagenavigation( $query ); ?>

		<?php endif; wp_reset_postdata(); ?>

	</div>

</div>