<?php

namespace Roots\Sage\Extras;

use Roots\Sage\Setup;

/**
 * Add <body> classes
 */
function body_class($classes) {
  // Add page slug if it doesn't exist
  if (is_single() || is_page() && !is_front_page()) {
    if (!in_array(basename(get_permalink()), $classes)) {
      $classes[] = basename(get_permalink());
    }
  }

  // Add class if sidebar is active
  if (Setup\display_sidebar()) {
    $classes[] = 'sidebar-active';
  }

  return $classes;
}
add_filter('body_class', __NAMESPACE__ . '\\body_class');

/**
 * Clean up the_excerpt()
 */
function excerpt_more() {  return '...'; }
function excerpt_length_custom( $length ) { return 20; }
add_filter( 'excerpt_length', __NAMESPACE__ . '\\excerpt_length_custom', 999 );
add_filter('excerpt_more', __NAMESPACE__ . '\\excerpt_more');

function excerpt($limit) {
    $excerpt = explode(' ', get_the_excerpt(), $limit);
    if (count($excerpt)>=$limit) {
        array_pop($excerpt);
        $excerpt = implode(" ",$excerpt).'...';
    } else {
        $excerpt = implode(" ",$excerpt);
    }
    $excerpt = preg_replace('`[[^]]*]`','',$excerpt);
    return $excerpt;
}




global $wp_query;
$big = 999999999; // need an unlikely integer
$translated = __( 'Page', 'mytextdomain' ); // Supply translatable string
echo paginate_links( array(
  'base' => str_replace( $big, '%#%', esc_url( get_pagenum_link( $big ) ) ),
  'format' => '?paged=%#%',
  'current' => max( 1, get_query_var('paged') ),
  'total' => $wp_query->max_num_pages,
        'before_page_number' => '<span class="screen-reader-text">'.$translated.' </span>'
) );

//Sätt klass på body beroende på vad för Webbläsare som används.
function mv_browser_body_class($classes) {
    global $is_lynx, $is_gecko, $is_IE, $is_opera, $is_NS4, $is_safari, $is_chrome, $is_iphone;
    if($is_lynx) $classes[] = 'lynx';
    elseif($is_gecko) $classes[] = 'firefox';
    elseif($is_opera) $classes[] = 'opera';
    elseif($is_NS4) $classes[] = 'ns4';
    elseif($is_safari) $classes[] = 'safari';
    elseif($is_chrome) $classes[] = 'chrome';
    elseif($is_IE) {
            $classes[] = 'ie';
            if(preg_match('/MSIE ([0-9]+)([a-zA-Z0-9.]+)/', $_SERVER['HTTP_USER_AGENT'], $browser_version))
            $classes[] = 'ie'.$browser_version[1];
    } else $classes[] = 'unknown';

    return $classes;
}
add_filter('body_class', __NAMESPACE__ . '\\mv_browser_body_class');

// custom filter to replace '=' with 'LIKE'
function my_posts_where( $where ) {
    $where = str_replace("meta_key = '{crm_doc_area}_%_{omradesnamn}'", "meta_key LIKE '{crm_doc_area}_%_{omradesnamn}'", $where);
    return $where;
}
add_filter('posts_where', __NAMESPACE__ . '\\my_posts_where');


add_filter( 'wp_image_editors', __NAMESPACE__ . '\\change_graphic_lib' );
function change_graphic_lib($array) {
return array( 'WP_Image_Editor_GD', 'WP_Image_Editor_Imagick' );
}

/** Automatically add IDs to headings such as <h2></h2> */
function auto_id_headings( $content ) {
    $content = preg_replace_callback( '/(\<h[2-2](.*?))\>(.*)(<\/h[2-2]>)/i', function( $matches ) {
        if ( ! stripos( $matches[0], 'id=' ) ) :
            $matches[0] = $matches[1] . $matches[2] . ' id="anchor_' . sanitize_title( $matches[3] ) . '">' . $matches[3] . $matches[4];
        endif;
        return $matches[0];
    }, $content );
    return $content;
}
add_filter( 'the_content',__NAMESPACE__ . '\\auto_id_headings' );



//Skapa knapp i backend för mailutskick.


//Skapa meddelande för mailutskick.
/*
function mail_to_subscribers() {
    ?>
    <div class="updated notice">
        <p><?php _e( 'Mailet skickades till alla prenumeranter på denna post.'); ?></p>
    </div>
    <?php
}

add_action('admin_notices', __NAMESPACE__ .'\\mail_to_subscribers');*/

//Tillåt Editor / redaktörer att ändra i menyn.
$role_object = get_role( 'editor' );
$role_object->add_cap( 'edit_theme_options' );


//Kortar ner perioden för simple history
add_filter("simple_history_db_purge_days_interval", function($days) {
  $days = 7;
  return $days;
});

//Flyttar ner Yoast SEO i botten på sidor & inlägg
add_filter( 'wpseo_metabox_prio', function() { return 'low';});

//GD Bloggen
add_action( 'init',__NAMESPACE__ . '\\create_posttype_ansvarigperson' );
function create_posttype_ansvarigperson() {
  register_post_type( 'ansvarigperson',
    array(
      'labels' => array(
         'name' => __('Sidansvariga', 'ansvarigperson'), // Rename these to suit
          'singular_name' => __('Sidansvariga', 'ansvarigperson'),
          'add_new' => __('Lägg till', 'ansvarigperson'),
          'add_new_item' => __('Lägg till', 'ansvarigperson'),
          'edit' => __('Ändra', 'ansvarigperson'),
          'edit_item' => __('Ändra', 'ansvarigperson'),
          'new_item' => __('Nytt', 'ansvarigperson'),
          'view' => __('Visa', 'ansvarigperson'),
          'view_item' => __('Visa', 'ansvarigperson'),
          'search_items' => __('Sök', 'ansvarigperson'),
          'not_found' => __('Inget funnet', 'ansvarigperson'),
          'not_found_in_trash' => __('Papperskorgen är tom', 'ansvarigperson')
      ),
          'public'                => true,
          'exclude_from_search'   => true,
          'hierarchical'          => false, // Allows your posts to behave like Hierarchy Pages
          'has_archive'           => true,
          'show_in_admin_bar'     => false,
          'rewrite' => array('with_front' => false),
          'menu_icon'             => 'dashicons-welcome-view-site',
          'supports' => array(
              // 'title',
              // 'editor',
              // 'excerpt',
              // 'thumbnail'
          ), // Go to Dashboard Custom HTML5 Blank post for supports
          'can_export'            => true, // Allows export in Tools > Export
          'taxonomies' => array(
              //'post_tag',
              //'category'
          ) // Add Category and Post Tags support
      )
  );
  flush_rewrite_rules();
}

//GD Bloggen
add_action( 'init',__NAMESPACE__ . '\\create_posttype_kontaktperson' );
function create_posttype_kontaktperson() {
  register_post_type( 'kontaktperson',
    array(
      'labels' => array(
         'name' => __('Kontaktpersoner', 'kontaktperson'), // Rename these to suit
          'singular_name' => __('Kontaktpersoner', 'kontaktperson'),
          'add_new' => __('Lägg till', 'kontaktperson'),
          'add_new_item' => __('Lägg till', 'kontaktperson'),
          'edit' => __('Ändra', 'kontaktperson'),
          'edit_item' => __('Ändra', 'kontaktperson'),
          'new_item' => __('Nytt', 'kontaktperson'),
          'view' => __('Visa', 'kontaktperson'),
          'view_item' => __('Visa', 'kontaktperson'),
          'search_items' => __('Sök', 'kontaktperson'),
          'not_found' => __('Inget funnet', 'kontaktperson'),
          'not_found_in_trash' => __('Papperskorgen är tom', 'kontaktperson')
      ),
          'public' => true,
          'hierarchical' => false, // Allows your posts to behave like Hierarchy Pages
          'has_archive' => false,
          'rewrite' => array('with_front' => false),

          'supports' => array(
              'title',
              'editor',
              'excerpt',
              'thumbnail'
          ), // Go to Dashboard Custom HTML5 Blank post for supports
          'can_export' => true, // Allows export in Tools > Export
          'taxonomies' => array(
              //'post_tag',
              //'category'
          ) // Add Category and Post Tags support
      )
  );
  flush_rewrite_rules();
}

function kontaktperson_icon() {
    echo '
        <style>
            #adminmenu #menu-posts-kontaktperson div.wp-menu-image:before { font-family: FontAwesome; content: "\f0c0"; }
        </style>
    ';
}
add_action( 'admin_head', __NAMESPACE__ . '\\kontaktperson_icon' );

//GD Bloggen
add_action( 'init',__NAMESPACE__ . '\\create_posttype_bloggen' );
function create_posttype_bloggen() {
  register_post_type( 'bloggen',
    array(
      'labels' => array(
         'name' => __('GD Bloggen', 'bloggen'), // Rename these to suit
          'singular_name' => __('GD Bloggen', 'bloggen'),
          'add_new' => __('Lägg till', 'bloggen'),
          'add_new_item' => __('Lägg till', 'bloggen'),
          'edit' => __('Ändra', 'bloggen'),
          'edit_item' => __('Ändra', 'bloggen'),
          'new_item' => __('Nytt', 'bloggen'),
          'view' => __('Visa', 'bloggen'),
          'view_item' => __('Visa', 'bloggen'),
          'search_items' => __('Sök', 'bloggen'),
          'not_found' => __('Inget funnet', 'bloggen'),
          'not_found_in_trash' => __('Papperskorgen är tom', 'bloggen')
      ),
          'public' => true,
          'hierarchical' => false, // Allows your posts to behave like Hierarchy Pages
          'has_archive' => true,
          'rewrite' => array('with_front' => false),

          'supports' => array(
              'title',
              'editor',
              'excerpt',
              'thumbnail'
          ), // Go to Dashboard Custom HTML5 Blank post for supports
          'can_export' => true, // Allows export in Tools > Export
          'taxonomies' => array(
              //'post_tag',
              //'category'
          ) // Add Category and Post Tags support
      )
  );
  flush_rewrite_rules();
}

function bloggen_icon() {
    echo '
        <style>
            #adminmenu #menu-posts-bloggen div.wp-menu-image:before { font-family: FontAwesome; content: "\f08d"; }
        </style>
    ';
}
add_action( 'admin_head', __NAMESPACE__ . '\\bloggen_icon' );

//Kalender
add_action( 'init',__NAMESPACE__ . '\\create_posttype_kalender' );
function create_posttype_kalender() {
  register_post_type( 'kalender',
    array(
      'labels' => array(
         'name' => __('Kalender', 'kalender'), // Rename these to suit
          'slug' => __('kalender', 'kalender'),
          'singular_name' => __('Kalender', 'kalender'),
          'add_new' => __('Lägg till', 'kalender'),
          'add_new_item' => __('Lägg till', 'kalender'),
          'edit' => __('Ändra', 'kalender'),
          'edit_item' => __('Ändra', 'kalender'),
          'new_item' => __('Nytt', 'kalender'),
          'view' => __('Visa', 'kalender'),
          'view_item' => __('Visa', 'kalender'),
          'search_items' => __('Sök', 'kalender'),
          'not_found' => __('Inget funnet', 'kalender'),
          'not_found_in_trash' => __('Papperskorgen är tom', 'kalender')
      ),
          'public' => true,
          'hierarchical' => false, // Allows your posts to behave like Hierarchy Pages
          'has_archive' => true,
          'rewrite' => array('with_front' => false),

          'supports' => array(
              'title',
              'editor',
              'excerpt',
              'thumbnail'
          ), // Go to Dashboard Custom HTML5 Blank post for supports
          'can_export' => true, // Allows export in Tools > Export
          'taxonomies' => array(
              //'post_tag',
              //'category'
          ) // Add Category and Post Tags support
      )
  );
  flush_rewrite_rules();
}

function kalender_icon() {
    echo '
        <style>
            #adminmenu #menu-posts-kalender div.wp-menu-image:before { font-family: FontAwesome; content: "\f073"; }
        </style>
    ';
}
add_action( 'admin_head', __NAMESPACE__ . '\\kalender_icon' );


add_action( 'admin_head', __NAMESPACE__ . '\\kalender_icon' );

add_action( 'init',__NAMESPACE__ . '\\create_posttype_swedac_utvalda' );

function create_posttype_swedac_utvalda() {
  register_post_type( 'swedac_utvalda',
    array(
      'labels' => array(
         'name' => __('Swedac Utvalda', 'swedac_utvalda'), // Rename these to suit
          'singular_name' => __('Swedac Utvalda', 'swedac_utvalda'),
          'add_new' => __('Lägg till', 'swedac_utvalda'),
          'add_new_item' => __('Lägg till', 'swedac_utvalda'),
          'edit' => __('Ändra', 'swedac_utvalda'),
          'edit_item' => __('Ändra', 'swedac_utvalda'),
          'new_item' => __('Nytt', 'swedac_utvalda'),
          'view' => __('Visa', 'swedac_utvalda'),
          'view_item' => __('Visa', 'swedac_utvalda'),
          'search_items' => __('Sök', 'swedac_utvalda'),
          'not_found' => __('Inget funnet', 'swedac_utvalda'),
          'not_found_in_trash' => __('Papperskorgen är tom', 'swedac_utvalda')
      ),
          'public' => true,
          'hierarchical' => false, // Allows your posts to behave like Hierarchy Pages
          'has_archive' => true,
          'rewrite' => array('with_front' => false),

          'supports' => array(
              'title',
              'editor',
              'excerpt',
              'thumbnail'
          ), // Go to Dashboard Custom HTML5 Blank post for supports
          'can_export' => true, // Allows export in Tools > Export
          'taxonomies' => array(
              //'post_tag',
              //'category'
          ) // Add Category and Post Tags support
      )
  );
  flush_rewrite_rules();
}

function swedac_utvalda_icon() {
    echo '
        <style>
            #adminmenu #menu-posts-swedac_utvalda div.wp-menu-image:before { font-family: FontAwesome; content: "\f1ea"; }
        </style>
    ';
}
add_action( 'admin_head', __NAMESPACE__ . '\\swedac_utvalda_icon' );


add_action( 'init',__NAMESPACE__ . '\\create_posttype_swedac_magasin' );

function create_posttype_swedac_magasin() {
  register_post_type( 'swedac_magasin',
    array(
      'labels' => array(
         'name' => __('Swedac Magasin', 'swedac_magasin'), // Rename these to suit
          'singular_name' => __('Swedac Magasin', 'swedac_magasin'),
          'add_new' => __('Lägg till', 'swedac_magasin'),
          'add_new_item' => __('Lägg till', 'swedac_magasin'),
          'edit' => __('Ändra', 'swedac_magasin'),
          'edit_item' => __('Ändra', 'swedac_magasin'),
          'new_item' => __('Nytt', 'swedac_magasin'),
          'view' => __('Visa', 'swedac_magasin'),
          'view_item' => __('Visa', 'swedac_magasin'),
          'search_items' => __('Sök', 'swedac_magasin'),
          'not_found' => __('Inget funnet', 'swedac_magasin'),
          'not_found_in_trash' => __('Papperskorgen är tom', 'swedac_magasin')
      ),
          'public' => true,
          'hierarchical' => false, // Allows your posts to behave like Hierarchy Pages
          'has_archive' => true,
          'rewrite' => array('with_front' => false),

          'supports' => array(
              'title',
              'editor',
              'excerpt',
              'thumbnail'
          ), // Go to Dashboard Custom HTML5 Blank post for supports
          'can_export' => true, // Allows export in Tools > Export
          'taxonomies' => array(
              //'post_tag',
              //'category'
          ) // Add Category and Post Tags support
      )
  );
  flush_rewrite_rules();
}

function swedac_magasin_icon() {
    echo '
        <style>
            #adminmenu #menu-posts-swedac_magasin div.wp-menu-image:before { font-family: FontAwesome; content: "\f1ea"; }
        </style>
    ';
}
add_action( 'admin_head', __NAMESPACE__ . '\\swedac_magasin_icon' );


//Ämnesområden
add_action( 'init',__NAMESPACE__ . '\\create_posttype_amnesomraden' );
function create_posttype_amnesomraden() {
  register_post_type( 'amnesomraden',
    array(
      'labels' => array(
         'name' => __('Ämnesområden', 'amnesomraden'), // Rename these to suit
          'singular_name' => __('Ämnesområden', 'amnesomraden'),
          'add_new' => __('Lägg till', 'amnesomraden'),
          'add_new_item' => __('Lägg till', 'amnesomraden'),
          'edit' => __('Ändra', 'amnesomraden'),
          'edit_item' => __('Ändra', 'amnesomraden'),
          'new_item' => __('Nytt', 'amnesomraden'),
          'view' => __('Visa', 'amnesomraden'),
          'view_item' => __('Visa', 'amnesomraden'),
          'search_items' => __('Sök', 'amnesomraden'),
          'not_found' => __('Inget funnet', 'amnesomraden'),
          'slug' => __('amnesomraden', 'amnesomraden'),
          'not_found_in_trash' => __('Papperskorgen är tom', 'amnesomraden')
      ),
          'public' => true,
          'hierarchical' => false, // Allows your posts to behave like Hierarchy Pages
          'has_archive' => false,
          'rewrite' => array('with_front' => false),

          'supports' => array(
              'title',
              'editor',
              'excerpt',
              'thumbnail'
          ), // Go to Dashboard Custom HTML5 Blank post for supports
          'can_export' => true, // Allows export in Tools > Export
          'taxonomies' => array(
              //'post_tag',
              //'category'
          ) // Add Category and Post Tags support
      )
  );
  flush_rewrite_rules();
}

function amnesomraden_icon() {
    echo '
        <style>
            #adminmenu #menu-posts-amnesomraden div.wp-menu-image:before { font-family: FontAwesome; content: "\f02c"; }
        </style>
    ';
}
add_action( 'admin_head', __NAMESPACE__ . '\\amnesomraden_icon' );


function create_amnesomraden_taxonomies() {
    $labels = array(
        'name'              => _x( 'Categories', 'taxonomy general name' ),
        'singular_name'     => _x( 'Category', 'taxonomy singular name' ),
        'search_items'      => __( 'Search Categories' ),
        'all_items'         => __( 'All Categories' ),
        'parent_item'       => __( 'Parent Category' ),
        'parent_item_colon' => __( 'Parent Category:' ),
        'edit_item'         => __( 'Edit Category' ),
        'update_item'       => __( 'Update Category' ),
        'add_new_item'      => __( 'Add New Category' ),
        'new_item_name'     => __( 'New Category Name' ),
        'menu_name'         => __( 'Categories' ),
    );

    $args = array(
        'hierarchical'      => true, // Set this to 'false' for non-hierarchical taxonomy (like tags)
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array( 'slug' => 'categories' ),
    );

    register_taxonomy( 'amnesomraden_categories', array( 'amnesomraden' ), $args );
}
add_action( 'init', __NAMESPACE__ . '\\create_amnesomraden_taxonomies', 0 );


//Interna länkar
add_action( 'init',__NAMESPACE__ . '\\create_posttype_intern_lankningar' );
function create_posttype_intern_lankningar() {
  register_post_type( 'intern_lankningar',
    array(
      'labels' => array(
         'name' => __('Länkar', 'intern_lankningar'), // Rename these to suit
          'singular_name' => __('Länkar', 'intern_lankningar'),
          'add_new' => __('Lägg till', 'intern_lankningar'),
          'add_new_item' => __('Lägg till', 'intern_lankningar'),
          'edit' => __('Ändra', 'intern_lankningar'),
          'edit_item' => __('Ändra', 'intern_lankningar'),
          'new_item' => __('Nytt', 'intern_lankningar'),
          'view' => __('Visa', 'intern_lankningar'),
          'view_item' => __('Visa', 'intern_lankningar'),
          'search_items' => __('Sök', 'intern_lankningar'),
          'not_found' => __('Inget funnet', 'intern_lankningar'),
          'not_found_in_trash' => __('Papperskorgen är tom', 'intern_lankningar')
      ),
          'public' => true,
          'hierarchical' => false, // Allows your posts to behave like Hierarchy Pages
          'has_archive' => false,
          'rewrite' => array('with_front' => false),
          'supports' => array(
              'title',
              'editor',
              'excerpt',
              'thumbnail'
          ), // Go to Dashboard Custom HTML5 Blank post for supports
          'can_export' => true, // Allows export in Tools > Export
          'taxonomies' => array(
              //'post_tag',
              //'category'
          ) // Add Category and Post Tags support
      )
  );
  flush_rewrite_rules();
}
function intern_lankningar_icon() {
    echo '
        <style>
            #adminmenu #menu-posts-intern_lankningar div.wp-menu-image:before { font-family: FontAwesome; content: "\f0c1"; }
        </style>
    ';
}
add_action( 'admin_head', __NAMESPACE__ . '\\intern_lankningar_icon' );

//Kurser
add_action( 'init',__NAMESPACE__ . '\\create_posttype_kurser' );
function create_posttype_kurser() {
  register_post_type( 'kurser',
    array(
      'labels' => array(
         'name' => __('Kurser', 'kurser'), // Rename these to suit
          'singular_name' => __('Kurser', 'kurser'),
          'add_new' => __('Lägg till', 'kurser'),
          'add_new_item' => __('Lägg till', 'kurser'),
          'edit' => __('Ändra', 'kurser'),
          'edit_item' => __('Ändra', 'kurser'),
          'new_item' => __('Nytt', 'kurser'),
          'view' => __('Visa', 'kurser'),
          'view_item' => __('Visa', 'kurser'),
          'search_items' => __('Sök', 'kurser'),
          'not_found' => __('Inget funnet', 'kurser'),
          'not_found_in_trash' => __('Papperskorgen är tom', 'kurser')
      ),
          'public' => true,
          'hierarchical' => false, // Allows your posts to behave like Hierarchy Pages
          'has_archive' => false,
          'rewrite' => array('with_front' => false),
          'supports' => array(
              'title',
              'editor',
              'excerpt',
              'thumbnail'
          ), // Go to Dashboard Custom HTML5 Blank post for supports
          'can_export' => true, // Allows export in Tools > Export
          'taxonomies' => array(
              //'post_tag',
              //'category'
          ) // Add Category and Post Tags support
      )
  );
  flush_rewrite_rules();
}
function kurser_icon() {
    echo '
        <style>
            #adminmenu #menu-posts-kurser div.wp-menu-image:before { font-family: FontAwesome; content: "\f19d"; }
        </style>
    ';
}
add_action( 'admin_head', __NAMESPACE__ . '\\kurser_icon' );


//Tomt
add_action( 'init',__NAMESPACE__ . '\\create_posttype_empty' );
function create_posttype_empty() {
  register_post_type( 'empty',
    array(
      'labels' => array(
         'name' => __('empty', 'empty'), // Rename these to suit
          'singular_name' => __('empty', 'empty'),
          'add_new' => __('Lägg till', 'empty'),
          'add_new_item' => __('Lägg till', 'empty'),
          'edit' => __('Ändra', 'empty'),
          'edit_item' => __('Ändra', 'empty'),
          'new_item' => __('Nytt', 'empty'),
          'view' => __('Visa', 'empty'),
          'view_item' => __('Visa', 'empty'),
          'search_items' => __('Sök', 'empty'),
          'not_found' => __('Inget funnet', 'empty'),
          'not_found_in_trash' => __('Papperskorgen är tom', 'empty')
      ),
          'public' => true,
          'hierarchical' => false, // Allows your posts to behave like Hierarchy Pages
          'has_archive' => false,
          'rewrite' => array('with_front' => false),
          'supports' => array(
              'title',
              'editor',
              'excerpt',
              'thumbnail'
          ), // Go to Dashboard Custom HTML5 Blank post for supports
          'can_export' => true, // Allows export in Tools > Export
          'taxonomies' => array(
              //'post_tag',
              //'category'
          ) // Add Category and Post Tags support
      )
  );
  flush_rewrite_rules();
}


//Nyheter
add_action( 'init',__NAMESPACE__ . '\\create_posttype_news' );
function create_posttype_news() {
  register_post_type( 'news',
    array(
      'labels' => array(
         'name' => __('News', 'swedac'), // Rename these to suit
          'singular_name' => __('News', 'swedac'),
          'add_new' => __('Add News', 'swedac'),
          'add_new_item' => __('Add', 'swedac'),
          'edit' => __('Edit', 'swedac'),
          'edit_item' => __('Edit', 'swedac'),
          'new_item' => __('New', 'swedac'),
          'view' => __('Show', 'swedac'),
          'view_item' => __('Show', 'swedac'),
          'search_items' => __('Search', 'swedac'),
          'not_found' => __('No Found', 'swedac'),
          'not_found_in_trash' => __('The trash is empty', 'swedac')
      ),
          'public' => true,
          'show_in_rest' => true,
          'hierarchical' => false, // Allows your posts to behave like Hierarchy Pages
          'has_archive' => true,
          'rewrite' => array('slug' => __('news', 'swedac_slugs')),
          'supports' => array(
              'title',
              'editor',
              'excerpt',
              'thumbnail'
          ), // Go to Dashboard Custom HTML5 Blank post for supports
          'can_export' => true, // Allows export in Tools > Export
          'taxonomies' => array(
              'post_tag',
              'category'
          ) // Add Category and Post Tags support
      )
  );
  flush_rewrite_rules();
}

//Medarbetare
add_action( 'init',__NAMESPACE__ . '\\create_posttype_medarbetare' );
function create_posttype_medarbetare() {
  register_post_type( 'medarbetare',
    array(
      'labels' => array(
         'name' => __('Medarbetare', 'medarbetare'), // Rename these to suit
          'singular_name' => __('Medarbetare', 'medarbetare'),
          'add_new' => __('Lägg till', 'medarbetare'),
          'add_new_item' => __('Lägg till', 'medarbetare'),
          'edit' => __('Ändra', 'medarbetare'),
          'edit_item' => __('Ändra', 'medarbetare'),
          'new_item' => __('Nytt', 'medarbetare'),
          'view' => __('Visa', 'medarbetare'),
          'view_item' => __('Visa', 'medarbetare'),
          'search_items' => __('Sök', 'medarbetare'),
          'not_found' => __('Inget funnet', 'medarbetare'),
          'not_found_in_trash' => __('Papperskorgen är tom', 'medarbetare')
      ),
          'public' => true,
          'hierarchical' => false, // Allows your posts to behave like Hierarchy Pages
          'has_archive' => false,
          'menu_icon'           => 'dashicons-admin-users',
          'rewrite' => array('with_front' => false),
          'supports' => array(
              'title',
              'editor',
              'excerpt',
              'thumbnail'
          ), // Go to Dashboard Custom HTML5 Blank post for supports
          'can_export' => true, // Allows export in Tools > Export
          'taxonomies' => array(
              //'post_tag',
              //'category'
          ) // Add Category and Post Tags support
      )
  );
  flush_rewrite_rules();
}

//Dokuement
add_action( 'init',__NAMESPACE__ . '\\create_posttype_dokument' );
function create_posttype_dokument() {
  register_post_type( 'dokument',
    array(
      'labels' => array(
         'name' => __('Dokument', 'dokument'), // Rename these to suit
          'singular_name' => __('Dokument', 'dokument'),
          'add_new' => __('Lägg till', 'dokument'),
          'add_new_item' => __('Lägg till', 'dokument'),
          'edit' => __('Ändra', 'dokument'),
          'edit_item' => __('Ändra', 'dokument'),
          'new_item' => __('Nytt', 'dokument'),
          'view' => __('Visa', 'dokument'),
          'view_item' => __('Visa', 'dokument'),
          'search_items' => __('Sök', 'dokument'),
          'not_found' => __('Inget funnet', 'dokument'),
          'not_found_in_trash' => __('Papperskorgen är tom', 'dokument')
      ),
          'public' => true,
          'hierarchical' => false, // Allows your posts to behave like Hierarchy Pages
          'has_archive' => false,
          'rewrite' => array('with_front' => false),
          'supports' => array(
              'title',
              'editor',
              'excerpt',
              'thumbnail'
          ), // Go to Dashboard Custom HTML5 Blank post for supports
          'can_export' => true, // Allows export in Tools > Export
          'taxonomies' => array(
              //'post_tag',
              //'category'
          ) // Add Category and Post Tags support
      )
  );
  flush_rewrite_rules();
}
function dokument_icon() {
    echo '
        <style>
            #adminmenu #menu-posts-dokument div.wp-menu-image:before { font-family: FontAwesome; content: "\f15b"; }
        </style>
    ';
}
add_action( 'admin_head', __NAMESPACE__ . '\\dokument_icon' );



//Dokuement
add_action( 'init',__NAMESPACE__ . '\\create_posttype_document_eng' );
function create_posttype_document_eng() {
    register_post_type('document_eng',
        array(
            'labels' => array(
                'name' => __('Dokument (Eng)', 'document_eng'), // Rename these to suit
                'singular_name' => __('Dokument (Eng)', 'document_eng'),
                'add_new' => __('Lägg till', 'document_eng'),
                'add_new_item' => __('Lägg till', 'document_eng'),
                'edit' => __('Ändra', 'document_eng'),
                'edit_item' => __('Ändra', 'document_eng'),
                'new_item' => __('Nytt', 'document_eng'),
                'view' => __('Visa', 'document_eng'),
                'view_item' => __('Visa', 'document_eng'),
                'search_items' => __('Sök', 'document_eng'),
                'not_found' => __('Inget funnet', 'document_eng'),
                'not_found_in_trash' => __('Papperskorgen är tom', 'document_eng')
            ),
            'public' => true,
            'hierarchical' => false, // Allows your posts to behave like Hierarchy Pages
            'has_archive' => false,
            'rewrite' => array('with_front' => false),
            'supports' => array(
                'title',
                'editor',
                'excerpt',
                'thumbnail'
            ), // Go to Dashboard Custom HTML5 Blank post for supports
            'can_export' => true, // Allows export in Tools > Export
            'taxonomies' => array(
                //'post_tag',
                //'category'
            ) // Add Category and Post Tags support
        )
    );

    flush_rewrite_rules();
}
// Kravspecifikationer taxonomy
add_action('init', __NAMESPACE__ . '\\swedac_register_krav_spec_taxonomy', 0);
function swedac_register_krav_spec_taxonomy()
{
    register_taxonomy(
        'kravspec_tax',
        'kravspecifikationer', 
        array(
            'label'        => __('Kategori', 'textdomain'),
            'rewrite' => array(
                'slug' => 'kravspec_tax',
            ),
            'hierarchical' => true,
        )
    );
}

// Kravspecifikationer post_type
add_action('init',__NAMESPACE__ . '\\create_posttype_kravspecifikationer', 5);
function create_posttype_kravspecifikationer() {
    register_post_type('kravspecifikationer',
        array(
            'labels' => array(
                'name' => __('Kravspecifikationer', 'kravspecifikationer'), // Rename these to suit
                'singular_name' => __('Kravspecifikation', 'kravspecifikationer'),
                'add_new' => __('Lägg till', 'kravspecifikationer'),
                'add_new_item' => __('Lägg till', 'kravspecifikationer'),
                'edit' => __('Ändra', 'kravspecifikationer'),
                'edit_item' => __('Ändra', 'kravspecifikationer'),
                'new_item' => __('Nytt', 'kravspecifikationer'),
                'view' => __('Visa', 'kravspecifikationer'),
                'view_item' => __('Visa', 'kravspecifikationer'),
                'search_items' => __('Sök', 'kravspecifikationer'),
                'not_found' => __('Inget funnet', 'kravspecifikationer'),
                'not_found_in_trash' => __('Papperskorgen är tom', 'kravspecifikationer')
            ),
            'menu_icon' => 'dashicons-book',
            'public' => true,
            'hierarchical' => false, // Allows your posts to behave like Hierarchy Pages
            'has_archive' => false,
            'rewrite' => array('with_front' => false),
            'supports' => array(
                'title',
                'editor',
                'excerpt',
                'thumbnail'
            ), // Go to Dashboard Custom HTML5 Blank post for supports
            'can_export' => true, // Allows export in Tools > Export
            'taxonomies' => array(
                'kravspec_tax'
            ) // Add Category and Post Tags support
        )
    );
     
    flush_rewrite_rules();
}

function document_eng_icon() {
    echo '
        <style>
            #adminmenu #menu-posts-document_eng div.wp-menu-image:before { font-family: FontAwesome; content: "\f15b"; }
        </style>
    ';
}

add_action( 'admin_head', __NAMESPACE__ . '\\document_eng_icon' );

// Certifikat och godkännanden post_type
add_action('init',__NAMESPACE__ . '\\create_posttype_certifikat');
function create_posttype_certifikat() {
    register_post_type('certifikat',
        array(
            'labels' => array(
                'name' => __('Certifikat och godkännanden', 'Certifikat och Godkännanden'), // Rename these to suit
                'singular_name' => __('Certifikat och godkännanden', 'Certifikat och Godkännanden'),
                'add_new' => __('Lägg till', 'Certifikat och Godkännanden'),
                'add_new_item' => __('Lägg till', 'Certifikat och Godkännanden'),
                'edit' => __('Ändra', 'Certifikat och Godkännanden'),
                'edit_item' => __('Ändra', 'Certifikat och Godkännanden'),
                'new_item' => __('Nytt', 'Certifikat och Godkännanden'),
                'view' => __('Visa', 'Certifikat och Godkännanden'),
                'view_item' => __('Visa', 'Certifikat och Godkännanden'),
                'search_items' => __('Sök', 'Certifikat och Godkännanden'),
                'not_found' => __('Inget funnet', 'Certifikat och Godkännanden'),
                'not_found_in_trash' => __('Papperskorgen är tom', 'Certifikat och Godkännanden')
            ),
            'menu_icon' => 'dashicons-book',
            'public' => true,
            'hierarchical' => false, // Allows your posts to behave like Hierarchy Pages
            'has_archive' => false,
            'supports' => array(
                'title',
                'editor',
                'excerpt',
                'thumbnail'
            ), // Go to Dashboard Custom HTML5 Blank post for supports
            'can_export' => true, // Allows export in Tools > Export
            'rewrite' => array('slug' => __('/tjanster/reglerad-matteknik/certifikat-och-godkannanden'), 'with_front' => false),
            'taxonomies' => array(
            ) // Add Category and Post Tags support
        )
    );
     
    flush_rewrite_rules();
}
