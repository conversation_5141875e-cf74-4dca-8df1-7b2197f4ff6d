<?php

function addFormShortcodes(){

  $dd_mailto = get_field('dd_mailto');
  $dd_mailto = ($dd_mailto) ? $dd_mailto : '<EMAIL>' ;

  if(isset($_POST['submitted'])) {

      if(trim($_POST['firstname']) === '') {
        $nameError = 'Fyll i ditt förnamn.';
        $hasError = true;
      } else {
        $name = trim($_POST['firstname']);
      }
      if(trim($_POST['lastname']) === '') {
        $nameError = 'Fyll i ditt efternamn.';
        $hasError = true;
      } else {
        $name = trim($_POST['lastname']);
      }
      if(trim($_POST['email']) === '')  {
        $emailError = 'Fyll i din epost korrekt.';
        $hasError = true;
      } else {
        $email = trim($_POST['email']);
      }
      if(trim($_POST['improvments'])) {
        $improvments = trim($_POST['improvments']);
      }
      if(trim($_POST['message-customer'])) {
        $improvments = trim($_POST['message-customer']);
      }
      if(!isset($hasError)) {
        $emailTo = $dd_mailto;
        $subject = 'Epost från swedac.se hjälpformulär';
        $body    = "Namn: $firstname $lastname \n\nEpost: $email \n\nFörbättringar: $improvments";
        $headers = 'Från: Swedac <'.$emailTo.'>' . "\r\n" . 'Svara till: ' . $email;
        
        mail($emailTo, $subject, $body, $headers);

        $emailSent = true;

        add_action( 'wp_mail_failed', 'onMailError', 10, 1 );
        function onMailError( $wp_error ) {
          echo "<pre>";
          print_r($wp_error);
          echo "</pre>";
        } 
        
        // TILL EN TACKSIDA KANSKE? ELLER AJAX?
      }
  }



  // GÖR DETTA SITÄLLET https://sendgrid.com/docs/for-developers/sending-email/v3-php-code-example/

  // using SendGrid's PHP Library
  // https://github.com/sendgrid/sendgrid-php

  // if(isset($_POST['submitted'])) {
  //   require '../vendor/autoload.php';
  //   $email = new \SendGrid\Mail\Mail();
  //   $email->setFrom("<EMAIL>", "Swedac");
  //   $email->setSubject("Epost från swedac.se hjälpformulär");
  //   $email->addTo("<EMAIL>", "Markus Hedenborn");
  //   $email->addTo($dd_mailto, $dd_mailto);
  //   $email->addContent(
  //       "text/plain", "and easy to do anywhere, even with PHP"
  //   );
  //   $email->addContent(
  //       "text/html", "<strong>and easy to do anywhere, even with PHP</strong>"
  //   );
  //   $sendgrid = new \SendGrid(getenv('*********************************************************************'));
  //   try {
  //       $response = $sendgrid->send($email);
  //       print $response->statusCode() . "\n";
  //       print_r($response->headers());
  //       print $response->body() . "\n";
  //   } catch (Exception $e) {
  //       echo 'Caught exception: ',  $e->getMessage(), "\n";
  //   }
  // }

  //  START shortcode simple Form

  add_shortcode("help-form", "simplejack");
  function simplejack() {

  ob_start(); ?>

  <div id="simplejack" class="">

    <form name="form" action="" method="post">
      <div class="input-group">
        <div class="input-group-50">
          <label for="fname">Förnamn <small class="required">*</small></label>
          <input class="input-field" type="text" id="fname" name="firstname" placeholder="Ditt förnamn" required>
        </div>
        <div class="input-group-50">
          <label for="lname">Efternamn <small class="required">*</small></label>
          <input class="input-field" type="text" id="lname" name="lastname" placeholder="Ditt efternamn" required>
        </div>
      </div>
      <div class="input-group">

        <div class="<?= $if_dropdown = have_rows('dd_repeater')  ? 'input-group-50' : 'input-group-100' ; ?>">
          <label for="email">Epost</label>
          <input class="input-field femail" type="email" id="email" name="email" placeholder="Din epost">
        </div>

        <?php if( have_rows('dd_repeater') ): ?>
        <div class="input-group-50">

          <?php $dd_title = get_field('dd_title'); ?>
          <?php $dd_title = ($dd_title) ? $dd_title : 'Vad vill du förbättras?' ; ?>

          <label for="improvement"><?= $dd_title; ?></label>
          <select class="input-field select" id="improvement" name="improvement" required>

            <?php while( have_rows('dd_repeater') ) : the_row(); ?>

            <?php $dd_value = get_sub_field('dd_value'); ?>

            <option value="<?= $dd_value; ?>"><?= $dd_value; ?></option>

            <?php endwhile; ?>

          </select>

        </div>
        <?php endif; ?>

      </div>
      <div class="input-group">
        <label for="message-customer">Meddelande</label>
        <div>
          <textarea class="input-field textarea" id="message-customer" name="message-customer" type="textarea"
            placeholder="Skriv ditt meddelande..." rows="7"></textarea>
        </div>
        <input class="fsubmit" type="submit" value="Skicka" name="submitted">
      </div>
    </form>
  </div>

  <?php

  $form = ob_get_clean();

  return $form;

  }
}

add_action('acf/init', 'addFormShortcodes');// END simple form