<?php
add_action('template_redirect', 'cc_redirect_attachment_page');
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> till startsidan när man försöker komma åt attachment single sidan
 */
function cc_redirect_attachment_page()
{
    if (is_attachment()) {
        wp_redirect(home_url());
        exit;
    }
}

add_action('init', 'cc_check_subscribers_search_result');
/**
 * Kör en SQL query som letar efter sökresultat, om inga sökresultat hittas, skickas vi tillbaka med ett felmeddelande.
 */
function cc_check_subscribers_search_result()
{   
    if (isset($_POST['submit-search-subscribers']) && isset($_POST['search'])) {
        $result = cc_subscribers_wpdb_queries(intval($_POST['search']), 100)['subscribers'];

        if (empty($result) || $result === null) {
            $urlPart = urlencode(cc_base64_url_encode($_POST['search']));

            wp_redirect(admin_url('admin.php?page=manage-subscribers&search=no-results&keyword=' . cc_base64_url_encode($_POST['search'])));
            // wp_redirect(admin_url('?page=manage-subscribers&search=no-results&keyword=' . $urlPart));
            exit;
        } else {
            wp_redirect(admin_url('admin.php?page=manage-subscribers&search=found-results&keyword=' . cc_base64_url_encode($_POST['search'])));
            // wp_redirect(admin_url('?page=manage-subscribers&search=found-results&keyword=' . $urlPart));
            exit;
        }
    }
}

add_action('wp_ajax_cc_query_posts_subscribers', 'cc_query_posts_subscribers');
/**
 * Ajax funktion som hämtar alla dokument och ämnesområden från databasen.
 */
function cc_query_posts_subscribers()
{
    if (!isset($_POST['action']) || $_POST['action'] !== 'cc_query_posts_subscribers') {
        wp_send_json(false);
    }

    $postTypes = [
        'dokument',
        'amnesomraden',
    ];

    $data = [
        'docs' => [],
        'amnesomraden' => [],
    ];

    foreach ($postTypes as $postType) {
        $args = [
            'fields' => 'ids',
            'numberposts' => -1,
            'post_type' => $postType,
            'post_status' => 'publish',
        ];
        
        $postIds = get_posts($args);

        if (empty($postIds)) {
            wp_send_json(false);
        }

        foreach ($postIds as $id) {
            if ($postType === 'dokument') {
                $data['docs'][] = get_field('crm_doc_dokumentbeteckning', $id);
            } elseif ($postType === 'amnesomraden') {
                $temp = [
                    'post_title' => '',
                    'post_id' => 0,
                ];

                $temp['post_title'] = get_the_title($id);
                $temp['post_id'] = $id;

                $data['amnesomraden'][] = $temp;
            }
        }  
    } 

    wp_send_json($data);
}

add_action('wp_ajax_cc_delete_subscriber', 'cc_delete_subscriber');
/** 
 * Ajax funktion som tar bort en prenumerant och dess kopplade data ur databasen.
 */
function cc_delete_subscriber()
{
    if (!isset($_POST['action']) || $_POST['action'] !== 'cc_delete_subscriber' || empty($_POST['uid'])) {
        wp_send_json(false);
    }

    $uid = intval(base64_decode($_POST['uid']));

    global $wpdb;

    $tables = [
        'subscribers',
        'subscribers_document',
        'subscribers_post',
    ];

    $rows = [
        'id',
        'subscriber_id',
        'subscriber_id',
    ];

    foreach ($tables as $i => $table) {
        $result = $wpdb->delete($table, [$rows[$i] => $uid]);

        if ($result === false) {
            wp_send_json(false);
        }
    }

    wp_send_json(true);
}

add_action('wp_ajax_cc_get_subscribers_data', 'cc_get_subscribers_data');
/**
 * Ajax funktion som hämtar alla ämnesområden och föreskrifter som man bevakar.
 */
function cc_get_subscribers_data()
{
    if (!isset($_POST['action']) || $_POST['action'] !== 'cc_get_subscribers_data' || empty($_POST['uid'])) {
        wp_send_json(false);
    }

    $uid = intval(base64_decode($_POST['uid']));

    global $wpdb;

    $tables = [
        'subscribers_document',
        'subscribers_post',
    ];

    $returnValue = [];

    foreach ($tables as $table) {
        $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM `{$table}` WHERE `subscriber_id` = %s", $uid), ARRAY_A);

        if ($result === null) {
            wp_send_json(false);
        }

        $returnValue[] = $result;
    }

    wp_send_json($returnValue);
}

add_action('wp_ajax_cc_update_subscriber', 'cc_update_subscriber');
/**
 * Ajax funktion som uppdaterar befintlig prenumerants data och bevakningar.
 */
function cc_update_subscriber()
{
    if (!isset($_POST['action']) || $_POST['action'] !== 'cc_update_subscriber' || empty($_POST['data'])) {
        wp_send_json(false);
    }

    global $wpdb;

    $retrievedData = json_decode(stripslashes($_POST['data']));

    $subID = intval(base64_decode($retrievedData->subscriber_id));
    $subName = $retrievedData->subscriber_name;
    $subEmail = $retrievedData->subscriber_email;
    $subAreas = $retrievedData->areas;
    $subDocs = $retrievedData->docs;

    $updateData = [
        'namn' => $subName,
        'mail' => $subEmail,
    ];
    
    // Uppdatera prenumerantens namn och e-postadress.
    $result = $wpdb->update('subscribers', $updateData, ['id' => $subID]);

    if ($result === false) {
        wp_send_json(false);
    }

    // Funktion som hanterar uppdateringar i tabellerna subscribers_post och subscribers_document.
    function ccSubscribeUpdate($column, $table, $where, $updatedData, $insertData, $deleteCond)
    {   
        global $wpdb;

        $temp = $wpdb->get_results($wpdb->prepare("SELECT `{$column}` FROM `{$table}` WHERE `{$where['column']}` = %s", $where['id']), ARRAY_A);

        if ($temp === false) {
            return false;
        }

        $alreadySub = [];

        foreach ($temp as $sub) {
            $alreadySub[] = $sub[$column];
        }

        foreach ($updatedData as $data) {
            if ($table === 'subscribers_post') {
                $id = intval($data[0]);
                $insertData['post_id'] = $id;
                $deleteCond['post_id'] = $id;
            } elseif ($table === 'subscribers_document') {
                $id = $data[0];
                $insertData['document_name'] = $id;
                $deleteCond['document_name'] = $id;
            }

            $status = $data[1];


            // Om prenumeranten valt att bevaka $table, och det inte redan finns en bevakning, lägg till en bevakning.
            if ($status === true && in_array($id, $alreadySub) === false) {
               
                $result = $tempAlreadySub = $wpdb->insert($table, $insertData);

                if ($result === false) {
                    return false;
                }
            // Om prenumeranten inte bevakar $table längre och det finns kvar en bevakning, ta bort den.
            } elseif ($status === false) {
                $result = $wpdb->delete($table, $deleteCond);

                if ($result === false) {
                    return false;
                }
            }
        }

        return true;
    }

    // Uppdatera prenumerantens bevakade ämnesområden.
    $areaInsertData = [
        'hash' => date('Ymd').rand(10000, 99999),
        'subscriber_id' => $subID,
        'post_id' => 0,
        'datum' => date('Ymd'),
    ];

    $areaDeleteCond = [
        'subscriber_id' => $subID,
        'post_id' => 0,
    ];

    $result = ccSubscribeUpdate('post_id', 'subscribers_post', ['column' => 'subscriber_id', 'id' => $subID], $subAreas, $areaInsertData, $areaDeleteCond);
    if (!$result) {
        wp_send_json(false);
    }

    // Uppdatera prenumerantens bevakade dokument (föreskrifter).
    $docsInsertData = [
        'hash' => date('Ymd').rand(10000, 99999),
        'subscriber_id' => $subID,
        'document_name' => '',
        'paired_post_ids' => serialize([]),
        'subscribed_date' => date('Ymd'),
        'last_notified' => date('Ymd'),
    ];

    $docsDeleteCond = [
        'document_name' => '',
        'subscriber_id' => $subID,
    ];

    $result = ccSubscribeUpdate('document_name', 'subscribers_document', ['column' => 'subscriber_id', 'id' => $subID], $subDocs, $docsInsertData, $docsDeleteCond);
    if (!$result) {
        wp_send_json(false);
    }

    $subCount = [
        'areas' => 0,
        'docs' => 0,
    ];

    $countAreas = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM `subscribers_post` WHERE `subscriber_id` = %d", $subID));
    $countDocs = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM `subscribers_document` WHERE `subscriber_id` = %d", $subID));

    if ($countAreas === false || $countDocs === false) {
        wp_send_json(false);
    }

    $subCount['areas'] = $countAreas; 
    $subCount['docs'] = $countDocs; 

    wp_send_json($subCount);
}
