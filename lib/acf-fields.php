<?php

// if( function_exists('acf_add_local_field_group') ):

//   acf_add_local_field_group(array(
//     'key' => 'group_574014ef50eb9',
//     'title' => 'Bevaka innehåll',
//     'fields' => array(
//       array(
//         'key' => 'field_574015e70b914',
//         'label' => 'Bevaka innehåll',
//         'name' => 'bevaka_innehall',
//         'type' => 'radio',
//         'instructions' => 'Ange Ja / Nej ifall bevakning av innehåll ska vara möjligt. <br/> 
//   <span style="margin:10px 0; width:100%; float:left; "><i style="margin:0 7px 0 0;" class="fa fa-info-circle" aria-hidden="true"></i><b>Obs</b>: Om det är <b>första gången</b> du markerar "<u>Ja</u>" så <b>måste</b> omr<PERSON>det sparas innan du kan skicka en uppdatering till alla som bevakar. </span>
//   ',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'choices' => array(
//           'false' => 'Nej',
//           'true' => 'Ja, mjöligt att bevaka innehåll.',
//         ),
//         'allow_null' => 0,
//         'other_choice' => 0,
//         'save_other_choice' => 0,
//         'default_value' => '',
//         'layout' => 'vertical',
//         'return_format' => 'value',
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'amnesomraden',
//         ),
//       ),
//     ),
//     'menu_order' => -1000,
//     'position' => 'side',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_589430a5223d6',
//     'title' => 'Relaterad information',
//     'fields' => array(
//       array(
//         'key' => 'field_58943283e26a8',
//         'label' => 'Relaterad information',
//         'name' => 'relaterad_information',
//         'type' => 'relationship',
//         'instructions' => 'Ange vad detta är relaterat till.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'post_type' => array(
//           0 => 'amnesomraden',
//           1 => 'page',
//           2 => 'post',
//           3 => 'swedac_magasin',
//         ),
//         'taxonomy' => '',
//         'filters' => array(
//           0 => 'search',
//           1 => 'post_type',
//         ),
//         'elements' => '',
//         'min' => '',
//         'max' => '',
//         'return_format' => 'object',
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'post',
//         ),
//       ),
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'page',
//         ),
//       ),
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'swedac_magasin',
//         ),
//       ),
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'amnesomraden',
//         ),
//       ),
//     ),
//     'menu_order' => -10,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_5763ad8081817',
//     'title' => 'Visa nyhetsförfattare',
//     'fields' => array(
//       array(
//         'key' => 'field_5763ae70b60e5',
//         'label' => 'Visa författare',
//         'name' => 'hide_author_sv',
//         'type' => 'true_false',
//         'instructions' => 'Ange ifall författaren ska visas.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'message' => '',
//         'default_value' => 0,
//         'ui' => 0,
//         'ui_on_text' => '',
//         'ui_off_text' => '',
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'post',
//         ),
//       ),
//     ),
//     'menu_order' => -5,
//     'position' => 'acf_after_title',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_570ca03b24617',
//     'title' => 'Ikoner',
//     'fields' => array(
//       array(
//         'key' => 'field_570ca0535344f',
//         'label' => 'Länkar ikon (Färg)',
//         'name' => 'lankar_ikon_blue',
//         'type' => 'image',
//         'instructions' => 'Lägg till den ikon som passar till detta.
//   <br/>
//   <b>Blå ikon</b>',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 50,
//           'class' => '',
//           'id' => '',
//         ),
//         'return_format' => 'url',
//         'preview_size' => 'thumbnail',
//         'library' => 'all',
//         'min_width' => '',
//         'min_height' => '',
//         'min_size' => '',
//         'max_width' => '',
//         'max_height' => '',
//         'max_size' => '',
//         'mime_types' => '',
//       ),
//       array(
//         'key' => 'field_570ca16417a0f',
//         'label' => 'Länkar ikon (Vit)',
//         'name' => 'lankar_ikon_white',
//         'type' => 'image',
//         'instructions' => 'Lägg till den ikon som passar till detta.
//   <br/>
//   <b>Vit ikon</b>',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 50,
//           'class' => '',
//           'id' => '',
//         ),
//         'return_format' => 'url',
//         'preview_size' => 'thumbnail',
//         'library' => 'all',
//         'min_width' => '',
//         'min_height' => '',
//         'min_size' => '',
//         'max_width' => '',
//         'max_height' => '',
//         'max_size' => '',
//         'mime_types' => '',
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'intern_lankningar',
//         ),
//       ),
//     ),
//     'menu_order' => -1,
//     'position' => 'acf_after_title',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => array(
//       0 => 'permalink',
//       1 => 'the_content',
//       2 => 'excerpt',
//       3 => 'custom_fields',
//       4 => 'discussion',
//       5 => 'comments',
//       6 => 'revisions',
//       7 => 'slug',
//       8 => 'author',
//       9 => 'format',
//       10 => 'page_attributes',
//       11 => 'featured_image',
//       12 => 'categories',
//       13 => 'tags',
//       14 => 'send-trackbacks',
//     ),
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_56d942f53969e',
//     'title' => 'Sidintroduktion',
//     'fields' => array(
//       array(
//         'key' => 'field_56d9443312391',
//         'label' => 'Introduktion för sida',
//         'name' => 'sidintroduktion_sv',
//         'type' => 'textarea',
//         'instructions' => 'Ange en kort men informativ introduktion som visas tillsammans med titeln.	<br/>
//   <em>Det kan vara t.ex - Vad besökaren kommer att kunna hitta under den aktuella sidan.</em>',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Introduktion för sida',
//         'maxlength' => '',
//         'rows' => 3,
//         'new_lines' => 'wpautop',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'page_template',
//           'operator' => '==',
//           'value' => 'page-template_2.php',
//         ),
//       ),
//       array(
//         array(
//           'param' => 'page_template',
//           'operator' => '==',
//           'value' => 'page-template_3.php',
//         ),
//       ),
//       array(
//         array(
//           'param' => 'page_template',
//           'operator' => '==',
//           'value' => 'page-template-6-swedac-magasin.php',
//         ),
//       ),
//       array(
//         array(
//           'param' => 'page_template',
//           'operator' => '==',
//           'value' => 'page-template_minasidor.php',
//         ),
//       ),
//     ),
//     'menu_order' => -1,
//     'position' => 'acf_after_title',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => array(
//       0 => 'the_content',
//       1 => 'excerpt',
//       2 => 'custom_fields',
//       3 => 'discussion',
//       4 => 'comments',
//       5 => 'revisions',
//       6 => 'slug',
//       7 => 'author',
//       8 => 'format',
//       9 => 'featured_image',
//       10 => 'categories',
//       11 => 'tags',
//       12 => 'send-trackbacks',
//     ),
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_5ef71e73295e8',
//     'title' => 'Informationsruta - "Sticky"',
//     'fields' => array(
//       array(
//         'key' => 'field_5ef724f4e3d59',
//         'label' => 'Titel för informationsrutan',
//         'name' => 'title-infobox',
//         'type' => 'text',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => '',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//       ),
//       array(
//         'key' => 'field_5ef722f24a1ac',
//         'label' => 'Textinnehåll',
//         'name' => 'infobox-repeater',
//         'type' => 'repeater',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => '',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_5ef8b8ed48670',
//             'label' => 'Delstycke',
//             'name' => 'text-part',
//             'type' => 'repeater',
//             'instructions' => 'Först skriver du en titel och sedan lägger du till så många paragrafer du behöver.',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'collapsed' => '',
//             'min' => 0,
//             'max' => 0,
//             'layout' => 'block',
//             'button_label' => 'Lägg till ny stycke',
//             'sub_fields' => array(
//               array(
//                 'key' => 'field_5ef8b97a48671',
//                 'label' => 'Titel',
//                 'name' => 'sub-title',
//                 'type' => 'text',
//                 'instructions' => '',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => '',
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'default_value' => '',
//                 'placeholder' => '',
//                 'prepend' => '',
//                 'append' => '',
//                 'maxlength' => '',
//               ),
//               array(
//                 'key' => 'field_5ef8b98c48672',
//                 'label' => 'Textstycke',
//                 'name' => 'text-repeater',
//                 'type' => 'repeater',
//                 'instructions' => '',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => '',
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'collapsed' => '',
//                 'min' => 0,
//                 'max' => 0,
//                 'layout' => 'table',
//                 'button_label' => '',
//                 'sub_fields' => array(
//                   array(
//                     'key' => 'field_5ef8c8b802a12',
//                     'label' => 'Stycke',
//                     'name' => 'sub-text',
//                     'type' => 'text',
//                     'instructions' => '',
//                     'required' => 0,
//                     'conditional_logic' => 0,
//                     'wrapper' => array(
//                       'width' => '',
//                       'class' => '',
//                       'id' => '',
//                     ),
//                     'default_value' => '',
//                     'placeholder' => '',
//                     'prepend' => '',
//                     'append' => '',
//                     'maxlength' => '',
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ),
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'amnesomraden',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_5d6e61bc5a8fc',
//     'title' => 'Kravspecifikationer',
//     'fields' => array(
//       array(
//         'key' => 'field_5d6e6297772f9',
//         'label' => 'Kravspecifikationer',
//         'name' => 'kravspecifikationer',
//         'type' => 'repeater',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '100',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till kravspecifikation',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_5d6e62c9772fb',
//             'label' => 'Beskrivning',
//             'name' => 'beskrivning',
//             'type' => 'text',
//             'instructions' => '',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//           ),
//           array(
//             'key' => 'field_5d6e62c2772fa',
//             'label' => 'Titel',
//             'name' => 'title',
//             'type' => 'text',
//             'instructions' => '',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//           ),
//         ),
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'kravspecifikationer',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//     'modified' => 1568892075,
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_5d6fb6fea8e40',
//     'title' => 'Certifieringsordning',
//     'fields' => array(
//       array(
//         'key' => 'field_5d6fb705e8e84',
//         'label' => 'Kategori',
//         'name' => 'certifieringsordning_kategori',
//         'type' => 'taxonomy',
//         'instructions' => 'Vänligen välj från vilket kontrollformsområde kravspecifikationerna ska listas',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '100',
//           'class' => '',
//           'id' => '',
//         ),
//         'taxonomy' => 'kravspec_tax',
//         'field_type' => 'radio',
//         'allow_null' => 0,
//         'add_term' => 1,
//         'save_terms' => 0,
//         'load_terms' => 0,
//         'return_format' => 'id',
//         'multiple' => 0,
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'page_template',
//           'operator' => '==',
//           'value' => 'page-template-certifieringsorgan.php',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//     'modified' => 1567602572,
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_5a1ebb2f4a2db',
//     'title' => 'Information om medarbetaren',
//     'fields' => array(
//       array(
//         'key' => 'field_5a1ebb6bc4199',
//         'label' => 'Ingress',
//         'name' => 'medarbetare_ingress_sv',
//         'type' => 'textarea',
//         'instructions' => 'Ange en ingresstext.',
//         'required' => 1,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Ingress',
//         'maxlength' => '',
//         'rows' => 4,
//         'new_lines' => 'wpautop',
//       ),
//       array(
//         'key' => 'field_5a1ebb81c419a',
//         'label' => 'Roll',
//         'name' => 'medarbetare_roll_sv',
//         'type' => 'text',
//         'instructions' => 'Vad har personen för roll? <br/>
//   T.ex Jurist, bedömningsledare etc.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Roll',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'medarbetare',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'acf_after_title',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => array(
//       0 => 'excerpt',
//       1 => 'custom_fields',
//       2 => 'discussion',
//       3 => 'comments',
//       4 => 'slug',
//       5 => 'author',
//       6 => 'format',
//       7 => 'page_attributes',
//       8 => 'categories',
//       9 => 'tags',
//       10 => 'send-trackbacks',
//     ),
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_584ab42bb0297',
//     'title' => 'Epost inställningar',
//     'fields' => array(
//       array(
//         'key' => 'field_584ab46a6d338',
//         'label' => 'Företagsnamn',
//         'name' => 'mail_foretagsnamn',
//         'type' => 'text',
//         'instructions' => 'ex: ccsolutions AB',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '25',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => '',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//       ),
//       array(
//         'key' => 'field_584ab4776d339',
//         'label' => 'Kontakt epost',
//         'name' => 'mail_kontakt_epost',
//         'type' => 'email',
//         'instructions' => 'ex: <EMAIL>',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '25',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => '',
//         'prepend' => '',
//         'append' => '',
//       ),
//       array(
//         'key' => 'field_58556bb11e1f8',
//         'label' => 'Kontakt telefon',
//         'name' => 'mail_kontakt_telefon',
//         'type' => 'text',
//         'instructions' => 'ex: 0542031818',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '25',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'maxlength' => '',
//         'placeholder' => '',
//         'prepend' => '',
//         'append' => '',
//       ),
//       array(
//         'key' => 'field_584ab4906d33a',
//         'label' => 'Bas url till site',
//         'name' => 'mail_bas_url_till_site',
//         'type' => 'url',
//         'instructions' => 'ex: http://www.ccsolutions.se',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '25',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => '',
//       ),
//       array(
//         'key' => 'field_584ab51b6d33e',
//         'label' => 'Adress',
//         'name' => 'mail_adress',
//         'type' => 'textarea',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '100',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => '',
//         'maxlength' => '',
//         'rows' => 3,
//         'new_lines' => 'br',
//       ),
//       array(
//         'key' => 'field_584ab4d16d33b',
//         'label' => 'Huvudfärg',
//         'name' => 'mail_huvudfarg',
//         'type' => 'color_picker',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '34',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//       ),
//       array(
//         'key' => 'field_584ab4f36d33c',
//         'label' => 'Logotyp - Sidhuvud',
//         'name' => 'mail_logotyp_sidhuvud',
//         'type' => 'image',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '33',
//           'class' => '',
//           'id' => '',
//         ),
//         'return_format' => 'url',
//         'preview_size' => 'thumbnail',
//         'library' => 'all',
//         'min_width' => '',
//         'min_height' => '',
//         'min_size' => '',
//         'max_width' => '',
//         'max_height' => '',
//         'max_size' => '',
//         'mime_types' => '',
//       ),
//       array(
//         'key' => 'field_584ab5066d33d',
//         'label' => 'Logotyp - Sidfot',
//         'name' => 'mail_logotyp_sidfot',
//         'type' => 'image',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '33',
//           'class' => '',
//           'id' => '',
//         ),
//         'return_format' => 'url',
//         'preview_size' => 'thumbnail',
//         'library' => 'all',
//         'min_width' => '',
//         'min_height' => '',
//         'min_size' => '',
//         'max_width' => '',
//         'max_height' => '',
//         'max_size' => '',
//         'mime_types' => '',
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'options_page',
//           'operator' => '==',
//           'value' => 'custom-email-settings',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => array(
//       0 => 'excerpt',
//       1 => 'custom_fields',
//       2 => 'discussion',
//       3 => 'comments',
//       4 => 'revisions',
//     ),
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_589436399aac2',
//     'title' => 'Rubriker från CRM',
//     'fields' => array(
//       array(
//         'key' => 'field_589437ab3c28f',
//         'label' => 'Rubriker från CRM',
//         'name' => 'rubriker_fran_crm',
//         'type' => 'repeater',
//         'instructions' => 'Lägg till de rubriker ni har i ert CRM.<br>
//   <small>Lägg till <b>en</b> rubrik på varje rad.</small>',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till rubrik',
//         'collapsed' => '',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_589437b83c290',
//             'label' => 'Rubrik',
//             'name' => 'rubrik',
//             'type' => 'text',
//             'instructions' => 'Ange rubrik. <br/><b>Viktigt att det är exakt samma rubrik som finns i CRM:et</b>',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'maxlength' => '',
//             'placeholder' => 'Rubrik',
//             'prepend' => '',
//             'append' => '',
//           ),
//         ),
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'options_page',
//           'operator' => '==',
//           'value' => 'acf-options-rubriker-fran-crm',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));

//   acf_add_local_field_group(array(
//     'key' => 'group_588b1e9c18e1c',
//     'title' => 'Swedac Magasin',
//     'fields' => array(
//       array(
//         'key' => 'field_588b200ffb7e5',
//         'label' => 'Bild: Kvadrat',
//         'name' => 'swedac_magasin_kvadratisk',
//         'type' => 'image',
//         'instructions' => 'Ange bilden som ska visas i kvadratisk form. <br/>
//   T.ex 500 x 500. Kommer att beskäras automatiskt. Så beskär innan uppladdning.',
//         'required' => 1,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '50',
//           'class' => '',
//           'id' => '',
//         ),
//         'return_format' => 'array',
//         'preview_size' => 'thumbnail',
//         'library' => 'all',
//         'min_width' => '',
//         'min_height' => '',
//         'min_size' => '',
//         'max_width' => '',
//         'max_height' => '',
//         'max_size' => '',
//         'mime_types' => '',
//       ),
//       array(
//         'key' => 'field_588b2067fb7e7',
//         'label' => 'Bild: Rektangulär',
//         'name' => 'swedac_magasin_rektangular',
//         'type' => 'image',
//         'instructions' => 'Ange bilden som ska visas i rektangulär form. <br/>
//   T.ex 1920 x 500. Kommer att beskäras automatiskt. Så beskär innan uppladdning.',
//         'required' => 1,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '50',
//           'class' => '',
//           'id' => '',
//         ),
//         'return_format' => 'array',
//         'preview_size' => 'thumbnail',
//         'library' => 'all',
//         'min_width' => '',
//         'min_height' => '',
//         'min_size' => '',
//         'max_width' => '',
//         'max_height' => '',
//         'max_size' => '',
//         'mime_types' => '',
//       ),
//       array(
//         'key' => 'field_5f8026f1c0515',
//         'label' => 'Utvalda: rubrik',
//         'name' => 'swedac_magasin_rubrik',
//         'type' => 'text',
//         'instructions' => 'Rubrik för utvalda inlägg',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => '',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'swedac_magasin',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'acf_after_title',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));

//   acf_add_local_field_group(array(
//     'key' => 'group_5f80148f28233',
//     'title' => 'Swedac Utvalda',
//     'fields' => array(
//       array(
//         'key' => 'field_5f80151f4cf8c',
//         'label' => 'Utvalda poster',
//         'name' => 'utvalda_repeater',
//         'type' => 'repeater',
//         'instructions' => 'Lägg till ett nytt segment som inkluderar bilder, video och inlägg mm.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'block',
//         'button_label' => 'Lägg till nytt segment',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_5f801ff55ad77',
//             'label' => 'Inledande bild',
//             'name' => 'utvalda_hero',
//             'type' => 'image',
//             'instructions' => 'Ange bilden som ska visas i rektangulär form.
//   T.ex 1920 x 500. Kommer att beskäras automatiskt. Så beskär innan uppladdning.',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '50',
//               'class' => '',
//               'id' => '',
//             ),
//             'return_format' => 'array',
//             'preview_size' => 'medium',
//             'library' => 'all',
//             'min_width' => '',
//             'min_height' => '',
//             'min_size' => '',
//             'max_width' => '',
//             'max_height' => '',
//             'max_size' => '',
//             'mime_types' => '',
//           ),
//           array(
//             'key' => 'field_5f804258f0b9c',
//             'label' => 'Rubrik',
//             'name' => 'utvalda_hero_rubrik',
//             'type' => 'text',
//             'instructions' => 'Ange rubrik på inledande bild.',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '50',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//           ),
//           array(
//             'key' => 'field_5f80431f64ff3',
//             'label' => 'Underrubrik',
//             'name' => 'utvalda_underrubrik',
//             'type' => 'text',
//             'instructions' => 'Ange underrubrik som introducerar ämnet.',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '50',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//           ),
//           array(
//             'key' => 'field_5f80434b64ff4',
//             'label' => 'Text',
//             'name' => 'utvalda_text',
//             'type' => 'text',
//             'instructions' => 'Ange inledande text om ämnet.',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '50',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//           ),
//           array(
//             'key' => 'field_5f80220e94c1c',
//             'label' => 'Utvalda inlägg',
//             'name' => 'utvalda_repeater_inlagg',
//             'type' => 'repeater',
//             'instructions' => 'Lägg till 4-6 inlägg som ska visas (minst 4 och max 6st).',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'collapsed' => '',
//             'min' => 4,
//             'max' => 6,
//             'layout' => 'table',
//             'button_label' => '',
//             'sub_fields' => array(
//               array(
//                 'key' => 'field_5f8028a18c407',
//                 'label' => 'Inlägg',
//                 'name' => 'utvalda_inlagg',
//                 'type' => 'post_object',
//                 'instructions' => '',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => '1000',
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'post_type' => array(
//                   0 => 'swedac_magasin',
//                 ),
//                 'taxonomy' => '',
//                 'allow_null' => 0,
//                 'multiple' => 0,
//                 'return_format' => 'object',
//                 'ui' => 1,
//               ),
//             ),
//           ),
//           array(
//             'key' => 'field_5f8447d27b9e8',
//             'label' => 'Video',
//             'name' => 'utvalda_video',
//             'type' => 'oembed',
//             'instructions' => 'Ange URL för video',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '50',
//               'class' => '',
//               'id' => '',
//             ),
//             'width' => '',
//             'height' => '',
//           ),
//           array(
//             'key' => 'field_5f8447fc7b9e9',
//             'label' => 'Text',
//             'name' => 'utvalda_video_text',
//             'type' => 'text',
//             'instructions' => 'Ange text om video.',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '50',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//           ),
//           array(
//             'key' => 'field_5f8448983ee99',
//             'label' => 'Bild',
//             'name' => 'utvalda_video_bild',
//             'type' => 'image',
//             'instructions' => 'Posterbild till video. Ange bilden som ska visas i rektangulär form.
//   T.ex 1920 x 500. Kommer att beskäras automatiskt. Så beskär innan uppladdning.',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '50',
//               'class' => '',
//               'id' => '',
//             ),
//             'return_format' => 'array',
//             'preview_size' => 'medium',
//             'library' => 'all',
//             'min_width' => '',
//             'min_height' => '',
//             'min_size' => '',
//             'max_width' => '',
//             'max_height' => '',
//             'max_size' => '',
//             'mime_types' => '',
//           ),
//           array(
//             'key' => 'field_5f84496a31087',
//             'label' => 'Rubrik',
//             'name' => 'utvalda_video_rubrik',
//             'type' => 'text',
//             'instructions' => 'Ange rubrik till video.',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '50',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//           ),
//         ),
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'swedac_utvalda',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => array(
//       0 => 'permalink',
//       1 => 'the_content',
//       2 => 'excerpt',
//       3 => 'discussion',
//       4 => 'comments',
//       5 => 'slug',
//       6 => 'author',
//       7 => 'format',
//       8 => 'featured_image',
//       9 => 'categories',
//       10 => 'tags',
//       11 => 'send-trackbacks',
//     ),
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_583d5415aeae4',
//     'title' => 'Bedömarutbildning - Tillfällen',
//     'fields' => array(
//       array(
//         'key' => 'field_583d556d46123',
//         'label' => 'Bedömarutbildning - Tillfällen',
//         'name' => 'bedomarutbildning_tillfallen_rep',
//         'type' => 'repeater',
//         'instructions' => 'Lägg till de tillfällen som finns för bedömarutbildningar.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till tillfälle',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_583d557a46124',
//             'label' => 'Datum',
//             'name' => 'datum',
//             'type' => 'date_picker',
//             'instructions' => 'Lägg till datum',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'display_format' => 'j F, Y',
//             'return_format' => 'j F, Y',
//             'first_day' => 1,
//           ),
//         ),
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'page',
//           'operator' => '==',
//           'value' => '116',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'acf_after_title',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_57d105603c262',
//     'title' => 'Ansvarig person för aktuell sida',
//     'fields' => array(
//       array(
//         'key' => 'field_57d10f33a8267',
//         'label' => 'Hur går det till?',
//         'name' => '',
//         'type' => 'message',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '100',
//           'class' => '',
//           'id' => '',
//         ),
//         'message' => '<h4 style="margin:0 0 1px">Lägg till ansvarig person</h4>
//   <p style="margin:0 0 10px">Nedan anges ett namn, epost och vilka sidor som personen är ansvarig över. Personen i fråga kommer då få mail två gånger per år med en lista på alla sidor som han/hon är ansvarig över och bör kontrollera ifall något bör ändras eller rättas till.</p>
  
//   <h4 style="margin:0 0 1px">När görs utskicken?</h4>
//   <p style="margin:0 0 10px">Utskicken kommer göras den <u>1 mars</u> och <u>1 oktober</u>.</p> 
  
//   <h4 style="margin:0 0 1px">Vad gör vi om någon slutar?</h4>
//   <p style="margin:0 0 10px">Skulle någon av de inlagda personerna sluta så går det snabbt och enkelt att ändra Namn och E-post utan att fylla i valda sidor en gång till.</p>',
//         'new_lines' => '',
//         'esc_html' => 0,
//       ),
//       array(
//         'key' => 'field_57d10c9d0b20a',
//         'label' => 'Namn',
//         'name' => 'namn_ansvarig',
//         'type' => 'text',
//         'instructions' => 'Ange namn på personen som är ansvarig för denna sida.',
//         'required' => 1,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '33',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Namn',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//       ),
//       array(
//         'key' => 'field_57d10cc90b20b',
//         'label' => 'E-post',
//         'name' => 'e_post_ansvarig',
//         'type' => 'email',
//         'instructions' => 'Ange en epost för personen som är ansvarig för denna sida.',
//         'required' => 1,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '33',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'E-post',
//         'prepend' => '',
//         'append' => '',
//       ),
//       array(
//         'key' => 'field_57d10d1d0b20d',
//         'label' => 'Ansvarig för sida',
//         'name' => 'ansvarig_for_sida',
//         'type' => 'page_link',
//         'instructions' => 'Vilken/vilka sida/sidor är personen ansvarig för?',
//         'required' => 1,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '33',
//           'class' => '',
//           'id' => '',
//         ),
//         'post_type' => array(
//           0 => 'page',
//           1 => 'amnesomraden',
//         ),
//         'taxonomy' => array(
//         ),
//         'allow_null' => 0,
//         'allow_archives' => 0,
//         'multiple' => 1,
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'ansvarigperson',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => array(
//       0 => 'permalink',
//       1 => 'the_content',
//       2 => 'excerpt',
//       3 => 'discussion',
//       4 => 'comments',
//       5 => 'revisions',
//       6 => 'slug',
//       7 => 'author',
//       8 => 'format',
//       9 => 'page_attributes',
//       10 => 'featured_image',
//       11 => 'categories',
//       12 => 'tags',
//       13 => 'send-trackbacks',
//     ),
//     'active' => true,
//     'description' => 'Fältinställningar för att ange en ansvarig person för en eller flera sidor',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_5769414651213',
//     'title' => 'Berörda ämnesområden',
//     'fields' => array(
//       array(
//         'key' => 'field_5769423bc51cb',
//         'label' => 'Berörda ämnesområden',
//         'name' => 'berorda-amnesomraden_sv',
//         'type' => 'relationship',
//         'instructions' => 'Ange de ämnesområden som är har en koppling till detta ämnesområde.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'post_type' => array(
//           0 => 'amnesomraden',
//         ),
//         'taxonomy' => array(
//         ),
//         'filters' => array(
//           0 => 'search',
//         ),
//         'elements' => '',
//         'min' => '',
//         'max' => '',
//         'return_format' => 'object',
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'amnesomraden',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_575ff2de0300b',
//     'title' => 'Hämta alla inlägg i kategorin Produktcertifiering',
//     'fields' => array(
//       array(
//         'key' => 'field_575ff2ded45b0',
//         'label' => 'Hämta alla inlägg i kategorin	produktcertifiering',
//         'name' => 'produktcertifiering-section',
//         'type' => 'relationship',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'post_type' => array(
//           0 => 'amnesomraden',
//         ),
//         'taxonomy' => array(
//           0 => 'amnesomraden_categories:produktcertifiering',
//         ),
//         'filters' => '',
//         'elements' => '',
//         'min' => '',
//         'max' => '',
//         'return_format' => 'object',
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post',
//           'operator' => '==',
//           'value' => '969',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => false,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_575f9ff93f0b9',
//     'title' => 'Koppling till ämnesområde',
//     'fields' => array(
//       array(
//         'key' => 'field_575fa0f374a0f',
//         'label' => 'Koppling till ämnesområde',
//         'name' => 'koppling_till_amnesomrade',
//         'type' => 'relationship',
//         'instructions' => 'Ange vilka ämnesområden denna nyhet ska visas.
//   ',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'post_type' => array(
//           0 => 'amnesomraden',
//         ),
//         'taxonomy' => array(
//         ),
//         'filters' => array(
//           0 => 'search',
//           1 => 'taxonomy',
//         ),
//         'elements' => '',
//         'min' => '',
//         'max' => '',
//         'return_format' => 'object',
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'post',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_575920b4ae198',
//     'title' => 'Utvald nyhet',
//     'fields' => array(
//       array(
//         'key' => 'field_575921f4edcaa',
//         'label' => 'Ska denna nyhet visas som utvald?',
//         'name' => 'upphavd_nyhet_sv',
//         'type' => 'radio',
//         'instructions' => 'Visa denna nyhet större samt med bild på startsidan.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'choices' => array(
//           'false' => 'Nej',
//           'true' => 'Ja, visa denna nyhet med bild.',
//         ),
//         'allow_null' => 0,
//         'other_choice' => 0,
//         'default_value' => '',
//         'layout' => 'vertical',
//         'return_format' => 'value',
//         'save_other_choice' => 0,
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'post',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'side',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_5757b9659e595',
//     'title' => 'MID-certifikat',
//     'fields' => array(
//       array(
//         'key' => 'field_5757ba61f230a',
//         'label' => 'MID-certifikat',
//         'name' => 'mid-certifikat_rep',
//         'type' => 'repeater',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'row',
//         'button_label' => 'Lägg till rad',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_57624580d7f91',
//             'label' => 'Rubrik',
//             'name' => 'mid-certifikat_rep-title',
//             'type' => 'text',
//             'instructions' => 'Ange en rubrik.',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 20,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_579ee4d3105b2',
//             'label' => 'Rubrik (Eng)',
//             'name' => 'mid-certifikat_rep-title_eng',
//             'type' => 'text',
//             'instructions' => 'Ange en rubrik som visas på engelska',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 20,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_5766c6aef8831',
//             'label' => 'Kolumn 1',
//             'name' => 'kolumn_1',
//             'type' => 'text',
//             'instructions' => 'Ange vad som ska stå i kolumn nummer 1 <br/>
//   Certificate number, additions and amendments',
//             'required' => '',
//             'conditional_logic' => '',
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => 'Certificate number, additions and amendments',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_5766c6c4f8832',
//             'label' => 'Kolumn 2',
//             'name' => 'kolumn_2',
//             'type' => 'text',
//             'instructions' => 'Ange vad som ska stå i kolumn nummer 2 <br/>
//   Issue date',
//             'required' => '',
//             'conditional_logic' => '',
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => 'Issue date',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_5766c6c9f8833',
//             'label' => 'Kolumn 3',
//             'name' => 'kolumn_3',
//             'type' => 'text',
//             'instructions' => 'Ange vad som ska stå i kolumn nummer 3 <br/>
//   Expiry date',
//             'required' => '',
//             'conditional_logic' => '',
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => 'Expiry date',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_5766c6cdf8834',
//             'label' => 'Kolumn 4',
//             'name' => 'kolumn_4',
//             'type' => 'text',
//             'instructions' => 'Ange vad som ska stå i kolumn nummer 4 <br/>
//   Type of measuring instrument',
//             'required' => '',
//             'conditional_logic' => '',
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => 'Type of measuring instrument',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_5766c6d1f8835',
//             'label' => 'Kolumn 5',
//             'name' => 'kolumn_5',
//             'type' => 'text',
//             'instructions' => 'Ange vad som ska stå i kolumn nummer 5 <br/>
//   Issued to/manufacturer',
//             'required' => '',
//             'conditional_logic' => '',
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => 'Issued to/manufacturer',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_5766c6daf8836',
//             'label' => 'Kolumn 6',
//             'name' => 'kolumn_6',
//             'type' => 'text',
//             'instructions' => 'Ange vad som ska stå i kolumn nummer 6 <br/>
//   Issued by/notified body',
//             'required' => '',
//             'conditional_logic' => '',
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => 'Issued by/notified body',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_5766c6def8837',
//             'label' => 'Kolumn 7',
//             'name' => 'kolumn_7',
//             'type' => 'text',
//             'instructions' => 'Ange vad som ska stå i kolumn nummer 7 <br/>
//   Withdrawals',
//             'required' => '',
//             'conditional_logic' => '',
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => 'Withdrawals',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_5757da2983798',
//             'label' => 'Certifikat',
//             'name' => 'certifikat_rep',
//             'type' => 'repeater',
//             'instructions' => '',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 80,
//               'class' => '',
//               'id' => '',
//             ),
//             'collapsed' => '',
//             'min' => 0,
//             'max' => 0,
//             'layout' => 'block',
//             'button_label' => 'Lägg till certifikat',
//             'sub_fields' => array(
//               array(
//                 'key' => 'field_5757ba76f230b',
//                 'label' => 'Certificate number, additions and amendments',
//                 'name' => 'nestled_rep',
//                 'type' => 'repeater',
//                 'instructions' => 'Certificate number, additions and amendments',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => 100,
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'collapsed' => '',
//                 'min' => 0,
//                 'max' => 0,
//                 'layout' => 'block',
//                 'button_label' => 'Lägg till tidigare certifikat',
//                 'sub_fields' => array(
//                   array(
//                     'key' => 'field_5757bac1f230c',
//                     'label' => 'Länknamn',
//                     'name' => 'lanknamn',
//                     'type' => 'text',
//                     'instructions' => 'Ange ett länknamn',
//                     'required' => 1,
//                     'conditional_logic' => 0,
//                     'wrapper' => array(
//                       'width' => 50,
//                       'class' => '',
//                       'id' => '',
//                     ),
//                     'default_value' => '',
//                     'placeholder' => 'Namn',
//                     'prepend' => '',
//                     'append' => '',
//                     'maxlength' => '',
//                     'readonly' => 0,
//                     'disabled' => 0,
//                   ),
//                   array(
//                     'key' => 'field_5757bad5f230d',
//                     'label' => 'Fil',
//                     'name' => 'fil',
//                     'type' => 'file',
//                     'instructions' => 'Ange fil',
//                     'required' => 0,
//                     'conditional_logic' => 0,
//                     'wrapper' => array(
//                       'width' => 50,
//                       'class' => '',
//                       'id' => '',
//                     ),
//                     'return_format' => 'url',
//                     'library' => 'all',
//                     'min_size' => '',
//                     'max_size' => '',
//                     'mime_types' => '',
//                   ),
//                 ),
//               ),
//               array(
//                 'key' => 'field_5757bc79f230e',
//                 'label' => 'Issue date',
//                 'name' => 'issue_date',
//                 'type' => 'date_picker',
//                 'instructions' => 'Issue date',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => 50,
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'display_format' => 'd/m/Y',
//                 'return_format' => 'd/m/Y',
//                 'first_day' => 1,
//               ),
//               array(
//                 'key' => 'field_5757bca4f230f',
//                 'label' => 'Expiry date',
//                 'name' => 'expiry_date',
//                 'type' => 'date_picker',
//                 'instructions' => 'Expiry date',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => 50,
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'display_format' => 'd/m/Y',
//                 'return_format' => 'd/m/Y',
//                 'first_day' => 1,
//               ),
//               array(
//                 'key' => 'field_5757bcb4f2310',
//                 'label' => 'Type of measuring instrument',
//                 'name' => 'type_of_measuring_instrument',
//                 'type' => 'textarea',
//                 'instructions' => 'Type of measuring instrument',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => 35,
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'default_value' => '',
//                 'placeholder' => 'Type of measuring instrument',
//                 'maxlength' => '',
//                 'rows' => 4,
//                 'new_lines' => 'wpautop',
//                 'readonly' => 0,
//                 'disabled' => 0,
//               ),
//               array(
//                 'key' => 'field_5757bceaf2311',
//                 'label' => 'Issued to (manufacturer)',
//                 'name' => 'issued_to_manufacturer',
//                 'type' => 'textarea',
//                 'instructions' => 'Issued to (manufacturer)',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => 25,
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'default_value' => '',
//                 'placeholder' => 'Issued to (manufacturer)',
//                 'maxlength' => '',
//                 'rows' => 4,
//                 'new_lines' => 'wpautop',
//                 'readonly' => 0,
//                 'disabled' => 0,
//               ),
//               array(
//                 'key' => 'field_5757bd1af2312',
//                 'label' => 'Issued by (notified body)',
//                 'name' => 'issued_by_notified_body',
//                 'type' => 'textarea',
//                 'instructions' => 'Issued by (notified body)',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => 25,
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'default_value' => '',
//                 'placeholder' => 'Issued by (notified body)',
//                 'maxlength' => '',
//                 'rows' => 4,
//                 'new_lines' => 'wpautop',
//                 'readonly' => 0,
//                 'disabled' => 0,
//               ),
//               array(
//                 'key' => 'field_5757bd38f2313',
//                 'label' => 'Withdrawals',
//                 'name' => 'withdrawals',
//                 'type' => 'text',
//                 'instructions' => 'Withdrawals',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => 15,
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'default_value' => '',
//                 'placeholder' => 'Withdrawals',
//                 'prepend' => '',
//                 'append' => '',
//                 'maxlength' => '',
//                 'readonly' => 0,
//                 'disabled' => 0,
//               ),
//             ),
//           ),
//         ),
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'page',
//           'operator' => '==',
//           'value' => '35931',
//         ),
//       ),
//       array(
//         array(
//           'param' => 'page',
//           'operator' => '==',
//           'value' => '35936',
//         ),
//       ),
//       array(
//         array(
//           'param' => 'page',
//           'operator' => '==',
//           'value' => '35939',
//         ),
//       ),
//       array(
//         array(
//           'param' => 'page',
//           'operator' => '==',
//           'value' => '35943',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => array(
//       0 => 'permalink',
//       1 => 'the_content',
//       2 => 'excerpt',
//       3 => 'custom_fields',
//       4 => 'discussion',
//       5 => 'comments',
//       6 => 'revisions',
//       7 => 'slug',
//       8 => 'author',
//       9 => 'format',
//       10 => 'page_attributes',
//       11 => 'featured_image',
//       12 => 'categories',
//       13 => 'tags',
//       14 => 'send-trackbacks',
//     ),
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_573eba8097e47',
//     'title' => 'Föreskrifter & Dokument - Genvägar',
//     'fields' => array(
//       array(
//         'key' => 'field_573ebb83e5a00',
//         'label' => 'Genvägar',
//         'name' => 'foreskrifter_dokument_genvagar',
//         'type' => 'repeater',
//         'instructions' => 'Lägg till genvägar.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till genväg',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_573ebb93e5a01',
//             'label' => 'Länknamn',
//             'name' => 'lanknamn',
//             'type' => 'text',
//             'instructions' => 'Ange ett länknamn',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 30,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Länknamn',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_573ebba5e5a02',
//             'label' => 'Länkmål',
//             'name' => 'lankmal',
//             'type' => 'page_link',
//             'instructions' => 'Ange ett länkmål',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 70,
//               'class' => '',
//               'id' => '',
//             ),
//             'post_type' => array(
//               0 => 'page',
//               1 => 'amnesomraden',
//             ),
//             'taxonomy' => array(
//             ),
//             'allow_null' => 0,
//             'multiple' => 0,
//             'allow_archives' => 1,
//           ),
//         ),
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'page_template',
//           'operator' => '==',
//           'value' => 'page-template-8-foreskrifter-dokument.php',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_573d63583c07e',
//     'title' => 'Ledningen',
//     'fields' => array(
//       array(
//         'key' => 'field_573d643772185',
//         'label' => 'Avdelning',
//         'name' => 'ledning_avdelning',
//         'type' => 'repeater',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till ny avdelning',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_573d64dd72186',
//             'label' => 'Avdelningstitel',
//             'name' => 'avdelningstitel',
//             'type' => 'text',
//             'instructions' => 'Ange en titel för denna avdelning.',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 25,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Avdelningstitel',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_573d650072187',
//             'label' => 'Personer',
//             'name' => 'personer',
//             'type' => 'repeater',
//             'instructions' => 'Lägg till de personer som hör till denna avdelning.',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'collapsed' => '',
//             'min' => 0,
//             'max' => 0,
//             'layout' => 'table',
//             'button_label' => 'Lägg till person',
//             'sub_fields' => array(
//               array(
//                 'key' => 'field_573d652e72188',
//                 'label' => 'Bild',
//                 'name' => 'bild',
//                 'type' => 'image',
//                 'instructions' => 'Lägg till en profilbild.',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => 15,
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'return_format' => 'array',
//                 'preview_size' => 'thumbnail',
//                 'library' => 'all',
//                 'min_width' => '',
//                 'min_height' => '',
//                 'min_size' => '',
//                 'max_width' => '',
//                 'max_height' => '',
//                 'max_size' => '',
//                 'mime_types' => '',
//               ),
//               array(
//                 'key' => 'field_573d67780eff3',
//                 'label' => 'Roll',
//                 'name' => 'roll',
//                 'type' => 'text',
//                 'instructions' => 'Ange en roll. ',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => 25,
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'default_value' => '',
//                 'placeholder' => 'Roll',
//                 'prepend' => '',
//                 'append' => '',
//                 'maxlength' => '',
//                 'readonly' => 0,
//                 'disabled' => 0,
//               ),
//               array(
//                 'key' => 'field_573d654772189',
//                 'label' => 'Namn',
//                 'name' => 'namn',
//                 'type' => 'text',
//                 'instructions' => 'Ange ett namn.',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => 25,
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'default_value' => '',
//                 'placeholder' => 'Namn',
//                 'prepend' => '',
//                 'append' => '',
//                 'maxlength' => '',
//                 'readonly' => 0,
//                 'disabled' => 0,
//               ),
//               array(
//                 'key' => 'field_573d65537218a',
//                 'label' => 'Epost',
//                 'name' => 'epost',
//                 'type' => 'email',
//                 'instructions' => 'Ange en epost.',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => 35,
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'default_value' => '',
//                 'placeholder' => 'Epost @',
//                 'prepend' => '',
//                 'append' => '',
//               ),
//             ),
//           ),
//         ),
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'page',
//           'operator' => '==',
//           'value' => '52',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_573c56a049d4e',
//     'title' => 'Ankarlänkar',
//     'fields' => array(
//       array(
//         'key' => 'field_573c57a655bba',
//         'label' => 'Ankarlänkar',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'left',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_573c578155bb9',
//         'label' => 'Ankarlänkar',
//         'name' => 'page_ankarlankar',
//         'type' => 'repeater',
//         'instructions' => 'Möjligt att lägga till ankarlänkar. <br/>
//   (Se <b>"Hur gör man en ankarlänk?"</b> för hjälp)',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till ankarlänk',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_573c580355bbd',
//             'label' => 'Namn',
//             'name' => 'namn',
//             'type' => 'text',
//             'instructions' => 'Ange en länknamn för ankarlänken. <br/>
//   <b><em>Tips</em></b> - Använd samma namn på länken som rubriken som länken kommer leda till.',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 50,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Länknamn',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_573c584f55bbe',
//             'label' => 'Länk med ID',
//             'name' => 'lank',
//             'type' => 'text',
//             'instructions' => 'Ange rubrikens ID tillsammans med strängen "#anchor_" som länken ska leda till. <br>
//   (Se <b>"Hur gör man en ankarlänk?"</b> för hjälp)',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 50,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '#anchor_',
//             'placeholder' => 'ID #anchor_rubrik',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_573c57b255bbb',
//         'label' => 'Hur gör man en ankarlänk?',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_573c57c555bbc',
//         'label' => 'Såhär kopplar du en länk till en titel',
//         'name' => '',
//         'type' => 'message',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'message' => '<h4>Hur fungerar detta?</h4>
//   <p>För att skapa en ankarlänk så behövs ett ID att länka till.<br/>
//   Varje rubrik i rubriknivå 2 (Rubrik 2) får automatiskt ett ID på sig vid publicering.</p>
  
//   <b><u>T.ex</u></b> <br/>
//   <span style="font-size:18px;">Detta är en rubrik</span>
  
//   <p>Ovanstående rubrik får automatiskt ett ID på sig baserat på självaste rubriken.<br/>
//   ID: blir följande: "<b>anchor_detta-ar-en-rubrik</b>". Som du ser så försvinner alla tecken och mellanslagen blir bindestreck istället.</p>
  
//   <p>Så länken som ska placeras i fältet "<b>Länk med ID</b>" blir då följande: #anchor_detta-ar-en-rubrik. <br>
//   Anledningen till hashtagen (<b>#</b>) är för att tala om för länken att det är till ett specifikt ID fönstret ska scrolla till när man klickar på länken.</p>
  
//   <h4>Varför fungerar inte länken för mig?</h4>
//   <ul>
//     <li>Kontrollera att länken innehåller ett ID. (#anchor_rubrik)
//     <li>Kontrollera att länken innehåller en hashtag (#) frmaför <b>#</b>anchor_rubrik</li>
//     <li>Kontrollera att rubriken du försöker länka till är i rubriknivå 2 (Rubrik 2)</li>
//   </ul>',
//         'new_lines' => 'wpautop',
//         'esc_html' => 0,
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'page_template',
//           'operator' => '==',
//           'value' => 'page-template-with-sidebar.php',
//         ),
//       ),
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'amnesomraden',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'acf_after_title',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_573c341434c39',
//     'title' => 'Kontaktinformation',
//     'fields' => array(
//       array(
//         'key' => 'field_573c34f5f2f86',
//         'label' => 'Kontor',
//         'name' => 'kontaktuppgifter_kontor',
//         'type' => 'repeater',
//         'instructions' => 'Lägg till ett kontor med tillhörande kontaktuppgifter',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till kontor',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_573c3515f2f87',
//             'label' => 'Typ av kontor',
//             'name' => 'typ_av_kontor',
//             'type' => 'text',
//             'instructions' => 'Ange typ av kontor. <br/>
//   T.ex "Huvudförvaltning", "Stockholmskontor".',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 25,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Typ av kontor',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_573c3566f2f88',
//             'label' => 'Besöksadress',
//             'name' => 'besoksadress',
//             'type' => 'textarea',
//             'instructions' => 'Ange besöksadressen. ',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 20,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Besöksadress',
//             'maxlength' => '',
//             'rows' => 3,
//             'new_lines' => 'wpautop',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_573c3597f2f89',
//             'label' => 'Postadress',
//             'name' => 'postadress',
//             'type' => 'textarea',
//             'instructions' => 'Ange postadressen.',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 20,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Postadress',
//             'maxlength' => '',
//             'rows' => 3,
//             'new_lines' => 'wpautop',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_573c35b0f2f8a',
//             'label' => 'Karta',
//             'name' => 'karta',
//             'type' => 'google_map',
//             'instructions' => 'Bläddra in karta ',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 35,
//               'class' => '',
//               'id' => '',
//             ),
//             'center_lat' => '57.7241252',
//             'center_lng' => '12.9376506',
//             'zoom' => 16,
//             'height' => 250,
//           ),
//         ),
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'page',
//           'operator' => '==',
//           'value' => '46',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'acf_after_title',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_573c30ca4a70b',
//     'title' => 'Uppgifter till skribent på inlägg för Generaldirektörens blogg',
//     'fields' => array(
//       array(
//         'key' => 'field_573c31c08999f',
//         'label' => 'Namn',
//         'name' => 'gd_blogg_skribent_namn',
//         'type' => 'text',
//         'instructions' => 'Ange namn på skribenten för detta inlägg. <br/>
//   <small>Om inget namn anges skrivs inget ut.</small>',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 50,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Namn',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_573c3206899a0',
//         'label' => 'Epost',
//         'name' => 'gd_blogg_skribent_epost',
//         'type' => 'email',
//         'instructions' => 'Ange eposten till skribenten för detta inlägg. <br/>
//   <small>Om ingen epost anges skrivs inget ut.</small>',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 50,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Epost @',
//         'prepend' => '',
//         'append' => '',
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'bloggen',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'acf_after_title',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => array(
//       0 => 'excerpt',
//       1 => 'custom_fields',
//       2 => 'discussion',
//       3 => 'comments',
//       4 => 'slug',
//       5 => 'author',
//       6 => 'format',
//       7 => 'page_attributes',
//       8 => 'categories',
//       9 => 'tags',
//       10 => 'send-trackbacks',
//     ),
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_5729df4a1bced',
//     'title' => 'Dokument',
//     'fields' => array(
//       array(
//         'key' => 'field_5729e125ec293',
//         'label' => 'Dokument (URL',
//         'name' => 'crm_doc_doc',
//         'type' => 'url',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 100,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Url till dokumentet',
//       ),
//       array(
//         'key' => 'field_57643f0d3d530',
//         'label' => 'Heading',
//         'name' => 'crm_doc_heading',
//         'type' => 'text',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 25,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Heading',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_5729e046ec28f',
//         'label' => 'Rubrik',
//         'name' => 'crm_doc_title',
//         'type' => 'text',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 25,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Rubrik',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_5729e051ec290',
//         'label' => 'Beskrivning',
//         'name' => 'crm_doc_description',
//         'type' => 'textarea',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 50,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Beskrivning',
//         'maxlength' => '',
//         'rows' => 4,
//         'new_lines' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_5729df50ec28c',
//         'label' => 'Dokumentbeteckning',
//         'name' => 'crm_doc_dokumentbeteckning',
//         'type' => 'text',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 40,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'T.ex 2015:8',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_5729e0edec292',
//         'label' => 'Celexnummer',
//         'name' => 'crm_doc_celexnumber',
//         'type' => 'text',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 40,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'T.ex 32014L0031',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_5729e17046448',
//         'label' => 'Typ av dokument',
//         'name' => 'crm_doc_type',
//         'type' => 'text',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 20,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'T.ex STAFS, DOC,	REP, INFO',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_5729e023ec28e',
//         'label' => 'Område',
//         'name' => 'crm_doc_area',
//         'type' => 'repeater',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 75,
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till område',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_5768e63916ffa',
//             'label' => 'Heading',
//             'name' => 'heading',
//             'type' => 'text',
//             'instructions' => '',
//             'required' => '',
//             'conditional_logic' => '',
//             'wrapper' => array(
//               'width' => 60,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Heading',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_576259be95194',
//             'label' => 'Områdesnamn',
//             'name' => 'omradesnamn',
//             'type' => 'text',
//             'instructions' => 'Ange ett områdesnamn',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 40,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Områdesnamn',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_5729e1b046449',
//         'label' => 'Upphävd',
//         'name' => 'crm_doc_repealed',
//         'type' => 'true_false',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 25,
//           'class' => '',
//           'id' => '',
//         ),
//         'message' => '',
//         'default_value' => 0,
//         'ui' => 0,
//         'ui_on_text' => '',
//         'ui_off_text' => '',
//       ),
//       array(
//         'key' => 'field_5729e1e84644a',
//         'label' => 'Grundförfattning',
//         'name' => 'crm_doc_originalver',
//         'type' => 'text',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 25,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => '',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_5729e2a54644c',
//         'label' => 'Ändringsförfattning',
//         'name' => 'crm_doc_amendmentver',
//         'type' => 'repeater',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 25,
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till rad',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_576259f295195',
//             'label' => 'Författning',
//             'name' => 'forfattning',
//             'type' => 'text',
//             'instructions' => '',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Författning',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_5729e063ec291',
//         'label' => 'Bemyndigande',
//         'name' => 'crm_doc_authorization',
//         'type' => 'textarea',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 25,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => '',
//         'maxlength' => '',
//         'rows' => '',
//         'new_lines' => 'wpautop',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_5729e22e4644b',
//         'label' => 'Konsoliderad',
//         'name' => 'crm_doc_consolidatedver',
//         'type' => 'text',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 25,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => '',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'dokument',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_572b2fc987aa6',
//     'title' => 'Kurser',
//     'fields' => array(
//       array(
//         'key' => 'field_572b30a7acf0c',
//         'label' => 'Kursinformation',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_572b30b3acf0d',
//         'label' => 'Kurstillfälle',
//         'name' => 'swedac_academy_kurstillfallen',
//         'type' => 'repeater',
//         'instructions' => 'Lägg till kurstillfällen för denna kurs.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 100,
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till kurstillfälle',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_572b30c8acf0e',
//             'label' => 'Datum',
//             'name' => 'datum',
//             'type' => 'date_picker',
//             'instructions' => 'Ange ett datum',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 50,
//               'class' => '',
//               'id' => '',
//             ),
//             'display_format' => 'j F Y',
//             'return_format' => 'j F Y',
//             'first_day' => 1,
//           ),
//           array(
//             'key' => 'field_572b30e1acf0f',
//             'label' => 'Tid',
//             'name' => 'tid',
//             'type' => 'text',
//             'instructions' => 'Ange ett klockslag.',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 50,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Tid',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//         ),
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'kurser',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'acf_after_title',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => array(
//       0 => 'permalink',
//       1 => 'excerpt',
//       2 => 'custom_fields',
//       3 => 'discussion',
//       4 => 'comments',
//       5 => 'revisions',
//       6 => 'slug',
//       7 => 'author',
//       8 => 'format',
//       9 => 'page_attributes',
//       10 => 'featured_image',
//       11 => 'categories',
//       12 => 'tags',
//       13 => 'send-trackbacks',
//     ),
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_572889d1191bf',
//     'title' => 'Hämta alla inlägg i kategorin ledningssystem',
//     'fields' => array(
//       array(
//         'key' => 'field_57288aa566006',
//         'label' => 'Hämta alla inlägg i kategorin ledningssystem & produktcertifiering',
//         'name' => 'certifiering-section',
//         'type' => 'relationship',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'post_type' => array(
//           0 => 'amnesomraden',
//         ),
//         'taxonomy' => array(
//           0 => 'amnesomraden_categories:ledningssystem',
//           1 => 'amnesomraden_categories:produktcertifiering',
//         ),
//         'filters' => '',
//         'elements' => '',
//         'min' => '',
//         'max' => '',
//         'return_format' => 'object',
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post',
//           'operator' => '==',
//           'value' => '971',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => false,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_571dad25cd1d4',
//     'title' => 'Kontaktperson',
//     'fields' => array(
//       array(
//         'key' => 'field_571dad2cbd20e',
//         'label' => 'Profilbild',
//         'name' => 'kontaktperson_profilbild',
//         'type' => 'image',
//         'instructions' => 'Ange en profilbild.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 10,
//           'class' => '',
//           'id' => '',
//         ),
//         'return_format' => 'array',
//         'preview_size' => 'thumbnail',
//         'library' => 'all',
//         'min_width' => '',
//         'min_height' => '',
//         'min_size' => '',
//         'max_width' => '',
//         'max_height' => '',
//         'max_size' => '',
//         'mime_types' => '',
//       ),
//       array(
//         'key' => 'field_571dad50bd20f',
//         'label' => 'Namn',
//         'name' => 'kontaktperson_namn',
//         'type' => 'text',
//         'instructions' => 'Ange ett namn.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 20,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Namn',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_571dad62bd210',
//         'label' => 'Roll',
//         'name' => 'kontaktperson_roll',
//         'type' => 'text',
//         'instructions' => 'Ange en roll. T.ex Kommunikationschef',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 20,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Roll',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_571dad9dbd211',
//         'label' => 'Telefonnummer',
//         'name' => 'kontaktperson_telefonnummer',
//         'type' => 'text',
//         'instructions' => 'Ange ett telefonnummer.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 25,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Telefonnummer',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_571dadb0bd212',
//         'label' => 'Epost',
//         'name' => 'kontaktperson_epost',
//         'type' => 'email',
//         'instructions' => 'Ange en epost.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 25,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Epost',
//         'prepend' => '',
//         'append' => '',
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'kontaktperson',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'acf_after_title',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => array(
//       0 => 'permalink',
//       1 => 'the_content',
//       2 => 'excerpt',
//       3 => 'custom_fields',
//       4 => 'discussion',
//       5 => 'comments',
//       6 => 'revisions',
//       7 => 'slug',
//       8 => 'author',
//       9 => 'format',
//       10 => 'page_attributes',
//       11 => 'featured_image',
//       12 => 'categories',
//       13 => 'tags',
//       14 => 'send-trackbacks',
//     ),
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_5715f3e47e602',
//     'title' => 'Sidopanel',
//     'fields' => array(
//       array(
//         'key' => 'field_5715f3f680dbd',
//         'label' => 'Om bloggen',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_5715f3fd80dbe',
//         'label' => 'Profilbild',
//         'name' => 'gd_bloggen_profilbild',
//         'type' => 'image',
//         'instructions' => 'Ange en profilbild.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 10,
//           'class' => '',
//           'id' => '',
//         ),
//         'return_format' => 'array',
//         'preview_size' => 'thumbnail',
//         'library' => 'all',
//         'min_width' => '',
//         'min_height' => '',
//         'min_size' => '',
//         'max_width' => '',
//         'max_height' => '',
//         'max_size' => '',
//         'mime_types' => '',
//       ),
//       array(
//         'key' => 'field_5715f41f80dbf',
//         'label' => 'Rubrik',
//         'name' => 'gd_bloggen_rubrik',
//         'type' => 'text',
//         'instructions' => 'Ange en passande rubrik. <br>
//   T.ex: "Om GD-Bloggen".',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 15,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Rubrik',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_5715f44880dc0',
//         'label' => 'Beskrivning',
//         'name' => 'gd_bloggen_beskrivning',
//         'type' => 'textarea',
//         'instructions' => 'Ange en beskrivning om bloggen',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 25,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Beskrivning',
//         'maxlength' => '',
//         'rows' => 4,
//         'new_lines' => 'wpautop',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_5715f47980dc1',
//         'label' => 'Epost',
//         'name' => 'gd_bloggen_epost',
//         'type' => 'email',
//         'instructions' => 'Ange en epost ifall besökarna ska kunna kontakta den ansvariga.
//   Länknamnet kommer då bli "Maila mig".',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 25,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Epost @',
//         'prepend' => '',
//         'append' => '',
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'page_template',
//           'operator' => '==',
//           'value' => 'page-template-7-bloggen.php',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'acf_after_title',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => array(
//       0 => 'the_content',
//       1 => 'excerpt',
//       2 => 'custom_fields',
//       3 => 'discussion',
//       4 => 'comments',
//       5 => 'revisions',
//       6 => 'slug',
//       7 => 'author',
//       8 => 'format',
//       9 => 'featured_image',
//       10 => 'categories',
//       11 => 'tags',
//       12 => 'send-trackbacks',
//     ),
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_57147d92d4d33',
//     'title' => 'Swedac Magasin',
//     'fields' => array(
//       array(
//         'key' => 'field_57147da0c4531',
//         'label' => 'Senaste numret',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_57147db1c4532',
//         'label' => 'Omslag',
//         'name' => 'swedac_magasin_omslag',
//         'type' => 'image',
//         'instructions' => 'Ange en bild på omslaget för det senaste numret av Swedac Magasin',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 25,
//           'class' => '',
//           'id' => '',
//         ),
//         'return_format' => 'array',
//         'preview_size' => 'thumbnail',
//         'library' => 'all',
//         'min_width' => '',
//         'min_height' => '',
//         'min_size' => '',
//         'max_width' => '',
//         'max_height' => '',
//         'max_size' => '',
//         'mime_types' => '',
//       ),
//       array(
//         'key' => 'field_57147e12c4533',
//         'label' => 'Rubrik',
//         'name' => 'swedac_magasin_rurbik',
//         'type' => 'text',
//         'instructions' => 'Ange en rubrik. <br>
//   T.ex: Läs senaste numret',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 30,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Rubrik',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_57147e40c4534',
//         'label' => 'Beskrivning',
//         'name' => 'swedac_magasin_beskrivning',
//         'type' => 'textarea',
//         'instructions' => 'Ange en kort beskrivning. <br>
//   <em>Visas under titeln.</em>',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 45,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Beskrivning',
//         'maxlength' => '',
//         'rows' => 3,
//         'new_lines' => 'wpautop',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_57147e7bc4535',
//         'label' => 'Öppna e-tidning',
//         'name' => 'swedac_magasin_oppna_e-tidning_rubrik',
//         'type' => 'text',
//         'instructions' => 'Ange en rubrik för "Öppna e-tidning". ',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 50,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Öppna e-tidning',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_57147ec0c4536',
//         'label' => 'Tidningsarkiv',
//         'name' => 'swedac_magasin_tidningsarkiv_rubrik',
//         'type' => 'text',
//         'instructions' => 'Ange en rubrik för "Tidningsarkiv".',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 50,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Tidningsarkiv',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_57147edfc4537',
//         'label' => 'Öppna e-tidning (URL)',
//         'name' => 'swedac_magasin_oppna_e-tidning_url',
//         'type' => 'url',
//         'instructions' => 'Ange en länk till e-tidningen.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 50,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'url',
//       ),
//       array(
//         'key' => 'field_57147f13c4538',
//         'label' => 'Tidningsarkiv (URL)',
//         'name' => 'swedac_magasin_tidningsarkiv_url',
//         'type' => 'url',
//         'instructions' => 'Ange en länk till arkivet.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 50,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'url',
//       ),
//       array(
//         'key' => 'field_57568aac14adb',
//         'label' => 'Rubrik (Prenumerera)',
//         'name' => 'swedac_magasin_rubrik_prenumerera',
//         'type' => 'text',
//         'instructions' => 'Ange en rubrik för prenumerera sektionen.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 25,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Rubrik',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_57568ae614adc',
//         'label' => 'Beskrivning (Prenumerera)',
//         'name' => 'swedac_magasin_beskrivning_prenumerera',
//         'type' => 'textarea',
//         'instructions' => 'Ange en kort beskrivning. <br>
//   <em>Visas under titeln.</em>',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 45,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Beskrivning',
//         'maxlength' => '',
//         'rows' => 3,
//         'new_lines' => 'wpautop',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_57568b1914add',
//         'label' => 'Länkmål för att starta prenumeration',
//         'name' => 'swedac_magasin_starta_prenumeration',
//         'type' => 'url',
//         'instructions' => 'Ange ett länkmål.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 30,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Url',
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'page_template',
//           'operator' => '==',
//           'value' => 'page-template-6-swedac-magasin.php',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'acf_after_title',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_570e0b2a980a3',
//     'title' => 'Kalenderhändelse',
//     'fields' => array(
//       array(
//         'key' => 'field_570e0b3711632',
//         'label' => 'Startdatum',
//         'name' => 'kal_startdatum_sv',
//         'type' => 'date_picker',
//         'instructions' => 'Ange startdatum.<br/><br/><br/>',
//         'required' => 1,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 15,
//           'class' => '',
//           'id' => '',
//         ),
//         'display_format' => 'j F',
//         'return_format' => 'j F',
//         'first_day' => 1,
//       ),
//       array(
//         'key' => 'field_570e2981a0aa8',
//         'label' => 'Slutdatum',
//         'name' => 'kal_slutdatum_sv',
//         'type' => 'date_picker',
//         'instructions' => 'Ange slutdatum.<br/><br/><br/>',
//         'required' => 1,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 15,
//           'class' => '',
//           'id' => '',
//         ),
//         'display_format' => 'j F',
//         'return_format' => 'j F',
//         'first_day' => 1,
//       ),
//       array(
//         'key' => 'field_570e0edb5ddab',
//         'label' => 'År',
//         'name' => 'kal_manad_ar_sv',
//         'type' => 'text',
//         'instructions' => 'Ange år. T.ex 2017<br/><br/><br/>',
//         'required' => 1,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '20',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'År',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//       ),
//       array(
//         'key' => 'field_570e0baf5dda8',
//         'label' => 'Starttid',
//         'name' => 'kal_tid_sv',
//         'type' => 'text',
//         'instructions' => 'Ange ett klockslag för start.<br/>
//   <small>Tidformat - 12:00</small><br/><br/>',
//         'required' => 1,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 15,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => '12:00',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_576a7f6337ede',
//         'label' => 'Sluttid',
//         'name' => 'kal_tid_slut_sv',
//         'type' => 'text',
//         'instructions' => 'Ange ett klockslag för slut.<br/>
//   <small>Tidformat - 12:00</small><br/><br/>',
//         'required' => 1,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 15,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => '12:00',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_570e0f925ddac',
//         'label' => 'Plats',
//         'name' => 'kal_plats_sv',
//         'type' => 'text',
//         'instructions' => 'Ange en plats där händelsen äger rum.<br/><br/><br/>',
//         'required' => 1,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 20,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Plats',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'kalender',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'acf_after_title',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => array(
//       0 => 'permalink',
//       1 => 'excerpt',
//       2 => 'custom_fields',
//       3 => 'discussion',
//       4 => 'comments',
//       5 => 'revisions',
//       6 => 'slug',
//       7 => 'author',
//       8 => 'format',
//       9 => 'page_attributes',
//       10 => 'featured_image',
//       11 => 'categories',
//       12 => 'tags',
//       13 => 'send-trackbacks',
//     ),
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_570ca62d72a0a',
//     'title' => 'Visa länkar på detta ämnesområde med tillhörande information',
//     'fields' => array(
//       array(
//         'key' => 'field_5d6fa6e390912',
//         'label' => 'Certifieringsorgan',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_5d6fa6c890911',
//         'label' => 'Certifieringsorgan',
//         'name' => 'certifieringsorgan_lankar',
//         'type' => 'repeater',
//         'instructions' => 'Lägg till länkar som tar besökaren till listan med certifieringsorgan.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till rad',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_5d6fa71490913',
//             'label' => 'Namn',
//             'name' => 'namn',
//             'type' => 'text',
//             'instructions' => 'Ange namn på länken.',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//           ),
//           array(
//             'key' => 'field_5d6fa71a90914',
//             'label' => 'Sökurl',
//             'name' => 'sokurl',
//             'type' => 'text',
//             'instructions' => 'Ange urlen för söksträngen.',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_5886fb8483535',
//         'label' => 'Ackrediterade företag',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_5886f8198e412',
//         'label' => 'Ackrediterade företag',
//         'name' => 'ackrediterade_foretag',
//         'type' => 'repeater',
//         'instructions' => 'Lägg till länkar som tar besökaren till en sökning för ackrediterade företag.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till rad',
//         'collapsed' => '',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_5886fb5a8e414',
//             'label' => 'Sökurl',
//             'name' => 'sokurl',
//             'type' => 'url',
//             'instructions' => 'Ange urlen för söksträngen. <br/>
//   <small>T.ex <b>http://search.swedac.se/sv/ackrediteringar?s=bild-+och+funktionsmedicin</b></small>',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '50',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Sökurl',
//           ),
//           array(
//             'key' => 'field_5886f8458e413',
//             'label' => 'Namn',
//             'name' => 'namn',
//             'type' => 'text',
//             'instructions' => 'Ange namn på länken.',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '50',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'maxlength' => '',
//             'placeholder' => 'Namn',
//             'prepend' => '',
//             'append' => '',
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_57750146eaf09',
//         'label' => 'Tabell med 6 kolumner',
//         'name' => 'working_area_tabel',
//         'type' => 'repeater',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'row',
//         'button_label' => 'Lägg till tabell',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_57750313eaf11',
//             'label' => 'Tabell rader',
//             'name' => 'tabell_rader',
//             'type' => 'repeater',
//             'instructions' => '',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'collapsed' => '',
//             'min' => 0,
//             'max' => 0,
//             'layout' => 'table',
//             'button_label' => 'Lägg till rad',
//             'sub_fields' => array(
//               array(
//                 'key' => 'field_57750407eaf17',
//                 'label' => 'Anm.',
//                 'name' => 'anm',
//                 'type' => 'text',
//                 'instructions' => '',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => '15',
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'default_value' => '',
//                 'placeholder' => 'Anm.',
//                 'prepend' => '',
//                 'append' => '',
//                 'maxlength' => '',
//               ),
//               array(
//                 'key' => 'field_577503d9eaf16',
//                 'label' => 'Giltig',
//                 'name' => 'giltig',
//                 'type' => 'text',
//                 'instructions' => '',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => '15',
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'default_value' => '',
//                 'placeholder' => 'Giltig',
//                 'prepend' => '',
//                 'append' => '',
//                 'maxlength' => '',
//               ),
//               array(
//                 'key' => 'field_5775039ceaf14',
//                 'label' => 'Lägg till omfattningsbilaga',
//                 'name' => 'lagg_till_omfattningsbilaga',
//                 'type' => 'file',
//                 'instructions' => '',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => '25',
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'return_format' => 'url',
//                 'library' => 'all',
//                 'min_size' => '',
//                 'max_size' => '',
//                 'mime_types' => '',
//               ),
//               array(
//                 'key' => 'field_577503c9eaf15',
//                 'label' => 'Verksamt i',
//                 'name' => 'verksamt_i',
//                 'type' => 'text',
//                 'instructions' => '',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => '15',
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'default_value' => '',
//                 'placeholder' => 'Verksamt i',
//                 'prepend' => '',
//                 'append' => '',
//                 'maxlength' => '',
//               ),
//               array(
//                 'key' => 'field_57750320eaf12',
//                 'label' => 'Företag',
//                 'name' => 'foretag',
//                 'type' => 'textarea',
//                 'instructions' => '',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => '15',
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'default_value' => '',
//                 'placeholder' => 'Företag',
//                 'maxlength' => '',
//                 'rows' => 3,
//                 'new_lines' => 'wpautop',
//               ),
//               array(
//                 'key' => 'field_57750364eaf13',
//                 'label' => 'Adress',
//                 'name' => 'adress',
//                 'type' => 'textarea',
//                 'instructions' => '',
//                 'required' => 0,
//                 'conditional_logic' => 0,
//                 'wrapper' => array(
//                   'width' => '15',
//                   'class' => '',
//                   'id' => '',
//                 ),
//                 'default_value' => '',
//                 'placeholder' => 'Adress',
//                 'maxlength' => '',
//                 'rows' => 3,
//                 'new_lines' => 'wpautop',
//               ),
//             ),
//           ),
//           array(
//             'key' => 'field_577502a0eaf0f',
//             'label' => 'Giltigt',
//             'name' => 'giltigt',
//             'type' => 'text',
//             'instructions' => '',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Giltigt',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_577502f7eaf10',
//             'label' => 'Anm.',
//             'name' => 'anm',
//             'type' => 'text',
//             'instructions' => 'Anm.',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Anm.',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_57750289eaf0e',
//             'label' => 'Verksamt i',
//             'name' => 'verksamt',
//             'type' => 'text',
//             'instructions' => 'Verksamt i',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Verksamt i',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_57750272eaf0d',
//             'label' => 'Områden',
//             'name' => 'omraden',
//             'type' => 'text',
//             'instructions' => 'Områden',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Områden',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_57750228eaf0b',
//             'label' => 'Företag',
//             'name' => 'foretag',
//             'type' => 'text',
//             'instructions' => 'Företag',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Företag',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_5775025feaf0c',
//             'label' => 'Adress',
//             'name' => 'adress',
//             'type' => 'text',
//             'instructions' => 'Adress',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Adress',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_577501eaeaf0a',
//             'label' => 'Beskrivning',
//             'name' => 'beskrivning',
//             'type' => 'wysiwyg',
//             'instructions' => 'Ange en beskrivning om vad det är för information som ska visas i nedanstående tabell. <br/>
//   <small>Visas ovan tabellen</small>',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'tabs' => 'visual',
//             'toolbar' => 'full',
//             'media_upload' => 0,
//             'delay' => 0,
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_5775042eeaf18',
//         'label' => 'Tabell med 6 kolumner',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => '',
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_570cb6bed1773',
//         'label' => 'Externa länkar',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_570cb442d1770',
//         'label' => 'Externa länkar',
//         'name' => 'externa_lankar',
//         'type' => 'repeater',
//         'instructions' => 'Dessa länkar kommer öppnas i ny flik.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till externlänk',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_570cb66cd1771',
//             'label' => 'Länknamn',
//             'name' => 'lanknamn',
//             'type' => 'text',
//             'instructions' => 'Ange ett länknamn',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 33,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Länknamn',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_570cb69dd1772',
//             'label' => 'Länkmål',
//             'name' => 'lankmal',
//             'type' => 'url',
//             'instructions' => 'Klistra in länkens url',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 66,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Url',
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_570ca64715ce8',
//         'label' => 'Markera de länkar som ska visas på detta ämnesområde',
//         'name' => 'show_links_on_amnesomrade',
//         'type' => 'relationship',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'post_type' => array(
//           0 => 'intern_lankningar',
//         ),
//         'taxonomy' => array(
//         ),
//         'filters' => '',
//         'elements' => '',
//         'min' => '',
//         'max' => '',
//         'return_format' => 'id',
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'amnesomraden',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//     'modified' => 1567686112,
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_5707726052a90',
//     'title' => 'Ämnesområden',
//     'fields' => array(
//       array(
//         'key' => 'field_57077267ca021',
//         'label' => 'Gemensamma termer',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_5707727aca022',
//         'label' => 'Rubrik',
//         'name' => 'amnesomrade_gemensamma_termer_rubrik',
//         'type' => 'text',
//         'instructions' => 'Ange en rubrik för "Gemensamma termer". <br>
//   ',
//         'required' => 1,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Gemensamma termer',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_5707729bca023',
//         'label' => 'Länkar',
//         'name' => 'amnesomrade_gemensamma_termer_rep',
//         'type' => 'repeater',
//         'instructions' => 'Lägg till respektive länk med tillhörande länknamn som visas under sektionen för "Gemensamma termer".',
//         'required' => 1,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till länk',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_570772ed3bb17',
//             'label' => 'Länknamn',
//             'name' => 'lanknamn',
//             'type' => 'text',
//             'instructions' => 'Ange ett länknamn.',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 33,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Namn',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_570772fd3bb18',
//             'label' => 'Länkmål',
//             'name' => 'lankmal',
//             'type' => 'page_link',
//             'instructions' => 'Ange ett länkmål.',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 66,
//               'class' => '',
//               'id' => '',
//             ),
//             'post_type' => array(
//               0 => 'page',
//               1 => 'amnesomraden',
//             ),
//             'taxonomy' => array(
//             ),
//             'allow_null' => 0,
//             'multiple' => 0,
//             'allow_archives' => 1,
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_570775ceda1b8',
//         'label' => 'Puffar',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_5707757e078c2',
//         'label' => 'Puffar',
//         'name' => 'amnesomraden_puff_rep',
//         'type' => 'repeater',
//         'instructions' => 'Lägg till puffar som visas längst ner på sidan "Ämnesområde".',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till rad',
//         'collapsed' => '',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_5707764bda1b9',
//             'label' => 'Länknamn',
//             'name' => 'lanknamn',
//             'type' => 'text',
//             'instructions' => 'Ange ett länknamn.',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 33,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Namn',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_57077660da1ba',
//             'label' => 'Länkmål',
//             'name' => 'lankmal',
//             'type' => 'page_link',
//             'instructions' => 'Ange ett länkmål',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 66,
//               'class' => '',
//               'id' => '',
//             ),
//             'post_type' => array(
//               0 => 'page',
//             ),
//             'taxonomy' => array(
//             ),
//             'allow_null' => 0,
//             'multiple' => 0,
//             'allow_archives' => 1,
//           ),
//         ),
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'page_template',
//           'operator' => '==',
//           'value' => 'page-template_3.php',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => array(
//       0 => 'the_content',
//       1 => 'excerpt',
//       2 => 'custom_fields',
//       3 => 'discussion',
//       4 => 'comments',
//       5 => 'revisions',
//       6 => 'slug',
//       7 => 'author',
//       8 => 'format',
//       9 => 'featured_image',
//       10 => 'categories',
//       11 => 'tags',
//       12 => 'send-trackbacks',
//     ),
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_56e687661f235',
//     'title' => 'Extra sidattributer',
//     'fields' => array(
//       array(
//         'key' => 'field_56e68d2c334db',
//         'label' => 'Snabbval',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_56e6883406646',
//         'label' => 'Snabbval',
//         'name' => 'snabbval_subpage_sv',
//         'type' => 'repeater',
//         'instructions' => 'Här kan ni lägga till snabbval som visas under självaste huvudtexten.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till snabbval',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_56e688a906648',
//             'label' => 'Rubrik',
//             'name' => 'rubrik',
//             'type' => 'text',
//             'instructions' => 'Ange en passande rubrik.',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 25,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Rubrik',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_56e688c606649',
//             'label' => 'Länkmål',
//             'name' => 'lankmal',
//             'type' => 'page_link',
//             'instructions' => 'Ange ett länkmål.',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 75,
//               'class' => '',
//               'id' => '',
//             ),
//             'post_type' => array(
//               0 => 'page',
//               1 => 'amnesomraden',
//             ),
//             'taxonomy' => array(
//             ),
//             'allow_null' => 0,
//             'multiple' => 0,
//             'allow_archives' => 1,
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_571db7b87799e',
//         'label' => 'Kontaktperson',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_571db7c77799f',
//         'label' => 'Visa en kontaktperson',
//         'name' => 'kontaktperson_relation',
//         'type' => 'relationship',
//         'instructions' => 'Ange <b>En</b> kontaktperson som ska visas på denna sida.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'post_type' => array(
//           0 => 'kontaktperson',
//         ),
//         'taxonomy' => array(
//         ),
//         'filters' => array(
//           0 => 'search',
//         ),
//         'elements' => '',
//         'min' => '',
//         'max' => 1,
//         'return_format' => 'object',
//       ),
//       array(
//         'key' => 'field_571db7ef779a0',
//         'label' => 'Dokument',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_571dbc2abf591',
//         'label' => 'Lägg till ett dokument',
//         'name' => 'enskilda_dokument',
//         'type' => 'repeater',
//         'instructions' => 'Lägg till ett eller fler dokument för nedladdning. ',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till dokument',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_571dc47abf593',
//             'label' => 'Dokument',
//             'name' => 'dokument',
//             'type' => 'file',
//             'instructions' => 'Ange ett dokument.',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 25,
//               'class' => '',
//               'id' => '',
//             ),
//             'return_format' => 'id',
//             'library' => 'all',
//             'min_size' => '',
//             'max_size' => '',
//             'mime_types' => '',
//           ),
//           array(
//             'key' => 'field_571dc458bf592',
//             'label' => 'Namn',
//             'name' => 'namn',
//             'type' => 'text',
//             'instructions' => 'Ange ett namn. ',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 75,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Namn',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//         ),
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'page_template',
//           'operator' => '==',
//           'value' => 'page-template-with-sidebar.php',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_56e2bf477a9e5',
//     'title' => 'Ingress för undersidor',
//     'fields' => array(
//       array(
//         'key' => 'field_5bdaeed148f8f',
//         'label' => 'Fetstil',
//         'name' => 'page_ingress_subpage_bold_sv',
//         'type' => 'true_false',
//         'instructions' => 'Kontrollerar ifall ingressen ska vara fetstil eller inte.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'message' => 'Fetstil',
//         'default_value' => 1,
//         'ui' => 0,
//         'ui_on_text' => '',
//         'ui_off_text' => '',
//       ),
//       array(
//         'key' => 'field_56e2c02867069',
//         'label' => 'Ingress',
//         'name' => 'page_ingress_subpage_sv',
//         'type' => 'textarea',
//         'instructions' => 'Ange en ingresstext.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Ingress',
//         'maxlength' => '',
//         'rows' => 4,
//         'new_lines' => 'wpautop',
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'page_template',
//           'operator' => '==',
//           'value' => 'page-template-with-sidebar.php',
//         ),
//       ),
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'post',
//         ),
//       ),
//       array(
//         array(
//           'param' => 'post_type',
//           'operator' => '==',
//           'value' => 'amnesomraden',
//         ),
//       ),
//       array(
//         array(
//           'param' => 'page_template',
//           'operator' => '==',
//           'value' => 'page-template-certifieringsorgan.php',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'acf_after_title',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => array(
//       0 => 'excerpt',
//       1 => 'discussion',
//       2 => 'comments',
//       3 => 'slug',
//       4 => 'author',
//       5 => 'format',
//       6 => 'tags',
//       7 => 'send-trackbacks',
//     ),
//     'active' => true,
//     'description' => '',
//     'modified' => 1567597436,
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_56d84c40aa7fb',
//     'title' => 'Ingress för huvudsidor',
//     'fields' => array(
//       array(
//         'key' => 'field_56d84cf86cbe6',
//         'label' => 'Ingress',
//         'name' => 'page_ingress_sv',
//         'type' => 'textarea',
//         'instructions' => 'Ange en ingresstext. Texten automatiskt bli fetstilt. ',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Ingress',
//         'maxlength' => '',
//         'rows' => 4,
//         'new_lines' => 'wpautop',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'page_template',
//           'operator' => '==',
//           'value' => 'page-template_2.php',
//         ),
//       ),
//       array(
//         array(
//           'param' => 'page_template',
//           'operator' => '==',
//           'value' => 'page-template_3.php',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'acf_after_title',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => array(
//       0 => 'permalink',
//       1 => 'the_content',
//       2 => 'excerpt',
//       3 => 'custom_fields',
//       4 => 'discussion',
//       5 => 'comments',
//       6 => 'revisions',
//       7 => 'slug',
//       8 => 'author',
//       9 => 'format',
//       10 => 'featured_image',
//       11 => 'categories',
//       12 => 'tags',
//       13 => 'send-trackbacks',
//     ),
//     'active' => false,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_56d84a76ce793',
//     'title' => 'Snabbval - Template 2',
//     'fields' => array(
//       array(
//         'key' => 'field_56d84b418543e',
//         'label' => 'Snabbval',
//         'name' => 'snabbval_template_2_sv',
//         'type' => 'repeater',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till ett snabbval',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_56d84bae85440',
//             'label' => 'Rubrik',
//             'name' => 'rubrik',
//             'type' => 'text',
//             'instructions' => 'Ange en rubrik.',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 25,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Rubrik',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_56d84bd385441',
//             'label' => 'Länkmål',
//             'name' => 'lankmal',
//             'type' => 'page_link',
//             'instructions' => 'Ange ett länkmål.',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 75,
//               'class' => '',
//               'id' => '',
//             ),
//             'post_type' => array(
//               0 => 'page',
//             ),
//             'taxonomy' => array(
//             ),
//             'allow_null' => 0,
//             'multiple' => 0,
//             'allow_archives' => 1,
//           ),
//         ),
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'page_template',
//           'operator' => '==',
//           'value' => 'page-template_2.php',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => array(
//       0 => 'permalink',
//       1 => 'the_content',
//       2 => 'excerpt',
//       3 => 'custom_fields',
//       4 => 'discussion',
//       5 => 'comments',
//       6 => 'revisions',
//       7 => 'slug',
//       8 => 'author',
//       9 => 'format',
//       10 => 'featured_image',
//       11 => 'categories',
//       12 => 'tags',
//       13 => 'send-trackbacks',
//     ),
//     'active' => true,
//     'description' => '',
//   ));
  
//   acf_add_local_field_group(array(
//     'key' => 'group_56d5a6070d98b',
//     'title' => 'Allmäninformation',
//     'fields' => array(
//       array(
//         'key' => 'field_56d5a61b5d018',
//         'label' => 'Bildspel / Slider',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_56d5a6235d019',
//         'label' => 'Bildspel',
//         'name' => 'frontpage_slider_sv',
//         'type' => 'repeater',
//         'instructions' => 'Bildspel på startsidan<br>
//   <b>Anges inte en bild så blir det en bakgrundsfärg som är mörkblå.</b>',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till bild',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_56d5a645bd203',
//             'label' => 'Bild',
//             'name' => 'bild',
//             'type' => 'image',
//             'instructions' => 'Ange en bild',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 20,
//               'class' => '',
//               'id' => '',
//             ),
//             'return_format' => 'array',
//             'preview_size' => 'thumbnail',
//             'library' => 'all',
//             'min_width' => 0,
//             'min_height' => 0,
//             'min_size' => 0,
//             'max_width' => 0,
//             'max_height' => 0,
//             'max_size' => 0,
//             'mime_types' => '',
//           ),
//           array(
//             'key' => 'field_56d5a658bd204',
//             'label' => 'Rubrik',
//             'name' => 'rubrik',
//             'type' => 'text',
//             'instructions' => 'Ange en passande rubrik',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 25,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Rubrik',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_56d5a670bd205',
//             'label' => 'Beskrivning',
//             'name' => 'beskrivning',
//             'type' => 'textarea',
//             'instructions' => 'Ange en beskrivning.',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 55,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Beskrivning',
//             'maxlength' => '',
//             'rows' => 4,
//             'new_lines' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_56d5b324c471a',
//         'label' => 'Länkar',
//         'name' => 'lankar_sv',
//         'type' => 'repeater',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 7,
//         'layout' => 'table',
//         'button_label' => 'Lägg till en länk',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_56d5b33e4c2b7',
//             'label' => 'Länknamn',
//             'name' => 'lanknamn',
//             'type' => 'text',
//             'instructions' => 'Ange ett länknamn',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 30,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Ange ett länknamn',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_56d5b3564c2b8',
//             'label' => 'Länkmål',
//             'name' => 'lankmal',
//             'type' => 'page_link',
//             'instructions' => 'Ange ett länkmål',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 35,
//               'class' => '',
//               'id' => '',
//             ),
//             'post_type' => array(
//             ),
//             'taxonomy' => array(
//             ),
//             'allow_null' => 0,
//             'multiple' => 0,
//             'allow_archives' => 1,
//           ),
//           array(
//             'key' => 'field_57691a768e94f',
//             'label' => 'Externt länkmål',
//             'name' => 'externt_lankmal',
//             'type' => 'url',
//             'instructions' => 'Ange ett externt länkmål',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 35,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Externt länkmål',
//           ),
//           array(
//             'key' => 'field_5eff245d087a8',
//             'label' => 'Extra Class',
//             'name' => 'extra_class',
//             'type' => 'text',
//             'instructions' => '',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => '',
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => '',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_56d6dd7848e71',
//         'label' => 'Länkar (Nyheter & Nyhetsbrev)',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_56d6dd9148e72',
//         'label' => 'Länkar (Nyheter & Nyhetsbrev)',
//         'name' => 'frontpage_news_newsletter_links',
//         'type' => 'repeater',
//         'instructions' => 'Dessa länkar hamnar mellan nyheterna och puffarna på startsidan.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till länk',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_56d6dda948e73',
//             'label' => 'Namn',
//             'name' => 'namn',
//             'type' => 'text',
//             'instructions' => 'Ange ett länknamn',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 33,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Namn',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_56d6ddb548e74',
//             'label' => 'Länkmål',
//             'name' => 'lankmal',
//             'type' => 'page_link',
//             'instructions' => 'Ange en sidlänk',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 66,
//               'class' => '',
//               'id' => '',
//             ),
//             'post_type' => array(
//             ),
//             'taxonomy' => array(
//             ),
//             'allow_null' => 0,
//             'multiple' => 0,
//             'allow_archives' => 1,
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_56d6c385dcd37',
//         'label' => 'Puffar',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_56d6c437dcd38',
//         'label' => 'Puffar',
//         'name' => 'frontpage_puffar',
//         'type' => 'repeater',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 3,
//         'layout' => 'table',
//         'button_label' => 'Lägg till puff',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_56d6c443dcd39',
//             'label' => 'Bild',
//             'name' => 'bild',
//             'type' => 'image',
//             'instructions' => '',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 15,
//               'class' => '',
//               'id' => '',
//             ),
//             'return_format' => 'array',
//             'preview_size' => 'thumbnail',
//             'library' => 'all',
//             'min_width' => '',
//             'min_height' => '',
//             'min_size' => '',
//             'max_width' => '',
//             'max_height' => '',
//             'max_size' => '',
//             'mime_types' => '',
//           ),
//           array(
//             'key' => 'field_56d6c452dcd3a',
//             'label' => 'Rubrik',
//             'name' => 'rubrik',
//             'type' => 'text',
//             'instructions' => 'Ange en passande rubrik',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 25,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Rubrik',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_56d6c45bdcd3b',
//             'label' => 'Beskrivning',
//             'name' => 'beskrivning',
//             'type' => 'textarea',
//             'instructions' => 'Ange en passande beskrivning. Tänk på att om det överstiger 100 tecken så kommer det automatiskt klippa av texten och ersätta resterande med " ... ". ',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 45,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Beskrivning',
//             'maxlength' => '',
//             'rows' => 3,
//             'new_lines' => 'wpautop',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_56d6c494dcd3c',
//             'label' => 'Länkmål',
//             'name' => 'lankmal',
//             'type' => 'page_link',
//             'instructions' => '',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 15,
//               'class' => '',
//               'id' => '',
//             ),
//             'post_type' => array(
//               0 => 'page',
//               1 => 'post',
//             ),
//             'taxonomy' => array(
//             ),
//             'allow_null' => 0,
//             'multiple' => 0,
//             'allow_archives' => 1,
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_56d6e71123f05',
//         'label' => 'Swedac Academy',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_56d6e86c23f0b',
//         'label' => 'Bild',
//         'name' => 'swedac_academy_bild_sv',
//         'type' => 'image',
//         'instructions' => 'Ange en bild. ',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 15,
//           'class' => '',
//           'id' => '',
//         ),
//         'return_format' => 'array',
//         'preview_size' => 'thumbnail',
//         'library' => 'all',
//         'min_width' => '',
//         'min_height' => '',
//         'min_size' => '',
//         'max_width' => '',
//         'max_height' => '',
//         'max_size' => '',
//         'mime_types' => '',
//       ),
//       array(
//         'key' => 'field_56d6e72523f06',
//         'label' => 'Rubrik',
//         'name' => 'swedac_academy_rubrik_sv',
//         'type' => 'text',
//         'instructions' => 'Ange en rubrik. <br>
//   T.ex Swedac Academy',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 15,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Rubrik',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_56d6e78123f07',
//         'label' => 'Beskrivning',
//         'name' => 'swedac_academy_beskrivning_sv',
//         'type' => 'textarea',
//         'instructions' => 'Ange en passande beskrivning. <br>
//   T.ex Något som förklarar vad Swedac Academy är för något. ',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 25,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Beskrivning',
//         'maxlength' => '',
//         'rows' => 3,
//         'new_lines' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_56d6e7be23f08',
//         'label' => 'Länkar',
//         'name' => 'swedac_academy_lankar_sv',
//         'type' => 'repeater',
//         'instructions' => 'Ange ett par länkar som t.ex "Aktuella kurser" & "Om Swedac Acedemy"',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 45,
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till rad',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_56d6e80723f09',
//             'label' => 'Namn',
//             'name' => 'namn',
//             'type' => 'text',
//             'instructions' => 'Ange ett länknamn',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 50,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Namn',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_56d6e81e23f0a',
//             'label' => 'Länkmål',
//             'name' => 'lankmal',
//             'type' => 'page_link',
//             'instructions' => 'Ange ett länkmål',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 50,
//               'class' => '',
//               'id' => '',
//             ),
//             'post_type' => array(
//               0 => 'page',
//             ),
//             'taxonomy' => array(
//             ),
//             'allow_null' => 0,
//             'multiple' => 0,
//             'allow_archives' => 1,
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_56d707b24223c',
//         'label' => 'Fordon & Verkstäder',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_56d707c24223d',
//         'label' => 'Rubrik',
//         'name' => 'frontpage_fordon_verkstader_rubrik_sv',
//         'type' => 'text',
//         'instructions' => 'Ange en passande rubrik. <br>
//   T.ex Fordonsbesktning och ackrediterade bilverkstäder',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 25,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Rubrik',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_56d7080f4223e',
//         'label' => 'Introduktion',
//         'name' => 'frontpage_fordon_verkstader_intro_sv',
//         'type' => 'textarea',
//         'instructions' => 'Ange en passande introduktionstext',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 25,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Introduktion',
//         'maxlength' => '',
//         'rows' => 3,
//         'new_lines' => 'wpautop',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_56d7072142239',
//         'label' => 'Länkar för fordon & verkstäder',
//         'name' => 'frontpage_fordon_verkstader_rep_sv',
//         'type' => 'repeater',
//         'instructions' => 'Ange information om fordonsbeskrining och ackrediterade.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 50,
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till länk',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_56d707784223a',
//             'label' => 'Namn',
//             'name' => 'namn',
//             'type' => 'text',
//             'instructions' => 'Ange ett länknamn.',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 25,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Namn',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_56d707864223b',
//             'label' => 'Länkmål',
//             'name' => 'lankmal',
//             'type' => 'url',
//             'instructions' => 'Ange ett länkmål',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 75,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Url',
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_56d8139d6b491',
//         'label' => 'Sidfot',
//         'name' => '',
//         'type' => 'tab',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'placement' => 'top',
//         'endpoint' => 0,
//       ),
//       array(
//         'key' => 'field_56d813a46b492',
//         'label' => 'Om Swedac',
//         'name' => 'footer_about',
//         'type' => 'textarea',
//         'instructions' => 'Ange en kort beskrivning om vad Swedac är för något. ',
//         'required' => 1,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 100,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Om Swedac',
//         'maxlength' => '',
//         'rows' => 3,
//         'new_lines' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_56d813ca6b493',
//         'label' => 'Telefonnummer',
//         'name' => 'footer_phonenumber',
//         'type' => 'text',
//         'instructions' => 'Ange ett telefonnummer',
//         'required' => 1,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 50,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Telefonnummer',
//         'prepend' => '',
//         'append' => '',
//         'maxlength' => '',
//         'readonly' => 0,
//         'disabled' => 0,
//       ),
//       array(
//         'key' => 'field_56d814266b494',
//         'label' => 'Epostadress',
//         'name' => 'footer_epost',
//         'type' => 'email',
//         'instructions' => 'Ange en epostadress',
//         'required' => 1,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => 50,
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => '',
//         'placeholder' => 'Epost',
//         'prepend' => '',
//         'append' => '',
//       ),
//       array(
//         'key' => 'field_56d814706b495',
//         'label' => 'Adresser',
//         'name' => 'footer_adress_sv',
//         'type' => 'repeater',
//         'instructions' => 'Ange era adresser. ',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till adress',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_56d814776b496',
//             'label' => 'Område',
//             'name' => 'omrade',
//             'type' => 'text',
//             'instructions' => 'Ange ett passande namn för området </br>
//   T.ex Stockholmskontoret',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 33,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Område',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_56d814b26b497',
//             'label' => 'Adress',
//             'name' => 'adress',
//             'type' => 'textarea',
//             'instructions' => 'Ange adressen. ',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 66,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Adress',
//             'maxlength' => '',
//             'rows' => 3,
//             'new_lines' => 'wpautop',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_56d829aace1dd',
//         'label' => 'Sociala medier',
//         'name' => 'footer_socialmedia_sv',
//         'type' => 'repeater',
//         'instructions' => 'Lägg till era sociala medier genom att ange en ikon samt en länk till rätt media. ',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 0,
//         'layout' => 'table',
//         'button_label' => 'Lägg till social media',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_56d829d4ce1de',
//             'label' => 'Ikon',
//             'name' => 'icon',
//             'type' => 'font-awesome',
//             'instructions' => 'Ange rätt ikon för rätt media. ',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 25,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => 'fa-facebook',
//             'save_format' => 'element',
//             'allow_null' => 0,
//             'enqueue_fa' => 0,
//             'fa_live_preview' => '',
//             'choices' => array(
//               'null' => '- Select -',
//               'fa-500px' => '&#xf26e; fa-500px',
//               'fa-adjust' => '&#xf042; fa-adjust',
//               'fa-adn' => '&#xf170; fa-adn',
//               'fa-align-center' => '&#xf037; fa-align-center',
//               'fa-align-justify' => '&#xf039; fa-align-justify',
//               'fa-align-left' => '&#xf036; fa-align-left',
//               'fa-align-right' => '&#xf038; fa-align-right',
//               'fa-amazon' => '&#xf270; fa-amazon',
//               'fa-ambulance' => '&#xf0f9; fa-ambulance',
//               'fa-anchor' => '&#xf13d; fa-anchor',
//               'fa-android' => '&#xf17b; fa-android',
//               'fa-angellist' => '&#xf209; fa-angellist',
//               'fa-angle-double-down' => '&#xf103; fa-angle-double-down',
//               'fa-angle-double-left' => '&#xf100; fa-angle-double-left',
//               'fa-angle-double-right' => '&#xf101; fa-angle-double-right',
//               'fa-angle-double-up' => '&#xf102; fa-angle-double-up',
//               'fa-angle-down' => '&#xf107; fa-angle-down',
//               'fa-angle-left' => '&#xf104; fa-angle-left',
//               'fa-angle-right' => '&#xf105; fa-angle-right',
//               'fa-angle-up' => '&#xf106; fa-angle-up',
//               'fa-apple' => '&#xf179; fa-apple',
//               'fa-archive' => '&#xf187; fa-archive',
//               'fa-area-chart' => '&#xf1fe; fa-area-chart',
//               'fa-arrow-circle-down' => '&#xf0ab; fa-arrow-circle-down',
//               'fa-arrow-circle-left' => '&#xf0a8; fa-arrow-circle-left',
//               'fa-arrow-circle-o-down' => '&#xf01a; fa-arrow-circle-o-down',
//               'fa-arrow-circle-o-left' => '&#xf190; fa-arrow-circle-o-left',
//               'fa-arrow-circle-o-right' => '&#xf18e; fa-arrow-circle-o-right',
//               'fa-arrow-circle-o-up' => '&#xf01b; fa-arrow-circle-o-up',
//               'fa-arrow-circle-right' => '&#xf0a9; fa-arrow-circle-right',
//               'fa-arrow-circle-up' => '&#xf0aa; fa-arrow-circle-up',
//               'fa-arrow-down' => '&#xf063; fa-arrow-down',
//               'fa-arrow-left' => '&#xf060; fa-arrow-left',
//               'fa-arrow-right' => '&#xf061; fa-arrow-right',
//               'fa-arrow-up' => '&#xf062; fa-arrow-up',
//               'fa-arrows' => '&#xf047; fa-arrows',
//               'fa-arrows-alt' => '&#xf0b2; fa-arrows-alt',
//               'fa-arrows-h' => '&#xf07e; fa-arrows-h',
//               'fa-arrows-v' => '&#xf07d; fa-arrows-v',
//               'fa-asterisk' => '&#xf069; fa-asterisk',
//               'fa-at' => '&#xf1fa; fa-at',
//               'fa-backward' => '&#xf04a; fa-backward',
//               'fa-balance-scale' => '&#xf24e; fa-balance-scale',
//               'fa-ban' => '&#xf05e; fa-ban',
//               'fa-bar-chart' => '&#xf080; fa-bar-chart',
//               'fa-barcode' => '&#xf02a; fa-barcode',
//               'fa-bars' => '&#xf0c9; fa-bars',
//               'fa-battery-empty' => '&#xf244; fa-battery-empty',
//               'fa-battery-full' => '&#xf240; fa-battery-full',
//               'fa-battery-half' => '&#xf242; fa-battery-half',
//               'fa-battery-quarter' => '&#xf243; fa-battery-quarter',
//               'fa-battery-three-quarters' => '&#xf241; fa-battery-three-quarters',
//               'fa-bed' => '&#xf236; fa-bed',
//               'fa-beer' => '&#xf0fc; fa-beer',
//               'fa-behance' => '&#xf1b4; fa-behance',
//               'fa-behance-square' => '&#xf1b5; fa-behance-square',
//               'fa-bell' => '&#xf0f3; fa-bell',
//               'fa-bell-o' => '&#xf0a2; fa-bell-o',
//               'fa-bell-slash' => '&#xf1f6; fa-bell-slash',
//               'fa-bell-slash-o' => '&#xf1f7; fa-bell-slash-o',
//               'fa-bicycle' => '&#xf206; fa-bicycle',
//               'fa-binoculars' => '&#xf1e5; fa-binoculars',
//               'fa-birthday-cake' => '&#xf1fd; fa-birthday-cake',
//               'fa-bitbucket' => '&#xf171; fa-bitbucket',
//               'fa-bitbucket-square' => '&#xf172; fa-bitbucket-square',
//               'fa-black-tie' => '&#xf27e; fa-black-tie',
//               'fa-bluetooth' => '&#xf293; fa-bluetooth',
//               'fa-bluetooth-b' => '&#xf294; fa-bluetooth-b',
//               'fa-bold' => '&#xf032; fa-bold',
//               'fa-bolt' => '&#xf0e7; fa-bolt',
//               'fa-bomb' => '&#xf1e2; fa-bomb',
//               'fa-book' => '&#xf02d; fa-book',
//               'fa-bookmark' => '&#xf02e; fa-bookmark',
//               'fa-bookmark-o' => '&#xf097; fa-bookmark-o',
//               'fa-briefcase' => '&#xf0b1; fa-briefcase',
//               'fa-btc' => '&#xf15a; fa-btc',
//               'fa-bug' => '&#xf188; fa-bug',
//               'fa-building' => '&#xf1ad; fa-building',
//               'fa-building-o' => '&#xf0f7; fa-building-o',
//               'fa-bullhorn' => '&#xf0a1; fa-bullhorn',
//               'fa-bullseye' => '&#xf140; fa-bullseye',
//               'fa-bus' => '&#xf207; fa-bus',
//               'fa-buysellads' => '&#xf20d; fa-buysellads',
//               'fa-calculator' => '&#xf1ec; fa-calculator',
//               'fa-calendar' => '&#xf073; fa-calendar',
//               'fa-calendar-check-o' => '&#xf274; fa-calendar-check-o',
//               'fa-calendar-minus-o' => '&#xf272; fa-calendar-minus-o',
//               'fa-calendar-o' => '&#xf133; fa-calendar-o',
//               'fa-calendar-plus-o' => '&#xf271; fa-calendar-plus-o',
//               'fa-calendar-times-o' => '&#xf273; fa-calendar-times-o',
//               'fa-camera' => '&#xf030; fa-camera',
//               'fa-camera-retro' => '&#xf083; fa-camera-retro',
//               'fa-car' => '&#xf1b9; fa-car',
//               'fa-caret-down' => '&#xf0d7; fa-caret-down',
//               'fa-caret-left' => '&#xf0d9; fa-caret-left',
//               'fa-caret-right' => '&#xf0da; fa-caret-right',
//               'fa-caret-square-o-down' => '&#xf150; fa-caret-square-o-down',
//               'fa-caret-square-o-left' => '&#xf191; fa-caret-square-o-left',
//               'fa-caret-square-o-right' => '&#xf152; fa-caret-square-o-right',
//               'fa-caret-square-o-up' => '&#xf151; fa-caret-square-o-up',
//               'fa-caret-up' => '&#xf0d8; fa-caret-up',
//               'fa-cart-arrow-down' => '&#xf218; fa-cart-arrow-down',
//               'fa-cart-plus' => '&#xf217; fa-cart-plus',
//               'fa-cc' => '&#xf20a; fa-cc',
//               'fa-cc-amex' => '&#xf1f3; fa-cc-amex',
//               'fa-cc-diners-club' => '&#xf24c; fa-cc-diners-club',
//               'fa-cc-discover' => '&#xf1f2; fa-cc-discover',
//               'fa-cc-jcb' => '&#xf24b; fa-cc-jcb',
//               'fa-cc-mastercard' => '&#xf1f1; fa-cc-mastercard',
//               'fa-cc-paypal' => '&#xf1f4; fa-cc-paypal',
//               'fa-cc-stripe' => '&#xf1f5; fa-cc-stripe',
//               'fa-cc-visa' => '&#xf1f0; fa-cc-visa',
//               'fa-certificate' => '&#xf0a3; fa-certificate',
//               'fa-chain-broken' => '&#xf127; fa-chain-broken',
//               'fa-check' => '&#xf00c; fa-check',
//               'fa-check-circle' => '&#xf058; fa-check-circle',
//               'fa-check-circle-o' => '&#xf05d; fa-check-circle-o',
//               'fa-check-square' => '&#xf14a; fa-check-square',
//               'fa-check-square-o' => '&#xf046; fa-check-square-o',
//               'fa-chevron-circle-down' => '&#xf13a; fa-chevron-circle-down',
//               'fa-chevron-circle-left' => '&#xf137; fa-chevron-circle-left',
//               'fa-chevron-circle-right' => '&#xf138; fa-chevron-circle-right',
//               'fa-chevron-circle-up' => '&#xf139; fa-chevron-circle-up',
//               'fa-chevron-down' => '&#xf078; fa-chevron-down',
//               'fa-chevron-left' => '&#xf053; fa-chevron-left',
//               'fa-chevron-right' => '&#xf054; fa-chevron-right',
//               'fa-chevron-up' => '&#xf077; fa-chevron-up',
//               'fa-child' => '&#xf1ae; fa-child',
//               'fa-chrome' => '&#xf268; fa-chrome',
//               'fa-circle' => '&#xf111; fa-circle',
//               'fa-circle-o' => '&#xf10c; fa-circle-o',
//               'fa-circle-o-notch' => '&#xf1ce; fa-circle-o-notch',
//               'fa-circle-thin' => '&#xf1db; fa-circle-thin',
//               'fa-clipboard' => '&#xf0ea; fa-clipboard',
//               'fa-clock-o' => '&#xf017; fa-clock-o',
//               'fa-clone' => '&#xf24d; fa-clone',
//               'fa-cloud' => '&#xf0c2; fa-cloud',
//               'fa-cloud-download' => '&#xf0ed; fa-cloud-download',
//               'fa-cloud-upload' => '&#xf0ee; fa-cloud-upload',
//               'fa-code' => '&#xf121; fa-code',
//               'fa-code-fork' => '&#xf126; fa-code-fork',
//               'fa-codepen' => '&#xf1cb; fa-codepen',
//               'fa-codiepie' => '&#xf284; fa-codiepie',
//               'fa-coffee' => '&#xf0f4; fa-coffee',
//               'fa-cog' => '&#xf013; fa-cog',
//               'fa-cogs' => '&#xf085; fa-cogs',
//               'fa-columns' => '&#xf0db; fa-columns',
//               'fa-comment' => '&#xf075; fa-comment',
//               'fa-comment-o' => '&#xf0e5; fa-comment-o',
//               'fa-commenting' => '&#xf27a; fa-commenting',
//               'fa-commenting-o' => '&#xf27b; fa-commenting-o',
//               'fa-comments' => '&#xf086; fa-comments',
//               'fa-comments-o' => '&#xf0e6; fa-comments-o',
//               'fa-compass' => '&#xf14e; fa-compass',
//               'fa-compress' => '&#xf066; fa-compress',
//               'fa-connectdevelop' => '&#xf20e; fa-connectdevelop',
//               'fa-contao' => '&#xf26d; fa-contao',
//               'fa-copyright' => '&#xf1f9; fa-copyright',
//               'fa-creative-commons' => '&#xf25e; fa-creative-commons',
//               'fa-credit-card' => '&#xf09d; fa-credit-card',
//               'fa-credit-card-alt' => '&#xf283; fa-credit-card-alt',
//               'fa-crop' => '&#xf125; fa-crop',
//               'fa-crosshairs' => '&#xf05b; fa-crosshairs',
//               'fa-css3' => '&#xf13c; fa-css3',
//               'fa-cube' => '&#xf1b2; fa-cube',
//               'fa-cubes' => '&#xf1b3; fa-cubes',
//               'fa-cutlery' => '&#xf0f5; fa-cutlery',
//               'fa-dashcube' => '&#xf210; fa-dashcube',
//               'fa-database' => '&#xf1c0; fa-database',
//               'fa-delicious' => '&#xf1a5; fa-delicious',
//               'fa-desktop' => '&#xf108; fa-desktop',
//               'fa-deviantart' => '&#xf1bd; fa-deviantart',
//               'fa-diamond' => '&#xf219; fa-diamond',
//               'fa-digg' => '&#xf1a6; fa-digg',
//               'fa-dot-circle-o' => '&#xf192; fa-dot-circle-o',
//               'fa-download' => '&#xf019; fa-download',
//               'fa-dribbble' => '&#xf17d; fa-dribbble',
//               'fa-dropbox' => '&#xf16b; fa-dropbox',
//               'fa-drupal' => '&#xf1a9; fa-drupal',
//               'fa-edge' => '&#xf282; fa-edge',
//               'fa-eject' => '&#xf052; fa-eject',
//               'fa-ellipsis-h' => '&#xf141; fa-ellipsis-h',
//               'fa-ellipsis-v' => '&#xf142; fa-ellipsis-v',
//               'fa-empire' => '&#xf1d1; fa-empire',
//               'fa-envelope' => '&#xf0e0; fa-envelope',
//               'fa-envelope-o' => '&#xf003; fa-envelope-o',
//               'fa-envelope-square' => '&#xf199; fa-envelope-square',
//               'fa-eraser' => '&#xf12d; fa-eraser',
//               'fa-eur' => '&#xf153; fa-eur',
//               'fa-exchange' => '&#xf0ec; fa-exchange',
//               'fa-exclamation' => '&#xf12a; fa-exclamation',
//               'fa-exclamation-circle' => '&#xf06a; fa-exclamation-circle',
//               'fa-exclamation-triangle' => '&#xf071; fa-exclamation-triangle',
//               'fa-expand' => '&#xf065; fa-expand',
//               'fa-expeditedssl' => '&#xf23e; fa-expeditedssl',
//               'fa-external-link' => '&#xf08e; fa-external-link',
//               'fa-external-link-square' => '&#xf14c; fa-external-link-square',
//               'fa-eye' => '&#xf06e; fa-eye',
//               'fa-eye-slash' => '&#xf070; fa-eye-slash',
//               'fa-eyedropper' => '&#xf1fb; fa-eyedropper',
//               'fa-facebook' => '&#xf09a; fa-facebook',
//               'fa-facebook-official' => '&#xf230; fa-facebook-official',
//               'fa-facebook-square' => '&#xf082; fa-facebook-square',
//               'fa-fast-backward' => '&#xf049; fa-fast-backward',
//               'fa-fast-forward' => '&#xf050; fa-fast-forward',
//               'fa-fax' => '&#xf1ac; fa-fax',
//               'fa-female' => '&#xf182; fa-female',
//               'fa-fighter-jet' => '&#xf0fb; fa-fighter-jet',
//               'fa-file' => '&#xf15b; fa-file',
//               'fa-file-archive-o' => '&#xf1c6; fa-file-archive-o',
//               'fa-file-audio-o' => '&#xf1c7; fa-file-audio-o',
//               'fa-file-code-o' => '&#xf1c9; fa-file-code-o',
//               'fa-file-excel-o' => '&#xf1c3; fa-file-excel-o',
//               'fa-file-image-o' => '&#xf1c5; fa-file-image-o',
//               'fa-file-o' => '&#xf016; fa-file-o',
//               'fa-file-pdf-o' => '&#xf1c1; fa-file-pdf-o',
//               'fa-file-powerpoint-o' => '&#xf1c4; fa-file-powerpoint-o',
//               'fa-file-text' => '&#xf15c; fa-file-text',
//               'fa-file-text-o' => '&#xf0f6; fa-file-text-o',
//               'fa-file-video-o' => '&#xf1c8; fa-file-video-o',
//               'fa-file-word-o' => '&#xf1c2; fa-file-word-o',
//               'fa-files-o' => '&#xf0c5; fa-files-o',
//               'fa-film' => '&#xf008; fa-film',
//               'fa-filter' => '&#xf0b0; fa-filter',
//               'fa-fire' => '&#xf06d; fa-fire',
//               'fa-fire-extinguisher' => '&#xf134; fa-fire-extinguisher',
//               'fa-firefox' => '&#xf269; fa-firefox',
//               'fa-flag' => '&#xf024; fa-flag',
//               'fa-flag-checkered' => '&#xf11e; fa-flag-checkered',
//               'fa-flag-o' => '&#xf11d; fa-flag-o',
//               'fa-flask' => '&#xf0c3; fa-flask',
//               'fa-flickr' => '&#xf16e; fa-flickr',
//               'fa-floppy-o' => '&#xf0c7; fa-floppy-o',
//               'fa-folder' => '&#xf07b; fa-folder',
//               'fa-folder-o' => '&#xf114; fa-folder-o',
//               'fa-folder-open' => '&#xf07c; fa-folder-open',
//               'fa-folder-open-o' => '&#xf115; fa-folder-open-o',
//               'fa-font' => '&#xf031; fa-font',
//               'fa-fonticons' => '&#xf280; fa-fonticons',
//               'fa-fort-awesome' => '&#xf286; fa-fort-awesome',
//               'fa-forumbee' => '&#xf211; fa-forumbee',
//               'fa-forward' => '&#xf04e; fa-forward',
//               'fa-foursquare' => '&#xf180; fa-foursquare',
//               'fa-frown-o' => '&#xf119; fa-frown-o',
//               'fa-futbol-o' => '&#xf1e3; fa-futbol-o',
//               'fa-gamepad' => '&#xf11b; fa-gamepad',
//               'fa-gavel' => '&#xf0e3; fa-gavel',
//               'fa-gbp' => '&#xf154; fa-gbp',
//               'fa-genderless' => '&#xf22d; fa-genderless',
//               'fa-get-pocket' => '&#xf265; fa-get-pocket',
//               'fa-gg' => '&#xf260; fa-gg',
//               'fa-gg-circle' => '&#xf261; fa-gg-circle',
//               'fa-gift' => '&#xf06b; fa-gift',
//               'fa-git' => '&#xf1d3; fa-git',
//               'fa-git-square' => '&#xf1d2; fa-git-square',
//               'fa-github' => '&#xf09b; fa-github',
//               'fa-github-alt' => '&#xf113; fa-github-alt',
//               'fa-github-square' => '&#xf092; fa-github-square',
//               'fa-glass' => '&#xf000; fa-glass',
//               'fa-globe' => '&#xf0ac; fa-globe',
//               'fa-google' => '&#xf1a0; fa-google',
//               'fa-google-plus' => '&#xf0d5; fa-google-plus',
//               'fa-google-plus-square' => '&#xf0d4; fa-google-plus-square',
//               'fa-google-wallet' => '&#xf1ee; fa-google-wallet',
//               'fa-graduation-cap' => '&#xf19d; fa-graduation-cap',
//               'fa-gratipay' => '&#xf184; fa-gratipay',
//               'fa-h-square' => '&#xf0fd; fa-h-square',
//               'fa-hacker-news' => '&#xf1d4; fa-hacker-news',
//               'fa-hand-lizard-o' => '&#xf258; fa-hand-lizard-o',
//               'fa-hand-o-down' => '&#xf0a7; fa-hand-o-down',
//               'fa-hand-o-left' => '&#xf0a5; fa-hand-o-left',
//               'fa-hand-o-right' => '&#xf0a4; fa-hand-o-right',
//               'fa-hand-o-up' => '&#xf0a6; fa-hand-o-up',
//               'fa-hand-paper-o' => '&#xf256; fa-hand-paper-o',
//               'fa-hand-peace-o' => '&#xf25b; fa-hand-peace-o',
//               'fa-hand-pointer-o' => '&#xf25a; fa-hand-pointer-o',
//               'fa-hand-rock-o' => '&#xf255; fa-hand-rock-o',
//               'fa-hand-scissors-o' => '&#xf257; fa-hand-scissors-o',
//               'fa-hand-spock-o' => '&#xf259; fa-hand-spock-o',
//               'fa-hashtag' => '&#xf292; fa-hashtag',
//               'fa-hdd-o' => '&#xf0a0; fa-hdd-o',
//               'fa-header' => '&#xf1dc; fa-header',
//               'fa-headphones' => '&#xf025; fa-headphones',
//               'fa-heart' => '&#xf004; fa-heart',
//               'fa-heart-o' => '&#xf08a; fa-heart-o',
//               'fa-heartbeat' => '&#xf21e; fa-heartbeat',
//               'fa-history' => '&#xf1da; fa-history',
//               'fa-home' => '&#xf015; fa-home',
//               'fa-hospital-o' => '&#xf0f8; fa-hospital-o',
//               'fa-hourglass' => '&#xf254; fa-hourglass',
//               'fa-hourglass-end' => '&#xf253; fa-hourglass-end',
//               'fa-hourglass-half' => '&#xf252; fa-hourglass-half',
//               'fa-hourglass-o' => '&#xf250; fa-hourglass-o',
//               'fa-hourglass-start' => '&#xf251; fa-hourglass-start',
//               'fa-houzz' => '&#xf27c; fa-houzz',
//               'fa-html5' => '&#xf13b; fa-html5',
//               'fa-i-cursor' => '&#xf246; fa-i-cursor',
//               'fa-ils' => '&#xf20b; fa-ils',
//               'fa-inbox' => '&#xf01c; fa-inbox',
//               'fa-indent' => '&#xf03c; fa-indent',
//               'fa-industry' => '&#xf275; fa-industry',
//               'fa-info' => '&#xf129; fa-info',
//               'fa-info-circle' => '&#xf05a; fa-info-circle',
//               'fa-inr' => '&#xf156; fa-inr',
//               'fa-instagram' => '&#xf16d; fa-instagram',
//               'fa-internet-explorer' => '&#xf26b; fa-internet-explorer',
//               'fa-ioxhost' => '&#xf208; fa-ioxhost',
//               'fa-italic' => '&#xf033; fa-italic',
//               'fa-joomla' => '&#xf1aa; fa-joomla',
//               'fa-jpy' => '&#xf157; fa-jpy',
//               'fa-jsfiddle' => '&#xf1cc; fa-jsfiddle',
//               'fa-key' => '&#xf084; fa-key',
//               'fa-keyboard-o' => '&#xf11c; fa-keyboard-o',
//               'fa-krw' => '&#xf159; fa-krw',
//               'fa-language' => '&#xf1ab; fa-language',
//               'fa-laptop' => '&#xf109; fa-laptop',
//               'fa-lastfm' => '&#xf202; fa-lastfm',
//               'fa-lastfm-square' => '&#xf203; fa-lastfm-square',
//               'fa-leaf' => '&#xf06c; fa-leaf',
//               'fa-leanpub' => '&#xf212; fa-leanpub',
//               'fa-lemon-o' => '&#xf094; fa-lemon-o',
//               'fa-level-down' => '&#xf149; fa-level-down',
//               'fa-level-up' => '&#xf148; fa-level-up',
//               'fa-life-ring' => '&#xf1cd; fa-life-ring',
//               'fa-lightbulb-o' => '&#xf0eb; fa-lightbulb-o',
//               'fa-line-chart' => '&#xf201; fa-line-chart',
//               'fa-link' => '&#xf0c1; fa-link',
//               'fa-linkedin' => '&#xf0e1; fa-linkedin',
//               'fa-linkedin-square' => '&#xf08c; fa-linkedin-square',
//               'fa-linux' => '&#xf17c; fa-linux',
//               'fa-list' => '&#xf03a; fa-list',
//               'fa-list-alt' => '&#xf022; fa-list-alt',
//               'fa-list-ol' => '&#xf0cb; fa-list-ol',
//               'fa-list-ul' => '&#xf0ca; fa-list-ul',
//               'fa-location-arrow' => '&#xf124; fa-location-arrow',
//               'fa-lock' => '&#xf023; fa-lock',
//               'fa-long-arrow-down' => '&#xf175; fa-long-arrow-down',
//               'fa-long-arrow-left' => '&#xf177; fa-long-arrow-left',
//               'fa-long-arrow-right' => '&#xf178; fa-long-arrow-right',
//               'fa-long-arrow-up' => '&#xf176; fa-long-arrow-up',
//               'fa-magic' => '&#xf0d0; fa-magic',
//               'fa-magnet' => '&#xf076; fa-magnet',
//               'fa-male' => '&#xf183; fa-male',
//               'fa-map' => '&#xf279; fa-map',
//               'fa-map-marker' => '&#xf041; fa-map-marker',
//               'fa-map-o' => '&#xf278; fa-map-o',
//               'fa-map-pin' => '&#xf276; fa-map-pin',
//               'fa-map-signs' => '&#xf277; fa-map-signs',
//               'fa-mars' => '&#xf222; fa-mars',
//               'fa-mars-double' => '&#xf227; fa-mars-double',
//               'fa-mars-stroke' => '&#xf229; fa-mars-stroke',
//               'fa-mars-stroke-h' => '&#xf22b; fa-mars-stroke-h',
//               'fa-mars-stroke-v' => '&#xf22a; fa-mars-stroke-v',
//               'fa-maxcdn' => '&#xf136; fa-maxcdn',
//               'fa-meanpath' => '&#xf20c; fa-meanpath',
//               'fa-medium' => '&#xf23a; fa-medium',
//               'fa-medkit' => '&#xf0fa; fa-medkit',
//               'fa-meh-o' => '&#xf11a; fa-meh-o',
//               'fa-mercury' => '&#xf223; fa-mercury',
//               'fa-microphone' => '&#xf130; fa-microphone',
//               'fa-microphone-slash' => '&#xf131; fa-microphone-slash',
//               'fa-minus' => '&#xf068; fa-minus',
//               'fa-minus-circle' => '&#xf056; fa-minus-circle',
//               'fa-minus-square' => '&#xf146; fa-minus-square',
//               'fa-minus-square-o' => '&#xf147; fa-minus-square-o',
//               'fa-mixcloud' => '&#xf289; fa-mixcloud',
//               'fa-mobile' => '&#xf10b; fa-mobile',
//               'fa-modx' => '&#xf285; fa-modx',
//               'fa-money' => '&#xf0d6; fa-money',
//               'fa-moon-o' => '&#xf186; fa-moon-o',
//               'fa-motorcycle' => '&#xf21c; fa-motorcycle',
//               'fa-mouse-pointer' => '&#xf245; fa-mouse-pointer',
//               'fa-music' => '&#xf001; fa-music',
//               'fa-neuter' => '&#xf22c; fa-neuter',
//               'fa-newspaper-o' => '&#xf1ea; fa-newspaper-o',
//               'fa-object-group' => '&#xf247; fa-object-group',
//               'fa-object-ungroup' => '&#xf248; fa-object-ungroup',
//               'fa-odnoklassniki' => '&#xf263; fa-odnoklassniki',
//               'fa-odnoklassniki-square' => '&#xf264; fa-odnoklassniki-square',
//               'fa-opencart' => '&#xf23d; fa-opencart',
//               'fa-openid' => '&#xf19b; fa-openid',
//               'fa-opera' => '&#xf26a; fa-opera',
//               'fa-optin-monster' => '&#xf23c; fa-optin-monster',
//               'fa-outdent' => '&#xf03b; fa-outdent',
//               'fa-pagelines' => '&#xf18c; fa-pagelines',
//               'fa-paint-brush' => '&#xf1fc; fa-paint-brush',
//               'fa-paper-plane' => '&#xf1d8; fa-paper-plane',
//               'fa-paper-plane-o' => '&#xf1d9; fa-paper-plane-o',
//               'fa-paperclip' => '&#xf0c6; fa-paperclip',
//               'fa-paragraph' => '&#xf1dd; fa-paragraph',
//               'fa-pause' => '&#xf04c; fa-pause',
//               'fa-pause-circle' => '&#xf28b; fa-pause-circle',
//               'fa-pause-circle-o' => '&#xf28c; fa-pause-circle-o',
//               'fa-paw' => '&#xf1b0; fa-paw',
//               'fa-paypal' => '&#xf1ed; fa-paypal',
//               'fa-pencil' => '&#xf040; fa-pencil',
//               'fa-pencil-square' => '&#xf14b; fa-pencil-square',
//               'fa-pencil-square-o' => '&#xf044; fa-pencil-square-o',
//               'fa-percent' => '&#xf295; fa-percent',
//               'fa-phone' => '&#xf095; fa-phone',
//               'fa-phone-square' => '&#xf098; fa-phone-square',
//               'fa-picture-o' => '&#xf03e; fa-picture-o',
//               'fa-pie-chart' => '&#xf200; fa-pie-chart',
//               'fa-pied-piper' => '&#xf1a7; fa-pied-piper',
//               'fa-pied-piper-alt' => '&#xf1a8; fa-pied-piper-alt',
//               'fa-pinterest' => '&#xf0d2; fa-pinterest',
//               'fa-pinterest-p' => '&#xf231; fa-pinterest-p',
//               'fa-pinterest-square' => '&#xf0d3; fa-pinterest-square',
//               'fa-plane' => '&#xf072; fa-plane',
//               'fa-play' => '&#xf04b; fa-play',
//               'fa-play-circle' => '&#xf144; fa-play-circle',
//               'fa-play-circle-o' => '&#xf01d; fa-play-circle-o',
//               'fa-plug' => '&#xf1e6; fa-plug',
//               'fa-plus' => '&#xf067; fa-plus',
//               'fa-plus-circle' => '&#xf055; fa-plus-circle',
//               'fa-plus-square' => '&#xf0fe; fa-plus-square',
//               'fa-plus-square-o' => '&#xf196; fa-plus-square-o',
//               'fa-power-off' => '&#xf011; fa-power-off',
//               'fa-print' => '&#xf02f; fa-print',
//               'fa-product-hunt' => '&#xf288; fa-product-hunt',
//               'fa-puzzle-piece' => '&#xf12e; fa-puzzle-piece',
//               'fa-qq' => '&#xf1d6; fa-qq',
//               'fa-qrcode' => '&#xf029; fa-qrcode',
//               'fa-question' => '&#xf128; fa-question',
//               'fa-question-circle' => '&#xf059; fa-question-circle',
//               'fa-quote-left' => '&#xf10d; fa-quote-left',
//               'fa-quote-right' => '&#xf10e; fa-quote-right',
//               'fa-random' => '&#xf074; fa-random',
//               'fa-rebel' => '&#xf1d0; fa-rebel',
//               'fa-recycle' => '&#xf1b8; fa-recycle',
//               'fa-reddit' => '&#xf1a1; fa-reddit',
//               'fa-reddit-alien' => '&#xf281; fa-reddit-alien',
//               'fa-reddit-square' => '&#xf1a2; fa-reddit-square',
//               'fa-refresh' => '&#xf021; fa-refresh',
//               'fa-registered' => '&#xf25d; fa-registered',
//               'fa-renren' => '&#xf18b; fa-renren',
//               'fa-repeat' => '&#xf01e; fa-repeat',
//               'fa-reply' => '&#xf112; fa-reply',
//               'fa-reply-all' => '&#xf122; fa-reply-all',
//               'fa-retweet' => '&#xf079; fa-retweet',
//               'fa-road' => '&#xf018; fa-road',
//               'fa-rocket' => '&#xf135; fa-rocket',
//               'fa-rss' => '&#xf09e; fa-rss',
//               'fa-rss-square' => '&#xf143; fa-rss-square',
//               'fa-rub' => '&#xf158; fa-rub',
//               'fa-safari' => '&#xf267; fa-safari',
//               'fa-scissors' => '&#xf0c4; fa-scissors',
//               'fa-scribd' => '&#xf28a; fa-scribd',
//               'fa-search' => '&#xf002; fa-search',
//               'fa-search-minus' => '&#xf010; fa-search-minus',
//               'fa-search-plus' => '&#xf00e; fa-search-plus',
//               'fa-sellsy' => '&#xf213; fa-sellsy',
//               'fa-server' => '&#xf233; fa-server',
//               'fa-share' => '&#xf064; fa-share',
//               'fa-share-alt' => '&#xf1e0; fa-share-alt',
//               'fa-share-alt-square' => '&#xf1e1; fa-share-alt-square',
//               'fa-share-square' => '&#xf14d; fa-share-square',
//               'fa-share-square-o' => '&#xf045; fa-share-square-o',
//               'fa-shield' => '&#xf132; fa-shield',
//               'fa-ship' => '&#xf21a; fa-ship',
//               'fa-shirtsinbulk' => '&#xf214; fa-shirtsinbulk',
//               'fa-shopping-bag' => '&#xf290; fa-shopping-bag',
//               'fa-shopping-basket' => '&#xf291; fa-shopping-basket',
//               'fa-shopping-cart' => '&#xf07a; fa-shopping-cart',
//               'fa-sign-in' => '&#xf090; fa-sign-in',
//               'fa-sign-out' => '&#xf08b; fa-sign-out',
//               'fa-signal' => '&#xf012; fa-signal',
//               'fa-simplybuilt' => '&#xf215; fa-simplybuilt',
//               'fa-sitemap' => '&#xf0e8; fa-sitemap',
//               'fa-skyatlas' => '&#xf216; fa-skyatlas',
//               'fa-skype' => '&#xf17e; fa-skype',
//               'fa-slack' => '&#xf198; fa-slack',
//               'fa-sliders' => '&#xf1de; fa-sliders',
//               'fa-slideshare' => '&#xf1e7; fa-slideshare',
//               'fa-smile-o' => '&#xf118; fa-smile-o',
//               'fa-sort' => '&#xf0dc; fa-sort',
//               'fa-sort-alpha-asc' => '&#xf15d; fa-sort-alpha-asc',
//               'fa-sort-alpha-desc' => '&#xf15e; fa-sort-alpha-desc',
//               'fa-sort-amount-asc' => '&#xf160; fa-sort-amount-asc',
//               'fa-sort-amount-desc' => '&#xf161; fa-sort-amount-desc',
//               'fa-sort-asc' => '&#xf0de; fa-sort-asc',
//               'fa-sort-desc' => '&#xf0dd; fa-sort-desc',
//               'fa-sort-numeric-asc' => '&#xf162; fa-sort-numeric-asc',
//               'fa-sort-numeric-desc' => '&#xf163; fa-sort-numeric-desc',
//               'fa-soundcloud' => '&#xf1be; fa-soundcloud',
//               'fa-space-shuttle' => '&#xf197; fa-space-shuttle',
//               'fa-spinner' => '&#xf110; fa-spinner',
//               'fa-spoon' => '&#xf1b1; fa-spoon',
//               'fa-spotify' => '&#xf1bc; fa-spotify',
//               'fa-square' => '&#xf0c8; fa-square',
//               'fa-square-o' => '&#xf096; fa-square-o',
//               'fa-stack-exchange' => '&#xf18d; fa-stack-exchange',
//               'fa-stack-overflow' => '&#xf16c; fa-stack-overflow',
//               'fa-star' => '&#xf005; fa-star',
//               'fa-star-half' => '&#xf089; fa-star-half',
//               'fa-star-half-o' => '&#xf123; fa-star-half-o',
//               'fa-star-o' => '&#xf006; fa-star-o',
//               'fa-steam' => '&#xf1b6; fa-steam',
//               'fa-steam-square' => '&#xf1b7; fa-steam-square',
//               'fa-step-backward' => '&#xf048; fa-step-backward',
//               'fa-step-forward' => '&#xf051; fa-step-forward',
//               'fa-stethoscope' => '&#xf0f1; fa-stethoscope',
//               'fa-sticky-note' => '&#xf249; fa-sticky-note',
//               'fa-sticky-note-o' => '&#xf24a; fa-sticky-note-o',
//               'fa-stop' => '&#xf04d; fa-stop',
//               'fa-stop-circle' => '&#xf28d; fa-stop-circle',
//               'fa-stop-circle-o' => '&#xf28e; fa-stop-circle-o',
//               'fa-street-view' => '&#xf21d; fa-street-view',
//               'fa-strikethrough' => '&#xf0cc; fa-strikethrough',
//               'fa-stumbleupon' => '&#xf1a4; fa-stumbleupon',
//               'fa-stumbleupon-circle' => '&#xf1a3; fa-stumbleupon-circle',
//               'fa-subscript' => '&#xf12c; fa-subscript',
//               'fa-subway' => '&#xf239; fa-subway',
//               'fa-suitcase' => '&#xf0f2; fa-suitcase',
//               'fa-sun-o' => '&#xf185; fa-sun-o',
//               'fa-superscript' => '&#xf12b; fa-superscript',
//               'fa-table' => '&#xf0ce; fa-table',
//               'fa-tablet' => '&#xf10a; fa-tablet',
//               'fa-tachometer' => '&#xf0e4; fa-tachometer',
//               'fa-tag' => '&#xf02b; fa-tag',
//               'fa-tags' => '&#xf02c; fa-tags',
//               'fa-tasks' => '&#xf0ae; fa-tasks',
//               'fa-taxi' => '&#xf1ba; fa-taxi',
//               'fa-television' => '&#xf26c; fa-television',
//               'fa-tencent-weibo' => '&#xf1d5; fa-tencent-weibo',
//               'fa-terminal' => '&#xf120; fa-terminal',
//               'fa-text-height' => '&#xf034; fa-text-height',
//               'fa-text-width' => '&#xf035; fa-text-width',
//               'fa-th' => '&#xf00a; fa-th',
//               'fa-th-large' => '&#xf009; fa-th-large',
//               'fa-th-list' => '&#xf00b; fa-th-list',
//               'fa-thumb-tack' => '&#xf08d; fa-thumb-tack',
//               'fa-thumbs-down' => '&#xf165; fa-thumbs-down',
//               'fa-thumbs-o-down' => '&#xf088; fa-thumbs-o-down',
//               'fa-thumbs-o-up' => '&#xf087; fa-thumbs-o-up',
//               'fa-thumbs-up' => '&#xf164; fa-thumbs-up',
//               'fa-ticket' => '&#xf145; fa-ticket',
//               'fa-times' => '&#xf00d; fa-times',
//               'fa-times-circle' => '&#xf057; fa-times-circle',
//               'fa-times-circle-o' => '&#xf05c; fa-times-circle-o',
//               'fa-tint' => '&#xf043; fa-tint',
//               'fa-toggle-off' => '&#xf204; fa-toggle-off',
//               'fa-toggle-on' => '&#xf205; fa-toggle-on',
//               'fa-trademark' => '&#xf25c; fa-trademark',
//               'fa-train' => '&#xf238; fa-train',
//               'fa-transgender' => '&#xf224; fa-transgender',
//               'fa-transgender-alt' => '&#xf225; fa-transgender-alt',
//               'fa-trash' => '&#xf1f8; fa-trash',
//               'fa-trash-o' => '&#xf014; fa-trash-o',
//               'fa-tree' => '&#xf1bb; fa-tree',
//               'fa-trello' => '&#xf181; fa-trello',
//               'fa-tripadvisor' => '&#xf262; fa-tripadvisor',
//               'fa-trophy' => '&#xf091; fa-trophy',
//               'fa-truck' => '&#xf0d1; fa-truck',
//               'fa-try' => '&#xf195; fa-try',
//               'fa-tty' => '&#xf1e4; fa-tty',
//               'fa-tumblr' => '&#xf173; fa-tumblr',
//               'fa-tumblr-square' => '&#xf174; fa-tumblr-square',
//               'fa-twitch' => '&#xf1e8; fa-twitch',
//               'fa-umbrella' => '&#xf0e9; fa-umbrella',
//               'fa-underline' => '&#xf0cd; fa-underline',
//               'fa-undo' => '&#xf0e2; fa-undo',
//               'fa-university' => '&#xf19c; fa-university',
//               'fa-unlock' => '&#xf09c; fa-unlock',
//               'fa-unlock-alt' => '&#xf13e; fa-unlock-alt',
//               'fa-upload' => '&#xf093; fa-upload',
//               'fa-usb' => '&#xf287; fa-usb',
//               'fa-usd' => '&#xf155; fa-usd',
//               'fa-user' => '&#xf007; fa-user',
//               'fa-user-md' => '&#xf0f0; fa-user-md',
//               'fa-user-plus' => '&#xf234; fa-user-plus',
//               'fa-user-secret' => '&#xf21b; fa-user-secret',
//               'fa-user-times' => '&#xf235; fa-user-times',
//               'fa-users' => '&#xf0c0; fa-users',
//               'fa-venus' => '&#xf221; fa-venus',
//               'fa-venus-double' => '&#xf226; fa-venus-double',
//               'fa-venus-mars' => '&#xf228; fa-venus-mars',
//               'fa-viacoin' => '&#xf237; fa-viacoin',
//               'fa-video-camera' => '&#xf03d; fa-video-camera',
//               'fa-vimeo' => '&#xf27d; fa-vimeo',
//               'fa-vimeo-square' => '&#xf194; fa-vimeo-square',
//               'fa-vine' => '&#xf1ca; fa-vine',
//               'fa-vk' => '&#xf189; fa-vk',
//               'fa-volume-down' => '&#xf027; fa-volume-down',
//               'fa-volume-off' => '&#xf026; fa-volume-off',
//               'fa-volume-up' => '&#xf028; fa-volume-up',
//               'fa-weibo' => '&#xf18a; fa-weibo',
//               'fa-weixin' => '&#xf1d7; fa-weixin',
//               'fa-whatsapp' => '&#xf232; fa-whatsapp',
//               'fa-wheelchair' => '&#xf193; fa-wheelchair',
//               'fa-wifi' => '&#xf1eb; fa-wifi',
//               'fa-wikipedia-w' => '&#xf266; fa-wikipedia-w',
//               'fa-windows' => '&#xf17a; fa-windows',
//               'fa-wordpress' => '&#xf19a; fa-wordpress',
//               'fa-wrench' => '&#xf0ad; fa-wrench',
//               'fa-xing' => '&#xf168; fa-xing',
//               'fa-xing-square' => '&#xf169; fa-xing-square',
//               'fa-y-combinator' => '&#xf23b; fa-y-combinator',
//               'fa-yahoo' => '&#xf19e; fa-yahoo',
//               'fa-yelp' => '&#xf1e9; fa-yelp',
//               'fa-youtube' => '&#xf167; fa-youtube',
//               'fa-youtube-play' => '&#xf16a; fa-youtube-play',
//               'fa-youtube-square' => '&#xf166; fa-youtube-square',
//             ),
//             'show_preview' => 1,
//             'default_label' => '',
//           ),
//           array(
//             'key' => 'field_56d829f8ce1df',
//             'label' => 'Länkmål',
//             'name' => 'lankmal',
//             'type' => 'text',
//             'instructions' => 'Ange länken till rätt sociala media.',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 75,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Url',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_56d82a2ace1e0',
//         'label' => 'Länkar',
//         'name' => 'footer_lankar_sv',
//         'type' => 'repeater',
//         'instructions' => 'Ange länkar som ska visas i sidfoten med en rubrik, text samt ett länkmål. <br>
//   Max 3 st',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'collapsed' => '',
//         'min' => 0,
//         'max' => 4,
//         'layout' => 'table',
//         'button_label' => 'Lägg till länk',
//         'sub_fields' => array(
//           array(
//             'key' => 'field_56d82a3ece1e1',
//             'label' => 'Rubrik',
//             'name' => 'rubrik',
//             'type' => 'text',
//             'instructions' => 'Ange en passande rubrik.',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 25,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Rubrik',
//             'prepend' => '',
//             'append' => '',
//             'maxlength' => '',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_56d82a4dce1e2',
//             'label' => 'Beskrivning',
//             'name' => 'beskrivning',
//             'type' => 'textarea',
//             'instructions' => 'Ange en passande text. ',
//             'required' => 1,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 25,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => '',
//             'maxlength' => '',
//             'rows' => 4,
//             'new_lines' => 'wpautop',
//             'readonly' => 0,
//             'disabled' => 0,
//           ),
//           array(
//             'key' => 'field_56d82a69ce1e3',
//             'label' => 'Länkmål',
//             'name' => 'lankmal',
//             'type' => 'page_link',
//             'instructions' => 'Ange ett länkmål.',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 25,
//               'class' => '',
//               'id' => '',
//             ),
//             'post_type' => array(
//               0 => 'page',
//             ),
//             'taxonomy' => array(
//             ),
//             'allow_null' => 0,
//             'multiple' => 0,
//             'allow_archives' => 1,
//           ),
//           array(
//             'key' => 'field_56d82b6167422',
//             'label' => 'Externt länkmål',
//             'name' => 'externt_lankmal',
//             'type' => 'url',
//             'instructions' => 'Länka till externt länkmål.',
//             'required' => 0,
//             'conditional_logic' => 0,
//             'wrapper' => array(
//               'width' => 25,
//               'class' => '',
//               'id' => '',
//             ),
//             'default_value' => '',
//             'placeholder' => 'Url',
//           ),
//         ),
//       ),
//       array(
//         'key' => 'field_588b12e9453b2',
//         'label' => '',
//         'name' => '',
//         'type' => 'message',
//         'instructions' => '',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '',
//           'class' => '',
//           'id' => '',
//         ),
//         'message' => 'Dessa två nedanstående fält är länken som visas allra längst ner på sidan "Om swedac.se".',
//         'esc_html' => 0,
//         'new_lines' => 'wpautop',
//       ),
//       array(
//         'key' => 'field_588b119a453b0',
//         'label' => 'Om Swedac',
//         'name' => 'om_swedac_namn_footer',
//         'type' => 'text',
//         'instructions' => 'Ange namn',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '50',
//           'class' => '',
//           'id' => '',
//         ),
//         'default_value' => 'Om Swedac.se',
//         'maxlength' => '',
//         'placeholder' => 'Om Swedac.se',
//         'prepend' => '',
//         'append' => '',
//       ),
//       array(
//         'key' => 'field_588b12be453b1',
//         'label' => 'Om Swedac (länk)',
//         'name' => 'om_swedac_lank_footer',
//         'type' => 'page_link',
//         'instructions' => 'Ange länkmål.',
//         'required' => 0,
//         'conditional_logic' => 0,
//         'wrapper' => array(
//           'width' => '50',
//           'class' => '',
//           'id' => '',
//         ),
//         'post_type' => array(
//           0 => 'page',
//         ),
//         'taxonomy' => array(
//         ),
//         'allow_null' => 0,
//         'multiple' => 0,
//         'allow_archives' => 1,
//       ),
//     ),
//     'location' => array(
//       array(
//         array(
//           'param' => 'page_template',
//           'operator' => '==',
//           'value' => 'page-home.php',
//         ),
//       ),
//     ),
//     'menu_order' => 0,
//     'position' => 'normal',
//     'style' => 'default',
//     'label_placement' => 'top',
//     'instruction_placement' => 'label',
//     'hide_on_screen' => '',
//     'active' => true,
//     'description' => '',
//   ));
  
//   endif;