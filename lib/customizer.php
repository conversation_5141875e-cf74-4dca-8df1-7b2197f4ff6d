<?php

namespace Roots\Sage\Customizer;

use Roots\Sage\Assets;

/**
 * Add postMessage support
 */
function customize_register($wp_customize) {
  $wp_customize->get_setting('blogname')->transport = 'postMessage';
}
add_action('customize_register', __NAMESPACE__ . '\\customize_register');

/**
 * Customizer JS for login screen.
 */
function login_screen_js() {
  wp_enqueue_script('sage/customizer', Assets\asset_path('scripts/login-screen/login-dist.js'), ['customize-preview'], null, true);
}
add_action('login_head', __NAMESPACE__ . '\\login_screen_js');


/**
 * Customizer JS for admin
 */
function admin_js() {
  wp_enqueue_script('sage/customizer', Assets\asset_path('../dist/js/admin/admin-dist.js'), false, null);
}
add_action('admin_enqueue_scripts', __NAMESPACE__ . '\\admin_js');


// Update CSS within in Admin
function admin_style() {
  wp_enqueue_style('admin-styles', Assets\asset_path('../dist/css/admin/admin.css'), false, null);
}
add_action('admin_enqueue_scripts', __NAMESPACE__ . '\\admin_style');
add_action('login_head', __NAMESPACE__ . '\\admin_style');


//Lägger Yoast längst ner på sidor och inlägg
add_filter( 'wpseo_metabox_prio', function() { return 'low';});
