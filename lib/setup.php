<?php
namespace Roots\Sage\Setup;

use Roots\Sage\Assets;

/**
 * Theme setup
 */
function setup()
{
  add_theme_support('yoast-seo-breadcrumbs');

  // Make theme available for translation
  load_theme_textdomain('sage', get_template_directory() . '/lang');

  // Enable plugins to manage the document title
  add_theme_support('title-tag');

  // Register wp_nav_menu() menus
  register_nav_menus([
    'mainNav' => __('Huvudnavigation', 'sage'),
    'quickLinks' => __('Quick links (startpage)', 'sage'),
    'topNav_right' => __('Top (Right) navigation', 'sage'),
    'footerNav' => __('Footer navigation', 'sage')
  ]);

  // Enable post thumbnails
  add_theme_support('post-thumbnails');
  add_image_size('large', 700, '', true);
  add_image_size('medium', 250, '', true);
  add_image_size('small', 120, '', true);

  //Custom images sizes
  add_image_size('ledning', 300, 449, true); // ledning
  add_image_size('puff_news', 320, 220, true); // Medium Thumbnail
  add_image_size('swedac_magasin', 500, 500, true); // Medium Thumbnail
  add_image_size('swedac_magasin_single', 1920, 700, true); // Medium Thumbnail
  add_image_size('news_image', 680, 340, true); // Large Thumbnail
  add_image_size('snabbval_template_2', 680, 180, true); // Large Thumbnail
  add_image_size('page_thumbnail', 680, 600, true); // Large Thumbnail
  add_image_size('frontpage-slider-size', 1920, 500, true); // Custom Thumbnail

  /*------------------------------------*\
    ACF-OPTIONS
  \*------------------------------------*/
  if (function_exists('acf_add_options_page')) {
    // lägg till menyalternativ i Admin
    $parent = acf_add_options_page(array(
      'page_title' => 'Inställningar för webbplats',
      'menu_title' => 'Funktionalitet',
      'redirect' => false
    ));

    // Lägg till undersida till ovanstående
    acf_add_options_sub_page(array(
      'page_title' => 'Rubriker från CRM',
      'menu_title' => 'Rubriker från CRM',
      'parent_slug' => $parent['menu_slug'],
    ));

    acf_add_options_page(array(
      'page_title' => 'Epost inställningar',
      'menu_title' => 'Epost inställningar',
      'menu_slug' => 'custom-email-settings',
      'icon_url' => 'dashicons-email-alt',
      'post_id' => 'epost',
      'capability' => 'edit_posts',
      'redirect' => false
    ));
  }


  function removeCategoryDescrption($columns)
  {
    // only edit the columns on the current taxonomy
    if (!isset($_GET['taxonomy']) || $_GET['taxonomy'] != 'category')
      return $columns;

    // unset the description columns
    if ($posts = $columns['description']) {
      unset($columns['description']);
    }

    return $columns;
  }
  add_filter('manage_edit-category_columns', __NAMESPACE__ . '\\removeCategoryDescrption');

  // Enable HTML5 markup support
  add_theme_support('html5', ['caption', 'comment-form', 'comment-list', 'gallery', 'search-form']);

  // Use main stylesheet for visual editor
  // To add custom styles edit /assets/styles/layouts/_tinymce.scss
  // add_editor_style(Assets\asset_path('styles/structure.css'));
}
add_action('after_setup_theme', __NAMESPACE__ . '\\setup');

/**
 * Determine which pages should NOT display the sidebar
 */
function display_sidebar()
{
  static $display;

  isset($display) || $display = in_array(true, [
    // The sidebar will NOT be displayed if ANY of the following return true.
    // @link https://codex.wordpress.org/Conditional_Tags
    is_page_template('page-template-with-sidebar.php'),
    is_page_template('page-template-with-sidebar-new.php'),
    is_page_template('page-template-7-bloggen.php'),
  ]);

  return apply_filters('sage/display_sidebar', $display);
}

// Google API key for ACF Google Maps
function my_acf_google_map_api($api)
{
  $api['key'] = 'AIzaSyC2qn3hB4j4bm9JL8sk5VCuiToExXMbEqA';
  return $api;
}
add_filter('acf/fields/google_map/api', __NAMESPACE__ . '\\my_acf_google_map_api');

/**
 * Theme assets
 */
function assets()
{
  // CSS
  wp_enqueue_style('fonts', get_template_directory_uri() . '/assets/styles/fonts.css', [], null);
  wp_enqueue_style('main-styles', get_template_directory_uri() . '/dist/css/structure.css', [], filemtime(get_template_directory() . '/dist/css/structure.css'), false);

  // JS
  wp_enqueue_script('sage/js', Assets\asset_path('../dist/js/main-dist.js'), ['jquery'], null, true);

  if (is_admin()) {
    wp_enqueue_script('sage/js/admin', Assets\asset_path('../dist/js/admin/admin-dist.js'), [], null);
  }
}
add_action('wp_enqueue_scripts', __NAMESPACE__ . '\\assets', 100);
add_action('login_head', __NAMESPACE__ . '\\assets');

/**
 * Admin assets
 */
function adminAssets()
{
  wp_enqueue_style('admin-fa-styles', get_template_directory_uri() . '/assets/styles/font-awesome.min.css', [], false, false);
}
add_action('admin_enqueue_scripts', __NAMESPACE__ . '\\adminAssets');

add_filter('jpeg_quality', function () {
  return 100;
});
