<?php
/**
 * FriendlyCaptcha verification functionality
 */

/**
 * Verify FriendlyCaptcha response
 * 
 * @param string $captcha_response The response from the FriendlyCaptcha widget
 * @return bool True if verification was successful, false otherwise
 */
function swedac_verify_friendly_captcha($captcha_response)
{
    // Return early if no response
    if (empty($captcha_response)) {
        $log_message = 'FriendlyCaptcha verification failed: No response provided';
        swedac_log_to_file($log_message);
        return false;
    }

    // API key should be stored in wp-config.php or as an option
    $api_key = FRIENDLY_CAPTCHA_API_KEY;
    $site_key = FRIENDLY_CAPTCHA_SITEKEY; // This should match your JavaScript sitekey

    // Prepare the request
    $args = array(
        'body' => json_encode(array(
            'response' => $captcha_response,
            'sitekey' => $site_key
        )),
        'headers' => array(
            'Content-Type' => 'application/json',
            'X-API-Key' => $api_key
        ),
        'timeout' => 30
    );

    // Make the request to the FriendlyCaptcha API
    $response = wp_remote_post('https://global.frcapi.com/api/v2/captcha/siteverify', $args);

    // Check for errors
    if (is_wp_error($response)) {
        $log_message = 'FriendlyCaptcha verification failed: ' . $response->get_error_message();
        swedac_log_to_file($log_message);
        return false;
    }

    // Decode the response
    $body = wp_remote_retrieve_body($response);
    $result = json_decode($body, true);

    // Check if verification was successful
    if (isset($result['success']) && $result['success'] === true) {
        return true;
    }

    // Log error details if available
    if (isset($result['error-codes'])) {
        $log_message = 'FriendlyCaptcha verification failed: ' . implode(', ', $result['error-codes']);
        swedac_log_to_file($log_message);
    }

    return false;
}

/**
 * Hook into form submission processes to verify captcha
 */
function swedac_init_ninja_forms_captcha()
{
    // Add a filter to process the Ninja Forms submission data
    add_filter('ninja_forms_submit_data', 'swedac_ninja_forms_verify_captcha');
}
add_action('init', 'swedac_init_ninja_forms_captcha');

/**
 * Process Ninja Forms submission and verify the FriendlyCaptcha response
 *
 * @param array $form_data The form data being submitted
 * @return array The potentially modified form data
 */
function swedac_ninja_forms_verify_captcha($form_data)
{
    // Check if the form has a captcha response in the extra data or formData
    $captcha_response = null;

    // Check in the extra data - this is where we receive it based on the provided JS code


    if (isset($form_data['extra']) && isset($form_data['extra']['frCaptcha'])) {
        $captcha_response = $form_data['extra']['frCaptcha'];
        swedac_log_to_file(json_encode($captcha_response));
    }
    // Also check in the formData (as a fallback)
    else if (isset($form_data['formData']) && isset($form_data['formData']['frCaptcha'])) {
        $captcha_response = $form_data['formData']['frCaptcha'];
    }
    // Check directly in the form_data array as a last resort
    else if (isset($form_data['frc_captcha_response'])) {
        $captcha_response = $form_data['frc_captcha_response'];
    } else if (isset($form_data['frc-captcha-response'])) {
        $captcha_response = $form_data['frc-captcha-response'];
    }

    // If we found a captcha response, verify it
    if ($captcha_response) {
        $is_valid = swedac_verify_friendly_captcha($captcha_response);

        if (!$is_valid) {
            // Add error message to form
            if (!isset($form_data['errors']['fields'])) {
                $form_data['errors']['fields'] = array();
            }
            $form_data['errors']['form']['captcha'] = __('CAPTCHA validation failed. Please try again.', 'swedac_theme');

            // Log the validation failure
            $log_message = 'FriendlyCaptcha validation failed for Ninja Forms submission';
            swedac_log_to_file($log_message);
        } else {
            // Log successful validation
            $log_message = 'FriendlyCaptcha validation successful for Ninja Forms submission';
            swedac_log_to_file($log_message);
        }
    } else {
        // No captcha response found, add error
        if (!isset($form_data['errors']['fields'])) {
            $form_data['errors']['fields'] = array();
        }
        $form_data['errors']['form']['captcha'] = __('CAPTCHA validation is required. Please refresh the page and try again.', 'swedac_theme');
        $log_message = 'No FriendlyCaptcha response found in Ninja Forms submission';
        swedac_log_to_file($log_message);
    }

    return $form_data;
}

/**
 * THE FOLLOWING FUNCTION IS UNNECESSARY BUT MIGHT BE NEEDED IN THE FUTURE IF WE REFACTOR THE SENDER.PHP FILE
 * Handle FriendlyCaptcha verification for all forms
 * This catches the POST data directly when it's not properly processed through Ninja Forms 
 */
// function swedac_global_captcha_check()
// {
//     // Only run this check if the current script is not /sender.php
//     $current_script = isset($_SERVER['SCRIPT_NAME']) ? basename($_SERVER['SCRIPT_NAME']) : '';
//     if ($current_script !== 'sender.php') {
//         // Check if this is a form submission with a FriendlyCaptcha response
//         if (isset($_POST['frc-captcha-response'])) {
//             $captcha_response = sanitize_text_field($_POST['frc-captcha-response']);
//             $is_valid = swedac_verify_friendly_captcha($captcha_response);

//             // Store validation result in session for forms to access
//             if (!session_id()) {
//                 session_start();
//             }
//             $_SESSION['frc_captcha_valid'] = $is_valid;

//             // Optionally block submission if validation fails
//             if (!$is_valid && !wp_doing_ajax()) {
//                 $log_message = 'CAPTCHA validation failed. Please go back and try again.';
//                 wp_die(__($log_message, 'swedac_theme'));
//                 swedac_log_to_file($log_message);
//             }
//         }
//     }
// }
// add_action('init', 'swedac_global_captcha_check', 1);  // Priority 1 to run early

/**
 * Custom logging function to write to a file in the theme directory.
 *
 * @param string $log_message The message to log.
 */
function swedac_log_to_file($log_message)
{
    $log_file = get_stylesheet_directory() . '/friendly_captcha_log.txt'; // Path to the log file
    $timestamp = date('Y-m-d H:i:s');
    $message = "[$timestamp] " . $log_message . "\n";

    // Use error_log to append to the file
    error_log($message, 3, $log_file);
}
