<?php

use Roots\Sage\Setup;
use Roots\Sage\Wrapper;

?>
<!doctype html>
<html <?php language_attributes(); ?>>
    <?php get_template_part('templates/head'); ?>
    <body <?php body_class(); ?>>
        <!--[if IE]>
	        <div class="alert-ie-appeared">
	            <?php _e('Du använder <strong>Internet Explorer</strong> som webbläsare. Internet Explorer har från och med januari 2016 slutat få säkerhetsuppdateringar utav Microsoft Corporation. Så för att uppnå den bästa upplevelsen av vår webbplats, var god uppdatera till en annan <a href="http://browsehappy.com/" target="_blank">webbläsare</a>.'); ?>
	        </div>
    	<![endif]-->

	    <div id="main_wrapper">
	        <div class="" role="document">
	            <?php do_action('get_header'); get_template_part('templates/header');?>
				
				<section>
		            <div id="page-header-parent">
						<div class="title">
							<div class="wrapper">
								<?php
								function wps_parent_post()
								{
									global $post;

									if ($post->post_parent){
										$ancestors=get_post_ancestors($post->ID);
										$root=count($ancestors)-1;
										$parent = $ancestors[$root];
									} elseif ($post->post_ancestors){
										$ancestors=get_post_ancestors($post->ID);
										$root=count($ancestors)-1;
										$parent = $ancestors[$root];
									} else {
										$parent = $post->ID;
									}

									if ($post->ID != $parent) {
									echo get_page(array_pop(get_post_ancestors($post->ID)))->post_title;
									}
								}
								?>
								<span class="title-page">
									<?php wps_parent_post(); ?>
								</span>

							</div>
						</div>
						<?php cc_breadcrumb_renderer(true); ?>
					</div>

					<div class="wrapper">
						<?php
							// Slideshow / Hero

							?>
							<div id="slider">
							<?php
							$slides = get_field('slideshow')['slider_hero'];
							if( !empty( $slides ) ){
								$number_of_slides = count($slides);
							}else{
								$number_of_slides = 0;
							}
							
							$slideNumber = 1;
							?>
							<div id="sliderControlls" class="carousel slide alt-colors" data-ride="false" data-interval="false">
								<div class="carousel-inner">
									<?php
									foreach ($slides as $slide) {
									?>
									<div class="carousel-item <?= $slideNumber == 1 ? "active" : "" ?> ">
									<div class="slideCover"></div>
										<img class="d-block w-100 <?php if( isset( $slide[ 'image_mobile' ] ) && !empty($slide['image_mobile'])){echo "desktop";}?>" src="<?php echo $slide['image']['sizes']['frontpage-slider-size']; ?>" alt="<?php echo $slide['image']['alt']; ?>">
										<?php if( isset( $slide[ 'image_mobile' ] ) && !empty( $slide[ 'image_mobile' ] ) ): ?>
											<img class="d-block w-100 mobile" src="<?php echo $slide['image_mobile']['sizes']['frontpage-slider-size']; ?>" alt="<?php echo $slide['image']['alt']; ?>">
										<?php endif; ?>
										<div class="wrapper">
											<div class="info">
												<h2><?= $slide['title']; ?></h2>

												<div class="content">
													<?= $slide['description']; ?>
													<?php
														if((isset($slide['button_link']) && isset($slide['button_text'])) && (!empty($slide['button_link']) && !empty($slide['button_text']))){
															?>
															<br>
															<div class="link">
																<a href="<?php echo $slide['button_link'] ?>" class="button"><?php echo $slide['button_text'] ?></a>
															</div>
															<?php
														}
													?>
												</div>
											</div>
										</div>
										<?php if(isset($slide['copyright']) && !empty($slide['copyright'])) { ?>
											<div class="carousel-copyright">
												<div><?php echo wp_kses_post($slide['copyright']); ?></div>
											</div>
										<?php }	?>
									</div>
									<?php	
									$slideNumber++;
									}
									?>
								</div>
								<?php if($number_of_slides > 1): ?>
									<a class="carousel-control-prev" href="#sliderControlls" role="button" data-slide="prev">
										<span class="carousel-control-prev-icon" aria-hidden="true"></span>
										<span class="sr-only">Previous</span>
									</a>
									<a class="carousel-control-next" href="#sliderControlls" role="button" data-slide="next">
										<span class="carousel-control-next-icon" aria-hidden="true"></span>
										<span class="sr-only">Next</span>
									</a>
								<?php endif; ?>
							</div>
						</div>

		           		<div class="content row">
			           	    <div class="main">
			           	        <?php include Wrapper\template_path(); ?>
			           	    </div>
			           	    <?php if (Setup\display_sidebar()) : ?>
			           	        <div class="sidebar">
			           	            <?php include Wrapper\sidebar_path(); ?>
			           	        </div>
			           	    <?php endif; ?>
			           	</div>
		           	</div>
				</section>													   
	            <?php do_action('get_footer');  get_template_part('templates/footer'); wp_footer(); ?>
	        	<br class="clear">
	        </div>
	    </div>

    </body>
</html>
