<?php
$fritext = isset($_GET['fritext']) ? $_GET['fritext'] : '';
$doc_nr = isset($_GET['s']) ? $_GET['s'] : '';
$omraden = isset($_GET['omraden']) ? $_GET['omraden'] : '';
$doc_type = isset($_GET['certifikat_type']) ? $_GET['certifikat_type'] : '';
?>
<div id="sok_foreskriver_dokument_page">
    <div class="wrapper">
        <div class="title">
            <img alt="Sök" data-src="<?php echo get_template_directory_uri(); ?>/assets/images/icon-search.png">

            <span><h2><?=__('Sök Certifikat och Godkännanden', 'certificates')?></h2></span>
        </div>
        <form id="searchform_foreskrifter_dokument" action="<?php echo get_site_url(); ?>" method="get">
                <div class="row">
                    <input type="search" class="search-field" placeholder="<?php if($fritext) {echo __("Du sökte på ", 'certificates'). $fritext;} else {echo __("Fritext", 'certificates');} ?>" value="" name="fritext" title="<?php if($fritext) {echo __("Du sökte på ". $fritext, 'certificates');} else {echo __("Fritext", 'certificates');} ?>">
                </div>
                <div class="row">
                    <input type="text" class="search-field" placeholder="<?php if($doc_nr) {echo __("Du sökte på ", 'certificates'). $doc_nr;} else {echo __("Dokumentnummer", 'certificates');} ?>" name="s" title="<?php if($doc_nr) {echo __("Du sökte på ", 'certificates'). $doc_nr;} else {echo __("Dokumentnummer", 'certificates');} ?>">
                </div>
            <div class="row">
                <input type="hidden" id="hiddenOmrade" value="<?php echo $omraden; ?>">
                    <?php $all_omraden = array(
                        'post_type' 		=> 'amnesomraden',
                        'posts_per_page' 	=> -1,
                        'orderby'			=> 'title',
                        'order' 			=> 'ASC',
                        'tax_query'         => array(
                            array(
                                'taxonomy' 	=> 'amnesomraden_categories',
                                'field' 	=> 'slug',
                                'terms' 	=> 'reglerad-matteknik'
                            )
                        )
                    );

                    $query_all = new WP_Query($all_omraden);
                    if($query_all->have_posts()): ?>
                        <label class="label_select" for="all_omraden_select"><?= __('Område', 'certificates')?></label>
                        <select id="all_omraden_select" name="omraden">
                            <option selected value="" ><?= __('Välj område', 'certificates')?></option>
                            <?php while($query_all->have_posts()):$query_all->the_post();
                                $currentTitle = get_the_title();
                            ?>
                               <option value="<?php the_ID(); ?>"><?php the_title(); ?></option>
                            <?php endwhile; ?>
                        </select>
                        <?php else: ?>

                        <label class="label_select" for="all_omraden_select"><?= __('Område', 'certificates') ?></label>
                        <select id="all_omraden_select" name="omraden">
                            <option selected disabled><?= __('Inga områden finns', 'certificates')?></option>
                            <?php while($query_all->have_posts()):$query_all->the_post(); ?>
                               <option value="<?php the_title(); ?>"><?php the_title(); ?></option>
                            <?php endwhile; ?>
                        </select>
                    <?php endif; wp_reset_query(); ?>
            </div>
            <div class="row">
                <label class="label_select" for="typ_av_certifikat"><?= __('Certifikattyp', 'certificates')?></label>
                <select id="typ_av_certifikat" name="certifikat_type">
                    <option value="" selected><?= __('Alla typer av certifikat', 'certificates'); ?></option>
                    <option value="cottt" <?php if($_GET['certifikat_type'] == "cottt") {echo "selected='selected'";} ?>><?= __('Certifikat om tillsatsanordningar till taxametrar', 'certificates'); ?></option>
                    <option value="cumtawg" <?php if($_GET['certifikat_type'] == "cumtawg") {echo "selected='selected'";} ?>><?= __('Certifikat utfärdade med stöd av WELMEC Guide 8.8', 'certificates'); ?></option>
                    <option value="eoemk" <?php if($_GET['certifikat_type'] == "eoemk") {echo "selected='selected'";} ?>><?= __('EU-typintyg och EU-intyg om konstruktionskontroll (MID 2014/32/EU)', 'certificates'); ?></option>
                    <option value="gakmd" <?php if($_GET['certifikat_type'] == "gakmd") {echo "selected='selected'";} ?>><?= __('Godkännanden av kvalitetssystem, modul D', 'certificates'); ?></option>
                </select>
            </div>

            <?php if(ICL_LANGUAGE_CODE=='sv'): ?>
                <input name="site_section" type="hidden" value="certificate_pt" />
                <input type="hidden" name="lang" value="sv">
                <input type="hidden" name="post_type" value="dokument" />
                <button type="submit" class="search-submit"><svg fill="#fff" height="20" viewBox="0 0 461.516 461.516" width="20"><path d="m185.746 371.332c41.251.001 81.322-13.762 113.866-39.11l122.778 122.778c9.172 8.858 23.787 8.604 32.645-.568 8.641-8.947 8.641-23.131 0-32.077l-122.778-122.778c62.899-80.968 48.252-197.595-32.716-260.494s-197.594-48.252-260.493 32.716-48.252 197.595 32.716 260.494c32.597 25.323 72.704 39.06 113.982 39.039zm-98.651-284.273c54.484-54.485 142.82-54.486 197.305-.002s54.486 142.82.002 197.305-142.82 54.486-197.305.002c-.001-.001-.001-.001-.002-.002-54.484-54.087-54.805-142.101-.718-196.585.239-.24.478-.479.718-.718z"></path></svg>Sök</button>
            <?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
                <input name="site_section" type="hidden" value="certificate_pt" />
                <input type="hidden" name="lang" value="en">
                <input type="hidden" name="post_type" value="document" />
                <button type="submit" class="search-submit"><svg fill="#fff" height="20" viewBox="0 0 461.516 461.516" width="20"><path d="m185.746 371.332c41.251.001 81.322-13.762 113.866-39.11l122.778 122.778c9.172 8.858 23.787 8.604 32.645-.568 8.641-8.947 8.641-23.131 0-32.077l-122.778-122.778c62.899-80.968 48.252-197.595-32.716-260.494s-197.594-48.252-260.493 32.716-48.252 197.595 32.716 260.494c32.597 25.323 72.704 39.06 113.982 39.039zm-98.651-284.273c54.484-54.485 142.82-54.486 197.305-.002s54.486 142.82.002 197.305-142.82 54.486-197.305.002c-.001-.001-.001-.001-.002-.002-54.484-54.087-54.805-142.101-.718-196.585.239-.24.478-.479.718-.718z"></path></svg>Search</button>
            <?php endif;?>
        </form>
    </div>
</div>

<div class="wrapper">
    <div class="search_result_certifikat">
        <div class="senaste_dokument_result">
                <h2><?= __('Certifikat och Godkännanden', 'certificates')?></h2>
            <?php

                if(empty($_GET["certifikat_type"])) {

                    $doc_type = "cottt, cumtawg, eoemk, gakmd";

                    $fritext = isset($_GET['fritext']) ? $_GET['fritext'] : '';
                    $doc_nr = isset($_GET['s']) ? $_GET['s'] : '';
                    $omraden = isset($_GET['omraden']) ? $_GET['omraden'] : '';

                    $metaDataOver = array();
                    $metaDataUnder = array();

                    if(!empty($_GET["s"])){
                        $metaDataOver["beteckning"] = array(
                            'key'     => 'crm_cert_dokumentbeteckning',
                            'value'   => $doc_nr,
                            'compare' => 'LIKE',
                        );
                    }
                    if(isset($_GET["fritext"])){
                        if(!empty($_GET['fritext'])) {
                            if(empty($_GET["s"])) {
                                $doc_nr = $_GET["fritext"];
                                $metaDataUnder["beteckning"] = array(
                                    'key'     => 'crm_cert_dokumentbeteckning',
                                    'value'   => $doc_nr,
                                    'compare' => 'LIKE',
                                );

                            }
                            $metaDataUnder["fritext"] = array(
                                'key'     => 'crm_cert_title',
                                'value'   => $fritext,
                                'compare' => 'LIKE',
                            );
                            $metaDataUnder['innehall'] = array(
                                'key' => 'crm_cert_description',
                                'value' => $fritext,
                                'compare' => 'LIKE',
                            );
                        }
                    }
                    if(isset($_GET["certifikat_type"])){
                        $metaDataOver["certifikat_type"] =    array(
                            'key'     => 'crm_cert_type',
                            'value'   => array('cottt', 'cumtawg', 'eoemk', 'gakmd'),
                            'compare' => 'IN',
                        );
                    }

                    if(isset($_GET['omraden'])) {
                        $metaDataOver["omraden"] =    array(
                            'key'     => 'crm_relates_to',
                            'value'   => $omraden,
                            'compare' => 'LIKE',
                        );
                    }
                    $metaDataUnder = array_merge(array('relation' => 'OR'), $metaDataUnder);

                    $metaquery = array( 'meta_query' => array(
                        'relation' => 'AND',
                        $metaDataOver,
                        $metaDataUnder,
                    ));


                    $pt_doc_for_args = array(
                        'posts_per_page' 	=> -1,
                        'post_type'     	=> 'certifikat',
                        's'					=> array($fritext, $doc_nr),
                        'meta_query' => $metaquery,
                    );
                } else {

                    $fritext = isset($_GET['fritext']) ? $_GET['fritext'] : '';
                    $doc_nr = isset($_GET['s']) ? $_GET['s'] : '';
                    $omraden = isset($_GET['omraden']) ? $_GET['omraden'] : '';
                    $doc_type = isset($_GET['certifikat_type']) ? $_GET['certifikat_type'] : '';
                    $stafs_upphavd = false;
                    
                    $metaDataOver = array();
                    $metaDataUnder = array();

                    if(!empty($_GET["s"])){
                        $metaDataOver["beteckning"] = array(
                            'key'     => 'crm_cert_dokumentbeteckning',
                            'value'   => $doc_nr,
                            'compare' => 'LIKE',
                        );
                    }
                    if(isset($_GET["fritext"])){
                        if(!empty($_GET['fritext'])) {
                            if(empty($_GET["s"])) {
                                $doc_nr = $_GET["fritext"];
                                $metaDataUnder["beteckning"] = array(
                                    'key'     => 'crm_cert_dokumentbeteckning',
                                    'value'   => $doc_nr,
                                    'compare' => 'LIKE',
                                );

                            }
                            $metaDataUnder["fritext"] = array(
                                'key'     => 'crm_cert_title',
                                'value'   => $fritext,
                                'compare' => 'LIKE',
                            );
                        }



                    }
                    if(isset($_GET["certifikat_type"])){
                        $metaDataOver["certifikat_type"] =    array(
                            'key'     => 'crm_cert_type',
                            'value'   => $doc_type,
                            'compare' => '=',
                        );
                    }
                    if(isset($_GET['omraden']) && $_GET['omraden'] != "Välj område") {
                        $metaDataOver["omraden"] =    array(
                            'key'     => 'crm_relates_to',
                            'value'   => $omraden,
                            'compare' => 'LIKE',
                        );
                    }

                    $metaDataUnder = array_merge(array('relation' => 'OR'), $metaDataUnder);
                    $metaDataOver = array_merge(array('relation' => 'AND'), $metaDataOver);

                    $metaquery = array( 'meta_query' => array(
                        $metaDataOver,
                        $metaDataUnder,
                    ));

                    $pt_doc_for_args = array(
                        'posts_per_page' 	=> -1,
                        'post_type'     	=> 'certifikat',
                        's'					=> array($fritext, $doc_nr),
                        'meta_query' => $metaquery,
                    );

                }

            $pt_doc_for_query = new WP_Query( $pt_doc_for_args );

            $array = array();
            $array1 = array();
            $array2 = array();
            $array3 = array();
             $posts = get_posts($pt_doc_for_args);
                foreach ($posts as $post) {
                    $fileUrl = get_field('crm_cert_doc', $post->ID);
                    if(!empty($fileUrl)) {
                        //Hämtar fält
                        $subject = get_field('crm_cert_dokumentbeteckning',$post->ID);
                        $subject2 = get_field('crm_cert_dokumentbeteckning',$post->ID);

                        // Explodar efter STAFS och : samt hämtar ut årdet
                        // HÄR ÄR DET!



                            //Lägger in allt i en array med värden ovan
                            array_push( $array, array(
                                "title" 	=> get_the_title($post->ID),
                                "id" 		=> get_the_id($post->ID),
                                "link" 		=> get_permalink($post->ID),
                                "crm_cert_doc" 	=> get_field('crm_cert_doc', $post->ID),
                                "crm_cert_type" 	=> get_field('crm_cert_type', $post->ID)
                            ));

                        
                    }
                }



                //Sorterar på Störst > Lägst ex. 1993-2017

                usort($array, 'sortById');
                usort($array1, 'sortById');
                usort($array2, 'sortById');
                usort($array3, 'sortById');

                $array_first = array_merge($array, $array1);
                $array_second = array_merge($array2, $array3);

                $array = array_merge($array_first, $array_second);

                ?>
                <div class="table_wrapper">
                    <table id="cert_result">
                        <thead>
                            <tr>
                                <th><?= __('CERT.NR', 'certificates')?></th>
                                <th><?= __('RUBRIK', 'certificates')?></th>
                                <th><?= __('TYP AV CERTIFIKAT', 'certificates')?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($array as $item) {
                                $title 		= $item['title'];
                                $id 		= $item['id'];
                                $url 		= $item['link'];
                                $doc 		= $item['crm_cert_doc'];
                                switch ($item['crm_cert_type']) {
                                    case 'cottt':
                                        $doc_type = __('Certifikat om tillsatsanordningar till taxametrar', 'certificates');
                                        break;
                                    
                                    case 'cumtawg':
                                        $doc_type = __('Certifikat utfärdade med stöd av WELMEC Guide 8.8', 'certificates');
                                        break;
                                    
                                    case 'eoemk':
                                        $doc_type = __('EU-typintyg och EU-intyg om konstruktionskontroll (MID 2014/32/EU)', 'certificates');
                                        break;

                                    case 'gakmd':
                                        $doc_type = __('Godkännanden av kvalitetssystem, modul D', 'certificates');
                                        break;
                                    
                                    default:
                                        $doc_type = __('Certifikat om tillsatsanordningar till taxametrar', 'certificates');
                                        break;
                                }

                                $doc_bet = get_field('crm_cert_dokumentbeteckning', $id);
                                $doc_bet_2 = substr($doc_bet, 6); // returns "de"

                            ?>
                                <?php if($doc) { ?>
                                    <tr>
                                        <td><?php echo $doc_bet; ?></td>
                                        <td><a href="<?php echo $url; ?>"><?php echo __($title, 'certificates'); ?></a></td>
                                        <td><?php echo $doc_type; ?></td>
                                    </tr>
                                <?php } ?>
                            <?php } ?>
                        </tbody>
                    </table>

                    <?php if(empty($array)) { ?>
                        <div class="wrapper">
                            <div class="alert alert-warning">
                                <strong><?php echo __('Inget hittades', 'certificates'); ?></strong>
                                <em><?php echo sprintf( __( 'Sökningen'), $wp_query->found_posts ); echo '<span class="term">' . get_search_query() .'</span> '. __('gav dessvärre ingen träff i Certifikat och Godkännanden.','certificates'); ?></em>
                            </div>
                        </div>
                    <?php } else { ?>
                        <div class="search_result_buttons">
                            <?php $docCount = count($array); ?>
                            <?php if($docCount < 10) { ?>
                                <small class="smallCerts"><?= __('Visar', 'certificates')?> 1-<?php echo $docCount; ?> <?= __('av', 'certificates') ?> <?php echo $docCount; ?> <?php if($docCount == 1) {echo __(" träff", 'certificates');} else {echo __(" träffar", 'certificates');} ?></small>
                            <?php } else { ?>
                                <small class="smallCerts"><?= __('Visar', 'certificates')?> 1-10 <?= __('av', 'certificates') ?> <?php echo $docCount; ?> <?= __('träffar', 'certificates') ?></small>
                            <?php } ?>
                            <small class="smallCerts_total"><?= __('Visar alla', 'certificates')?> <?php echo $docCount; ?> <?= __('träffar', 'certificates') ?></small>

                            <?php if($docCount > 10) { ?>
                                <span id="loadMoreCerts" class="visa_fler_traffar"><i class="fa fa-spinner fa-spin loading-icon" aria-hidden="true"></i> <?= __('Visa alla', 'certificates') ?></span>
                            <?php } ?>
                        </div>
                    <?php } ?>
                </div>
        </div>
    </div>
</div>
