<?php
$fritext = isset($_GET['fritext']) ? $_GET['fritext'] : '';
$doc_nr = isset($_GET['s']) ? $_GET['s'] : '';
$omraden = isset($_GET['omraden']) ? $_GET['omraden'] : '';
$doc_type = isset($_GET['document_type']) ? $_GET['document_type'] : '';
?>
<div id="sok_foreskriver_dokument_page">
    <div class="wrapper">
        <div class="title">
            <img alt="Sök" data-src="<?php echo get_template_directory_uri(); ?>/assets/images/icon-search.png">

            <?php if(ICL_LANGUAGE_CODE=='sv'): ?>
                <span><h2>Sök föreskrifter & dokument</h2></span>
            <?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
                <span><h2>Search regulations and documents</h2></span>
            <?php endif;?>
        </div>
        <form id="searchform_foreskrifter_dokument" action="<?php echo get_site_url(); ?>" method="get">
            <?php if(ICL_LANGUAGE_CODE=='sv'): ?>
                <div class="row">
                    <input type="search" class="search-field" placeholder="<?php if($fritext) {echo "Du sökte på ". $fritext;} else {echo "Fritext";} ?>" value="" name="fritext" title="<?php if($fritext) {echo "Du sökte på ". $fritext;} else {echo "Fritext";} ?>">
                </div>
                <div class="row">
                    <input type="text" class="search-field" placeholder="<?php if($doc_nr) {echo "Du sökte på ". $doc_nr;} else {echo "Dokumentnummer";} ?>" name="s" title="<?php if($doc_nr) {echo "Du sökte på ". $doc_nr;} else {echo "Dokumentnummer";} ?>">
                </div>
            <?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
                <div class="row">
                    <input type="search" class="search-field" placeholder="<?php if($fritext) {echo "You searched for ". $fritext;} else {echo "Text search";} ?>" value="" name="fritext" title="<?php if($fritext) {echo "You searched for ". $fritext;} else {echo "Text search";} ?>">
                </div>
                <div class="row">
                    <input type="text" class="search-field" placeholder="<?php if($doc_nr) {echo "You searched for ". $doc_nr;} else {echo "Document number";} ?>" name="s" title="<?php if($doc_nr) {echo "You searched for ". $doc_nr;} else {echo "Document number";} ?>">
                </div>
            <?php endif;?>
            <div class="row">
                <input type="hidden" id="hiddenOmrade" value="<?php echo $omraden; ?>">
                <?php if(ICL_LANGUAGE_CODE=='sv'): ?>
                    <?php $all_omraden = array(
                        'post_type' 		=> 'amnesomraden',
                        'posts_per_page' 	=> -1,
                        'orderby'			=> 'title',
                        'order' 			=> 'ASC'
                    );

                    $query_all = new WP_Query($all_omraden);
                    if($query_all->have_posts()): ?>
                        <label class="label_select" for="all_omraden_select">Område</label>
                        <select id="all_omraden_select" name="omraden">
                            <option selected disabled>Välj område</option>
                            <?php while($query_all->have_posts()):$query_all->the_post();
                                $currentTitle = get_the_title();
                            ?>
                               <option value="<?php the_title(); ?>"><?php the_title(); ?></option>
                            <?php endwhile; ?>
                        </select>
                        <?php else: ?>

                        <label class="label_select" for="all_omraden_select">Område</label>
                        <select id="all_omraden_select" name="omraden">
                            <option selected disabled>Inga områden finns</option>
                            <?php while($query_all->have_posts()):$query_all->the_post(); ?>
                               <option value="<?php the_title(); ?>"><?php the_title(); ?></option>
                            <?php endwhile; ?>
                        </select>
                    <?php endif; wp_reset_query(); ?>
                <?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
                    <?php $all_omraden = array(
                        'post_type' 		=> 'amnesomraden',
                        'posts_per_page' 	=> -1,
                        'orderby'			=> 'title',
                        'order' 			=> 'ASC'
                    );

                    $query_all = new WP_Query($all_omraden);
                    if($query_all->have_posts()): ?>
                        <label class="label_select" id="all_omraden_select">Area</label>
                            <select id="all_omraden_select" name="omraden">
                                <option selected disabled>Choose area</option>
                                <?php while($query_all->have_posts()):$query_all->the_post(); ?>
                                   <option value="<?php the_title(); ?>"><?php the_title(); ?></option>
                                <?php endwhile; ?>
                            </select>

                        <?php else: ?>

                        <label class="label_select" for="all_omraden_select">Area</label>
                            <select id="all_omraden_select" name="omraden">
                                <option selected disabled>No areas available</option>
                                <?php while($query_all->have_posts()):$query_all->the_post(); ?>
                                   <option value="<?php the_title(); ?>"><?php the_title(); ?></option>
                                <?php endwhile; ?>
                            </select>
                    <?php endif; wp_reset_query(); ?>
                <?php endif;?>

            </div>
            <div class="row">
                    <?php if(ICL_LANGUAGE_CODE=='sv'): ?>
                        <label class="label_select" for="typ_av_dokument">Dokumenttyp</label>
                        <select id="typ_av_dokument" name="document_type">
                            <option value="" selected>Alla typer av dokument</option>
                            <option value="stafs" <?php if($_GET['document_type'] == "stafs") {echo "selected='selected'";} ?>>STAFS (Gällande föreskrifter)</option>
                            <option value="stafs-upphavd" <?php if($_GET['document_type'] == "stafs-upphavd") {echo "selected='selected'";} ?>>STAFS (Upphävd föreskrift)</option>
                            <option value="doc" <?php if($_GET['document_type'] == "doc") {echo "selected='selected'";} ?>>DOC (Väglednings-/tolkningsdokument)</option>
                            <option value="rep" <?php if($_GET['document_type'] == "rep") {echo "selected='selected'";} ?>>REP (Rapport)</option>
                            <option value="info" <?php if($_GET['document_type'] == "info") {echo "selected='selected'";} ?>>INFO (Informationsmaterial)</option>
                        </select>
                    <?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
                        <label class="label_select" for="typ_av_dokument">Type of document</label>
                        <select id="typ_av_dokument" name="document_type">
                            <option value="" selected>All types of documents</option>
                            <option value="stafs" <?php if($_GET['document_type'] == "stafs") {echo "selected='selected'";} ?>>STAFS (Current regulation)</option>
                            <option value="stafs-upphavd" <?php if($_GET['document_type'] == "stafs-upphavd") {echo "selected='selected'";} ?>>STAFS (Repealed regulation)</option>
                            <option value="doc" <?php if($_GET['document_type'] == "doc") {echo "selected='selected'";} ?>>DOC (Guidance document)</option>
                            <option value="rep" <?php if($_GET['document_type'] == "rep") {echo "selected='selected'";} ?>>REP (Report)</option>
                            <option value="info" <?php if($_GET['document_type'] == "info") {echo "selected='selected'";} ?>>INFO (Information material)</option>
                        </select>
                    <?php endif;?>
            </div>


            <input name="site_section" type="hidden" value="document_pt" />
            <?php if(ICL_LANGUAGE_CODE=='sv'): ?>
                <input type="hidden" name="lang" value="sv">
                <input type="hidden" name="post_type" value="dokument" />
                <button type="submit" class="search-submit" ><svg fill="#fff" height="20" viewBox="0 0 461.516 461.516" width="20"><path d="m185.746 371.332c41.251.001 81.322-13.762 113.866-39.11l122.778 122.778c9.172 8.858 23.787 8.604 32.645-.568 8.641-8.947 8.641-23.131 0-32.077l-122.778-122.778c62.899-80.968 48.252-197.595-32.716-260.494s-197.594-48.252-260.493 32.716-48.252 197.595 32.716 260.494c32.597 25.323 72.704 39.06 113.982 39.039zm-98.651-284.273c54.484-54.485 142.82-54.486 197.305-.002s54.486 142.82.002 197.305-142.82 54.486-197.305.002c-.001-.001-.001-.001-.002-.002-54.484-54.087-54.805-142.101-.718-196.585.239-.24.478-.479.718-.718z"></path></svg><?php echo (ICL_LANGUAGE_CODE == 'en') ? 'Search' : 'Sök'; ?></button>
            <?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
                <input type="hidden" name="lang" value="en">
                <input type="hidden" name="post_type" value="document" />
                <button type="submit" class="search-submit"><svg fill="#fff" height="20" viewBox="0 0 461.516 461.516" width="20"><path d="m185.746 371.332c41.251.001 81.322-13.762 113.866-39.11l122.778 122.778c9.172 8.858 23.787 8.604 32.645-.568 8.641-8.947 8.641-23.131 0-32.077l-122.778-122.778c62.899-80.968 48.252-197.595-32.716-260.494s-197.594-48.252-260.493 32.716-48.252 197.595 32.716 260.494c32.597 25.323 72.704 39.06 113.982 39.039zm-98.651-284.273c54.484-54.485 142.82-54.486 197.305-.002s54.486 142.82.002 197.305-142.82 54.486-197.305.002c-.001-.001-.001-.001-.002-.002-54.484-54.087-54.805-142.101-.718-196.585.239-.24.478-.479.718-.718z"></path></svg><?php echo (ICL_LANGUAGE_CODE == 'en') ? 'Search' : 'Sök'; ?></button>
            <?php endif;?>
        </form>
    </div>
</div>

<div class="wrapper">
    <div class="search_result_dokument">
        <div class="senaste_dokument_result">

            <?php if(ICL_LANGUAGE_CODE=='sv'): ?>
                <h2>Föreskrifter & Dokument fler resultat</h2>
            <?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
                <h2>Regulations & documents</h2>
            <?php endif;?>


            <?php if(ICL_LANGUAGE_CODE=='sv') {

                // filter
                function my_posts_where( $where ) {
					global $wpdb;
                	$where = str_replace("meta_key = 'crm_doc_area_%", "meta_key LIKE 'crm_doc_area_%", $wpdb->remove_placeholder_escape($where));
                	return $where;
                }
                add_filter('posts_where', 'my_posts_where');

                if(empty($doc_type)) {

                    $doc_type = "stafs, rep, doc, info";

                    $fritext = isset($_GET['fritext']) ? $_GET['fritext'] : '';
                    $doc_nr = isset($_GET['s']) ? $_GET['s'] : '';
                    $omraden = isset($_GET['omraden']) ? $_GET['omraden'] : '';
                    $doc_type = isset($_GET['document_type']) ? $_GET['document_type'] : '';

                    $metaDataOver = array();
                    $metaDataUnder = array();

                    if(!empty($_GET["s"])){
                        $metaDataOver["beteckning"] = array(
                            'key'     => 'crm_doc_dokumentbeteckning',
                            'value'   => $doc_nr,
                            'compare' => 'LIKE',
                        );
                    }
                    if(isset($_GET["fritext"])){
                        if(!empty($_GET['fritext'])) {
                            if(empty($_GET["s"])) {
                                $doc_nr = $_GET["fritext"];
                                $metaDataUnder["beteckning"] = array(
                                    'key'     => 'crm_doc_dokumentbeteckning',
                                    'value'   => $doc_nr,
                                    'compare' => 'LIKE',
                                );

                            }
                            $metaDataUnder["fritext"] = array(
                                'key'     => 'crm_doc_title',
                                'value'   => $fritext,
                                'compare' => 'LIKE',
                            );
                            $metaDataUnder['innehall'] = array(
                                'key' => 'crm_doc_text_content',
                                'value' => $fritext,
                                'compare' => 'LIKE',
                            );
                        }
                    }
                    if(isset($_GET["document_type"])){
                        $metaDataOver["document_type"] =    array(
                            'key'     => 'crm_doc_type',
                            'value'   => array('stafs', 'rep', 'doc', 'info'),
                            'compare' => 'IN',
                        );
                    }

                    if(isset($_GET['omraden'])) {
                        $metaDataOver["omraden"] =    array(
                            'key'     => 'crm_doc_area_%_omradesnamn',
                            'value'   => $omraden,
                            'compare' => 'LIKE',
                        );
                    }


                    $metaDataUnder = array_merge(array('relation' => 'OR'), $metaDataUnder);

                    $metaquery = array( 'meta_query' => array(
                        'relation' => 'AND',
                        $metaDataOver,
                        $metaDataUnder,
                    ));


                    $pt_doc_for_args = array(
                        'posts_per_page' 	=> -1,
                        'post_type'     	=> 'dokument',
                        's'					=> array($fritext, $doc_nr),
                        'meta_query' => $metaquery,
                    );
                } else {

                    $fritext = isset($_GET['fritext']) ? $_GET['fritext'] : '';
                    $doc_nr = isset($_GET['s']) ? $_GET['s'] : '';
                    $omraden = isset($_GET['omraden']) ? $_GET['omraden'] : '';
                    $doc_type = isset($_GET['document_type']) ? $_GET['document_type'] : '';

                    if($doc_type == "stafs-upphavd") {
                        $stafs_upphavd = true;
                    } else {
                        $stafs_upphavd = false;
                    }

                    $metaDataOver = array();
                    $metaDataUnder = array();

                    if(!empty($_GET["s"])){
                        $metaDataOver["beteckning"] = array(
                            'key'     => 'crm_doc_dokumentbeteckning',
                            'value'   => $doc_nr,
                            'compare' => 'LIKE',
                        );
                    }
                    if(isset($_GET["fritext"])){
                        if(!empty($_GET['fritext'])) {
                            if(empty($_GET["s"])) {
                                $doc_nr = $_GET["fritext"];
                                $metaDataUnder["beteckning"] = array(
                                    'key'     => 'crm_doc_dokumentbeteckning',
                                    'value'   => $doc_nr,
                                    'compare' => 'LIKE',
                                );

                            }
                            $metaDataUnder["fritext"] = array(
                                'key'     => 'crm_doc_title',
                                'value'   => $fritext,
                                'compare' => 'LIKE',
                            );
                        }



                    }
                    if(isset($_GET["document_type"])){
                        if($doc_type == "stafs-upphavd") {
                            $metaDataOver["document_type"] =    array(
                                'key'     => 'crm_doc_type',
                                'value'   => 'stafs',
                                'compare' => '=',
                            );
                            $metaDataOver["stafs_upphavd"] =    array(
                                'key'     => 'crm_doc_repealed',
                                'value'   => '1',
                                'compare' => '=',
                            );
                        } else {
                            $metaDataOver["document_type"] =    array(
                                'key'     => 'crm_doc_type',
                                'value'   => $doc_type,
                                'compare' => '=',
                            );
                            $metaDataOver["stafs_upphavd"] =    array(
                                'key'     => 'crm_doc_repealed',
                                'value'   => '1',
                                'compare' => '!=',
                            );
                        }

                    }

                    if(isset($_GET['omraden'])) {
                        $metaDataOver["omraden"] =    array(
                            'key'     => 'crm_doc_area_%_omradesnamn',
                            'value'   => $omraden,
                            'compare' => 'LIKE',
                        );
                    }
                    if(!empty($metaDataUnder)){
                        $metaDataUnder = array_merge(array('relation' => 'OR'), $metaDataUnder);
                    }
                    $metaDataOver = array_merge(array('relation' => 'AND'), $metaDataOver);

                    if(!empty($metaDataUnder)){
                        $metaquery = array( 'meta_query' => array(
                            $metaDataOver,
                            $metaDataUnder,
                        ));
                        
                    }else{
                        $metaquery = array( 'meta_query' => array(
                            $metaDataOver
                        ));

                    }

                    $pt_doc_for_args = array(
                        'posts_per_page' 	=> -1,
                        'post_type'     	=> 'dokument',
                        's'					=> array($fritext, $doc_nr),
                        'meta_query' => $metaquery,
                    );

                }

            } elseif(ICL_LANGUAGE_CODE=='en') {
                // filter
                function my_posts_where( $where ) {
					global $wpdb;
                	$where = str_replace("meta_key = 'crm_doc_area_en%", "meta_key LIKE 'crm_doc_area_en%", $wpdb->remove_placeholder_escape($where));
                	return $where;
                }
                add_filter('posts_where', 'my_posts_where');
                if(empty($doc_type)) {

                    $doc_type = "stafs, rep, doc, info";

                    $fritext = isset($_GET['fritext']) ? $_GET['fritext'] : '';
                    $doc_nr = isset($_GET['s']) ? $_GET['s'] : '';
                    $omraden = isset($_GET['omraden']) ? $_GET['omraden'] : '';
                    $doc_type = isset($_GET['document_type']) ? $_GET['document_type'] : '';

                    $metaDataOver = array();
                    $metaDataUnder = array();

                    if(!empty($_GET["s"])){
                        $metaDataOver["beteckning"] = array(
                            'key'     => 'crm_doc_dokumentbeteckning_en',
                            'value'   => $doc_nr,
                            'compare' => 'LIKE',
                        );
                    }
                    if(isset($_GET["fritext"])){
                        if(!empty($_GET['fritext'])) {
                            if(empty($_GET["s"])) {
                                $doc_nr = $_GET["fritext"];
                                $metaDataUnder["beteckning"] = array(
                                    'key'     => 'crm_doc_dokumentbeteckning_en',
                                    'value'   => $doc_nr,
                                    'compare' => 'LIKE',
                                );

                            }
                            $metaDataUnder["fritext"] = array(
                                'key'     => 'crm_doc_title_en',
                                'value'   => $fritext,
                                'compare' => 'LIKE',
                            );
                            $metaDataUnder['innehall'] = array(
                                'key' => 'crm_doc_text_content_en',
                                'value' => $fritext,
                                'compare' => 'LIKE',
                            );
                        }
                    }
                    if(isset($_GET["document_type"])){
                        $metaDataOver["document_type"] =    array(
                            'key'     => 'crm_doc_type_en',
                            'value'   => array('stafs', 'rep', 'doc', 'info'),
                            'compare' => 'IN',
                        );
                    }

                    if(isset($_GET['omraden'])) {
                        $metaDataOver["omraden"] =    array(
                            'key'     => 'crm_doc_area_en%_omradesnamn',
                            'value'   => $omraden,
                            'compare' => 'LIKE',
                        );
                    }

                    $metaDataUnder = array_merge(array('relation' => 'OR'), $metaDataUnder);

                    $metaquery = array( 'meta_query' => array(
                        'relation' => 'AND',
                        $metaDataOver,
                        $metaDataUnder,
                    ));


                    $pt_doc_for_args = array(
                        'posts_per_page' 	=> -1,
                        'post_type'     	=> 'document_eng',
                        's'					=> array($fritext, $doc_nr),
                        'meta_query' => $metaquery,
                    );


                } else {

                    $fritext = isset($_GET['fritext']) ? $_GET['fritext'] : '';
                    $doc_nr = isset($_GET['s']) ? $_GET['s'] : '';
                    $omraden = isset($_GET['omraden']) ? $_GET['omraden'] : '';
                    $doc_type = isset($_GET['document_type']) ? $_GET['document_type'] : '';

                    $metaDataOver = array();
                    $metaDataUnder = array();

                    if(!empty($_GET["s"])){
                        $metaDataOver["beteckning"] = array(
                            'key'     => 'crm_doc_dokumentbeteckning_en',
                            'value'   => $doc_nr,
                            'compare' => 'LIKE',
                        );
                    }
                    if(isset($_GET["fritext"])){
                        if(!empty($_GET['fritext'])) {
                            if(empty($_GET["s"])) {
                                $doc_nr = $_GET["fritext"];
                                $metaDataUnder["beteckning"] = array(
                                    'key'     => 'crm_doc_dokumentbeteckning_en',
                                    'value'   => $doc_nr,
                                    'compare' => 'LIKE',
                                );

                            }
                            $metaDataUnder["fritext"] = array(
                                'key'     => 'crm_doc_title_en',
                                'value'   => $fritext,
                                'compare' => 'LIKE',
                            );
                        }



                    }
                    if(isset($_GET["document_type"])){
                        $metaDataOver["document_type"] =    array(
                            'key'     => 'crm_doc_type_en',
                            'value'   => $doc_type,
                            'compare' => '=',
                        );
                    }

                    if(isset($_GET['omraden'])) {
                        $metaDataOver["omraden"] =    array(
                            'key'     => 'crm_doc_area_%_omradesnamn',
                            'value'   => $omraden,
                            'compare' => 'LIKE',
                        );
                    }

                    $metaDataUnder = array_merge(array('relation' => 'OR'), $metaDataUnder);
                    $metaDataOver = array_merge(array('relation' => 'AND'), $metaDataOver);

                    $metaquery = array( 'meta_query' => array(
                        $metaDataOver,
                        $metaDataUnder,
                    ));

                    $pt_doc_for_args = array(
                        'posts_per_page' 	=> -1,
                        'post_type'     	=> 'document_eng',
                        's'					=> array($fritext, $doc_nr),
                        'meta_query' => $metaquery,
                    );
                }


            }

            // $pt_doc_for_query = new WP_Query( $pt_doc_for_args );
            $array = array();
            $array1 = array();
            $array2 = array();
            $array3 = array();
            if(ICL_LANGUAGE_CODE=='sv') { ?>
                <?php $posts = get_posts($pt_doc_for_args);
                foreach ($posts as $post) {
                    $fileUrl = get_field('crm_doc_doc', $post->ID);
                    if(!empty($fileUrl)) {
                        //Hämtar fält
                        $subject = get_field('crm_doc_dokumentbeteckning',$post->ID);
                        $subject2 = get_field('crm_doc_dokumentbeteckning',$post->ID);

                        // Explodar efter STAFS och : samt hämtar ut årdet

                        if(strpos($subject2, 'STAFS ') !== false) {
                            $explode = explode('STAFS ', $subject2);
                            $field = end($explode);

                            $field = substr($field, 0, 4);
                            $explode = explode(':', $subject);
                            $result = end($explode);

                            //Lägger till 0 innan nummer som är lägre än 10
                            if($result < 10) {
                                $result = '0'.$result;
                            }

                            //Slår ihop variabler
                            $result = $field . $result;

                            //Lägger in allt i en array med värden ovan
                            array_push( $array, array(
                                "title" 	=> get_the_title($post->ID),
                                "id" 		=> get_the_id($post->ID),
                                "link" 		=> get_permalink($post->ID),
                                "crm_doc_dokumentbeteckning" => $result,
                                "crm_doc_doc" 	=> get_field('crm_doc_doc', $post->ID),
                                "crm_doc_type" 	=> get_field('crm_doc_type', $post->ID)
                            ));

                        } elseif (strpos($subject2, 'SWEDAC DOC ') !== false) {
                            $explode = explode('SWEDAC DOC ', $subject2);
                            $field = end($explode);

                            $field = substr($field, 0, 2); // hämtar första 2

                            $explode = explode(':', $subject);
                            $result = end($explode);

                            $explode = explode("0", $field);
                            $result = current($explode);
                            //Lägger till 0 innan nummer som är lägre än 10
                            if($result < 10) {
                                $result = '0'.$result;
                            }
                            if($field < 10) {
                                // $field = current(explode("0", $field));
                            }

                            //Slår ihop variabler
                            $result = $field . $result;

                            //Lägger in allt i en array med värden ovan
                            array_push( $array1, array(
                                "title" 	=> get_the_title($post->ID),
                                "id" 		=> get_the_id($post->ID),
                                "link" 		=> get_permalink($post->ID),
                                "crm_doc_dokumentbeteckning" => $result,
                                "crm_doc_doc" 	=> get_field('crm_doc_doc', $post->ID),
                                "crm_doc_type" 	=> get_field('crm_doc_type', $post->ID)
                            ));

                        } elseif (strpos($subject2, 'REP ') !== false) {
                            $explode = explode('REP ', $subject2);
                            $field = end($explode);

                            $field = substr($field, 0, 2); // hämtar första 2

                            $explode = explode(':', $subject);
                            $result = end($explode);

                            $explode = explode("0", $field);
                            $result = current($explode);
                            //Lägger till 0 innan nummer som är lägre än 10
                            if($result < 10) {
                                $result = '0'.$result;
                            }
                            if($field < 10) {
                                // $field = current(explode("0", $field));
                            }

                            //Slår ihop variabler
                            $result = $field . $result;

                            //Lägger in allt i en array med värden ovan
                            array_push( $array2, array(
                                "title" 	=> get_the_title($post->ID),
                                "id" 		=> get_the_id($post->ID),
                                "link" 		=> get_permalink($post->ID),
                                "crm_doc_dokumentbeteckning" => $result,
                                "crm_doc_doc" 	=> get_field('crm_doc_doc', $post->ID),
                                "crm_doc_type" 	=> get_field('crm_doc_type', $post->ID)
                            ));


                        } elseif (strpos($subject2, 'INFO ') !== false) {
                            $explode = explode('INFO ', $subject2);
                            $field = end($explode);
                            $field = substr($field, 0, 2); // hämtar första 2

                            $explode = explode(':', $subject);
                            $result = end($explode);

                            $explode = explode("0", $field);
                            $result = current($explode);
                            //Lägger till 0 innan nummer som är lägre än 10
                            if($result < 10) {
                                $result = '0'.$result;
                            }
                            if($field < 10) {
                                // $field = current(explode("0", $field));
                            }

                            //Slår ihop variabler
                            $result = $field . $result;

                            //Lägger in allt i en array med värden ovan
                            array_push( $array3, array(
                                "title" 	=> get_the_title($post->ID),
                                "id" 		=> get_the_id($post->ID),
                                "link" 		=> get_permalink($post->ID),
                                "crm_doc_dokumentbeteckning" => $result,
                                "crm_doc_doc" 	=> get_field('crm_doc_doc', $post->ID),
                                "crm_doc_type" 	=> get_field('crm_doc_type', $post->ID)
                            ));
                        }
                    }
                }



                //Sorterar på Störst > Lägst ex. 1993-2017

                usort($array, 'sortById');
                usort($array1, 'sortById');
                usort($array2, 'sortById');
                usort($array3, 'sortById');

                $array_first = array_merge($array, $array1);
                $array_second = array_merge($array2, $array3);

                $array = array_merge($array_first, $array_second);

                ?>


                <div class="table_wrapper">
                    <table id="docs_result">
                        <thead>
                            <tr>
                                <th>DOK.NR</th>
                                <th>RUBRIK</th>
                                <th>TYP AV DOKUMENT</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($array as $item) {

                                $title 		= $item['title'];
                                $id 		= $item['id'];
                                $url 		= $item['link'];
                                $doc 		= $item['crm_doc_doc'];
                                $doc_type 	= $item['crm_doc_type'];

                                $doc_bet = get_field('crm_doc_dokumentbeteckning', $id);
                                $doc_bet_2 = substr($doc_bet, 6); // returns "de"

                            ?>
                                <?php if($doc) { ?>
                                    <tr>
                                        <td><?php echo $doc_bet; ?></td>
                                        <td><a href="<?php echo $url; ?>" title="<?php echo $title; ?>"><?php echo $title; ?></a></td>
                                        <td><?php echo $doc_type; ?></td>
                                    </tr>
                                <?php } ?>
                            <?php } ?>
                        </tbody>
                    </table>

                    <?php if(empty($array)) { ?>
                        <div class="wrapper">
                            <div class="alert alert-warning">
                                <strong>Inget hittades</strong>
                                <em><?php echo sprintf( __( 'Sökordet '), $wp_query->found_posts ); echo '<span class="term">' . get_search_query() .'</span> gav dessvärre ingen träff i Föreskrifter & Dokument.'; ?></em>
                            </div>
                        </div>
                    <?php } else { ?>
                        <div class="search_result_buttons">
                            <?php $docCount = count($array); ?>
                            <?php if($docCount < 10) { ?>
                                <small class="smallDocs">Visar 1-<?php echo $docCount; ?> av <?php echo $docCount; ?> <?php if($docCount == 1) {echo " träff";} else {echo " träffar";} ?></small>
                            <?php } else { ?>
                                <small class="smallDocs">Visar 1-10 av <?php echo $docCount; ?> träffar</small>
                            <?php } ?>
                            <small class="smallDocs_total">Visar alla <?php echo $docCount; ?> träffar</small>

                            <?php if($docCount > 10) { ?>
                                <span id="loadMoreDocs" class="visa_fler_traffar"><i class="fa fa-spinner fa-spin loading-icon" aria-hidden="true"></i> Visa alla</span>
                            <?php } ?>
                        </div>
                    <?php } ?>
                </div>
            <?php } elseif(ICL_LANGUAGE_CODE=='en') { ?>

                <?php $posts = get_posts($pt_doc_for_args);

                foreach ($posts as $post){
                    //Hämtar fält
                    $subject = get_field('crm_doc_dokumentbeteckning_en',$post->ID);
                    $subject2 = get_field('crm_doc_dokumentbeteckning_en',$post->ID);

                    // Explodar efter STAFS och : samt hämtar ut årdet
                    $explode = explode('STAFS ', $subject2);
                    $field = end($explode);

                    $field = substr($field, 0, 4);
                    $explode = explode(':', $subject);
                    $result = end($explode);

                    //Lägger till 0 innan nummer som är lägre än 10
                    if($result < 10) {
                        $result = '0'.$result;
                    }

                    //Slår ihop variabler
                    $result = $field . $result;

                    //Lägger in allt i en array med värden ovan
                    array_push( $array, array(
                        "title" 	=> get_the_title($post->ID),
                        "id" 		=> get_the_id($post->ID),
                        "link" 		=> get_permalink($post->ID),
                        "crm_doc_dokumentbeteckning_en" => $result,
                        "crm_doc_doc" 	=> get_field('crm_doc_doc_en', $post->ID),
                        "crm_doc_type" 	=> get_field('crm_doc_type_en', $post->ID)
                    ));
                }
                //Sorterar på Störst > Lägst ex. 1993-2017

                usort($array, 'sortById'); ?>

                <div class="table_wrapper">
                    <table id="docs_result">
                        <thead>
                            <tr>
                                <th>DOC.NR</th>
                                <th>TITLE</th>
                                <th>TYPE OF DOCUMENT</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($array as $item) {

                                $title 		= $item['title'];
                                $id 		= $item['id'];
                                $url 		= $item['link'];
                                $doc 		= $item['crm_doc_doc'];
                                $doc_type 	= $item['crm_doc_type'];

                                $doc_bet = get_field('crm_doc_dokumentbeteckning_en', $id);
                                $doc_bet_2 = substr($doc_bet, 6); // returns "de"
                            ?>
                                <?php if($doc) { ?>
                                    <tr>
                                        <td><?php echo $doc_bet; ?></td>
                                        <td><a href="<?php echo $url; ?>" title="<?php echo $title; ?>"><?php echo $title; ?></a></td>
                                        <td><?php echo $doc_type; ?></td>
                                    </tr>
                                <?php } ?>
                            <?php } ?>
                        </tbody>
                    </table>


                    <?php if(empty($array)) { ?>
                        <div class="wrapper">
                            <div class="alert alert-warning">
                                <strong>Nothing found</strong>
                                <em><?php echo sprintf( __( 'The keyword '), $wp_query->found_posts ); echo '<span class="term">' . get_search_query() .'</span> gave unfortunately no hit in the search source Regulations & documents.'; ?></em>
                            </div>
                        </div>
                    <?php } else { ?>
                        <div class="search_result_buttons">
                            <?php $docCount = count($array); ?>
                            <?php if($docCount < 10) { ?>
                                <small class="smallDocs">Shows 1-<?php echo $docCount; ?> of <?php echo $docCount; ?> <?php if($docCount == 1) {echo " result";} else {echo " results";} ?></small>
                            <?php } else { ?>
                                <small class="smallDocs">Shows 1-10 of <?php echo $docCount; ?> results</small>
                            <?php } ?>

                            <small class="smallDocs_total">Showing all <?php echo $docCount; ?> results</small>

                            <?php if($docCount > 10) { ?>
                                <span id="loadMoreDocs" class="visa_fler_traffar"><i class="fa fa-spinner fa-spin loading-icon" aria-hidden="true"></i> Show all</span>
                            <?php } ?>
                            <a href="https://www.swedac.se/law-order/swedacs-regulations/search-regulations-documents/?lang=en">
                                <span class="avancerad_sokning">
                                    <i class="fa fa-external-link" aria-hidden="true"></i> Advanced search
                                </span>
                            </a>
                        </div>
                    <?php } ?>

                </div>
            <?php } ?>

        </div>
    </div>
</div>
