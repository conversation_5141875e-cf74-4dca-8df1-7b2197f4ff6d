<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
    <div class="wrapper">
        <?php // Hämta in förslag på resutlat baserat på sökningen
        $search_term = utf8_decode($search_term);
        global $wpdb;
        $getID = "SELECT DISTINCT(id) FROM wp_serach_log WHERE term LIKE '%".$search_term."%'";
        $resultID = $wpdb->get_results($getID);

        $forslagArray = array();
        $i=0; foreach ( $resultID as $item ) {
            $rowId = $item->id;

            $getReults = "SELECT forslag FROM wp_serach_log_forslag WHERE row_id = '".$rowId."'";
            $rows = $wpdb->get_results($getReults);

            if($i==0) {
                echo "<ul class='suggestion_list sv'>";
            }
            if(!empty($rows)) {
                foreach ($rows as $row) {
                    $forslag        = $row->forslag;
                    if(!in_array($forslag, $forslagArray)) {
                        $forslagForm    = $row->forslag;
                        $forslagForm    = preg_replace('/\s+/', '+', $forslagForm);

                        // Fix för att undvika konstiga url:er när ingen sökfras är satt
                        if ($search_term_form === '') {
                            $forslagFormUrl = str_replace('?s=&', '?s=' . $forslagForm . '&', $searchUrlResults);
                        } else {
                            $forslagFormUrl = preg_replace("/$search_term_form/", $forslagForm, $searchUrlResults);
                        }

                        echo "<li><a href='".$forslagFormUrl."'>".$forslag."</a></li>";
                        $forslagArray[] = $forslag;
                    }
                }
            }
        $i++;}
        if($i==0) {
            echo "</ul>";
        }?>
    </div>


<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
    <div class="wrapper">
        <?php // Hämta in förslag på resutlat baserat på sökningen
        $search_term = utf8_decode($search_term);
        global $wpdb;
        $getID = "SELECT DISTINCT(id) FROM wp_serach_log_en WHERE term LIKE '%".$search_term."%'";
        $resultID = $wpdb->get_results($getID);

        $forslagArray = array();
        $i=0; foreach ( $resultID as $item ) {
            $rowId = $item->id;

            $getReults = "SELECT forslag FROM wp_serach_log_forslag_en WHERE row_id = '".$rowId."'";
            $rows = $wpdb->get_results($getReults);

            if($i==0) {
                echo "<ul class='suggestion_list en'>";
            }
            if(!empty($rows)) {
                foreach ($rows as $row) {
                    $forslag        = $row->forslag;
                    if(!in_array($forslag, $forslagArray)) {
                        $forslagForm    = $row->forslag;
                        $forslagForm    = preg_replace('/\s+/', '+', $forslagForm);

                        // Fix för att undvika konstiga url:er när ingen sökfras är satt
                        if ($search_term_form === '') {
                            $forslagFormUrl = str_replace('?s=&', '?s=' . $forslagForm . '&', $searchUrlResults);
                        } else {
                            $forslagFormUrl = preg_replace("/$search_term_form/", $forslagForm, $searchUrlResults);
                        }

                        echo "<li><a href='".$forslagFormUrl."'>".$forslag."</a></li>";
                        $forslagArray[] = $forslag;
                    }
                }
            }
        $i++;}
        if($i==0) {
            echo "</ul>";
        }?>
    </div>

<?php endif; ?>
