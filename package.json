{"private": true, "name": "cc-theme-sage", "author": "cc.", "version": "1.0.0", "description": "Standard tema.", "main": "gulpfile.js", "devDependencies": {"@babel/core": "^7.11.6", "@babel/preset-env": "^7.11.5", "browser-sync": "^2.26.12", "dotenv": "^16.4.7", "gulp": "^4.0.0", "gulp-autoprefixer": "^7.0.1", "gulp-babel": "^8.0.0", "gulp-clean-css": "^4.3.0", "gulp-concat": "^2.6.1", "gulp-minify": "^3.1.0", "gulp-plumber": "^1.2.0", "gulp-rename": "^2.0.0", "gulp-sass": "5.1.0", "gulp-sourcemaps": "^2.6.4", "streamqueue": "^1.1.2"}, "scripts": {"build": "gulp build", "build:prod": "gulp build", "watch": "gulp watch"}, "license": "ISC", "babel": {"presets": [["@babel/preset-env", {"targets": ["> 0.25%", "not dead", "ie 11"]}]]}, "browserslist": ["> 0.5%", "last 2 versions", "Firefox ESR", "not dead"], "dependencies": {"sass": "^1.34.1"}}