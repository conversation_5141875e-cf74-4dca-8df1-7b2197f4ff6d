jQuery(document).ready(function(jQuery) {
	//<PERSON>ka<PERSON> knapp i admin
	var post_ID = jQuery("#post_ID").val();
	var current_url = window.location.href;     // Returns full URL

    jQuery('.send_to_subscribers_textarea').show();
    jQuery('.send_to_subscribers_message').hide();
	jQuery("#acf-group_574014ef50eb9 .acf-radio-list input[type='radio']").change(function () {

		if (jQuery("#acf-field_574015e70b914-true").attr("checked")) {

        	if(window.location.href.indexOf("%3Fsuccess_sending") > -1) {
               jQuery('.send_to_subscribers_textarea').addClass('disabled');
               jQuery('.send_to_subscribers_textarea').hide();
               jQuery('.send_to_subscribers_message').show();

            } else {
            	jQuery('.send_to_subscribers_textarea').removeClass('disabled');
            }
        }
        else {
            jQuery('.send_to_subscribers_textarea').addClass('disabled');
        }
	});

	if(window.location.href.indexOf("%3Fsuccess_sending") > -1) {
       jQuery('.send_to_subscribers_message').show();
       jQuery('.send_to_subscribers_textarea').hide();
    }


    //Avaktivera "svakt lösenord" på profilen
    jQuery('.pw-checkbox').prop('disabled', true);
    jQuery('.pw-weak label').append('<p style="margin:10px 0 0 0;">Du måste ange ett <b>Starkt</b> lösenord för att skapa en användare. <br/> <em>Tips: Använd <u>stora</u> & <u>små bokstäver</u>, <u>siffror</u> och <u>symboler</u> samt minst 14 tecken långt.</em></p>');

    // Söklogg
    var count = 0;
    jQuery(function(){
        jQuery('.add_row').click(function(){
            var id = jQuery(this).attr('data-newButton');
            count += 1;
            jQuery('<input class="field_row field_' + count + '" name="fields[]' + '" type="text" placeholder="Synonym" required/>').insertBefore('.addNewBox[data-newRow='+id+']');
        });
    });

    // Körs enbart på hantera prenumerater sidan.
    if (document.querySelector('.toplevel_page_manage-subscribers')) {
        // Skapar en osorterad lista med checkbox och text för angivna parametrar.
        function ccSubscribersCheckbox(availableSubs, existingSubs, type = 'areas') {
            const ul = document.createElement('ul');

            for (let i = 0; i < availableSubs.length; i++) {
                let controller, text, value;

                if (type === 'areas') {
                    controller = availableSubs[i].post_id.toString();
                    text = availableSubs[i].post_title;
                    value = availableSubs[i].post_id;
                } else {
                    controller = availableSubs[i].toString();
                    text = availableSubs[i];
                    value = availableSubs[i];
                }

                const li = document.createElement('li');
                const checkbox = document.createElement('input');
                const span = document.createElement('span');

                li.setAttribute('value', value);
                checkbox.setAttribute('type', 'checkbox');

                if (existingSubs.includes(controller)) {
                    checkbox.setAttribute('checked', 'checked');
                }

                span.textContent = text;

                li.appendChild(checkbox);
                li.appendChild(span);
                ul.appendChild(li)
            }

            return ul;
        }

        const posts = {
            documents: [],
            areas: [],
        }

        const data = { 'action': 'cc_query_posts_subscribers' };

        jQuery.post(
            ajaxurl,
            data,
            function (response, status) {
                const encodedResponse = response;

                if (encodedResponse !== false && status === 'success') {
                    posts.documents = encodedResponse['docs'];
                    posts.areas = encodedResponse['amnesomraden'];
                }
            }
        );

        if (document.querySelectorAll('.edit-subscriber')) {
            const editSubs = document.querySelectorAll('.edit-subscriber');

            if (editSubs.length > 0) {
                function ccEditableFieldsSubs(type, value) {
                    const field = document.createElement('input');

                    field.setAttribute('type', type);
                    field.setAttribute('required', 'required');
                    field.setAttribute('value', value);

                    return field;
                }

                // Uppdatera textContent till Laddar.. och returnera node elementet.
                function ccUpdateLoadingText(field, cssClass) {
                    const child = field.parentNode.parentNode.parentNode.parentNode.querySelector(cssClass).firstElementChild;

                    child.textContent = 'Laddar..';

                    return child;
                }

                editSubs.forEach((sub) => {
                    sub.addEventListener('click', (event) => {
                        const subName = sub.parentNode.parentNode.parentNode.firstElementChild;
                        const subEmail = sub.parentNode.parentNode.parentNode.nextElementSibling.firstElementChild;

                        if (sub.textContent === 'Redigera') {
                            sub.textContent = 'Spara';
                            const cancelBtn = sub.parentNode.nextElementSibling;

                            cancelBtn.style.display = 'inline';

                            // Gör namnet redigeringsbart.
                            const editSubName = ccEditableFieldsSubs('text', subName.textContent);
                            subName.replaceWith(editSubName);

                            // Gör e-postadressen redigeringsbar.
                            const editSubEmail = ccEditableFieldsSubs('email', subEmail.textContent);
                            subEmail.replaceWith(editSubEmail);

                            // Uppdatera antal till Laddar..
                            const subAreas = ccUpdateLoadingText(sub, '.areas');
                            const subDocs = ccUpdateLoadingText(sub, '.docs');

                            const data = {
                                'action': 'cc_get_subscribers_data',
                                'uid': sub.getAttribute('id'),
                            }

                            // Hämta befintliga bevakningar från databasen.
                            jQuery.post(
                                ajaxurl,
                                data,
                                function (response, status) {
                                    const encodedResponse = response;

                                    if (encodedResponse !== false && status === 'success') {
                                        const subData = encodedResponse;

                                        // Om det finns dokument (föreskrifter).
                                        if (posts.documents.length > 0) {
                                            const existingSubs = [];

                                            for (let i = 0; i < subData[0].length; i++) {
                                                existingSubs.push(subData[0][i].document_name);
                                            }

                                            const ul = ccSubscribersCheckbox(posts.documents, existingSubs, 'docs');

                                            subDocs.replaceWith(ul);
                                        }

                                        // Om det finns ämnesområden.
                                        if (posts.areas.length > 0) {
                                            const existingSubs = [];

                                            for (let i = 0; i < subData[1].length; i++) {
                                                existingSubs.push(subData[1][i].post_id);
                                            }

                                            const ul = ccSubscribersCheckbox(posts.areas, existingSubs);

                                            subAreas.replaceWith(ul);
                                        }
                                    }
                                }
                            );
                        } else if (sub.textContent === 'Spara') {
                            function ccCreateSubUpdateJSON(selector) {
                                const data = [];

                                const element = sub.parentNode.parentNode.parentNode.parentNode.querySelector(selector).firstElementChild.childNodes;

                                for (let i = 0; i < element.length; i++) {
                                    const temp = [];

                                    const checkbox = element[i].firstElementChild;

                                    let activeSub = false;

                                    if (checkbox.checked === true) {
                                        activeSub = true;
                                    }

                                    temp.push(element[i].getAttribute('value'));
                                    temp.push(activeSub);

                                    data.push(temp);
                                }

                                return data;
                            }

                            // Hämta de uppdaterade värderna.
                            const name = subName.value;
                            const email = subEmail.value;
                            const areas = ccCreateSubUpdateJSON('.areas');
                            const docs = ccCreateSubUpdateJSON('.docs');

                            const jsonAreas = JSON.stringify({
                                'subscriber_id': sub.getAttribute('id'),
                                'subscriber_name': name,
                                'subscriber_email': email,
                                'areas': areas,
                                'docs': docs,
                            });

                            const data = {
                                'action': 'cc_update_subscriber',
                                'data': jsonAreas,
                            };

                            jQuery.post(
                                ajaxurl,
                                data,
                                function (response, status) {
                                    const encodedResponse = response;

                                    if (encodedResponse !== false && status === 'success') {
                                        sub.textContent = 'Redigera';

                                        const labelName = document.createElement('label');
                                        labelName.textContent = name;
                                        subName.replaceWith(labelName);

                                        const labelEmail = document.createElement('label');
                                        labelEmail.textContent = email;
                                        subEmail.replaceWith(labelEmail);
                                    }

                                    const countSubs = encodedResponse;

                                    const ulArea = sub.parentNode.parentNode.parentNode.parentNode.querySelector('.areas').firstElementChild;
                                    const areaCountLabel = document.createElement('label');
                                    areaCountLabel.textContent = countSubs.areas + ' st'; 
                                    ulArea.replaceWith(areaCountLabel);

                                    const ulDocs = sub.parentNode.parentNode.parentNode.parentNode.querySelector('.docs').firstElementChild;
                                    const docDocuntLabel = document.createElement('label');
                                    docDocuntLabel.textContent = countSubs.docs + ' st'; 
                                    ulDocs.replaceWith(docDocuntLabel);

                                    sub.parentNode.parentNode.querySelector('.cancel').style.display = 'none';
                                }
                            );
                        }
                    });
                });
            }
        }

        // Avbryt ändring prenumeranter.
        if (document.querySelectorAll('.cancel-edit-subscriber')) {
            const cancelEditSubs = document.querySelectorAll('.cancel-edit-subscriber');

            cancelEditSubs.forEach((cancelSub) => {
                cancelSub.addEventListener('click', (event) => {
                    const editBtn = cancelSub.parentNode.parentNode.firstElementChild.firstElementChild;
                    const cancelSubName = cancelSub.parentNode.parentNode.parentNode.firstElementChild;
                    const subEmail = cancelSub.parentNode.parentNode.parentNode.nextElementSibling.firstElementChild;
                    const subAreas = cancelSub.parentNode.parentNode.parentNode.parentNode.querySelector('.areas');
                    const subDocs = cancelSub.parentNode.parentNode.parentNode.parentNode.querySelector('.docs');

                    // Ändra spara till redigera
                    editBtn.textContent = 'Redigera';
                    cancelSub.parentNode.style.display = 'none';

                    // Namnfältet
                    const newLabelName = document.createElement('label');
                    newLabelName.textContent = cancelSubName.value;
                    cancelSubName.replaceWith(newLabelName);

                    // E-postfältet
                    const newLabelEmail = document.createElement('label');
                    newLabelEmail.textContent = subEmail.value;
                    subEmail.replaceWith(newLabelEmail);

                    // Ämnesområden
                    const newLabelAreas = document.createElement('label');
                    newLabelAreas.textContent = subAreas.getAttribute('data-existing-subs') + ' st';
                    subAreas.firstElementChild.replaceWith(newLabelAreas);

                    // Föreskrifter
                    const newLabelDocs = document.createElement('label');
                    newLabelDocs.textContent = subDocs.getAttribute('data-existing-subs') + ' st';
                    subDocs.firstElementChild.replaceWith(newLabelDocs);
                });
            });   
        }

        // Radera prenumeranter.
        if (document.querySelectorAll('.delete-subscriber')) {
            let deleteSubs = document.querySelectorAll('.delete-subscriber');

            if (deleteSubs.length > 0) {
                deleteSubs.forEach((sub) => {
                    sub.addEventListener('click', (event) => {
                        const subID = sub.getAttribute('data-user-id');
                        const subName = event.target.parentNode.parentNode.parentNode.firstElementChild.textContent;
                        const confirmCheck = confirm(`Vill du verkligen ta bort ${subName} som prenumerant?`);

                        if (confirmCheck === true) {
                            const data = {
                                'action': 'cc_delete_subscriber',
                                'uid': subID,
                            }

                            jQuery.post(
                                ajaxurl,
                                data,
                                function (response, status) {
                                    const encodedResponse = response;

                                    if (encodedResponse === true && status === 'success') {
                                        window.location.replace(window.location + '&delete-user=complete');
                                    }
                                }
                            );
                        }
                    });
                });
            }
        }
    }
});