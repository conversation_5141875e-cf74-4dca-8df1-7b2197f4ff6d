jQuery(document).ready(function(e){var t=e("#post_ID").val(),n=window.location.href;e("#acf-group_574014ef50eb9 .acf-input").append('<form class="send_to_subscribers_form" method="post" action="https://www.swedac.se/subscribers.php?add_sendjob"><input class="send_to_subscribers" type="submit" value="Skicka uppdatering"><span class="send_to_subscribers_message"><i class="fa fa-info-circle" aria-hidden="true"></i> En ändring i innehållet måste göras och sparas för att kunna skicka uppdatering till alla bevakare.</span><input type="hidden" name="url" value="'+n+'"/><input type="hidden" name="post_id" value="'+t+'"></form>'),e("#acf-field_574015e70b914").attr("checked")&&e(".send_to_subscribers").addClass("disabled"),e(".send_to_subscribers_message").hide(),e("#acf-group_574014ef50eb9 .acf-radio-list input[type='radio']").change(function(){e("#acf-field_574015e70b914-true").attr("checked")?window.location.href.indexOf("%3Fsuccess_sending")>-1?(e(".send_to_subscribers").addClass("disabled"),e(".send_to_subscribers_message").show()):e(".send_to_subscribers").removeClass("disabled"):e(".send_to_subscribers").addClass("disabled")}),window.location.href.indexOf("%3Fsuccess_sending")>-1&&(e(".send_to_subscribers").addClass("disabled"),e(".send_to_subscribers_message").show()),e(".pw-checkbox").prop("disabled",!0),e(".pw-weak label").append('<p style="margin:10px 0 0 0;">Du måste ange ett <b>Starkt</b> lösenord för att skapa en användare. <br/> <em>Tips: Använd <u>stora</u> & <u>små bokstäver</u>, <u>siffror</u> och <u>symboler</u> samt minst 14 tecken långt.</em></p>');var i=0;e(function(){e(".add_row").click(function(){var t=e(this).attr("data-newButton");i+=1,e('<input class="field_row field_'+i+'" name="fields[]" type="text" placeholder="Synonym" required/>').insertBefore(".addNewBox[data-newRow="+t+"]")})})});
//# sourceMappingURL=admin-dist.js.map