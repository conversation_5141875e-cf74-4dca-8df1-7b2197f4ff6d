// Globalt
//-------------------------------------------
*,
*:after,
*:before {
    padding: 0;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
}

html {}

body {
    margin: 0;
    font-size: 18px;
    font-weight: 400;
    color: #333;
    float: left;
    width: 100%;
    overflow-x: hidden;

    a {

        text-decoration: underline;

    }

    &.avancerad-sokning {
        .relaterad_information_custom {
            display: none !important;
        }
    }

    &.keyboard-active a:focus {
        background-color: rgba(0, 0, 0, 0.1);
        outline: 0.08em dashed $black;
        outline-offset: 0;
    }

    //Om en wrapper ej finns.
    #main_wrapper {
        float: left;
        width: 100%;

        .content.row {
            float: left;
            width: 100%;
            margin: 0;

            .main {
                float: left;
                width: 100%;
            }
        }
    }

    //Om en sidebar ej finns
    #main_wrapper {
        float: left;
        width: 100%;

        .wrapper {
            .content.row {
                float: left;
                width: 100%;

                .main {
                    float: left;
                    width: 100%;
                }
            }
        }
    }

    //Om en sidebar finns.
    &.sidebar-active {
        #main_wrapper {
            .wrapper {
                .content.row {
                    padding: 0;

                    .main {
                        float: left;
                        width: 100%;
                        max-width: 700px;
                        border-right: solid 1px #DEDEDE;
                        padding: 0 60px 0 0;
                        margin: 0 20px 0 0;
                    }

                    .sidebar {
                        float: right;
                        width: 100%;
                        max-width: 320px;
                        padding: 0 0 0 40px;
                    }
                }

                .sidebar {
                    float: right;
                    width: 100%;
                    max-width: 320px;
                    padding: 0 0 0 40px;
                }
            }
        }
    }

    &.page-template-page-template_2 {
        #footer {
            margin: 0;
        }
    }

    //Om det är IE
    &.ie {
        .label_select {
            &:after {
                display: none !important;
            }

            select {
                padding: 15px !important;
            }
        }
    }
}

.wrapper {
    max-width: 1040px !important;
    width: 100%;
    margin: 0 auto;
    padding: 0;
    position: relative;
}

.alert-ie-appeared {
    float: left;
    width: 100%;
    padding: 10px 15px;
    text-align: center;
    background: #FF6767;
    color: #fff;
    margin: 0 0 20px;
    position: relative;
    z-index: 10;

    a {
        color: #000;
        font-weight: bold;
    }
}

// Cookie notice
.cookie-notice-container {
    background: $color-dark-grey;
    color: #fff !important;
    padding: 15px;
    font-size: 14px;

    a {
        color: inherit;

        &:hover {
            color: inherit;
        }
    }
}

.alert-warning {
    float: left;
    width: 100%;
    margin: 20px 0 0;
    padding: 0;
    font-size: 16px;

    h2 {
        float: left;
        width: 100%;
        border-bottom: solid 1px #DADADA !important;
        padding: 0 0 15px !important;
        margin: 0 0 15px !important;
    }

    b {
        float: left;
        width: 100%;
        margin: 0 0 5px;
    }

    em {
        .term {
            font-weight: bold;
        }
    }
}

.clear {
    clear: both;
    *zoom: 1;

    &:before {
        content: '';
        display: table;
    }

    &:after {
        content: '';
        display: table;
    }
}

img {
    opacity: 1;
    transition: opacity 0.3s;
    max-width: 100%;
    height: auto;

    &[data-src] {
        opacity: 0;
    }
}

figure {
    max-width: 100% !important;

    img {
        max-width: 100%;
        height: auto;
        margin-top: -2px;
    }

    .wp-caption-text {
        float: left;
        width: 100%;
        font-size: 12px;
        line-height: 19px;
        color: #666;
        margin: 5px 0 0;
        margin-bottom: 10px;
    }

    &.alignleft {
        margin: 10px 20px 0 0;
        float: left !important;

        @media only screen and (max-width:480px) {
            width: 100% !important;
            margin: auto;
            margin-bottom: 30px;
            text-align: center !important;
        }
    }

    &.aligncenter {
        width: 100% !important;
        margin: auto;
        margin-bottom: 30px;
        text-align: center !important;
    }

    &.alignright {
        margin: 10px 0 20px 20px;
        float: right !important;

        @media only screen and (max-width:480px) {
            width: 100% !important;
            margin: auto;
            margin-bottom: 30px;
            text-align: center !important;
        }
    }
}

h1,
h2,
h3,
h4 {
    color: #333;
    font-weight: 300;
    font-family: "Inter", sans-serif;
}

h1 {
    margin: 0 0 10px;
    font-size: 50px;
}

h2 {
    margin: 0 0 8px;
    font-size: 30px;
    letter-spacing: 0.8px;
}

h3 {
    margin: 0 0 8px;
    font-size: 18px;
    letter-spacing: 0.2px;
}

h4 {
    margin: 0 0 8px;
    font-size: 18px;
    letter-spacing: 0.2px;
}

p {
    margin: 0 0 20px 0;
    line-height: 28px;
}

i,
em {
    font-style: italic;
}

blockquote {
    font-size: 30px;
    font-weight: 300;
    color: $color-text-darker;
    margin: 0 0 20px;

    p {
        line-height: 34px;
    }
}

b,
strong {
    letter-spacing: 0.2px;
    font-weight: 500;
}

ul {
    margin: -5px 0 15px 19px;

    li {
        list-style: disc;
        padding: 2px 0;
        line-height: 27px;
    }
}

ol {
    margin: -5px 0 15px 19px;

    li {
        list-style: decimal;
        padding: 2px 0;
        line-height: 27px;
    }
}

a {
    color: $color-bla;
    transition: all .2s;

    img {
        border: none;
        outline: none;
    }

    &.page-numbers {
        background: none;
        color: $color-bla;
        border-color: #fff !important;
        padding: 11px 15px !important;
        font-size: 14px;

        &:hover {
            color: red;
        }
    }
}

input,
textarea,
select {
    font-size: 15px;
    resize: none;
    outline: 0;
    border: solid 1px #D0D0D0;
    padding: 15px;
    transition: all .2s;

    &.error {
        background: #FDD;
        border: solid 1px #CC002F !important;
        padding: 15px;

        &:-webkit-autofill {
            -webkit-box-shadow: 0 0 0px 1000px #FDD inset;
        }
    }

    &.valid {
        background: #E5FFE5;
        border: solid 1px #00925C !important;
        padding: 15px;

        &:-webkit-autofill {
            -webkit-box-shadow: 0 0 0px 1000px #E5FFE5 inset;
        }
    }

    &:focus {
        outline: 0;
        background: #fff;
        border-top: solid 1px #D0D0D0;
        border-left: solid 1px #D0D0D0;
        border-right: solid 1px #ccc;
    }
}

select {
    appearance: none;

    &.error {
        background-color: #FDD !important;
        border: solid 1px #CC002F !important;
    }

    &.valid {
        background-color: #E5FFE5 !important;
        border: solid 1px #00925C !important;
    }
}

.label_select {
    position: relative;
    float: left;
    width: 100%;
    margin: 0 0 5px;

    &:after {
        content: '\f107';
        font-family: $FA;
        font-size: 35px;
        right: 15px;
        top: 8px;
        position: absolute;
        pointer-events: none;
        line-height: 0;
        top: 55px;
    }
}

button {
    border: solid 1px #E0E0E0;

    &:focus {
        outline: 0;
    }
}

fieldset {
    border: 0;

    legend {
        margin: 0 0 8px;
    }
}


//Meddelanden
//-------------------------------------------
.header_message_unsub {
    float: left;
    width: 100%;
    margin: -8px 0 8px 0;

    .inner {
        padding: 14px 0;
        font-size: 16px;
        text-align: center;

        &.success {
            background: #E5FFE5;
            border-bottom: solid 1px #00925C;
        }

        &.failed {
            background: #FDD;
            border-bottom: solid 1px #CC002F;
        }
    }
}

//Relaterad infomration
.relaterad_information_custom {
    float: left;
    width: 100%;
    background: $color-light;
    padding: 30px 40px;
    margin: 30px 0 0 0;

    h2 {
        font-weight: 600;
        margin: 0 0 10px;
        position: relative;
        float: left;
        letter-spacing: normal;
        font-size: 22px;
        color: $color-da-blue;
    }

    ul {
        margin: 0;
        padding: 0;

        li {
            float: left;
            width: 100%;
            list-style: none;
            padding: 15px 0;
            border-top: solid 1px #ccc;

            &:first-child {
                border-top: none;
                padding-top: 0;
            }

            a {}

            span {
                font-size: 14px;
                color: $grey;
            }
        }
    }
}

//Lyckades eller misslyckades
.header_message {
    float: left;
    width: 100%;
    margin: 0 0 35px;

    .inner {
        float: left;
        width: 100%;
        padding: 20px;

        span {
            float: left;
            width: 100%;
            font-size: 16px;
        }

        &.failed {
            background: #FDD;
            border: solid 1px #CC002F;
        }

        &.success {
            background: #E5FFE5;
            border: solid 1px #00925C;
        }
    }
}

.messange_box {
    float: left;
    width: 100%;
    margin: 0 0 30px;

    .inner {
        float: left;
        width: 100%;
        padding: 20px;

        h2 {
            margin: 0 0 15px;
            letter-spacing: normal;
        }

        p {
            margin: 0 0 0px;
        }

        b {
            letter-spacing: normal;
        }

        &.failed {
            background-color: #FDD !important;
            border: solid 1px #CC002F !important;
        }

        &.success {
            background-color: #E5FFE5 !important;
            border: solid 1px #00925C !important;
        }

    }
}

// Läs mer - Excerpt
//-------------------------------------------
.read-more-link {
    float: left;
    width: 100%;
    margin: 10px 0;

    .view-article {
        border: solid 1px #B5B3B3;
        padding: 5px 20px;
        background: transparent;
        float: right;
        transition: all .2s;

        &:hover {
            border: solid 1px $color-bla;
        }
    }
}

// Rubrik fix - Om den är för lång.
//-------------------------------------------
.title-cuter {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

// Google maps
//-------------------------------------------
#map {
    float: left;
    width: 100%;
    height: 400px;
    display: none;

    .map-icon-postal-code {
        color: $color-bla;
        font-size: 45px;
    }
}

// Brödsmulor
//-------------------------------------------
#breadcrumbs {
    float: left;
    width: 100%;
    padding: 10px 0 0;
    margin: 0 0 25px;
    font-size: 12px;

    span {
        color: #575757;

        span a {
            color: #4d4d4d;
        }
    }
}

// Ingress
//-------------------------------------------
.ingress {
    float: left;
    width: 100%;
    color: #666;
    margin: 0 0 20px;

    p {
        line-height: 30px;
        margin: 0;
        font-size: 22px;

    }
}

// Pagination
//--------------------------------------------
.easy-wp-page-navigation {
    margin: 0 !important;
    float: left;
    width: 100%;
    padding: 28px 0 24px;
    border-bottom: solid 1px #cccccc;
    border-top: solid 1px #cccccc;

    ul.easy-wp-page-nav {
        vertical-align: inherit;
        float: left;
        width: 100%;
        margin: 0 !important;

        li {
            float: left !important;
            width: auto !important;
            padding: 0 !important;
            border-bottom: none !important;
            margin: 0 4px 0 0 !important;

            .page-numbers {
                background: none;
                color: $color-bla;
                border-color: #fff !important;
                padding: 11px 15px !important;
                font-size: 14px;

                &.current {
                    border-color: #ccc !important;
                    color: $color-bla;
                }

                &:hover {
                    border-color: #ccc !important;
                    color: $color-bla;
                }

                &.prev {
                    font-size: 25px;
                    line-height: 12px;
                    width: 40px;
                    height: 38px;
                }

                &.next {
                    font-size: 25px;
                    line-height: 12px;
                    width: 40px;
                    height: 38px;
                }
            }

            &.first-page {
                display: none !important;
            }

            &.last-page {
                display: none !important;
            }
        }
    }
}


// Single post info
//-------------------------------------------
.post_settings {
    float: left;
    width: 100%;
    max-width: 700px;
    margin: 20px 0 0;

    .author {
        float: left;
        width: 100%;
        margin: 15px 0 10px;
        font-size: 16px;

        span {
            text-transform: capitalize;
            font-weight: 600;
        }
    }

    .publish_change {
        float: left;
        width: 100%;
        margin: 8px 0 10px;
        font-size: 16px;

        span {
            float: left;
            width: 100%;
            color: $grey;
            margin: 0 0 3px
        }
    }

    .share_post {
        float: left;
        width: 100%;
        margin: 15px 0 0;
        display: flex;
        justify-content:
            flex-start;
        flex-wrap: wrap;

        .share {
            background: #fff;
            color: #333;
            border: solid 1px #ccc;
            flex-basis: auto;
            font-size: 14px;
            margin: 0 auto 25px 0;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;

            .fa {
                font-size: 25px;
                margin: 0 5px;
            }

            span {
                float: none;
                margin: 0;
                position: relative;
                top: -3px;
                width: auto;
                text-decoration: underline;
            }

            &.facebook {
                .fa {
                    color: #3B5998;
                }
            }

            &.linkedin {
                .fa {
                    color: #1c87bd;
                }
            }

            &.mail {
                .fa {
                    color: $color-da-blue;
                    font-size: 20px;
                    padding: 2px 0 3px;
                }
            }

            &:hover {
                cursor: pointer;
            }
        }
    }
}


//Shortcode knappar
//-------------------------------------------
.symple-button {
    transition: all .2s;
    float: left;
    width: 100%;
    padding: 15px;
    margin: 15px 0 15px;
    text-align: center;
    font-size: 15px;
    font-weight: 200;
    background: $color-shortcode !important;
    color: #fff;
    border-radius: 2px !important;

    &:hover {
        background: darken($color-shortcode, 5%) !important;
        opacity: 1 !important;
        color: #fff !important;
    }

    span {
        white-space: normal;
        line-height: 22px;
    }
}

//Toggle
.symple-toggle {
    width: 100%;
    float: left;
    border-bottom: solid 1px #ccc;

    .symple-toggle-trigger {
        position: relative;
        color: #000;
        margin: 0 !important;
        font-size: 18px !important;
        letter-spacing: 0.8px !important;
        font-weight: inherit !important;
        border: none;
        background: #fff !important;
        padding: 15px 15px 15px 37px !important;

        &:before {
            content: '';
            float: left;
            width: 20px;
            height: 20px;
            background: url("../../assets/images/svg/plus-black.svg") no-repeat center center;
            background-size: 20px;
            position: absolute;
            top: 18px;
            left: 0;
        }

        &.active {
            background-image: none !important;

            &:before {
                content: '';
                float: left;
                width: 20px;
                height: 20px;
                background: url("../../assets/images/svg/minus-black.svg") no-repeat center center;
                background-size: 20px;
                position: absolute;
                top: 18px;
                left: 0;
            }

            &:hover {
                &:before {
                    content: '';
                    float: left;
                    width: 20px;
                    height: 20px;
                    background: url("../../assets/images/svg/minus-black.svg") no-repeat center center;
                    background-size: 20px;
                    position: absolute;
                    top: 18px;
                    left: 0;
                }
            }
        }

        &:hover {
            &:before {
                content: '';
                float: left;
                width: 20px;
                height: 20px;
                background: url("../../assets/images/svg/plus-black.svg") no-repeat center center;
                background-size: 20px;
                position: absolute;
                top: 18px;
                left: 0;
            }
        }
    }

    .symple-toggle-container {
        border: none;
        padding: 0 0 15px 0;

        p {}
    }

    &.first {
        margin: 0;
        border-top: solid 1px #ccc;
    }
}


// Post/page thumbnail
//-------------------------------------------
.page_thumbnail {
    float: left;
    width: 100%;
    margin: 15px 0 25px;

    img {
        width: 100%;
        height: auto;
        border: solid 1px #DEDEDE;
    }

    .wp-caption-text {
        margin-bottom: 0;
    }
}

figure.page_thumbnail {
    margin: 15px 0 10px;
}

// Iframe (Youtube)
//-------------------------------------------
iframe {
    float: left;
    width: 100%;
    margin: 0 0 20px;
    border: none;
}


// Rubriker med överblick
//-------------------------------------------
#header-content {
    float: left;
    position: relative;
    width: 100%;
    background: $color-da-blue;
    padding: 1rem;

    .page-header {
        position: relative;
        z-index: 2;

        .title-page {
            font-size: 30px;
            color: #fff;
            font-weight: 200;
            width: 100%;
        }

        .intro {
            float: left;
            width: 60%;
            margin: 10px 0 0;
            color: $color-text;

            p {
                margin: 0;
                line-height: 30px;
                font-size: 22px;
            }
        }
    }
}

#page-header-parent {
    float: left;
    width: 100%;

    .title {
        float: left;
        position: relative;
        width: 100%;
        background: $color-da-blue;
        padding: 15px 0;

        .wrapper {
            .title-page {
                font-size: 30px;
                margin: 0;
                color: #fff;
                font-weight: 200;
                letter-spacing: normal;
                float: left;
                width: 100%;

                h1 {
                    color: $white;
                }
            }
        }
    }

    .post_thumbnail {
        .image {
            background-position: top !important;
            background-size: cover !important;
            background-attachment: inherit !important;
            height: 500px;
            background-repeat: no-repeat;
        }
    }
}

// Slider
//-------------------------------------------
#frontpage_owl_slider {
    float: left;
    width: 100%;
    position: relative;
    height: 475px;

    ul {
        margin: 0;
        padding: 0;

        li {
            height: 475px;
            width: 100%;
            padding: 0;
            line-height: 1;
            list-style: none;

            .slider-cover {
                top: 0;
                position: absolute;
                width: 100%;
                height: 100%;
                background: rgba(33, 33, 33, .35);
                z-index: 1;
            }

            .wrapper {
                height: 100%;
                padding: 4% 0;
                top: 0;

                .info {
                    margin: 3% 0 0 0;
                    float: left;
                    width: 65%;
                    position: relative;
                    z-index: 2;

                    h1,
                    h2 {
                        font-size: 75px;
                        color: #fff;
                        letter-spacing: normal;
                        line-height: 1.1;
                        margin: 0 0 20px;
                        font-weight: 200;
                    }

                    .content {
                        color: #fff;
                        font-size: 23px;
                        line-height: 28px;
                    }
                }

                .slider_links {
                    width: 30%;
                    float: right;
                    position: relative;
                    z-index: 2;

                    ul {
                        margin: 0;
                        padding: 0;

                        li {
                            height: auto;
                            line-height: 1;
                            padding: 0;
                            float: left;
                            width: 100%;
                            margin: 0 0 7px;
                            height: auto;
                            list-style: none;
                            background: #fff;
                            border-radius: 5px;

                            a {
                                line-height: 18px;
                                position: relative;
                                padding: 12px 15px 8px;
                                float: left;
                                width: 100%;
                                text-align: left;
                                color: #333;
                                font-size: 14px;
                                text-decoration: none;

                                &.nav-item-blue {
                                    color: $white;
                                    background-color: $color-da-blue;
                                    border-radius: 5px;
                                }

                                &:after {
                                    float: right;
                                    width: 20px;
                                    height: 20px;
                                    background: url("../../assets/images/pil-black.png") no-repeat center center;
                                    background-size: 16px;
                                    content: '';
                                    margin: -2px 5px 2px 5px;
                                    transition: all .2s;
                                }

                                &:hover {
                                    &:after {
                                        margin: -2px 0 2px 0;
                                    }
                                }
                            }

                            &:nth-last-child(-n+4) {
                                background: $color-dark-grey;

                                a {
                                    color: #fff;

                                    &:after {
                                        float: right;
                                        width: 20px;
                                        height: 20px;
                                        background: url("../../assets/images/pil-white.png") no-repeat center center;
                                        background-size: 16px;
                                        content: '';
                                        margin: -2px 5px 2px 0;
                                        transition: all .2s;
                                    }

                                    &:hover {
                                        &:after {
                                            margin: -2px 0 2px 0;
                                        }
                                    }
                                }
                            }

                            &:nth-child(4) {
                                a {
                                    color: $white;
                                    background-color: $color-da-blue;
                                    border-radius: 5px;
                                }
                            }
                        }
                    }
                }
            }

            &.no_image {
                background: $color-da-blue;
            }
        }
    }
}

.toggle-search {
    display: none;
}


//Certifikat
.certifikat_rep {
    float: left;
    width: 100%;
    margin: 25px 0 0;
    padding: 25px 0 0;
    border-top: solid 1px #DEDEDE;

    .cert_content {
        float: left;
        width: 100%;
        margin: 0 0 10px;
        padding: 0 0 10px;
        position: relative;

        .r_toggle {
            margin: 0;
            font-size: 25px;
            padding: 0 0 0 30px;

            &:hover {
                cursor: pointer;
            }

            &:before {
                content: "";
                height: 20px;
                width: 20px;
                background: url("../../assets/images/pil-blue.png") no-repeat center center;
                background-size: 16px;
                margin: 4px 8px 4px 0;
                float: left;
                color: #0070a8;
                position: absolute;
                left: 0;
            }
        }

        .cert_div {
            display: none;

            table {
                float: left;
                width: 100%;
                text-align: left;
                margin: 8px 0 0;
                border-collapse: collapse;
                border-spacing: 0;

                thead {
                    tr {
                        background: #333333;
                        color: #fff;

                        th {
                            padding: 10px 10px;
                            font-size: 13px;
                            font-weight: normal;
                            letter-spacing: 0.5px;
                        }
                    }
                }

                tbody {
                    background: #fff;
                    border-collapse: separate;
                    border-spacing: 0;

                    tr {
                        background: #fff;

                        td {
                            padding: 10px 10px;
                            font-size: 15px;

                            span {
                                float: left;
                                width: 100%;
                            }

                            p {
                                margin: 0;
                                line-height: 23px;
                            }
                        }

                        &:nth-child(even) {
                            background: #e0e5e8;
                        }
                    }
                }
            }
        }

        &:last-child {
            border-bottom: none;
            padding: 0;
            margin: 0;
        }
    }
}


#search_form {
    float: left;
    width: 100%;
    background: #ebeff2;
    padding: 30px 0;
    margin: 0;

    .wrapper {
        form#search-form {
            float: left;
            width: 100%;
            font-size: 15px;

            .main_serach {
                float: left;
                width: 80%;
                padding: 0 10px 0 0;

                .search-field {
                    appearance: none;
                    font-size: 15px;
                    padding: 1rem;
                    float: left;
                    width: 85%;
                    border-radius: 4px;
                    border-bottom-right-radius: 0;
                    border-top-right-radius: 0;
                    border: none;
                    background: #fff;
                    background-size: 30px 30px;
                    background-position: top 9px left 8px;
                }

                .search-submit {
                    appearance: none;
                    font-size: 15px;
                    transition: all .2s;
                    border: none;
                    float: left;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: .5rem;
                    width: 15%;
                    border-radius: 4px;
                    padding: 1rem 1.5rem;
                    background: $color-bla;
                    color: #fff;
                    border-bottom-left-radius: 0;
                    border-top-left-radius: 0;

                    &:hover {
                        background: lighten($color-bla, 10%);
                        text-decoration: none;
                        cursor: pointer;
                    }
                }
            }

            .adv_search {
                float: right;
                width: 20%;
                padding: 0 0 0 10px;

                #mainmenu {
                    margin: 0;

                    li {
                        list-style: none;
                        padding: 0;
                        float: left;
                        width: 100%;
                        position: relative;

                        span {
                            line-height: 17px;
                            float: left;
                            width: 100%;
                            border-radius: 4px;
                            padding: 1.2rem 1.5rem;
                            background: $color-bla;
                            color: #fff;
                            transition: all .2s;

                            .fa-angle-down {
                                font-size: 30px;
                                position: absolute;
                                top: 10px;
                                right: 15px;
                            }

                            &.active {
                                border-bottom-right-radius: 0;
                                border-bottom-left-radius: 0;

                                .fa-angle-down {
                                    transform: rotate(180deg);
                                }
                            }

                            &:hover {
                                background: lighten($color-bla, 10%);
                                text-decoration: none;
                                cursor: pointer;
                            }
                        }

                        #submenu {
                            position: absolute;
                            z-index: 1;
                            top: 100%;
                            display: none;
                            transition: all .2s;
                            float: left;
                            width: 250px;
                            margin: 0;
                            padding: 0;
                            border-radius: 4px;
                            border-top-left-radius: 0;
                            padding: 10px 0;
                            background: $color-bla;
                            margin: 0 0 0;

                            li {
                                float: left;
                                width: 100%;
                                padding: 8px 15px;

                                a {
                                    color: #fff;
                                    position: relative;
                                    text-decoration: none;

                                    &:before {
                                        content: '\f178';
                                        font-family: $FA;
                                        float: left;
                                        margin: 0px 10px 0 0;
                                        transition: all .2s;
                                    }
                                }
                            }

                            &.open {
                                display: block;
                                animation-name: slideUp;
                                animation-duration: .5s;
                                animation-fill-mode: forwards;
                                animation-timing-function: ease;
                            }
                        }
                    }
                }


            }

            #sokkallor {
                float: left;
                width: 100%;
                display: flex;
                margin: 20px 0 0;

                .alt {
                    flex-grow: 5;
                    background: $color-knapp;
                    text-align: center;
                    padding: 15px 0;
                    margin: 0 15px 0 0;
                    border-radius: 4px;
                    color: #fff;
                    transition: all .2s;

                    input {
                        padding: inherit;
                        position: relative;
                        top: 1px;
                        left: -3px;
                    }

                    span {
                        display: inline-table;
                        line-height: 0;
                    }

                    &:last-child {
                        margin: 0;
                        float: right;
                    }

                    &:hover {
                        background: lighten($color-knapp, 3%);
                        cursor: pointer;
                    }
                }
            }
        }
    }
}

//Press
.meta {
    float: left;
    width: 100%;

    span {
        font-size: 14px;
        color: $color-text-darker;
        float: left;
        line-height: 9px;
        margin: 0 0 10px;
        text-transform: lowercase;
    }
}

// Förslag på sökträffar samt menade du
ul.suggestion_list,
ul.did_you_mean_suggestion_list {
    float: left;
    width: 100%;

    h3.suggestion_listTitle {
        margin: 0 0 12px
    }

    li {
        float: left;
        list-style: none;

        a {
            padding: 8px 15px;
            float: left;
            margin: 0 5px 2px 0;
            border: solid 1px #ccc;
            border-radius: 4px;
            font-size: 15px;

            &:first-letter {
                text-transform: uppercase;
            }

            &:hover {
                background: #f9f9f9;
            }
        }
    }
}

ul.did_you_mean_suggestion_list {
    margin: 0;
}

ul.suggestion_list {
    margin: 30px 0 0;
}

.search_result_buttons {
    float: left;
    width: 100%;
    margin: 15px 0;

    small {
        float: left;
        width: 100%;
        font-size: 13px;
        margin: 0;
        color: #6d6d6d;
    }

    small.small_post_page_total {
        display: none;
    }

    small.smallDocs_total {
        display: none;
    }

    span {
        font-size: 15px;
        float: left;
        margin: 15px 15px 0 0;
        padding: 10px 15px;
        border-radius: 4px;
        text-align: center;
        background: $color-bla;
        color: #fff;
        transition: all .2s;

        .fa {
            margin: 0 5px 0 0;
            font-size: 14px;
        }

        .loading-icon {
            display: none;
        }

        &.loading {
            .loading-icon {
                display: block;
                float: left;
                margin: 3px 12px 0 0;
            }
        }

        &.disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        &:hover {
            background: lighten($color-bla, 10%);
            text-decoration: none;
            cursor: pointer;
        }
    }
}

// Toggla till sökresultat
#toggleToResults {
    float: left;
    width: 100%;
    margin: 30px 0 0;

    .wrapper {
        display: flex;
    }

    .toggleButton {
        float: left;
        width: 25%;
        margin: 0 7px;

        &:last-child {
            margin: 0 0 0 7px;
        }

        &:first-child {
            margin: 0 7px 0 0;
        }

        a {
            font-size: 15px;
            padding: 10px 15px;
            border-radius: 4px;
            text-align: center;
            background: $color-bla;
            color: #fff;
            float: left;
            width: 100%;

            span {
                float: left;
                width: 100%;
                margin: 0 0 4px;
                font-size: 13px;
            }

            &:hover {
                background: lighten($color-bla, 10%);
                text-decoration: none;
            }
        }
    }
}

//Sökresultat
.searchResultSection {
    float: left;
    width: 100%;
    margin: 15px 0;

    .wrapper {
        border-bottom: solid 2px #8a8a8a;
        padding: 15px 0 30px;
    }

    &:last-child {
        margin: 0;

        .wrapper {
            padding: 15px 0 0;
            border: none;
        }
    }

    &:first-child {
        margin: 0;

        .wrapper {
            padding: 0 0 30px;
        }
    }
}

.search_result {
    float: left;
    width: 100%;
    margin: 10px 0 0;

    h2 {
        float: left;
        width: 100%;
        border-bottom: solid 1px #DADADA;
        padding: 0 0 15px;
        margin: 0;
        letter-spacing: 0.8px;
    }

    article.search_post {
        float: left;
        width: 100%;
        border-bottom: solid 1px #DADADA;
        padding: 25px 0 20px;
        display: none;
        animation-name: slideUp;
        animation-duration: .5s;
        animation-fill-mode: forwards;
        animation-timing-function: ease;

        .left {
            float: left;
            width: 70%;

            .meta {
                float: left;
                width: 100%;

                .updated {
                    font-size: 14px;
                    color: $grey;
                    float: left;
                    line-height: 9px;
                    margin: 0 0 10px;
                }
            }

            h3 {

                .highlight {
                    background: #F1F100;
                }
            }

            .excerpt {
                line-height: 24px;
                font-size: 16px;

                .highlight {
                    background: #F1F100;
                }
            }
        }

        .right {
            float: right;
            width: 15%;

            img {}
        }

        //Ackrediterade Organ
        .name {
            float: left;
            border-right: solid 1px #acacac;
            padding: 0 8px;

            &:last-child {
                border-right: none;
            }
        }

        &:last-child {
            border-bottom: none;
            padding: 25px 0 0 0;
        }
    }
}

//Namnstämplar
.search_result_namnstampelregistret {
    float: left;
    width: 100%;

    h2 {
        float: left;
        width: 100%;
        border-bottom: solid 1px #DADADA;
        padding: 0 0 15px;
        margin: 0;
        letter-spacing: 0.8px;
    }

    article.search_post_namnstampel {
        float: left;
        width: 100%;
        border-bottom: solid 1px #DADADA;
        padding: 25px 0 20px;

        .excerpt {
            float: left;
            width: 100%;

            table {
                float: left;
                width: 100%;
                text-align: left;
                margin: 0;
                border-collapse: collapse;
                border-spacing: 0;
                font-size: 16px;

                thead {
                    border-bottom: solid 2px #acacac;

                    tr {
                        background: #E2E2E2;

                        th {
                            padding: 10px 10px;
                        }
                    }
                }

                tbody {
                    border-bottom: solid 2px #acacac;
                    background: #fff;
                    border-collapse: separate;
                    border-spacing: 0;

                    tr {
                        display: none;
                        background: #fff;
                        border-bottom: solid 1px #CECECE;
                        animation-name: slideUp;
                        animation-duration: .5s;
                        animation-fill-mode: forwards;
                        animation-timing-function: ease;

                        td {
                            padding: 10px 10px;

                        }

                        &:nth-child(even) {
                            background: $color-light;
                        }
                    }
                }

            }
        }

        &:last-child {
            border-bottom: none;
            padding: 25px 0 0 0;
        }
    }
}

//Organ
.search_result_organ {
    float: left;
    width: 100%;

    h2 {
        float: left;
        width: 100%;
        border-bottom: solid 1px #DADADA;
        padding: 0 0 15px;
        margin: 0;
        letter-spacing: 0.8px;
    }

    #searchresult-mapinfotext {
        float: left;
        width: 100%;
        margin: 10px 0;
        font-style: italic;
        font-size: 14px;
        color: $color-text;
    }

    article.search_post_organ {
        float: left;
        width: 100%;
        border-bottom: solid 1px #DADADA;
        padding: 25px 0 20px;
        animation-name: slideUp;
        animation-duration: .5s;
        animation-fill-mode: forwards;
        animation-timing-function: ease;

        .excerpt {
            float: left;
            width: 100%;

            //Ackrediterade Organ
            .name {
                float: left;
                text-align: center;
                padding: 0 15px 0 0;
                margin: 4px 0;

                &:last-child {
                    border-right: none;
                }
            }
        }


        &:last-child {
            border-bottom: none;
            padding: 25px 0 0 0;
        }
    }
}

.search_result_dokument {
    float: left;
    width: 100%;

    h2 {
        float: left;
        width: 100%;
        border-bottom: solid 1px #DADADA;
        padding: 0 0 15px;
        margin: 0;
        letter-spacing: 0.8px;
    }

    article.search_post_dokument {
        float: left;
        width: 100%;
        border-bottom: solid 1px #DADADA;
        padding: 25px 0 20px;

        .left {
            float: left;
            width: 70%;

            .meta {
                float: left;
                width: 100%;

                .updated {
                    font-size: 14px;
                    color: $grey;
                    float: left;
                    line-height: 9px;
                    margin: 0 0 10px;
                }
            }

            h3 {}

            .excerpt {
                line-height: 24px;
                font-size: 16px;
            }
        }

        .right {
            float: right;
            width: 15%;

            img {}
        }

        &:last-child {
            border-bottom: none;
            padding: 25px 0 0 0;
        }

    }

    &.all_kallor {
        margin: 30px 0 0 0;
    }
}

.search_result_certifikat {
    float: left;
    width: 100%;

    h2 {
        float: left;
        width: 100%;
        border-bottom: solid 1px #DADADA;
        padding: 0 0 15px;
        margin: 0;
        letter-spacing: 0.8px;
    }

    article.search_post_certifikat {
        float: left;
        width: 100%;
        border-bottom: solid 1px #DADADA;
        padding: 25px 0 20px;

        .left {
            float: left;
            width: 70%;

            .meta {
                float: left;
                width: 100%;

                .updated {
                    font-size: 14px;
                    color: $grey;
                    float: left;
                    line-height: 9px;
                    margin: 0 0 10px;
                }
            }

            h3 {}

            .excerpt {
                line-height: 24px;
                font-size: 16px;
            }
        }

        .right {
            float: right;
            width: 15%;

            img {}
        }

        &:last-child {
            border-bottom: none;
            padding: 25px 0 0 0;
        }

    }

    &.all_kallor {
        margin: 30px 0 0 0;
    }
}

#sok_foreskriver_dokument,
#sok_certifikat,
#monitor_content_for_updates {
    float: left;
    width: 100%;
    padding: 30px 0 30px;
    margin: 0 0 40px;
    background: $color-light;

    .wrapper,
    .inner-container {
        .title {
            float: left;
            width: 100%;
            margin: 0 0 15px;

            span {
                float: left;
                margin: 3px 0 0 0;
            }

            img {
                float: left;
                width: 40px;
                height: auto;
                margin: 0 18px 0 0;
            }
        }

        #searchform_foreskrifter_dokument,
        #searchform_certifikat,
        #monitor_content_form {

            .row {
                float: left;
                width: 48%;
                margin: 0 0 30px;

                .search-field,
                .req {
                    border: solid 1px #D0D0D0;
                    appearance: none;
                    font-size: 15px;
                    padding: 15px;
                    float: left;
                    width: 100%;
                    border-radius: 4px;
                }

                select {
                    margin: 0;
                    padding: 0;
                    padding: 15px;
                    float: left;
                    width: 100%;
                    font-size: 16px;
                    border-radius: 4px;
                    background: #fff;
                    -webkit-appearance: none;

                    option {
                        padding: 0;
                        list-style: none;
                        float: left;
                        padding: 0 30px 0 0;
                        width: 100%;
                        margin: 0 0 7px;
                    }
                }

                &:nth-child(even) {
                    float: right;
                }
            }

            .search-submit,
            .monitor-submit {
                appearance: none;
                font-size: 15px;
                transition: all .2s;
                border: none;
                float: left;
                width: 10%;
                display: flex;
                align-items: center;
                border-radius: 4px;
                padding: 1rem 1.5rem;
                background: $color-bla;
                color: #fff;

                &:hover {
                    background: lighten($color-bla, 10%);
                    text-decoration: none;
                    cursor: pointer;
                }
            }

            .monitor-submit {
                width: auto;
            }
        }
    }

    &.foreskrifter {
        margin: 40px 0 0;
        padding: 30px 15px;

        .inner-container #monitor_content_form .row:last-child {
            margin: 0;
        }
    }
}

#sok_foreskriver_dokument_page {
    float: left;
    width: 100%;
    padding: 30px 0 30px;
    margin: 0;
    background: $color-light;
    margin: 0 0 30px;

    .wrapper {
        .title {
            float: left;
            width: 100%;
            margin: 0 0 15px;

            span {
                float: left;
                margin: 3px 0 0 0;
            }

            img {
                float: left;
                width: 40px;
                height: auto;
                margin: 0 18px 0 0;
            }
        }

        #searchform_foreskrifter_dokument {

            .row {
                float: left;
                width: 48%;
                margin: 0 0 30px;

                .search-field {
                    border: solid 1px #D0D0D0;
                    appearance: none;
                    font-size: 15px;
                    padding: 15px;
                    float: left;
                    width: 100%;
                    border-radius: 4px;
                }

                select {
                    margin: 0;
                    padding: 0;
                    padding: 15px;
                    float: left;
                    width: 100%;
                    font-size: 16px;
                    border-radius: 4px;
                    background: #fff;
                    -webkit-appearance: none;

                    option {
                        padding: 0;
                        list-style: none;
                        float: left;
                        padding: 0 30px 0 0;
                        width: 100%;
                        margin: 0 0 7px;
                    }
                }

                &:nth-child(even) {
                    float: right;
                }
            }

            .search-submit {
                appearance: none;
                font-size: 15px;
                transition: all .2s;
                border: none;
                float: left;
                display: flex;
                align-items: center;

                width: 10%;
                border-radius: 4px;
                padding: 1rem 1.5rem;
                background: $color-bla;
                color: #fff;

                &:hover {
                    background: lighten($color-bla, 10%);
                    text-decoration: none;
                    cursor: pointer;
                }
            }
        }
    }
}

//Senaste dokument
.senaste_dokument {
    float: left;
    width: 100%;
    font-size: 16px;
    display: none;

    h3 {
        float: left;
        width: 100%;
        border-bottom: solid 1px #888;
        padding: 0 0 15px;
        margin: 0 0 20px;
        letter-spacing: normal;
    }

    table {
        float: left;
        width: 100%;
        text-align: left;
        margin: 0;
        border-collapse: collapse;
        border-spacing: 0;

        thead {
            tr {
                background: #333333;
                color: #fff;

                th {
                    padding: 10px 10px;
                    font-size: 13px;
                    font-weight: normal;
                    letter-spacing: 0.5px;

                    &:nth-child(2) {
                        width: 50%;
                    }
                }
            }
        }

        tbody {
            background: #fff;
            border-collapse: separate;
            border-spacing: 0;

            tr {
                background: #fff;

                td {
                    padding: 10px 10px;

                }

                &:nth-child(even) {
                    background: #e0e5e8;
                }
            }
        }

    }
}

//Genvägar dokument
.dokument_genvagar {
    float: left;
    width: 100%;
    font-size: 16px;
    margin: 30px 0 0 0;

    h3 {
        float: left;
        width: 100%;
        border-bottom: solid 1px #888;
        padding: 0 0 15px;
        margin: 0 0 20px;
        letter-spacing: normal;
    }

    ul {
        margin: 0;
        padding: 0;

        li {
            padding: 0;
            list-style: none;
            float: left;
            padding: 0 30px 0 0;
            width: 100%;
            margin: 0 0 7px;

            a {
                position: relative;
                color: $color-bla;
                font-size: 18px;

                &:before {
                    float: left;
                    width: 20px;
                    height: 20px;
                    background: url("../../assets/images/pil-blue.png") no-repeat center center;
                    background-size: 16px;
                    content: '';
                    margin: 2px 8px 0 0;
                }
            }
        }
    }
}

.senaste_dokument_result {
    float: left;
    width: 100%;
    font-size: 16px;

    h2 {
        float: left;
        width: 100%;
        border-bottom: none;
        padding: 0 0 15px;
        margin: 0;
        letter-spacing: 0.8px;
    }

    table {
        float: left;
        width: 100%;
        text-align: left;
        margin: 0;
        border-collapse: collapse;
        border-spacing: 0;

        thead {
            tr {
                background: #333333;
                color: #fff;

                th {
                    padding: 10px 10px;
                    font-size: 13px;
                    font-weight: normal;
                    letter-spacing: 0.5px;

                    &:nth-child(2) {
                        width: 50%;
                    }
                }
            }
        }

        tbody {
            background: #fff;
            border-collapse: separate;
            border-spacing: 0;

            tr {
                display: none;
                background: #fff;
                animation-name: slideUp;
                animation-duration: .5s;
                animation-fill-mode: forwards;
                animation-timing-function: ease;

                td {
                    padding: 10px 10px;

                    &.docTitle {
                        .highlight {
                            background: #F1F100;
                        }
                    }
                }

                &:nth-child(even) {
                    background: #e0e5e8;
                }
            }
        }

    }
}

.senaste_certifikat_result {
    float: left;
    width: 100%;
    font-size: 16px;

    h2 {
        float: left;
        width: 100%;
        border-bottom: none;
        padding: 0 0 15px;
        margin: 0;
        letter-spacing: 0.8px;
    }

    table {
        float: left;
        width: 100%;
        text-align: left;
        margin: 0;
        border-collapse: collapse;
        border-spacing: 0;

        thead {
            tr {
                background: #333333;
                color: #fff;

                th {
                    padding: 10px 10px;
                    font-size: 13px;
                    font-weight: normal;
                    letter-spacing: 0.5px;

                    &:nth-child(2) {
                        width: 50%;
                    }
                }
            }
        }

        tbody {
            background: #fff;
            border-collapse: separate;
            border-spacing: 0;

            tr {
                display: none;
                background: #fff;
                animation-name: slideUp;
                animation-duration: .5s;
                animation-fill-mode: forwards;
                animation-timing-function: ease;

                td {
                    padding: 10px 10px;

                    &.certTitle {
                        .highlight {
                            background: #F1F100;
                        }
                    }
                }

                &:nth-child(even) {
                    background: #e0e5e8;
                }
            }
        }

    }
}

.download-list_item {

    background-color: $color-light;

    i {

        line-height: inherit;

    }

}

.donwload-list-title {

    background-color: $color-darker-blue;
    color: $white;
    margin: 0;

}

.ninja-forms-field[disabled] {
    cursor: not-allowed;
}