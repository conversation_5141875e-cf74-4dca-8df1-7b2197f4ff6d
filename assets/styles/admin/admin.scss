//Variable
$color-bla:         #0070a8;
$color-gul:         #FDBD25;
$color-da-blue:     #012138;
$color-text:        #95a6ad;
$color-knapp:       #162943;
$color-shortcode:   #0070a8;
$color-light:       #ebeff2;
//Variable
$admin-color: #ff6600;

@keyframes slideUp {
    0%   {opacity:0; transform:translate3d(0,-10px,0);}
    100% {opacity:1; transform:translate3d(0,0,0);}
}
@keyframes slideright {
    0%   {opacity:0; transform:translate3d(-40px,0,0);}
    100% {opacity:1; transform:translate3d(0,0,0);}
}
@keyframes background_change {
    0% {background-color: white;}
    50% {background:rgb(255, 223, 223) none repeat scroll 0% 0%;}
    1000% {background-color: white;}
}
.google_a { transition: all .2s; animation-name: background_change; animation-duration: 3s; animation-iteration-count: infinite;
    p {}
    ul { list-style-type:disc; margin:10px 0 10px 15px;
        li { margin:  6px 0;
            ul { list-style-type:circle; margin:5px 0 20px 15px;
                li {margin: 4px 0;}
            }
        }
    }
}
#TB_ajaxContent {
    overflow: visible !important;
}

//Inloggning
.login {
    background: rgba(255,255,255,1);
    background: -moz-radial-gradient(center, ellipse cover, rgba(255,255,255,1) 0%, rgba(255,248,240,1) 100%);
    background: -webkit-gradient(radial, center center, 0px, center center, 100%, color-stop(0%, rgba(255,255,255,1)), color-stop(100%, rgba(255,248,240,1)));
    background: -webkit-radial-gradient(center, ellipse cover, rgba(255,255,255,1) 0%, rgba(255,248,240,1) 100%);
    background: -o-radial-gradient(center, ellipse cover, rgba(255,255,255,1) 0%, rgba(255,248,240,1) 100%);
    background: -ms-radial-gradient(center, ellipse cover, rgba(255,255,255,1) 0%, rgba(255,248,240,1) 100%);
    background: radial-gradient(ellipse at center, rgba(255,255,255,1) 0%, rgba(255,248,240,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#fff8f0', GradientType=1 );
    #login { width: 650px;
        h1 {
            a {background-image: url('../../../assets/images/logotype.png'); background-size: auto;width: auto; outline: 0; margin:0 0 20px; box-shadow: none;
                &:hover {outline: 0;}
                &:focus {outline: 0;}
                &:active {outline: 0;}
            }
        }

        //Meddelande
        #login_error { animation-name: slideright;animation-duration: .5s;animation-fill-mode:forwards;animation-timing-function: ease; font-size: 16px;}

        p.message {animation-name: slideright;animation-duration: .5s;animation-fill-mode:forwards;animation-timing-function: ease; font-size: 16px;}

        //Form
        #loginform {  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.45); padding: 26px 24px;
            p {
                //username
                label[for=user_login] { float: left; width: 48%;
                    input { font-size: 14px; padding: 10px 15px; margin:0;
                        &:-webkit-autofill { -webkit-box-shadow: 0 0 0px 1000px #fff inset;}
                    }
                }

                //password
                label[for=user_pass] { float: right; width: 48%;
                    input { font-size: 14px; padding: 10px 15px; margin:0;
                        &:-webkit-autofill { -webkit-box-shadow: 0 0 0px 1000px #fff inset;}
                    }
                }
                //Google Authenticator
                label[for=authenticator] { float: left; width: 100%; margin: 10px 0 0;
                    small {display: none;}
                    input {font-size: 14px; padding: 10px 15px; margin:0;
                        &:-webkit-autofill { -webkit-box-shadow: 0 0 0px 1000px #fff inset;}
                    }
                }
                //forgetmenot
                &.forgetmenot { float: left; width: 48%; margin:15px 0 0;
                    label[for=rememberme] { float: left; width: 100%;
                        input { margin:0 10px 0 0;
                            &:-webkit-autofill { -webkit-box-shadow: 0 0 0px 1000px #fff inset;}
                            &:before {color:#507E02; margin:-12px 0 0 -7px; font-size: 35px;}
                        }
                    }
                }
                //Login button
                &.submit { width: 25%; float: right; margin:15px 0 0;
                    input { border-radius: 0; padding:10px 15px; height: auto; line-height: 21px; width: 100%; background:#507E02; text-shadow:none; box-shadow:none; border:0; transition:background .3s;
                        &:hover {background:darken(#507E02, 5%); outline: 0;}
                        &:focus {outline: 0;}
                        &:active {outline: 0;}
                    }
                }
            }
        }

        #lostpasswordform {box-shadow: 0 1px 3px rgba(0, 0, 0, 0.45);
            p {
                //username
                label[for=user_login] { float: left; width: 74%;
                    input { font-size: 14px; padding: 10px 15px; margin:0;

                    }
                }
                //Login button
                &.submit { width: 24%; float: right; margin:24px 0 0;
                    input { border-radius: 0; padding:10px 15px; height: auto; line-height: 21px; width: 100%; background:#507E02; text-shadow:none; box-shadow:none; border:0; transition:background .3s;
                        &:hover {background:darken(#507E02, 5%); }
                    }
                }
            }
        }

        //Glömt lösenord
        p {
            &#nav { float: right; width: 48%; margin: 15px 0 0; text-align: center;

            }
            &#backtoblog { float: left; width: 48%; margin:15px 0 0; text-align: center;

            }
        }
    }
}

//Admin style
//Admin Bar (Toppen)
#wpadminbar {
    .quicklinks {
        ul.ab-top-menu {
            li { border-right:solid 1px #555;
                a {transition: color .2s; color: #DADADA !important; padding: 0 12px;
                    &:hover, &:focus {color: $admin-color !important;}
                    &:before {color: #fff !important;}

                    .ab-icon { color: #fff;
                        &:before {color: #fff !important;}
                    }
                    .ab-label {color: #fff;

                    }

                }

                .ab-sub-wrapper { padding: 5px 10px !important;
                    ul { padding: 0;
                        li { border-bottom: solid 1px #555; border-right: none;
                            .ab-item {padding: 5px 0; position: relative; height: 19px !important; line-height: 19px !important;
                                &:after {content: '\f101'; font-family: FontAwesome; float: right; margin-right: 3px; font-size: 12px; transition: margin .2s;}
                                &:before {display: none;}
                                &:hover { color: #fff;
                                    &:after {margin-right: 0;}
                                }
                            }

                            &:last-child{border-bottom: none;}
                        }
                    }
                }
                &#wp-admin-bar-wp-logo, &#wp-admin-bar-comments {display: none;}

                &:hover {
                    a {
                        .ab-label {color: $admin-color !important;}
                    }
                }
                &#wp-admin-bar-customize {display: none;}
            }
        }

        ul.ab-top-secondary {
            li { border-left: solid 1px #555;
                a {

                }
                .ab-sub-wrapper { width: 350px; right: -380px; margin: 0 -2px 0 0; display: block !important; opacity: 0; transition: all .3s;
                    ul#wp-admin-bar-user-actions {
                        li { border-left: none; margin-left:110px; margin-right: 0px;
                            a { position: relative; padding: 5px 0 5px 20px;
                                span { float: left; width: 100%; line-height: 19px; display: inline-block;
                                    &.display-name {text-transform: capitalize;}
                                    &.username {float: right; font-style: italic; display: none;}
                                }
                                .avatar {left:-80px;height: 64px; width: 64px;}

                                &:after { position: absolute; left: 0; font-family: FontAwesome; margin:1px 8px 0 0; float: left; color: #fff;}
                            }


                            &#wp-admin-bar-user-info {margin-bottom: 0;
                                a { position: relative;
                                    &:after {content: '\f007'; font-family: FontAwesome;}
                                }
                            }
                            &#wp-admin-bar-edit-profile {
                                a { position: relative;
                                    &:after {content: '\f085'; font-family: FontAwesome;}
                                }
                            }
                            &#wp-admin-bar-logout {
                                a { position: relative;
                                    &:after {content: '\f08b'; font-family: FontAwesome;}
                                }
                            }
                        }
                    }
                }
                &#wp-admin-bar-my-account {
                    a {padding: 0 15px;}
                }
                &#wp-admin-bar-search {display: none;}
                &.hover {
                    .ab-sub-wrapper {opacity: 1; right: 0;}
                }

            }
        }
    }
}

//Göm fält för bilder
div.settings > label:nth-child(5) {display: none;}
.edit-attachment {display: none !important;}


//Seperator Meny
#adminmenu li.wp-menu-separator {height: 5px; margin: 7px 0; background: rgba(0, 112, 168, 0.7);}



//Skicka ut uppdatering till alla prenumeranter
.send_to_subscribers_form { float: left; width: 100%; padding: 15px 0 20px !important;
    .send_to_subscribers_textarea {float:left; width: 100%; margin: 0 0 10px; min-height: 100px; max-height: 200px; resize: vertical; display:block; font-size: 13px;
        &.disabled { pointer-events: none; opacity: 0.7;}
    }
    .send_to_subscribers {font-size: 13px; width: 100%; float: left; margin: 0; padding: 7px 0; color: #fff; background: $color-bla; border: none; transition: all .2s;
        &:hover {background:lighten($color-bla, 10%); text-decoration: none; cursor: pointer; }
        &.disabled { pointer-events: none; opacity: 0.7;}
    }
    .send_to_subscribers_message {margin:15px 0 0; float: left; width: 100%;}
}

// Custom search log
.message_box { float: right; width: 100%; margin: 30px 0 0 0;
    .inner_box {opacity: 0; padding: 15px; box-shadow: 0 1px 3px rgba(0,0,0,0.45);background: #fff; animation-name: slideright;animation-duration: .5s;animation-fill-mode:forwards; animation-delay: 0.8s; animation-timing-function: ease;
        p {margin:0; font-size: 15px;}
        &.success { border-left: solid 5px #009e00 !important;}
        &.failed { border-left: solid 5px #9e0000 !important;}
    }
}

#custom_search_log { margin:30px 20px 0 2px; float: left; width: 95%;
    .custom_search_log_inner {padding: 20px;box-shadow: 0 1px 3px rgba(0,0,0,0.45);background: #fff; float: left; width: 100%;
        h1 {font-size: 30px;font-weight: 800;float: left;width: 100%;margin-bottom: 0;padding: 0 0 15px;}
        p {font-size: 15px;}
        #ul_header { float: left; width: 100%; margin:0;  background:#333333; display: -webkit-flex; display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
            li { float:left; color: #fff; font-weight: bold; padding:13px 10px; font-size: 13px; letter-spacing: 0.5px; margin:0;
                &.row_1 {width: 4%; text-align: center;}
                &.row_2 {width: 18%;}
                &.row_3 {width: 12%;}
                &.row_4 {width: 27%;}
                &.row_5 {width: 15%;}
                &.row_6 {width: 9%; text-align: right;}
            }
        }
        #ul_body { float: left; width: 100%; margin:0;

            .content { float: left;  background:#fff; width: 100%; transition: background-color .2s; border-bottom: 1px solid #999;
                &.noResults {background: #ffd8d8 !important;}
                li { float: left; padding: 19px 10px; font-size: 15px; margin:0;
                    &.row_1 {width: 4%; text-align: center;}
                    &.row_2 {width: 18%;}
                    &.row_3 {width: 12%;}
                    &.row_4 {width: 27%; padding: 8px 10px 7px;
                        .forslag_form {float: left;width: 100%; transition: all .2s;
                            span {float: left; width: 88%; margin:0; padding: 3px 0 3px 5px;}
                            button {border:none; background: none; padding: 0; color:#9e0000; float: left; width: 10%; transition: all .2s;
                                .fa {}

                                &:hover { color: lighten(#9e0000, 5%); cursor: pointer;;}
                            }
                            // &:nth-child(even) {background: #f0f0f0;}
                        }
                        .change_row_form{ float: left; width: 100%; margin: 5px 0 0; padding: 5px 0 0; border-top: solid 1px #ccc;
                            .addNewBox {float: left; width: 75%; margin:5px 0 0 0;
                                span { font-size: 12px; float: left; width: auto; padding: 3px 15px 5px; background:#ff9b00; color: #fff;
                                    .fa {}

                                    &:hover {cursor: pointer; background: lighten(#ff9b00, 5%);}
                                }
                            }
                        }
                        input.submit {float: right; width: 60px; font-size: 12px; margin:5px 0 0 0; padding: 5px 5px; text-align: center; background: #009e00; color: #fff; transition: all .2s; border-radius: 0;
                            &:hover {background:lighten(#009e00, 5%); cursor: pointer; }
                        }
                        input.field_row {float: left; padding:6px 5px; width: 100%; border-radius:0; margin:0 0 4px; }
                    }
                    &.row_5 {width: 15%;
                        a {display:block; overflow:hidden; text-overflow:ellipsis; white-space:nowrap;}
                    }
                    &.row_6 {width: 9%; padding: 8px 10px 7px;
                        input {float:right;  padding: 6px 5px; margin:0;border-radius: 0; background: #9e0000; color: #fff; transition: all .2s;
                            &:hover {background:lighten(#9e0000, 5%); cursor: pointer; }
                        }
                    }

                }
                &:nth-child(even) {}
            }


        }
    }

}

// Styling för hantering av prenumeranter sidan i admin.
.toplevel_page_manage-subscribers {
    form {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
        margin: 0 0 1rem;

        p {
            text-align: right;
            width: 100%;
        }
    }

    .row-actions {
        .trash a {
            cursor: pointer;
        }
    }

    tr {
        td {
            label {
                cursor: default;
            }

            .row-actions .cancel {
                display: none;
            }
        }
    }

    .areas,
    .docs {
        ul {
            border: 1px solid #ddd;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.07);
            background-color: #fff;
            max-height: 200px;
            max-width: 300px;
            overflow-y: scroll;
            padding: 3px 5px;
        }
    }
}
