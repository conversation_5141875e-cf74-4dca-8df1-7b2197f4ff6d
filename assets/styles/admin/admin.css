@import url(https://fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,400,300,600,700);
@-webkit-keyframes slideUp {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -10px, 0);
            transform: translate3d(0, -10px, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
  }
}
@keyframes slideUp {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -10px, 0);
            transform: translate3d(0, -10px, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes slideright {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-40px, 0, 0);
            transform: translate3d(-40px, 0, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
  }
}

@keyframes slideright {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-40px, 0, 0);
            transform: translate3d(-40px, 0, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes background_change {
  0% {
    background-color: white;
  }
  50% {
    background: #ffdfdf none repeat scroll 0% 0%;
  }
  1000% {
    background-color: white;
  }
}

@keyframes background_change {
  0% {
    background-color: white;
  }
  50% {
    background: #ffdfdf none repeat scroll 0% 0%;
  }
  1000% {
    background-color: white;
  }
}

.google_a {
  -webkit-transition: all .2s;
  transition: all .2s;
  -webkit-animation-name: background_change;
          animation-name: background_change;
  -webkit-animation-duration: 3s;
          animation-duration: 3s;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
}

.google_a ul {
  list-style-type: disc;
  margin: 10px 0 10px 15px;
}

.google_a ul li {
  margin: 6px 0;
}

.google_a ul li ul {
  list-style-type: circle;
  margin: 5px 0 20px 15px;
}

.google_a ul li ul li {
  margin: 4px 0;
}

#TB_ajaxContent {
  overflow: visible !important;
}

.login {
  background: white;
  background: radial-gradient(ellipse at center, white 0%, #fff8f0 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#fff8f0', GradientType=1 );
}

.login #login {
  width: 650px;
}

.login #login h1 a {
  background-image: url("../../../assets/images/logotype.png");
  background-size: auto;
  width: auto;
  outline: 0;
  margin: 0 0 20px;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.login #login h1 a:hover {
  outline: 0;
}

.login #login h1 a:focus {
  outline: 0;
}

.login #login h1 a:active {
  outline: 0;
}

.login #login #login_error {
  -webkit-animation-name: slideright;
          animation-name: slideright;
  -webkit-animation-duration: .5s;
          animation-duration: .5s;
  -webkit-animation-fill-mode: forwards;
          animation-fill-mode: forwards;
  -webkit-animation-timing-function: ease;
          animation-timing-function: ease;
  font-size: 16px;
}

.login #login p.message {
  -webkit-animation-name: slideright;
          animation-name: slideright;
  -webkit-animation-duration: .5s;
          animation-duration: .5s;
  -webkit-animation-fill-mode: forwards;
          animation-fill-mode: forwards;
  -webkit-animation-timing-function: ease;
          animation-timing-function: ease;
  font-size: 16px;
}

.login #login #loginform {
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.45);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.45);
  padding: 26px 24px;
}

.login #login #loginform p label[for=user_login] {
  float: left;
  width: 48%;
}

.login #login #loginform p label[for=user_login] input {
  font-size: 14px;
  padding: 10px 15px;
  margin: 0;
}

.login #login #loginform p label[for=user_login] input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px #fff inset;
}

.login #login #loginform p label[for=user_pass] {
  float: right;
  width: 48%;
}

.login #login #loginform p label[for=user_pass] input {
  font-size: 14px;
  padding: 10px 15px;
  margin: 0;
}

.login #login #loginform p label[for=user_pass] input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px #fff inset;
}

.login #login #loginform p label[for=authenticator] {
  float: left;
  width: 100%;
  margin: 10px 0 0;
}

.login #login #loginform p label[for=authenticator] small {
  display: none;
}

.login #login #loginform p label[for=authenticator] input {
  font-size: 14px;
  padding: 10px 15px;
  margin: 0;
}

.login #login #loginform p label[for=authenticator] input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px #fff inset;
}

.login #login #loginform p.forgetmenot {
  float: left;
  width: 48%;
  margin: 15px 0 0;
}

.login #login #loginform p.forgetmenot label[for=rememberme] {
  float: left;
  width: 100%;
}

.login #login #loginform p.forgetmenot label[for=rememberme] input {
  margin: 0 10px 0 0;
}

.login #login #loginform p.forgetmenot label[for=rememberme] input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px #fff inset;
}

.login #login #loginform p.forgetmenot label[for=rememberme] input:before {
  color: #507E02;
  margin: -12px 0 0 -7px;
  font-size: 35px;
}

.login #login #loginform p.submit {
  width: 25%;
  float: right;
  margin: 15px 0 0;
}

.login #login #loginform p.submit input {
  border-radius: 0;
  padding: 10px 15px;
  height: auto;
  line-height: 21px;
  width: 100%;
  background: #507E02;
  text-shadow: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 0;
  -webkit-transition: background .3s;
  transition: background .3s;
}

.login #login #loginform p.submit input:hover {
  background: #406502;
  outline: 0;
}

.login #login #loginform p.submit input:focus {
  outline: 0;
}

.login #login #loginform p.submit input:active {
  outline: 0;
}

.login #login #lostpasswordform {
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.45);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.45);
}

.login #login #lostpasswordform p label[for=user_login] {
  float: left;
  width: 74%;
}

.login #login #lostpasswordform p label[for=user_login] input {
  font-size: 14px;
  padding: 10px 15px;
  margin: 0;
}

.login #login #lostpasswordform p.submit {
  width: 24%;
  float: right;
  margin: 24px 0 0;
}

.login #login #lostpasswordform p.submit input {
  border-radius: 0;
  padding: 10px 15px;
  height: auto;
  line-height: 21px;
  width: 100%;
  background: #507E02;
  text-shadow: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 0;
  -webkit-transition: background .3s;
  transition: background .3s;
}

.login #login #lostpasswordform p.submit input:hover {
  background: #406502;
}

.login #login p#nav {
  float: right;
  width: 48%;
  margin: 15px 0 0;
  text-align: center;
}

.login #login p#backtoblog {
  float: left;
  width: 48%;
  margin: 15px 0 0;
  text-align: center;
}

#wpadminbar .quicklinks ul.ab-top-menu li {
  border-right: solid 1px #555;
}

#wpadminbar .quicklinks ul.ab-top-menu li a {
  -webkit-transition: color .2s;
  transition: color .2s;
  color: #DADADA !important;
  padding: 0 12px;
}

#wpadminbar .quicklinks ul.ab-top-menu li a:hover, #wpadminbar .quicklinks ul.ab-top-menu li a:focus {
  color: #ff6600 !important;
}

#wpadminbar .quicklinks ul.ab-top-menu li a:before {
  color: #fff !important;
}

#wpadminbar .quicklinks ul.ab-top-menu li a .ab-icon {
  color: #fff;
}

#wpadminbar .quicklinks ul.ab-top-menu li a .ab-icon:before {
  color: #fff !important;
}

#wpadminbar .quicklinks ul.ab-top-menu li a .ab-label {
  color: #fff;
}

#wpadminbar .quicklinks ul.ab-top-menu li .ab-sub-wrapper {
  padding: 5px 10px !important;
}

#wpadminbar .quicklinks ul.ab-top-menu li .ab-sub-wrapper ul {
  padding: 0;
}

#wpadminbar .quicklinks ul.ab-top-menu li .ab-sub-wrapper ul li {
  border-bottom: solid 1px #555;
  border-right: none;
}

#wpadminbar .quicklinks ul.ab-top-menu li .ab-sub-wrapper ul li .ab-item {
  padding: 5px 0;
  position: relative;
  height: 19px !important;
  line-height: 19px !important;
}

#wpadminbar .quicklinks ul.ab-top-menu li .ab-sub-wrapper ul li .ab-item:after {
  content: '\f101';
  font-family: FontAwesome;
  float: right;
  margin-right: 3px;
  font-size: 12px;
  -webkit-transition: margin .2s;
  transition: margin .2s;
}

#wpadminbar .quicklinks ul.ab-top-menu li .ab-sub-wrapper ul li .ab-item:before {
  display: none;
}

#wpadminbar .quicklinks ul.ab-top-menu li .ab-sub-wrapper ul li .ab-item:hover {
  color: #fff;
}

#wpadminbar .quicklinks ul.ab-top-menu li .ab-sub-wrapper ul li .ab-item:hover:after {
  margin-right: 0;
}

#wpadminbar .quicklinks ul.ab-top-menu li .ab-sub-wrapper ul li:last-child {
  border-bottom: none;
}

#wpadminbar .quicklinks ul.ab-top-menu li#wp-admin-bar-wp-logo, #wpadminbar .quicklinks ul.ab-top-menu li#wp-admin-bar-comments {
  display: none;
}

#wpadminbar .quicklinks ul.ab-top-menu li:hover a .ab-label {
  color: #ff6600 !important;
}

#wpadminbar .quicklinks ul.ab-top-menu li#wp-admin-bar-customize {
  display: none;
}

#wpadminbar .quicklinks ul.ab-top-secondary li {
  border-left: solid 1px #555;
}

#wpadminbar .quicklinks ul.ab-top-secondary li .ab-sub-wrapper {
  width: 350px;
  right: -380px;
  margin: 0 -2px 0 0;
  display: block !important;
  opacity: 0;
  -webkit-transition: all .3s;
  transition: all .3s;
}

#wpadminbar .quicklinks ul.ab-top-secondary li .ab-sub-wrapper ul#wp-admin-bar-user-actions li {
  border-left: none;
  margin-left: 110px;
  margin-right: 0px;
}

#wpadminbar .quicklinks ul.ab-top-secondary li .ab-sub-wrapper ul#wp-admin-bar-user-actions li a {
  position: relative;
  padding: 5px 0 5px 20px;
}

#wpadminbar .quicklinks ul.ab-top-secondary li .ab-sub-wrapper ul#wp-admin-bar-user-actions li a span {
  float: left;
  width: 100%;
  line-height: 19px;
  display: inline-block;
}

#wpadminbar .quicklinks ul.ab-top-secondary li .ab-sub-wrapper ul#wp-admin-bar-user-actions li a span.display-name {
  text-transform: capitalize;
}

#wpadminbar .quicklinks ul.ab-top-secondary li .ab-sub-wrapper ul#wp-admin-bar-user-actions li a span.username {
  float: right;
  font-style: italic;
  display: none;
}

#wpadminbar .quicklinks ul.ab-top-secondary li .ab-sub-wrapper ul#wp-admin-bar-user-actions li a .avatar {
  left: -80px;
  height: 64px;
  width: 64px;
}

#wpadminbar .quicklinks ul.ab-top-secondary li .ab-sub-wrapper ul#wp-admin-bar-user-actions li a:after {
  position: absolute;
  left: 0;
  font-family: FontAwesome;
  margin: 1px 8px 0 0;
  float: left;
  color: #fff;
}

#wpadminbar .quicklinks ul.ab-top-secondary li .ab-sub-wrapper ul#wp-admin-bar-user-actions li#wp-admin-bar-user-info {
  margin-bottom: 0;
}

#wpadminbar .quicklinks ul.ab-top-secondary li .ab-sub-wrapper ul#wp-admin-bar-user-actions li#wp-admin-bar-user-info a {
  position: relative;
}

#wpadminbar .quicklinks ul.ab-top-secondary li .ab-sub-wrapper ul#wp-admin-bar-user-actions li#wp-admin-bar-user-info a:after {
  content: '\f007';
  font-family: FontAwesome;
}

#wpadminbar .quicklinks ul.ab-top-secondary li .ab-sub-wrapper ul#wp-admin-bar-user-actions li#wp-admin-bar-edit-profile a {
  position: relative;
}

#wpadminbar .quicklinks ul.ab-top-secondary li .ab-sub-wrapper ul#wp-admin-bar-user-actions li#wp-admin-bar-edit-profile a:after {
  content: '\f085';
  font-family: FontAwesome;
}

#wpadminbar .quicklinks ul.ab-top-secondary li .ab-sub-wrapper ul#wp-admin-bar-user-actions li#wp-admin-bar-logout a {
  position: relative;
}

#wpadminbar .quicklinks ul.ab-top-secondary li .ab-sub-wrapper ul#wp-admin-bar-user-actions li#wp-admin-bar-logout a:after {
  content: '\f08b';
  font-family: FontAwesome;
}

#wpadminbar .quicklinks ul.ab-top-secondary li#wp-admin-bar-my-account a {
  padding: 0 15px;
}

#wpadminbar .quicklinks ul.ab-top-secondary li#wp-admin-bar-search {
  display: none;
}

#wpadminbar .quicklinks ul.ab-top-secondary li.hover .ab-sub-wrapper {
  opacity: 1;
  right: 0;
}

div.settings > label:nth-child(5) {
  display: none;
}

.edit-attachment {
  display: none !important;
}

#adminmenu li.wp-menu-separator {
  height: 5px;
  margin: 7px 0;
  background: rgba(0, 112, 168, 0.7);
}

.send_to_subscribers_form {
  float: left;
  width: 100%;
  padding: 15px 0 20px !important;
}

.send_to_subscribers_form .send_to_subscribers_textarea {
  float: left;
  width: 100%;
  margin: 0 0 10px;
  min-height: 100px;
  max-height: 200px;
  resize: vertical;
  display: block;
  font-size: 13px;
}

.send_to_subscribers_form .send_to_subscribers_textarea.disabled {
  pointer-events: none;
  opacity: 0.7;
}

.send_to_subscribers_form .send_to_subscribers {
  font-size: 13px;
  width: 100%;
  float: left;
  margin: 0;
  padding: 7px 0;
  color: #fff;
  background: #0070a8;
  border: none;
  -webkit-transition: all .2s;
  transition: all .2s;
}

.send_to_subscribers_form .send_to_subscribers:hover {
  background: #0092db;
  text-decoration: none;
  cursor: pointer;
}

.send_to_subscribers_form .send_to_subscribers.disabled {
  pointer-events: none;
  opacity: 0.7;
}

.send_to_subscribers_form .send_to_subscribers_message {
  margin: 15px 0 0;
  float: left;
  width: 100%;
}

.message_box {
  float: right;
  width: 100%;
  margin: 30px 0 0 0;
}

.message_box .inner_box {
  opacity: 0;
  padding: 15px;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.45);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.45);
  background: #fff;
  -webkit-animation-name: slideright;
          animation-name: slideright;
  -webkit-animation-duration: .5s;
          animation-duration: .5s;
  -webkit-animation-fill-mode: forwards;
          animation-fill-mode: forwards;
  -webkit-animation-delay: 0.8s;
          animation-delay: 0.8s;
  -webkit-animation-timing-function: ease;
          animation-timing-function: ease;
}

.message_box .inner_box p {
  margin: 0;
  font-size: 15px;
}

.message_box .inner_box.success {
  border-left: solid 5px #009e00 !important;
}

.message_box .inner_box.failed {
  border-left: solid 5px #9e0000 !important;
}

#custom_search_log {
  margin: 30px 20px 0 2px;
  float: left;
  width: 95%;
}

#custom_search_log .custom_search_log_inner {
  padding: 20px;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.45);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.45);
  background: #fff;
  float: left;
  width: 100%;
}

#custom_search_log .custom_search_log_inner h1 {
  font-size: 30px;
  font-weight: 800;
  float: left;
  width: 100%;
  margin-bottom: 0;
  padding: 0 0 15px;
}

#custom_search_log .custom_search_log_inner p {
  font-size: 15px;
}

#custom_search_log .custom_search_log_inner #ul_header {
  float: left;
  width: 100%;
  margin: 0;
  background: #333333;
  display: -webkit-box;
  display: -ms-flexbox;
  display: box;
  display: flex;
}

#custom_search_log .custom_search_log_inner #ul_header li {
  float: left;
  color: #fff;
  font-weight: bold;
  padding: 13px 10px;
  font-size: 13px;
  letter-spacing: 0.5px;
  margin: 0;
}

#custom_search_log .custom_search_log_inner #ul_header li.row_1 {
  width: 4%;
  text-align: center;
}

#custom_search_log .custom_search_log_inner #ul_header li.row_2 {
  width: 18%;
}

#custom_search_log .custom_search_log_inner #ul_header li.row_3 {
  width: 12%;
}

#custom_search_log .custom_search_log_inner #ul_header li.row_4 {
  width: 27%;
}

#custom_search_log .custom_search_log_inner #ul_header li.row_5 {
  width: 15%;
}

#custom_search_log .custom_search_log_inner #ul_header li.row_6 {
  width: 9%;
  text-align: right;
}

#custom_search_log .custom_search_log_inner #ul_body {
  float: left;
  width: 100%;
  margin: 0;
}

#custom_search_log .custom_search_log_inner #ul_body .content {
  float: left;
  background: #fff;
  width: 100%;
  -webkit-transition: background-color .2s;
  transition: background-color .2s;
  border-bottom: 1px solid #999;
}

#custom_search_log .custom_search_log_inner #ul_body .content.noResults {
  background: #ffd8d8 !important;
}

#custom_search_log .custom_search_log_inner #ul_body .content li {
  float: left;
  padding: 19px 10px;
  font-size: 15px;
  margin: 0;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_1 {
  width: 4%;
  text-align: center;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_2 {
  width: 18%;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_3 {
  width: 12%;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_4 {
  width: 27%;
  padding: 8px 10px 7px;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_4 .forslag_form {
  float: left;
  width: 100%;
  -webkit-transition: all .2s;
  transition: all .2s;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_4 .forslag_form span {
  float: left;
  width: 88%;
  margin: 0;
  padding: 3px 0 3px 5px;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_4 .forslag_form button {
  border: none;
  background: none;
  padding: 0;
  color: #9e0000;
  float: left;
  width: 10%;
  -webkit-transition: all .2s;
  transition: all .2s;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_4 .forslag_form button:hover {
  color: #b80000;
  cursor: pointer;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_4 .change_row_form {
  float: left;
  width: 100%;
  margin: 5px 0 0;
  padding: 5px 0 0;
  border-top: solid 1px #ccc;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_4 .change_row_form .addNewBox {
  float: left;
  width: 75%;
  margin: 5px 0 0 0;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_4 .change_row_form .addNewBox span {
  font-size: 12px;
  float: left;
  width: auto;
  padding: 3px 15px 5px;
  background: #ff9b00;
  color: #fff;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_4 .change_row_form .addNewBox span:hover {
  cursor: pointer;
  background: #ffa51a;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_4 input.submit {
  float: right;
  width: 60px;
  font-size: 12px;
  margin: 5px 0 0 0;
  padding: 5px 5px;
  text-align: center;
  background: #009e00;
  color: #fff;
  -webkit-transition: all .2s;
  transition: all .2s;
  border-radius: 0;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_4 input.submit:hover {
  background: #00b800;
  cursor: pointer;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_4 input.field_row {
  float: left;
  padding: 6px 5px;
  width: 100%;
  border-radius: 0;
  margin: 0 0 4px;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_5 {
  width: 15%;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_5 a {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_6 {
  width: 9%;
  padding: 8px 10px 7px;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_6 input {
  float: right;
  padding: 6px 5px;
  margin: 0;
  border-radius: 0;
  background: #9e0000;
  color: #fff;
  -webkit-transition: all .2s;
  transition: all .2s;
}

#custom_search_log .custom_search_log_inner #ul_body .content li.row_6 input:hover {
  background: #b80000;
  cursor: pointer;
}

.toplevel_page_manage-subscribers form {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  margin: 0 0 1rem;
}

.toplevel_page_manage-subscribers form p {
  text-align: right;
  width: 100%;
}

.toplevel_page_manage-subscribers .row-actions .trash a {
  cursor: pointer;
}

.toplevel_page_manage-subscribers tr td label {
  cursor: default;
}

.toplevel_page_manage-subscribers tr td .row-actions .cancel {
  display: none;
}

.toplevel_page_manage-subscribers .areas ul,
.toplevel_page_manage-subscribers .docs ul {
  border: 1px solid #ddd;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.07);
          box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.07);
  background-color: #fff;
  max-height: 200px;
  max-width: 300px;
  overflow-y: scroll;
  padding: 3px 5px;
}
/*# sourceMappingURL=admin.css.map */