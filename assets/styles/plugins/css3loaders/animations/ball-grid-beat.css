@-webkit-keyframes ball-grid-beat {
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

@keyframes ball-grid-beat {
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.ball-grid-beat {
  width: 57px;
}

.ball-grid-beat > div:nth-child(1) {
  -webkit-animation-delay: -0.03s;
          animation-delay: -0.03s;
  -webkit-animation-duration: 1.59s;
          animation-duration: 1.59s;
}

.ball-grid-beat > div:nth-child(2) {
  -webkit-animation-delay: -0.02s;
          animation-delay: -0.02s;
  -webkit-animation-duration: 0.77s;
          animation-duration: 0.77s;
}

.ball-grid-beat > div:nth-child(3) {
  -webkit-animation-delay: 0.11s;
          animation-delay: 0.11s;
  -webkit-animation-duration: 1.46s;
          animation-duration: 1.46s;
}

.ball-grid-beat > div:nth-child(4) {
  -webkit-animation-delay: 0.54s;
          animation-delay: 0.54s;
  -webkit-animation-duration: 0.75s;
          animation-duration: 0.75s;
}

.ball-grid-beat > div:nth-child(5) {
  -webkit-animation-delay: 0.36s;
          animation-delay: 0.36s;
  -webkit-animation-duration: 1.56s;
          animation-duration: 1.56s;
}

.ball-grid-beat > div:nth-child(6) {
  -webkit-animation-delay: 0.48s;
          animation-delay: 0.48s;
  -webkit-animation-duration: 1.29s;
          animation-duration: 1.29s;
}

.ball-grid-beat > div:nth-child(7) {
  -webkit-animation-delay: 0.38s;
          animation-delay: 0.38s;
  -webkit-animation-duration: 0.92s;
          animation-duration: 0.92s;
}

.ball-grid-beat > div:nth-child(8) {
  -webkit-animation-delay: 0.77s;
          animation-delay: 0.77s;
  -webkit-animation-duration: 0.91s;
          animation-duration: 0.91s;
}

.ball-grid-beat > div:nth-child(9) {
  -webkit-animation-delay: 0.31s;
          animation-delay: 0.31s;
  -webkit-animation-duration: 1.39s;
          animation-duration: 1.39s;
}

.ball-grid-beat > div {
  background-color: #fff;
  width: 15px;
  height: 15px;
  border-radius: 100%;
  margin: 2px;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
  display: inline-block;
  float: left;
  -webkit-animation-name: ball-grid-beat;
          animation-name: ball-grid-beat;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
  -webkit-animation-delay: 0;
          animation-delay: 0;
}
/*# sourceMappingURL=ball-grid-beat.css.map */