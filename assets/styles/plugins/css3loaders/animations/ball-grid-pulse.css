@-webkit-keyframes ball-grid-pulse {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(0.5);
            transform: scale(0.5);
    opacity: 0.7;
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
  }
}

@keyframes ball-grid-pulse {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(0.5);
            transform: scale(0.5);
    opacity: 0.7;
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
  }
}

.ball-grid-pulse {
  width: 57px;
}

.ball-grid-pulse > div:nth-child(1) {
  -webkit-animation-delay: 0.31s;
          animation-delay: 0.31s;
  -webkit-animation-duration: 1.53s;
          animation-duration: 1.53s;
}

.ball-grid-pulse > div:nth-child(2) {
  -webkit-animation-delay: 0.41s;
          animation-delay: 0.41s;
  -webkit-animation-duration: 1.35s;
          animation-duration: 1.35s;
}

.ball-grid-pulse > div:nth-child(3) {
  -webkit-animation-delay: 0.27s;
          animation-delay: 0.27s;
  -webkit-animation-duration: 1s;
          animation-duration: 1s;
}

.ball-grid-pulse > div:nth-child(4) {
  -webkit-animation-delay: 0.66s;
          animation-delay: 0.66s;
  -webkit-animation-duration: 0.74s;
          animation-duration: 0.74s;
}

.ball-grid-pulse > div:nth-child(5) {
  -webkit-animation-delay: 0.43s;
          animation-delay: 0.43s;
  -webkit-animation-duration: 0.97s;
          animation-duration: 0.97s;
}

.ball-grid-pulse > div:nth-child(6) {
  -webkit-animation-delay: 0.68s;
          animation-delay: 0.68s;
  -webkit-animation-duration: 0.93s;
          animation-duration: 0.93s;
}

.ball-grid-pulse > div:nth-child(7) {
  -webkit-animation-delay: 0.32s;
          animation-delay: 0.32s;
  -webkit-animation-duration: 1.44s;
          animation-duration: 1.44s;
}

.ball-grid-pulse > div:nth-child(8) {
  -webkit-animation-delay: 0.56s;
          animation-delay: 0.56s;
  -webkit-animation-duration: 0.81s;
          animation-duration: 0.81s;
}

.ball-grid-pulse > div:nth-child(9) {
  -webkit-animation-delay: 0.53s;
          animation-delay: 0.53s;
  -webkit-animation-duration: 1.12s;
          animation-duration: 1.12s;
}

.ball-grid-pulse > div {
  background-color: #fff;
  width: 15px;
  height: 15px;
  border-radius: 100%;
  margin: 2px;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
  display: inline-block;
  float: left;
  -webkit-animation-name: ball-grid-pulse;
          animation-name: ball-grid-pulse;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
  -webkit-animation-delay: 0;
          animation-delay: 0;
}
/*# sourceMappingURL=ball-grid-pulse.css.map */