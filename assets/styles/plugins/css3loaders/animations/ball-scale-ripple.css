@-webkit-keyframes ball-scale-ripple {
  0% {
    -webkit-transform: scale(0.1);
            transform: scale(0.1);
    opacity: 1;
  }
  70% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 0.7;
  }
  100% {
    opacity: 0.0;
  }
}

@keyframes ball-scale-ripple {
  0% {
    -webkit-transform: scale(0.1);
            transform: scale(0.1);
    opacity: 1;
  }
  70% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 0.7;
  }
  100% {
    opacity: 0.0;
  }
}

.ball-scale-ripple > div {
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
  height: 50px;
  width: 50px;
  border-radius: 100%;
  border: 2px solid #fff;
  -webkit-animation: ball-scale-ripple 1s 0s infinite cubic-bezier(0.21, 0.53, 0.56, 0.8);
          animation: ball-scale-ripple 1s 0s infinite cubic-bezier(0.21, 0.53, 0.56, 0.8);
}
/*# sourceMappingURL=ball-scale-ripple.css.map */