.rek-prediction {
    float: left;
    width: 80%;
    &__title {
        font-size: 1.25rem;
        color: #fff;
        font-weight: 200;
        font-family: "Inter", sans-serif;
        width: 100%;
        margin: 0.5rem 0 1.5rem;
    }
    &__list {
        margin-left: 0;
    }
    &__item {
        border-radius: 200px;
        list-style: none;
        a {
            background-color: white;
            color: #012138;
            text-decoration: none;
            &:hover,
            &:focus {
                background-color: #0070a8 !important;
                color: white;
            }
        }
    }
}

#search_form {
    .wrapper {
        form#search-form {
            .main_serach {
                .rekai-autocomplete {
                    width: 85%;
                    float: left;
                    .search-field {
                        width: 100%;
                    }
                }
            }
        }
    }
}

// Questions and answers
.search-bar .rekai-autocomplete .rekai-question:not(.rekai-question ~ .rekai-question) .rekai-accordion__header::before,
#search_form .rekai-autocomplete .rekai-question:not(.rekai-question ~ .rekai-question) .rekai-accordion__header::before {
    content: "<PERSON><PERSON><PERSON> och svar";
    display: block;
    font-size: 18px;
    line-height: 24px;
    margin-bottom: 8px;
    font-weight: 600;
    margin-top: 8px;
    margin-left: 12px;
}

html[lang="en-US"] .rekai-autocomplete .rekai-question:not(.rekai-question ~ .rekai-question) .rekai-accordion__header::before {
    content: "Questions and answers";
}

.search-bar {
    .rekai-autocomplete {
        .rekai-accordion__body p {
            font-size: 16px;
            line-height: 22px;
        }
    }
}

#search_form {
    .rekai-autocomplete {
        .rekai-accordion__body p {
            font-size: 14px;
            line-height: 16px;
        }
    }
}

p.rekai-accordion__answer-text {
    margin-bottom: 12px;
}

p.rekai-accordion__answer-page {
    margin-top: 12px;
}