#simplejack {
  width: 100%;
  margin: 69px auto 0;
  padding: 15px;
  clear: both;
  background-color: rgba($color-light, .25);
  border-radius: 4px;


  .input-group {
    display: inline-block;
    width: 100%;

    &-50 {
      width: calc(50% - 10px);
      float: left;
      margin-bottom: 12px;

      &:nth-child(even) {
        float: right;
      }
    }

    &-100 {
      width: 100%;
    }

    label {
      display: block;
      font-size: 13px;
      margin: 0 5px 5px;
    }
  }

  .input-field {
    width: 100%;
    border: 1px solid $color-text-darker;
    border-radius: 4px;

    &.select {
      position: relative;
      background-image: url(./svg/drop-down-menu.svg);
      background-position: right 12px center;
      background-size: 13px;
      background-repeat: no-repeat;
    }

    &.textarea {}
  }

  .required {
    color: red;
  }

  .fsubmit {
    display: block;
    width: 100%;
    padding: 12px 20px;
    margin-top: 12px;
    color: white;
    border: none;
    border-radius: 4px;
    background-color: #0070a8;
    cursor: pointer;
  }

  .fsubmit:hover {
    background-color: #012138;
  }



  .failure {
    background: rgba(255, 0, 0, .1) !important;
  }

  .success {
    background: rgba(0, 255, 0, .1) !important;
  }

  @media screen and (max-width: 768px) {

    .input-group {
      display: inline-block;
      width: 100%;

      &-50 {
        width: 100%;
        float: none;
        margin-bottom: 12px;

        &:nth-child(even) {
          float: none;
        }
      }
    }
  }
}