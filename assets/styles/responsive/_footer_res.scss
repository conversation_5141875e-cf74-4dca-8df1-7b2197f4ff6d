@media only screen and (max-width:1440px) {}

@media only screen and (max-width:1280px) {}

@media only screen and (max-width:1140px) {}

@media only screen and (max-width:1024px) {

	//Copyright
	#wrapper_footer {
		.copyright {
			max-width: 1080px;
			padding: 15px 20px;
		}
	}

}

@media only screen and (max-width:966px) {}

@media only screen and (max-width:768px) {}

@media only screen and (max-width:736px) {
	#footer {
		text-align: center;

		.footer_left {
			width: 100%;
			padding: 0;

			.logotype {
				float: left;
				width: 100%;
				max-width: none;
				height: auto;
				padding: 0 20px;

				img {
					width: 100%;
					max-width: 300px;
					height: auto;
				}
			}

			.footer_text {
				float: left;
				width: 100%;
				margin: 20px 0;
				line-height: 24px;

			}

			.footer_contact {
				float: left;
				width: 100%;

				.tel {
					float: left;
					width: 100%;
					margin: 0 0 20px;

					.left {
						float: left;
						width: 100%;

						img {
							max-width: 55px;
							width: 100%;
							height: auto;
						}
					}

					.right {
						float: right;
						width: 100%;

						span {
							font-size: 24px;
							margin: 10px 0 0;
							float: left;
							width: 100%;
							color: $color-bla;
						}
					}
				}

				.epost {
					float: left;
					width: 100%;

					.left {
						float: left;
						width: 100%;
						margin: 0 0 10px;

						img {
							max-width: 55px;
							width: 100%;
							height: auto;
						}
					}

					.right {
						float: right;
						width: 100%;

						span {
							font-size: 24px;
							margin: 0;
							float: left;
							width: 100%;

							a {
								color: $color-bla;
							}
						}

					}
				}

				.adresser {
					margin: 30px 0 0;

					ul {
						li {
							b {}

							span {
								p {}
							}
						}
					}
				}
			}

			.menu-footer-container {
				float: left;
				width: 100%;

				ul {
					margin: 0 auto;
				}
			}
		}

		.footer_right {
			float: right;
			width: 100%;
			padding: 20px 0 0;

			.socialmedia {
				float: left;
				width: 100%;
				height: 80px;

				ul {
					margin: 0;
					padding: 0;
					float: none;
					display: -webkit-inline-box;

					li {
						list-style: none;
						float: none;
						padding: 0 6px;

						span {
							a {
								display: inline-block;
								width: 40px;
								height: 40px;
								float: left;
								border: solid 1px #fff;
								color: #fff;
								text-align: center;
								padding: 7px 0 0 0;
								border-radius: 50em;

								.fa {
									font-size: 18px;
									transition: all .2s;
								}

								&:hover {
									.fa {
										color: $color-bla;
									}
								}
							}
						}
					}
				}
			}

			.lankar {
				float: left;
				width: 100%;

				ul {
					margin: 0;
					padding: 0;

					li {
						list-style: none;
						float: left;
						width: 100%;
						padding: 0;
						margin: 0 0 15px;
						padding: 0 0 15px;

						.right {
							float: left;
							width: 100%;

							a {
								color: $color-text;
								float: left;
								width: 100%;

								h2 {
									margin: -3px 0 2px;
									color: #fff;
									font-size: 24px;
								}
							}

							.text {
								float: left;
								width: 100%;
								color: $color-text;
								font-size: 18px;

								p {
									margin: 0;
									line-height: 22px;
									font-size: 16px;
								}
							}
						}
					}
				}
			}

			#news_letter {
				h2 {}

				.widget_wysija {
					input {
						&.wysija-input {
							width: 100%;
							border-radius: 3px;
							margin: 0 0 10px;
						}

						&.wysija-submit {
							margin: 0;
							width: 100%;
							border-radius: 3px;
						}
					}
				}
			}
		}
	}

	#wrapper_footer {
		.copyright {
			display: block;
			float: left;

			.copy {
				width: 100%;
				text-align: center;
				margin: 0 0 15px;
			}

			.links {
				width: 100%;
				text-align: center;

				a {
					border-right: solid 1px #acacac;
					padding: 0 8px;

					&.about {}

					&.api {}

					&.rss {
						i {}
					}
				}
			}
		}
	}

}

@media only screen and (max-width:568px) {}

@media only screen and (max-width:450px) {}

@media only screen and (max-width:414px) {}

@media only screen and (max-width:320px) {}