@media only screen and (max-width:1440px) {}

@media only screen and (max-width:1280px) {}

@media only screen and (max-width:1140px) {}

@media only screen and (max-width:1024px) {}

@media only screen and (max-width:966px) {}

@media only screen and (max-width:768px) {
	aside.sidebar {
		#sidebar_sub_menu {
			display: none;
		}
	}
}

@media only screen and (max-width:736px) {

	//sidebar
	.sidebar {
		.sidebar_title {
			font-size: 16px;
			color: $color-da-blue;
			margin: 0 0 20px;
		}

		ul {
			margin: 0;

			li {
				padding: 0;
				margin: 0;
				list-style: none;
				padding: 0 0 15px;

				.meta {
					float: left;
					width: 100%;

					.updated {
						text-transform: lowercase;
						font-size: 13px;
						color: $color-text;
						float: left;
						line-height: 9px;
						margin: 0 0 10px;
					}

					span {
						text-transform: lowercase;
						font-size: 13px;
						color: $color-text;
						float: left;
						line-height: 9px;
						margin: 0 0 10px;
					}
				}

				h2 {
					font-size: 18px;
					line-height: 24px;
										font-weight: 200;
					letter-spacing: normal;

					a {
						color: #333;
						color: $color-bla;
					}
				}
			}

			.arkiv_link {
				float: left;
				width: 100%;
				position: relative;
				font-size: 16px;

				&:before {
					float: left;
					width: 20px;
					height: 20px;
					background: url(".../../assets/images/pil-blue.png") no-repeat center center;
					background-size: 16px;
					content: '';
					margin: 1px 8px 0 0;
				}
			}
		}

		//Sidebar ämnesomraden
		#sidebar_amnesomraden {
			float: left;
			width: 100%;

			.sidebar_title {
				font-size: 16px;
				color: $color-da-blue;
			}

			ul {
				margin: 0;
				padding: 0;

				li {
					float: left;
					width: 100%;
					padding: 10px 0;
					border-bottom: solid 1px #ccc;

					.images {
						float: left;
						margin: 0px 15px -6px 0;

						img {
							width: 22px;
							height: auto;
						}
					}

					h2 {
						margin: 5px 0;
						letter-spacing: normal;
					}

					&:last-child {
						border-bottom: none;
					}

					&.krav-och-vagledningsdokument {
						.images {
							margin: 0px 15px -6px 0;

							img {}
						}
					}
				}
			}
		}
	}
}

@media only screen and (max-width:568px) {}

@media only screen and (max-width:450px) {}

@media only screen and (max-width:414px) {}

@media only screen and (max-width:320px) {}