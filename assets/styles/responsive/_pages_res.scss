@media only screen and (max-width:1440px) {}

@media only screen and (max-width:1280px) {}

@media only screen and (max-width:1140px) {}

@media only screen and (max-width:1024px) {


	//Template 2 - Snabbval (<PERSON><PERSON><PERSON><PERSON><PERSON>, om swedac och Lag & Rätt)
	#snabbval {
		ul {
			li {
				width: 100%;
				max-width: 301px;

				.rubrik {
					h2 {
						font-size: 20px;
					}
				}

				&.middle {
					margin: 0 40px 40px;
				}

			}
		}
	}

	//Ämnesområden
	#snabbval_amnesomrodan {
		#grid[data-columns]::before {
			content: '3 .column.size-1of3';
			position: absolute;
			top: -9999px;
			left: -9999px;
		}

		.column {
			float: left;
		}

		.size-1of3 {
			width: 33.333%;
			max-width: 300px;

			&:nth-child(2) {}
		}

		.category_section {
			padding: 0;
			width: 100%;
			float: left;
			margin: 0 0 40px;
			transition: all .2s;

			.content {
				min-height: 320px;
			}
		}
	}

	//Swed<PERSON> Magasin (Bloggen)
	#page-magasin-content {
		ul {
			li {
				width: 100%;
				max-width: 205px;

				.content {
					position: relative;
					height: 205px;
				}

				//Rad 1
				&.article_1 {
					width: 50%;
					max-width: 450px;

					.content {
						height: 450px;
						overflow: hidden;
					}
				}

				&.article_2 {
					float: left;
				}

				&.article_3 {
					float: right;
					margin: 0 0 40px;
				}

				&.article_4 {
					margin: 0 40px 40px 0;
				}

				&.article_5 {
					margin: 0 0 40px 0;
				}

				//rad 2
				&.article_6 {
					float: left;
				}

				&.article_7 {
					float: right;
				}

				&.article_8 {
					margin: 0 40px 0 0;
				}

				&.article_9 {
					margin: 0;
				}

				//Article_10 flyttas med hjälp av jquery och placeras efter article_5
				&.article_10 {
					max-width: 450px;

					.content {
						height: 450px;
						overflow: hidden;
					}
				}


			}
		}
	}

	#swedac_magasin_latest {
		.left {
			width: 25%;

			.omslag {
				img {}
			}
		}

		.right {
			width: 79%;
			max-width: none;

			.nuvarande_info {
				h2 {}

				span {}

				ul {
					li {
						a {}
					}
				}
			}

			.prenumerera_magasin {
				h2 {}

				span {}

				ul {
					li {
						a {}
					}
				}
			}
		}
	}
}

@media only screen and (max-width:966px) {

	//Nyheter
	#frontpage_news {
		float: left;
		width: 100%;
		margin: 40px 0 0;

		.left {
			width: 65.5%;

			ul {
				margin: 0;
				padding: 0;

				li {
					list-style: none;
					float: right;
					width: 100%;
					margin: 0 0 15px;

					.image {
						img {
							transition: normal;
							float: left;
							width: 100%;
							height: auto;
						}
					}

					.info {
						h2 {
							font-size: 42px;
						}
					}
				}
			}
		}

		.right {
			width: 34.5%;
			padding: 0 0 15px 40px;

			ul {
				margin: 0;
				padding: 0;

				li {
					list-style: none;
					float: right;
					width: 100%;
					margin: 0 0 15px;

					.info {
						float: left;
						width: 100%;

						h2 {
							font-size: 18px;
						}
					}
				}
			}
		}
	}



	#swedac_magasin {
		float: left;
		width: 100%;
		margin: 40px 0 0;

		h2 {}

		ul {
			li {
				max-width: 190px;

				.content {
					height: 190px;

					.post_image {
						img {}
					}

					.info {
						h2 {}

						.excerpt {}
					}
				}

				&:nth-child(odd) {}

				&:nth-child(even) {}

				&:nth-last-child(-n+2) {}

				&:first-child {
					max-width: 500px;

					.content {
						height: 420px;

						.info {
							h2 {}

							.excerpt {}
						}
					}
				}
			}
		}
	}

	#fordonsbesiktning {
		.wrapper {
			.left {
				.box {}
			}

			.right {
				padding: 0 30px 0 0;
			}
		}
	}

	//Template 2 - Snabbval (Tjänster, om swedac och Lag & Rätt)
	#snabbval {
		margin: 0;

		ul {
			li {
				width: 100%;
				max-width: 282px;

				.rubrik {
					h2 {
						font-size: 20px;
					}
				}

				&.middle {
					margin: 0 40px 40px;
				}

			}
		}
	}

	//Ämnesområden
	#snabbval_amnesomrodan {
		#grid[data-columns]::before {
			content: '3 .column.size-1of3';
			position: absolute;
			top: -9999px;
			left: -9999px;
		}

		.column {
			float: left;
		}

		.size-1of3 {
			width: 33.333%;
			max-width: 282px;

			&:nth-child(2) {}
		}

		.category_section {
			padding: 0;
			width: 100%;
			float: left;
			margin: 0 0 40px;
			transition: all .2s;

			.content {
				min-height: 320px;
			}
		}
	}

	//Swedac Magasin (Bloggen)
	#page-magasin-content {
		ul {
			li {
				width: 100%;
				max-width: 190px;

				.content {
					position: relative;
					height: 190px;
				}

				//Rad 1
				&.article_1 {
					width: 50%;
					max-width: 426px;

					.content {
						height: 420px;
						overflow: hidden;
					}
				}

				&.article_2 {
					float: left;
				}

				&.article_3 {
					float: right;
					margin: 0 0 40px;
				}

				&.article_4 {
					margin: 0 40px 40px 0;
				}

				&.article_5 {
					margin: 0 0 40px 0;
				}

				//rad 2
				&.article_6 {
					float: left;
				}

				&.article_7 {
					float: right;
				}

				&.article_8 {
					margin: 0 40px 0 0;
				}

				&.article_9 {
					margin: 0;
				}

				//Article_10 flyttas med hjälp av jquery och placeras efter article_5
				&.article_10 {
					max-width: 426px;

					.content {
						height: 420px;
						overflow: hidden;
					}
				}


			}
		}
	}

}

@media only screen and (max-width:768px) {

	//Nyheter
	#frontpage_news {
		float: left;
		width: 100%;
		margin: 40px 0 0;

		.left {
			width: 60%;

			ul {
				margin: 0;
				padding: 0;

				li {
					list-style: none;
					float: right;
					width: 100%;
					margin: 0 0 15px;

					.image {
						img {
							transition: normal;
							float: left;
							width: 100%;
							height: auto;
						}
					}

					.info {
						h2 {
							font-size: 32px;
						}
					}
				}
			}
		}

		.right {
			width: 40%;
			padding: 0 0 15px 0;

			ul {
				margin: 0;
				padding: 0;

				li {
					list-style: none;
					float: right;
					width: 100%;
					margin: 0 0 15px;

					.info {
						float: left;
						width: 100%;

						h2 {
							font-size: 18px;
						}
					}
				}
			}
		}
	}

	//Puffar på startsidan
	#frontpage_puffar {
		ul {
			li {
				.content {
					.info {
						h2 {
							font-size: 18px;
						}
					}
				}

				&:nth-child(even) {
					margin: 0 28px
				}
			}
		}
	}

	#swedac_magasin {
		float: left;
		width: 100%;
		margin: 40px 0 0;

		h2 {}

		ul {
			li {
				max-width: none;
				margin: 0 0 40px;
				padding: 0 0 0 20px;

				.content {
					height: 364px;

					.post_image {
						img {}
					}

					.info {
						h2 {}

						.excerpt {}
					}
				}

				&:nth-child(odd) {
					width: 50%;
					max-width: none;
				}

				&:nth-child(even) {
					width: 50%;
					max-width: none;
				}

				&:nth-child(4) {
					padding: 0 20px 0 0;
				}

				&:nth-last-child(-n+2) {
					width: 50%;
					max-width: none;
					margin: 0;
				}

				&:first-child {
					max-width: 364px;
					padding: 0 20px 0 0;
					margin: 0 0 40px;

					.content {
						height: 364px;

						.info {
							h2 {
								font-size: 40px;
								line-height: 45px;
							}

							.excerpt {}
						}
					}
				}
			}
		}
	}

	.h-500 {
		height: 250px;
	}

	.h-250 {
		padding-bottom: 75%;
	}


	//Template 2 - Snabbval (Tjänster, om swedac och Lag & Rätt)
	#snabbval {
		ul {
			li {
				width: 47%;
				max-width: none;
				float: left;

				.rubrik {
					h2 {
						font-size: 18px;
					}
				}

				&.middle {
					margin: 0 0 40px;
				}

				&:nth-child(even) {
					float: right;
				}
			}
		}
	}

	//Ämnesområden
	#snabbval_amnesomrodan {
		#grid[data-columns]::before {
			content: '2 .column.size-1of2';
			position: absolute;
			top: -9999px;
			left: -9999px;
		}

		.column {
			float: left;
		}

		.size-1of3 {
			width: 33.333%;
			max-width: 300px;

			&:nth-child(2) {}
		}

		.size-1of2 {
			width: 50%;
			padding: 0 20px 0 0;

			&:nth-child(even) {
				padding: 0 0 0 20px;
			}
		}

		.category_section {
			padding: 0;
			width: 100%;
			float: left;
			margin: 0 0 40px;
			transition: all .2s;

			.content {
				min-height: 320px;
			}
		}
	}

	//Swedac Magasin (Bloggen)
	#page-magasin-content {
		ul {
			li {
				max-width: none;
				margin: 0 0 40px;
				padding: 0 0 0 20px;

				.content {
					height: 142px;
					overflow: hidden;

					.post_image {
						img {}
					}

					.info {
						h2 {}

						.excerpt {}
					}
				}

				&:nth-child(odd) {
					width: 50%;
					max-width: none;
				}

				&:nth-child(even) {
					width: 50%;
					max-width: none;
				}

				&:nth-child(4) {
					padding: 0 20px 0 0;
					margin: 0;
				}

				&:nth-last-child(-n+2) {
					width: 50%;
					max-width: none;
					margin: 0;
				}

				&:first-child {
					max-width: 364px;
					padding: 0 20px 0 0;
					margin: 0 0 40px;

					.content {
						height: 324px;
						overflow: hidden;

						.info {
							h2 {
								font-size: 40px;
								line-height: 45px;
							}

							.excerpt {}
						}
					}
				}

				&.article_6 {
					float: left;
					padding: 0 20px 0 0;
				}

				&.article_7 {
					float: right;
					padding: 0 20px 0 0;
				}

				&.article_8 {
					padding: 0 20px 0 0;
				}

				&.article_9 {}

				&.article_10 {
					max-width: 364px;
					padding: 0 0 0 20px;
					margin: 0 0 40px;

					.content {
						height: 324px;
						overflow: hidden;

						.info {
							h2 {
								font-size: 40px;
								line-height: 45px;
							}

							.excerpt {}
						}
					}
				}
			}
		}
	}

	#swedac_magasin_latest {
		.left {
			max-width: 174px;

			.omslag {
				img {}
			}
		}

		.right {
			max-width: none;
			width: 76%;
			;

			.nuvarande_info {
				padding: 15px 20px 20px 20px;

				span {
					p {
						margin: 0 0 17px 0;
					}
				}
			}

			.prenumerera_magasin {
				padding: 15px 20px 15px 20px;

				span {
					p {
						margin: 0 0 17px 0;
					}
				}
			}
		}
	}
}

@media only screen and (max-width:736px) {
	.page-content .certification-container ol li h2 {
		font-size: 18px;
	}

	//Standard
	.page-header {
		h1 {
			font-size: 25px;
			line-height: 40px;
			margin: 0 0 10px;
			letter-spacing: 0.8px;
			font-weight: 400;
		}
	}

	//Nyheter
	#frontpage_news {
		float: left;
		width: 100%;
		margin: 40px 0 0;

		.left {
			width: 100%;
			padding: 0;

			ul {
				margin: 0;
				padding: 0;

				li {
					list-style: none;
					float: right;
					width: 100%;
					margin: 0 0 15px;

					.image {
						img {
							transition: normal;
							float: left;
							width: 100%;
							height: auto;
						}
					}

					.info {
						h2 {
							font-size: 24px;
							line-height: 30px;
							margin: 0 0 5px;
						}

						.excerpt {
							p {
								margin: 0;
							}
						}
					}
				}
			}
		}

		.right {
			width: 100%;
			padding: 0;

			ul {
				margin: 0;
				padding: 0;

				li {
					list-style: none;
					float: right;
					width: 100%;
					margin: 0 0 4px;

					.info {
						float: left;
						width: 100%;

						h2 {
							font-size: 20px;
							margin: 0 0 1px;
						}

						.excerpt {
							p {
								margin: 0;
							}
						}
					}
				}
			}
		}
	}

	#news_newsletter {
		float: left;
		width: 100%;
		margin: 20px 0 0 0;
	}

	//Puffar på startsidan
	#frontpage_puffar {
		ul {
			li {
				width: 100%;
				padding: 15px;

				.content {
					.info {
						h2 {
							font-size: 18px;
						}
					}
				}
			}
		}
	}

	#frontpage_swedac_academy {
		padding: 40px 0;
		margin: 40px 0 0;

		.wrapper {
			.content_swedac_academy {
				width: 100%;

				h2 {
					font-size: 22px;
					color: $color-bla;
					font-weight: 400;
					margin: 0 0 15px;
					letter-spacing: normal;
				}

				.text {
					font-size: 32px;
					font-weight: 300;
					line-height: 38px;

				}

				.lankar {
					margin: 20px 0 0;

					ul {
						margin: 0;
						padding: 0;

						li {
							width: 100%;
							margin: 0 0 10px;

							a {
								float: left;
								width: 100%;
								text-align: center;

							}

							&:last-child {
								margin: 0;
							}
						}
					}
				}
			}

			.images {
				float: right;
				width: 100%;
				padding: 0 0 0 30px;

				img {}
			}
		}
	}

	#swedac_magasin {
		float: left;
		width: 100%;
		margin: 40px 0 0;

		.wrapper {
			padding: 0 !important;
		}

		h2 {
			padding: 0 20px;
		}

		ul {
			li {
				max-width: none;
				margin: 0 !important;
				padding: 0 !important;
				width: 100% !important;
				max-width: none !important;
				max-height: none !important;
				line-height: 0;

				.content {
					a {
						&:focus-visible {
							.cover {
								outline: solid 3px yellow;
								outline-offset: -5px;
							}
						}
					}

					.post_image {
						position: relative;

						img {
							width: 100%;
							height: auto;
							line-height: 1;
						}
					}

					.info {
						h2 {
							font-size: 35px !important;
							padding: 0 !important;
							margin: 0 0 4px !important;
							line-height: 37px !important;
							letter-spacing: 0.8px !important;
						}

						.excerpt {
							font-size: 16px !important;
						}
					}
				}
			}
		}
	}

	#fordonsbesiktning {
		.wrapper {
			display: inherit;
			padding: 0 20px;

			.left {
				float: left;
				width: 100%;
				margin: 0 0 20px;

				.box {
					text-align: center;
				}
			}

			.right {
				float: right;
				width: 100%;
				text-align: center;
				padding: 0;

				h2 {
					float: left;
					width: 100%;
					text-align: center;
					font-weight: 600;
									}

				.text {
					p {}
				}

				.lankar {
					margin: 15px 0 0;
					float: left;
					width: 100%;

					ul {
						li {
							float: none;
							display: block;
							padding: 0;

							a {
								&:before {
									float: none;
									display: inline-block;
									position: relative;
									top: 3px;
								}
							}
						}
					}
				}
			}
		}

	}

	//Template 2 - Snabbval (Tjänster, om swedac och Lag & Rätt)
	#snabbval {
		ul {
			li {
				width: 100%;
				max-width: none;
				float: left;
				margin: 0 0 20px;

				.rubrik {
					h2 {
						font-size: 18px;
					}
				}

				&.middle {
					margin: 0 0 20px;
				}

				&:nth-child(even) {
					float: right;
				}
			}
		}
	}

	//Ankarlänkar
	.page_ankarlankar {
		float: left;
		width: 100%;
		margin: 0 0 15px;

		ul {
			margin: 0;
			padding: 0;

			li {
				padding: 0;
				margin: 0 0 5px;

				a {
					font-size: 16px;

					&:before {
						float: left;
						width: 20px;
						height: 20px;
						background: url("../../assets/images/pil-blue.png") no-repeat center center;
						background-size: 16px;
						content: '';
						margin: 4px 8px 4px 0;
					}
				}
			}
		}
	}

	.page-content {
		float: left;
		width: 100%;

		p {
			&:last-child {
				margin: 0;
			}
		}

		.docs_numberordning {
			.info {
				ul {

					.content {
						h2 {}

						li {
							span {
								&.bet {
									width: 100%;
								}

								&.title {
									width: 100%;
									padding: 0;
								}
							}
						}
					}
				}
			}
		}

		table {
			float: left;
			width: 100% !important;
			font-size: 16px;

			tbody {
				tr {
					td {
						padding: 5px 15px;
					}
				}
			}
		}

		//Bedömarutbildning
		#reg_bedomarutbildning {
			float: left;
			width: 100%;
			display: none;

			#bedomarutbildning_course {
				float: left;
				width: 100%;
				margin: 0;
				padding: 30px;
				background: $color-light;

				.top {
					margin: 0 0 20px;

					.row {
						select {
							padding: 10px 15px;
						}
					}
				}

				.middle {
					.row {
						&.left {
							width: 100%;
						}

						&.right {
							width: 100%;
						}

						&.full {
							width: 100%;
						}

						i {
							top: 11px;
						}
					}
				}

				.bottom {
					.submit-booking {
						padding: 10px 30px;
						float: left;
						width: 100%;
						text-align: center;
					}

					span {
						float: left;
						width: 100%;
					}
				}
			}
		}

		//Intresseanmälan till teknisk bedömare
		#bli-teknisk-bedomare {
			float: left;
			width: 100%;
			margin: 0 0 20px;

			.top {
				.row {
					i {
						top: 11px;
					}

					&.fname {
						width: 100%;
					}

					&.lname {
						width: 100%;
					}

					&.email {
						width: 100%;
					}

					&.phone {
						width: 100%;
					}

					&.analyserar_verksamhet {
						float: left;
						width: 100%;
						border-bottom: solid 1px #D0D0D0;
						padding: 0 0 15px;
					}

					&.verksamheten_utgors_av {
						float: left;
						width: 100%;
					}

					&.intresseorganisation {
						float: left;
						width: 100%;
						border-bottom: solid 1px #D0D0D0;
						padding: 0 0 15px;
					}

					&.ackrediteringssystemet {
						float: left;
						width: 100%;
						border-bottom: solid 1px #D0D0D0;
						padding: 0 0 15px;
					}

					&.ackrediteringsstandarder {}


					&:last-child {
						margin: 0;
					}
				}
			}

			.bottom {
				.submit-booking {
					padding: 10px 30px;
					float: left;
					width: 100%;
					text-align: center;
				}

				span {
					text-align: center;
				}
			}
		}

		// klagomål och synpunkter
		#klagomal_synpunkter {
			float: left;
			width: 100%;
			margin: 0 0 30px;
			padding: 0 0 30px;
			border-bottom: solid 1px #D0D0D0;

			.top {
				.row {
					float: left;
					width: 100%;
					margin: 0 0 20px;
					position: relative;

					i {
						top: 11px;
					}

					label {
						display: none;
					}

					input.inputbox {
						float: left;
						width: 100%;
					}

					textarea.textarea {
						float: left;
						width: 100%;
					}

					&.fname {
						width: 100%;
						float: left;
					}

					&.lname {
						width: 100%;
						float: right;
					}

					&.email {
						width: 100%;
						float: left;
					}

					&.phone {
						width: 100%;
						float: right;
					}

					&:last-child {
						margin: 0;
					}
				}
			}

			.bottom {
				float: left;
				width: 100%;
				margin: 20px 0 0;

				.submit-booking {
					padding: 10px 30px;
					width: 100%;
					float: left;
					text-align: center;
				}

				span {
					text-align: center;
				}
			}
		}

		//Kurser
		#kurser_section {
			float: left;
			width: 100%;
			margin: 30px 0 0;

			ul {
				margin: 0;
				padding: 0;

				li {
					list-style: none;
					padding: 0;
					width: 100%;
					float: left;
					margin: 0 0 15px;
					transition: all .2s;

					.rubrik {
						float: left;
						width: 100%;
						background: $color-knapp;
						padding: 13px 13px;
						margin: 0;

						h2 {
							font-size: 20px;
							font-weight: 400;
							margin: 0;
							position: relative;
							float: left;
							width: 100%;
							color: #fff;
														letter-spacing: normal;

							&:after {
								float: right;
								width: 100%;
								height: 20px;
								background: url("../../assets/images/pil-white.png") no-repeat center right;
								background-size: 20px;
								content: '';
							}
						}
					}

					&:last-child {
						margin: 0;
					}
				}
			}
		}

		#booking_course {
			float: left;
			width: auto;
			margin: 30px -20px 0;
			padding: 20px;
			background: $color-light;

			.messange_content {
				float: left;
				width: 100%;
				margin: 0 0 30px;

				.inner {
					float: left;
					width: 100%;
					padding: 20px;

					h2 {
						margin: 0 0 15px;
						letter-spacing: normal;
					}

					p {
						margin: 0 0 0px;
					}

					b {
						letter-spacing: normal;
					}

					&.failed {
						background-color: #FDD !important;
						border: solid 1px #CC002F !important;
					}

					&.success {
						background-color: #E5FFE5 !important;
						border: solid 1px #00925C !important;
					}

				}
			}

			h3 {
				margin: 0 0 10px;
				letter-spacing: normal;
			}

			.top {
				float: left;
				width: 100%;
				margin: 0 0 30px;

				.row {
					float: left;
					width: 100%;
					margin: 0 0 20px;
					position: relative;

					select {
						margin: 0;
						padding: 0;
						padding: 10px 15px;
						float: left;
						width: 100%;
						font-size: 15px;
						border-radius: 4px;
						background: #fff url("../../assets/images/select-bg.png") no-repeat right;
						-webkit-appearance: none;

						option {
							padding: 0;
							list-style: none;
							float: left;
							padding: 0 30px 0 0;
							width: 100%;
							margin: 0 0 7px;
						}

						&#swedac_education {
							margin: 0 0 15px;
						}
					}

					.kurstillfalle_section {
						display: none;
						float: left;
						width: 100%;

						select {
							margin: 0;
							padding: 0;
							padding: 10px 15px;
							float: left;
							width: 100%;
							font-size: 15px;
							border-radius: 4px;
							background: #fff url("../../assets/images/select-bg.png") no-repeat right;
							-webkit-appearance: none;

							option {
								padding: 0;
								list-style: none;
								float: left;
								padding: 0 30px 0 0;
								width: 100%;
								margin: 0 0 7px;
							}
						}
					}


					i {
						position: absolute;
						top: 18px;
						right: 65px;
						transform: scale(0);
						transition: transform .2s;

						&.fa-times {
							color: #CC002F;
						}

						&.fa-check {
							color: #00925C;
						}
					}

					&.education {}
				}
			}

			.middle {
				.row {
					i {
						top: 11px;
					}

					input {
						padding: 10px 15px;
					}

					&.fname {
						width: 100%;
					}

					&.lname {
						width: 100%;
					}

					&:last-child {
						margin: 0;
					}
				}
			}

			.middle_extra {
				margin: 0 0 15px;

				.create_one_more {
					padding: 10px 30px;
					width: 100%;
					text-align: center;
				}

				#extra_section {
					float: left;
					width: 100%;

					.field_section {
						.row {
							i {
								top: 11px;

								&.fa-times {
									color: #CC002F;
								}

								&.fa-check {
									color: #00925C;
								}
							}

							input {
								padding: 10px 15px;
							}

							&.fname {
								width: 100%;
							}

							&.lname {
								width: 100%;
							}

						}

						&:last-child {
							border-bottom: none;
						}
					}
				}

				.no_deltagare {
					float: left;
					width: 100%;
					font-size: 16px;
					margin: 0 0 10px;
				}

			}

			.bottom {
				float: left;
				width: 100%;
				margin: 20px 0 0 0;

				.left {
					float: left;
					width: 100%;
					margin: 0 0 15px;

					.submit-booking {
						padding: 10px 30px;
						width: 100%;
						text-align: center;

						&:hover {
							background: lighten($color-bla, 10%);
							text-decoration: none;
							cursor: pointer;
						}
					}
				}

				.right {
					float: left;
					width: 100%;
					padding: 0;
					margin: 0 0 15px;

					a {
						text-align: center;
					}
				}

				span {
					float: left;
					width: 100%;
					text-align: center;
				}
			}
		}

		//Snabbval
		#page_snabbval {
			float: left;
			width: 100%;
			margin: 30px 0 0;

			ul {
				margin: 0;
				padding: 0;

				li {
					list-style: none;
					padding: 0;
					width: 100%;
					float: left;
					margin: 0 0 30px;
					transition: all .2s;

					.rubrik {
						float: left;
						width: 100%;
						background: $color-knapp;
						padding: 13px 13px;
						margin: 0;

						h2 {
							font-size: 20px;
							font-weight: 400;
							margin: 0;
							position: relative;
							float: left;
							width: 100%;
							color: #fff;
														letter-spacing: normal;

							&:after {
								float: right;
								width: 100%;
								height: 20px;
								background: url("../../assets/images/pil-white.png") no-repeat center right;
								background-size: 20px;
								content: '';
							}
						}
					}

					&.last-child {
						margin: 0;
					}
				}
			}
		}

		//Kontaktperson
		.kontaktperson_section {
			padding: 20px;
			margin: 20px 0 0;

			ul {
				li {
					.left {}

					.right {
						span {
							&.namn {}

							&.telefon {}

							&.epost {}
						}
					}
				}
			}
		}

		//Dokument
		.dokument_section {
			float: left;
			width: 100%;
			background: $color-light;
			padding: 20px;
			margin: 40px 0 0 0;

			h2 {
				font-weight: 600;
				margin: 0 0 10px;
				position: relative;
				float: left;
								letter-spacing: normal;
			}

			ul {
				margin: 0;
				padding: 0;

				li {
					float: left;
					width: 100%;
					list-style: none;
					padding: 15px 0;
					border-top: solid 1px #ccc;

					.left {
						display: block;
						max-width: 30px;
					}

					.right {
						float: right;
						width: 85%;
						padding: 0;

						span {
							float: left;
							width: 100%;
							font-size: 16px;
							margin: 0;

							&.file {
								line-height: 23px
							}

							&.file_name {
								text-transform: lowercase;
								color: $color-text-darker;
							}

							&.file_type_size {
								text-transform: uppercase;
								color: $color-text-darker;
							}
						}
					}

					&:first-child {
						border-top: none;
						padding: 0;
					}
				}
			}
		}

		//Pressmeddelande
		.rss_press {
			float: left;
			width: 100%;
			margin: 20px 0 0 0;

			b {
				float: left;
				width: 100%;
				border-bottom: solid 1px #ccc;
				margin: 0 0 20px;
				padding: 0 0 10px;
			}

			.item {
				padding: 15px 0;

				h2 {
					letter-spacing: normal;
					font-size: 20px;
					font-weight: 400;
					margin: 0 0 10px;
					position: relative;
					float: left;
					width: 100%;
					color: #fff;
									}

				.description {
					p {
						font-size: 16px;
						line-height: 24px;
					}
				}
			}
		}

		//Ledning
		.ledning_page {
			float: left;
			width: 100%;

			.sektion {
				float: left;
				width: 100%;
				margin: 0 0 15px;
				padding: 0 0 15px;
				border-bottom: solid 1px #dddddd;

				h3 {
					letter-spacing: normal;
					margin: 0 0 15px;
				}

				ul {
					float: left;
					width: 100%;
					margin: 0;

					li {
						float: left;
						width: 100%;
						list-style: none;
						padding: 0;
						margin: 0 0 10px;
						text-align: center;

						.profilbild {
							float: left;
							width: 100%;
							min-height: 250px;
							margin: 0 0 5px;

							img {
								width: 100%;
								max-width: none;
								height: auto;
							}
						}

						.roll {
							float: left;
							width: 100%;
							line-height: 25px;
						}

						.namn {
							float: left;
							width: 100%;
						}

						&:nth-child(even) {
							width: 100%;
							float: left;
						}
					}
				}
			}
		}

		//Kontaktuppgifter
		.kontaktuppgifter_kontor {
			margin: 15px 0 20px;

			ul {
				li {
					width: 100%;

					.google_map {
						float: left;
						width: 100%;
						height: 200px;
						margin: 0;
					}

					.adress {
						float: left;
						width: 100%;
						margin: 0 0 15px;
					}

					&:nth-child(even) {
						width: 100%;
						float: right;
					}
				}
			}
		}
	}

	//Ämnesområden
	#snabbval_amnesomrodan {
		#grid[data-columns]::before {
			content: '1 .column.size-1of1';
			position: absolute;
			top: -9999px;
			left: -9999px;
		}

		.column {
			float: left;
		}

		.size-1of3 {
			width: 33.333%;
			max-width: 300px;

			&:nth-child(2) {}
		}

		.size-1of2 {
			width: 50%;
			padding: 0 20px 0 0;

			&:nth-child(even) {
				padding: 0 0 0 20px;
			}
		}

		.size-1of1 {
			width: 100%;
			padding: 0;

			&:nth-child(even) {
				padding: 0;
			}
		}

		.category_section {
			padding: 0;
			width: 100%;
			float: left;
			margin: 0 0 20px;
			transition: all .2s;

			.content {
				min-height: 150px;
			}
		}
	}


	//Nyheter
	#page-news-content {
		ul {
			li {
				padding: 20px 0;

				.left {
					width: 100%;

					h2 {
												letter-spacing: normal;
						font-size: 20px;
					}

					.excerpt {
						line-height: 22px;
						font-size: 16px;
					}
				}

				.right {
					display: none;
				}

				&:last-child {
					border-bottom: none;
				}
			}
		}
	}

	//Swedac Magasin (Bloggen)
	#page-magasin-content {
		float: left;
		margin: 0 -20px;
		width: auto;

		.wrapper {
			padding: 0 !important;
		}

		ul {
			li {
				max-width: none;
				margin: 0 !important;
				padding: 0 !important;
				width: 100% !important;
				max-width: none !important;
				max-height: none !important;
				line-height: 0;

				.content {
					height: auto !important;
					overflow: hidden;

					.post_image {
						position: relative;

						img {
							width: 100%;
							height: auto;
							line-height: 1;
						}
					}

					.info {
						h2 {
							font-size: 35px !important;
							padding: 0 !important;
							margin: 0 0 4px !important;
							line-height: 37px !important;
							letter-spacing: 0.8px !important;
						}

						.excerpt {
							font-size: 16px !important;
						}
					}
				}
			}
		}
	}

	#swedac_magasin_latest {
		margin: 40px -20px 0 -20px;
		width: auto;

		.left {
			max-width: none;
			width: 100%;

			.omslag {
				img {
					line-height: 0;
				}
			}
		}

		.right {
			max-width: none;
			width: 100%;

			.nuvarande_info {
				padding: 15px 20px 20px 20px;

				span {
					p {
						margin: 0 0 17px 0;
					}
				}
			}

			.prenumerera_magasin {
				padding: 15px 20px 15px 20px;

				span {
					p {
						margin: 0 0 17px 0;
					}
				}

				.prenumerera_magasin_form {
					float: left;
					width: 100%;
					margin: 10px 0 0;

					form {
						.top {
							.row {
								input {}

								&.namn {
									width: 40%;
								}

								&.postadress {
									width: 56%;
									margin: 0 0 0 4%;
								}
							}

							.send {
								float: left;
								width: 100%;
								margin: 15px 0 0;

								.submit {
									padding: 10px 0;
								}
							}
						}
					}
				}
			}
		}
	}

	//GD Bloggen
	#page-bloggen-content {
		.blogg-post {
			float: left;
			width: 100%;
			padding: 0 0 70px 0;

			.post-header {
				h2 {
					font-size: 35px;
					line-height: 40px;
					margin: 0 0 10px;
				}

				.meta-info {
					font-size: small;

					.updated {
						text-transform: lowercase;
					}

					.author {}
				}
			}

			.post-content {
				p {
					&:last-child {
						margin: 0;
					}
				}
			}

			.post-footer {
				float: left;
				width: 100%;
				margin: 20px 0 0 0;

			}

			&:last-child {
				padding: 0;
			}
		}
	}

	#toggleToResults .wrapper {
		flex-direction: column;

		.toggleButton {
			width: 100%;
			margin: 0 0 25px;

			&:last-child {
				margin: 0;
			}
		}
	}

}

@media only screen and (max-width:568px) {

	// Medarbetare (Träffa swedacs medarbetare)
	.medarbetare_holder {
		ul {
			li {
				.medar_left {
					float: left;
					width: 100%;

					img {
						width: auto;
						line-height: 0;
					}
				}

				.medar_right {
					float: left;
					width: 100%;
					padding: 20px 0 0 0;
				}

				.medar_full {
					float: left;
					width: 100%;
				}

			}
		}
	}

}

@media only screen and (max-width:450px) {

	// Medarbetare (Träffa swedacs medarbetare)
	.medarbetare_holder {
		ul {
			li {
				.medar_left {
					img {
						width: 100%;
					}
				}
			}
		}
	}
}

@media only screen and (max-width:414px) {}

@media only screen and (max-width:320px) {}