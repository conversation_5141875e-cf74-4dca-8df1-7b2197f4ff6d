@media only screen and (max-width:1440px) {}

@media only screen and (max-width:1280px) {}

@media only screen and (max-width:1140px) {}

@media only screen and (max-width:1024px) {
	#single_section_post {
		.main {
			padding: 0 30px 0 0;
			max-width: 660px;

			.single-post {
				.post-header {
					h1 {
						font-size: 50px;
						line-height: 55px;
						letter-spacing: 1.2px;
					}
				}

				.post-content {
					p {
						&:last-child {
							margin: 0;
						}
					}
				}

				.post-footer {}
			}

			//Kurser
			.kurs-post {
				.post-header {
					h1 {
						font-size: 50px;
						line-height: 55px;
						letter-spacing: 1.2px;
					}
				}

				.post-content {
					p {
						&:last-child {
							margin: 0;
						}
					}
				}

				#reg_course {
					#booking_course {
						.top {
							.row {
								i {}
							}
						}

						.middle {
							.row {
								i {}

								&.fname {
									width: 48%;
								}

								&.lname {
									width: 48%;
								}

								&:last-child {
									margin: 0;
								}
							}
						}

						.middle_extra {

							.create_one_more {}

							#extra_section {
								.field_section {
									.row {
										i {}

										&.fname {
											width: 48%;
										}

										&.lname {
											width: 48%;
										}

										&:last-child {
											margin: 0;
										}
									}

									&:last-child {
										border-bottom: none;
									}
								}
							}
						}

						.bottom {}
					}
				}

				.post-footer {}
			}
		}

		.sidebar {
			float: right;
			width: 100%;
			max-width: 300px;
			padding: 0 0 0 5px;

		}
	}

	//Ämnesområde (Single)
	.single_section_post_amnesomrade {
		.main {
			padding: 0 30px 0 0;
			max-width: 660px;

			.single-post {
				.post-header {
					h1 {}
				}

				//Ledningssystem
				.ledningssystem-section {
					ul {
						li {
							a {}
						}
					}
				}

				.post-content {
					float: left;
					width: 100%;

					p {
						&:last-child {
							margin: 0;
						}
					}
				}

				.post-footer {
					#spec_links {
						li {
							.head {
								.images {
									img {}
								}

							}

							.r_more {
								top: 25px;
							}

							.hidden_content {
								//Dokuemnt

								//Externa länkar
								.externa_lankar {
									float: left;
									width: 100%;

									ul {
										margin: 0 0 0 10px;
									}
								}

								//Relaterad information
								.relaterad_information {
									float: left;
									width: 100%;

									ul {
										margin: 0 0 0 10px;
									}
								}

								//Bevaka innehåll
								.starta_bevakning {
									span {}

									.row {
										width: 48%;
										position: relative;

										input {}

										&.name {}

										&.email {}

										i {}
									}

									.submit_bevakning {
										border-radius: 4px;
									}
								}

							}

							&:last-child {
								margin: 0;
							}
						}
					}
				}
			}
		}

		.sidebar {
			float: right;
			width: 100%;
			max-width: 300px;
			padding: 0 0 0 5px;

		}
	}

	//swedac magasin

	#single_section_post_swedac_magasin {
		.main {
			.latest_swedac_magasin {
				ul {
					li {
						width: 100%;
						max-width: 215px;
						float: left;
						padding: 0;
						list-style: none;
						margin: 0 0 40px;

						.content {
							position: relative;
							height: 215px;

							.post_image {
								position: absolute;

								img {}
							}

							.info {
								position: absolute;
								z-index: 2;
								color: #fff;
								float: left;
								width: 100%;
								padding: 15px;
								bottom: 0;

								h2 {
									color: #fff;
									font-size: 20px;
									line-height: 24px;
									margin: 0 0 4px;
																		letter-spacing: 0.8px;
								}

								.excerpt {
									font-size: 16px;
								}
							}
						}

						&:nth-child(odd) {
							float: right;
						}

						&:nth-child(even) {
							float: left;
							margin: 0 0 40px;
						}

						&:nth-last-child(-n+2) {
							margin: 0;
						}

						&:first-child {
							width: 50%;
							max-width: 470px;
							float: left !important;
							margin: 0 40px 0 0;

							.content {
								width: 100%;
								height: 470px;
								margin: 0 40px 0 0;
								position: relative;

								.info {
									position: absolute;
									z-index: 2;
									color: #fff;
									float: left;
									width: 100%;
									padding: 15px;
									bottom: 0;

									h2 {
										color: #fff;
										font-size: 50px;
										line-height: 50px;
										margin: 0 0 10px;
																			}

									.excerpt {
										font-size: 16px;
									}
								}
							}
						}
					}
				}
			}
		}
	}

	//kalender
	#single_section_posts_kalender {
		h1 {}

		.single_section_post_kalender {
			padding: 40px;

			.kalender_date {
				width: 15%;

			}

			.kalender_info {
				width: 80%;

				table {
					tr {
						td {
							&.first {}

							&.last {}
						}
					}
				}
			}

			.entry-summary {
				width: 70%;
			}

			&.last-child {
				margin: 0;
			}
		}

		//Single sida
		.main {
			float: left;
			width: 100%;
			padding: 0 30px 0 0;
			max-width: 660px;
			margin: 0;
			border-right: solid 1px #DEDEDE;

			.single-post-kalender {
				float: left;
				width: 100%;

				.kalender_date {
					width: 25%;
				}

				.kalender_info {
					width: 70%;

					table {
						tr {
							td {
								&.first {}

								&.last {}
							}
						}
					}
				}

				.post-content {}

				&.last-child {
					margin: 0;
				}
			}
		}

		.sidebar {
			float: right;
			width: 100%;
			max-width: 300px;
			padding: 0 0 0 5px;

		}
	}



}

@media only screen and (max-width:966px) {
	#single_section_post {
		.main {
			padding: 0 30px 0 0;
			max-width: 630px;

			.single-post {
				.post-header {
					h1 {
						font-size: 50px;
						line-height: 55px;
						letter-spacing: 1.2px;
					}
				}

				.post-content {
					p {
						&:last-child {
							margin: 0;
						}
					}
				}

				.post-footer {}
			}

			//Kurser
			.kurs-post {
				.post-header {
					h1 {
						font-size: 50px;
						line-height: 55px;
						letter-spacing: 1.2px;
					}
				}

				.post-content {
					p {
						&:last-child {
							margin: 0;
						}
					}
				}

				#reg_course {
					#booking_course {
						.top {
							.row {
								i {}
							}
						}

						.middle {
							.row {
								i {}

								&.fname {
									width: 48%;
								}

								&.lname {
									width: 48%;
								}

								&:last-child {
									margin: 0;
								}
							}
						}

						.middle_extra {

							.create_one_more {}

							#extra_section {
								.field_section {
									.row {
										i {}

										&.fname {
											width: 48%;
										}

										&.lname {
											width: 48%;
										}

										&:last-child {
											margin: 0;
										}
									}

									&:last-child {
										border-bottom: none;
									}
								}
							}
						}

						.bottom {}
					}
				}

				.post-footer {}
			}
		}

		.sidebar {
			float: right;
			width: 100%;
			max-width: 270px;
			padding: 0 0 0 5px;

		}
	}

	//Ämnesområde (Single)
	.single_section_post_amnesomrade {
		.main {
			padding: 0 30px 0 0;
			max-width: 630px;

			.single-post {
				.post-header {
					h1 {}
				}

				//Ledningssystem
				.ledningssystem-section {
					ul {
						li {
							a {}
						}
					}
				}

				.post-content {
					float: left;
					width: 100%;

					p {
						&:last-child {
							margin: 0;
						}
					}
				}

				.post-footer {
					#spec_links {
						li {
							.head {
								.images {
									img {}
								}

							}

							.r_more {
								top: 25px;
							}

							.hidden_content {
								//Dokuemnt

								//Externa länkar
								.externa_lankar {
									float: left;
									width: 100%;

									ul {
										margin: 0 0 0 10px;
									}
								}

								//Relaterad information
								.relaterad_information {
									float: left;
									width: 100%;

									ul {
										margin: 0 0 0 10px;
									}
								}

								//Bevaka innehåll
								.starta_bevakning {
									span {}

									.row {
										width: 48%;
										position: relative;

										input {}

										&.name {}

										&.email {}

										i {}
									}

									.submit_bevakning {
										border-radius: 4px;
									}
								}

							}

							&:last-child {
								margin: 0;
							}
						}
					}
				}
			}
		}

		.sidebar {
			float: right;
			width: 100%;
			max-width: 270px;
			padding: 0 0 0 5px;

		}
	}

	#single_section_post_swedac_magasin {
		.main {
			.latest_swedac_magasin {
				ul {
					li {
						width: 100%;
						max-width: 200px;
						float: left;
						padding: 0;
						list-style: none;
						margin: 0 0 40px;

						.content {
							position: relative;
							height: 200px;

							.post_image {
								position: absolute;

								img {}
							}

							.info {
								position: absolute;
								z-index: 2;
								color: #fff;
								float: left;
								width: 100%;
								padding: 15px;
								bottom: 0;

								h2 {
									color: #fff;
									font-size: 20px;
									line-height: 24px;
									margin: 0 0 4px;
																		letter-spacing: 0.8px;
								}

								.excerpt {
									font-size: 16px;
								}
							}
						}

						&:nth-child(odd) {
							float: right;
						}

						&:nth-child(even) {
							float: left;
							margin: 0 0 40px;
						}

						&:nth-last-child(-n+2) {
							margin: 0;
						}

						&:first-child {
							width: 50%;
							max-width: 442px;
							float: left !important;
							margin: 0 40px 0 0;

							.content {
								width: 100%;
								height: 442px;
								margin: 0 40px 0 0;
								position: relative;

								.info {
									position: absolute;
									z-index: 2;
									color: #fff;
									float: left;
									width: 100%;
									padding: 15px;
									bottom: 0;

									h2 {
										color: #fff;
										font-size: 50px;
										line-height: 50px;
										margin: 0 0 10px;
																			}

									.excerpt {
										font-size: 16px;
									}
								}
							}
						}
					}
				}
			}
		}
	}


	//kalender
	#single_section_posts_kalender {
		h1 {}

		.single_section_post_kalender {
			padding: 40px;

			.kalender_date {
				width: 15%;

			}

			.kalender_info {
				width: 80%;

				table {
					tr {
						td {
							&.first {}

							&.last {}
						}
					}
				}
			}

			.entry-summary {
				width: 70%;
			}

			&.last-child {
				margin: 0;
			}
		}

		//Single sida
		.main {
			padding: 0 30px 0 0;
			max-width: 630px;
			border-right: none;
			border-bottom: solid 1px #DEDEDE;

			.single-post-kalender {
				float: left;
				width: 100%;
				padding: 20px;

				.kalender_date {
					width: 25%;
				}

				.kalender_info {
					width: 70%;

					table {
						tr {
							td {
								&.first {}

								&.last {}
							}
						}
					}
				}

				.post-content {}
			}
		}

		.sidebar {
			float: right;
			width: 100%;
			max-width: 270px;
			padding: 0 0 0 5px;

		}
	}

}

@media only screen and (max-width:768px) {
	#single_section_post {
		.main {
			padding: 0 0 20px 0;
			max-width: none;
			margin: 0;
			border-right: none;
			border-bottom: solid 1px #DEDEDE;

			.single-post {
				.post-header {
					h1 {
						font-size: 50px;
						line-height: 55px;
						letter-spacing: 1.2px;
					}
				}

				.post-content {
					p {
						&:last-child {
							margin: 0;
						}
					}
				}

				.post-footer {}
			}

			//Kurser
			.kurs-post {
				.post-header {
					h1 {
						font-size: 50px;
						line-height: 55px;
						letter-spacing: 1.2px;
					}
				}

				.post-content {
					p {
						&:last-child {
							margin: 0;
						}
					}
				}

				#reg_course {
					#booking_course {
						.top {
							.row {
								i {}
							}
						}

						.middle {
							.row {
								i {}

								&.fname {
									width: 48%;
								}

								&.lname {
									width: 48%;
								}

								&:last-child {
									margin: 0;
								}
							}
						}

						.middle_extra {

							.create_one_more {}

							#extra_section {
								.field_section {
									.row {
										i {}

										&.fname {
											width: 48%;
										}

										&.lname {
											width: 48%;
										}

										&:last-child {
											margin: 0;
										}
									}

									&:last-child {
										border-bottom: none;
									}
								}
							}


						}

						.bottom {}
					}
				}

				.post-footer {}
			}
		}

		.sidebar {
			float: right;
			width: 100%;
			max-width: none;
			padding: 20px 0 0 0;

		}
	}

	//Ämnesområde (Single)
	.single_section_post_amnesomrade {
		.main {
			padding: 30px 0 0 0;
			margin: 0;
			max-width: none;
			border-right: none;
			border-top: solid 1px #DEDEDE;

			.single-post {
				.post-header {
					h1 {}
				}

				//Ledningssystem
				.ledningssystem-section {
					ul {
						li {
							a {}
						}
					}
				}

				.post-content {
					float: left;
					width: 100%;

					p {
						&:last-child {
							margin: 0;
						}
					}
				}

				.post-footer {
					#spec_links {
						li {
							.head {
								.images {
									img {}
								}

							}

							.r_more {
								top: 25px;
							}

							.hidden_content {
								//Dokuemnt

								//Externa länkar
								.externa_lankar {
									float: left;
									width: 100%;

									ul {
										margin: 0 0 0 10px;
									}
								}

								//Relaterad information
								.relaterad_information {
									float: left;
									width: 100%;

									ul {
										margin: 0 0 0 10px;
									}
								}

								//Bevaka innehåll
								.starta_bevakning {
									span {}

									.row {
										width: 48%;
										position: relative;

										input {}

										&.name {}

										&.email {}

										i {}
									}

									.submit_bevakning {
										border-radius: 4px;
									}
								}

							}

							&:last-child {
								margin: 0;
							}
						}
					}
				}
			}
		}

		.sidebar {
			float: right;
			width: 100%;
			max-width: none;
			padding: 0 0 20px 0;

			#sidebar_amnesomraden {
				.sidebar_title {
					margin: 0 0 10px;
				}

				ul {
					li {
						padding: 6px 0;
					}
				}
			}
		}
	}


	//kalender
	#single_section_posts_kalender {
		h1 {}

		.single_section_post_kalender {
			padding: 20px;

			.kalender_date {
				width: 20%;

			}

			.kalender_info {
				width: 75%;

				table {
					tr {
						td {
							&.first {}

							&.last {}
						}
					}
				}
			}

			.entry-summary {
				width: 100%;
			}

			&.last-child {
				margin: 0;
			}
		}

		//Single sida
		.main {
			padding: 0 0 20px 0;
			max-width: none;
			margin: 0;
			border-right: none;
			border-bottom: solid 1px #DEDEDE;

			.single-post-kalender {
				float: left;
				width: 100%;
				padding: 0;

				.kalender_date {
					width: 25%;
				}

				.kalender_info {
					width: 70%;

					table {
						tr {
							td {
								&.first {}

								&.last {}
							}
						}
					}
				}

				.post-content {}
			}
		}

		.sidebar {
			float: right;
			width: 100%;
			max-width: none;
			padding: 20px 0 0 0;

		}
	}

	//Swedac Magasin
	#single_section_post_swedac_magasin {
		.main {
			float: left;
			width: 100%;
			padding: 0;
			margin: 0;

			.single-post {
				padding: 30px 0 0 0;
				margin: 0;

				.post-header {
					float: left;
					width: 100%;
					max-width: none;
					padding: 0;
					margin: 0;
					background: #fff;

					h1 {
						font-size: 50px;
						line-height: 55px;
						margin: 0 0 15px;
					}
				}

				.post-content {
					p {
						width: 100%;
						max-width: none;

						&:last-child {
							margin: 0;
						}
					}
				}

				.post-footer {
					float: left;
					width: 100%;
					margin: 30px 0 0 0;

				}
			}


			.latest_swedac_magasin {
				float: left;
				width: 100%;
				margin: 40px 0 0;

				ul {
					li {
						max-width: none;
						margin: 0 0 40px;
						padding: 0 0 0 20px;

						.content {
							height: 152px;
							overflow: hidden;

							.post_image {
								img {}
							}

							.info {
								h2 {}

								.excerpt {}
							}
						}

						&:nth-child(odd) {
							width: 50%;
							max-width: none;
						}

						&:nth-child(even) {
							width: 50%;
							max-width: none;
						}

						&:nth-child(4) {
							padding: 0 20px 0 0;
						}

						&:nth-last-child(-n+2) {
							width: 50%;
							max-width: none;
							margin: 0;
						}

						&:first-child {
							max-width: 364px;
							padding: 0 20px 0 0;
							margin: 0 0 40px;

							.content {
								height: 344px;
								overflow: hidden;

								.info {
									h2 {}

									.excerpt {}
								}
							}
						}
					}
				}
			}
		}
	}

}

@media only screen and (max-width:736px) {

	//Dokument
	#single_section_post_dokument {
		.main {
			.single-post-dokument {
				.post-header {
					h1 {
						font-size: 30px;
						line-height: 40px;
						margin: 0 0 10px;
						letter-spacing: 0.8px;
					}

					.dokument_section {
						float: left;
						width: 100%;

						ul {
							li {
								float: left;
								width: 100%;

								.left {
									max-width: 45px;
									margin: 8px 0;
								}

								.right {
									span {
										&.file {
											h3 {
												margin: 0;
											}
										}

										&.file_name {}

										&.file_type_size {}
									}
								}

								&:first-child {
									border-top: none;
									padding-top: 0;
								}
							}
						}
					}
				}

				.post-content {
					float: left;
					width: 100%;
					max-width: 700px;
					margin: 30px 0;

					p {
						&:last-child {
							margin: 0;
						}
					}

					h3 {
						float: left;
						width: 100%;
						border-bottom: solid 1px #DADADA;
						padding: 0 0 10px;
						margin: 0 0 10px;
						letter-spacing: normal;
					}

					.allman_info {
						float: left;
						width: 100%;
						margin: 0 0 30px;

						.table_wrapper {
							overflow: auto;
						}

						table {
							width: auto;

							tbody {
								tr {
									td {
										padding: 0 0 5px;

										&.a {
											width: 100%;
											float: left;
											color: $color-text;
										}

										&.b {
											width: 100%;
											float: left;
										}
									}
								}
							}
						}
					}



					.relateade_omraden {
						float: left;
						width: 100%;
						margin: 0 0 15px;

						ul {
							li {
								margin: 0 0 5px;

								a {
									position: relative;
									color: $color-bla;
									font-size: 16px;

									&:before {
										float: left;
										width: 20px;
										height: 20px;
										background: url("../../assets/images/pil-blue.png") no-repeat center center;
										background-size: 16px;
										content: '';
										margin: 2px 8px 0 0;
									}
								}
							}
						}
					}

					.post-footer {
						float: left;
						width: 100%;
						border-top: solid 1px #DADADA;
						padding: 10px 0 0;
						margin: 10px 0 0;

					}
				}
			}
		}
	}

	#single_section_post {
		.main {
			padding: 0 0 20px 0;
			max-width: none;
			margin: 0;
			border-right: none;
			border-bottom: solid 1px #DEDEDE;

			.single-post {
				.post-header {
					h1 {
						font-size: 25px;
						line-height: 40px;
						margin: 0 0 10px;
						letter-spacing: 0.8px;
						font-weight: 400;
					}

					.meta-info {
						font-size: small;

						.updated {}

						.author {}
					}
				}

				.post-content {
					p {
						&:last-child {
							margin: 0;
						}
					}
				}

				.post-footer {}
			}

			//Kurser
			.kurs-post {
				.post-header {
					h1 {
						font-size: 25px;
						line-height: 40px;
						margin: 0 0 10px;
						letter-spacing: 0.8px;
						font-weight: 400;
					}

					.meta-info {
						font-size: small;

						.updated {}

						.author {}
					}
				}

				.post-content {
					p {
						&:last-child {
							margin: 0;
						}
					}
				}

				#reg_course {
					float: left;
					width: 100%;
					display: none;

					#booking_course {
						float: left;
						width: 100%;
						margin: 0;
						padding: 30px;
						background: $color-light;

						h3 {
							margin: 0 0 10px;
							letter-spacing: normal;
						}

						.top {
							float: left;
							width: 100%;
							margin: 0 0 30px;

							.row {
								float: left;
								width: 100%;
								margin: 0 0 0;
								position: relative;

								select {
									margin: 0;
									padding: 10px 15px;
									float: left;
									width: 100%;
									font-size: 16px;
									border-radius: 4px;
									background: #fff url("../../assets/images/select-bg.png") no-repeat right;
									-webkit-appearance: none;

									option {
										padding: 0;
										list-style: none;
										float: left;
										padding: 0 30px 0 0;
										width: 100%;
										margin: 0 0 7px;
									}
								}

								i {
									position: absolute;
									top: 18px;
									right: 65px;
									transform: scale(0);
									transition: transform .2s;

									&.fa-times {
										color: #CC002F;
									}

									&.fa-check {
										color: #00925C;
									}
								}

								&.education {}
							}
						}

						.middle {
							float: left;
							width: 100%;
							margin: 0 0 30px;

							.row {
								float: left;
								width: 100%;
								margin: 0 0 20px;
								position: relative;

								i {
									position: absolute;
									top: 18px;
									right: 10px;
									transform: scale(0);
									transition: transform .2s;

									&.fa-times {
										color: #CC002F;
									}

									&.fa-check {
										color: #00925C;
									}
								}

								label {
									display: none;
								}

								input {
									float: left;
									width: 100%;
								}

								&.fname {
									width: 48%;
									float: left;
								}

								&.lname {
									width: 48%;
									float: right;
								}

								&:last-child {
									margin: 0;
								}
							}
						}

						.middle_extra {
							float: left;
							width: 100%;
							margin: 0 0 30px;

							.create_one_more {
								float: left;
								font-size: 16px;
								margin: 0;
								padding: 15px 30px;
								border-radius: 4px;
								color: #fff;
								background: $color-bla;
								border-left: none;
								transition: all .2s;

								&:hover {
									background: lighten($color-bla, 10%);
									text-decoration: none;
									cursor: pointer;
								}
							}

							#extra_section {
								float: left;
								width: 100%;

								.field_section {
									float: left;
									width: 100%;
									padding: 15px 0;
									margin: 15px 0;
									border-bottom: solid 1px #DEDEDE;
									position: relative;

									.remove_item_button {
										position: absolute;
										top: -15px;
										right: 0;

										.fa-times {
											color: #CC002F;
										}
									}

									.row {
										float: left;
										width: 100%;
										margin: 0 0 20px;
										position: relative;

										i {
											position: absolute;
											top: 18px;
											right: 10px;
											transform: scale(0);
											transition: transform .2s;

											&.fa-times {
												color: #CC002F;
											}

											&.fa-check {
												color: #00925C;
											}
										}

										label {
											display: none;
										}

										input {
											float: left;
											width: 100%;
										}

										&.fname {
											width: 48%;
											float: left;
										}

										&.lname {
											width: 48%;
											float: right;
										}

										&:last-child {
											margin: 0;
										}
									}

									&:last-child {
										border-bottom: none;
									}
								}
							}

							.no_deltagare {
								float: left;
								width: 100%;
								font-size: 16px;
								margin: 0 0 10px;
							}

						}

						.bottom {
							.left {
								float: left;
								width: 50%;
								margin: 0 0 30px;

								.submit-booking {
									font-size: 16px;
									margin: 0;
									padding: 15px 30px;
									border-radius: 4px;
									color: #fff;
									background: $color-bla;
									border-left: none;
									transition: all .2s;

									&:hover {
										background: lighten($color-bla, 10%);
										text-decoration: none;
										cursor: pointer;
									}
								}
							}

							.right {
								float: left;
								width: 50%;
								padding: 15px 0;

								a {
									float: left;
									width: 100%;
									text-align: right;
								}
							}

							span {
								float: left;
								width: 100%;
							}
						}
					}
				}

				.post-footer {}
			}
		}

		.sidebar {
			float: right;
			width: 100%;
			max-width: none;
			padding: 20px 0 0 0;

		}
	}

	//Ämnesområde (Single)
	.single_section_post_amnesomrade {
		.main {
			padding: 30px 0 0 0;
			margin: 0;
			max-width: none;
			border-right: none;
			border-top: solid 1px #DEDEDE;

			.single-post {
				.post-header {
					h1 {
						font-size: 25px;
						line-height: 40px;
						margin: 0 0 10px;
						letter-spacing: 0.8px;
						font-weight: 400;
					}
				}

				//Ledningssystem
				.ledningssystem-section {
					ul {
						li {
							a {}
						}
					}
				}

				.post-content {
					float: left;
					width: 100%;

					p {
						&:last-child {
							margin: 0;
						}
					}
				}

				.post-footer {
					width: auto;
					margin: 30px -20px 0;

					#spec_links {
						li {
							.head {
								.images {
									left: 15px;

									img {}
								}

								h2 {
									font-size: 20px;
									margin: 12px 50px 0 42px;
								}

							}

							.r_more {
								top: 25px;
							}

							.hidden_content {
								//Dokuemnt

								//Externa länkar
								.externa_lankar {
									float: left;
									width: 100%;

									ul {
										margin: 0 0 0 10px;
									}
								}

								//Relaterad information
								.relaterad_information {
									float: left;
									width: 100%;

									ul {
										margin: 0 0 0 10px;
									}
								}

								//Bevaka innehåll
								.starta_bevakning {
									span {}

									.row {
										width: 100%;
										position: relative;
										margin: 0 0 8px;

										input {
											padding: 10px;
										}

										&.name {}

										&.email {}

										i {}
									}

									.submit_bevakning {
										border-radius: 4px;
										width: 100%;
										padding: 10px 30px;
										text-align: center;
										margin: 0;
									}
								}

							}

							&:last-child {
								margin: 0;
							}
						}
					}
				}
			}
		}

		.sidebar {
			float: right;
			width: 100%;
			max-width: none;
			padding: 0 0 20px 0;

			#sidebar_amnesomraden {
				.sidebar_title {
					margin: 0 0 10px;
				}

				ul {
					li {
						padding: 6px 0;
					}
				}
			}
		}
	}

	//Ämnesområde (Single Tabell)
	#working_area_tabel {
		float: left;
		width: 100%;
		margin: 25px 0 0;
		padding: 25px 0 0;
		border-top: solid 1px #DEDEDE;

		.working_info {
			float: left;
			width: 100%;
			margin: 0 0 15px;

			p {
				&:last-child {
					margin: 0;
				}
			}
		}

		.table_wrapper {
			float: left;
			width: 100%;
			overflow-x: scroll;

			table {
				width: 1024px;

				thead {
					tr {
						th {}
					}
				}

				tbody {
					tr {
						td {
							span {}

							p {}
						}

						&:nth-child(even) {}
					}
				}
			}
		}
	}

	//kalender
	#single_section_posts_kalender {
		h1 {
			font-size: 25px;
			line-height: 40px;
			margin: 0 0 10px;
			letter-spacing: 0.8px;
			font-weight: 400;
		}

		.single_section_post_kalender {
			padding: 20px;

			.kalender_date {
				width: 100%;
				padding: 20px 20px;

			}

			.kalender_info {
				width: 100%;
				padding: 15px 0 0;

				table {
					tr {
						td {
							&.first {}

							&.last {}
						}
					}
				}
			}

			.entry-summary {
				width: 100%;
			}

			&.last-child {
				margin: 0;
			}
		}

		//Single sida
		.main {
			padding: 0 0 20px 0;
			max-width: none;
			margin: 0;
			border-right: none;
			border-bottom: solid 1px #DEDEDE;

			.single-post-kalender {
				float: left;
				width: 100%;
				padding: 0;

				.kalender_date {
					width: 100%;
					padding: 20px 20px;
				}

				.kalender_info {
					width: 100%;
					padding: 10px 0 0;

					table {
						tr {
							td {
								&.first {}

								&.last {}
							}
						}
					}
				}

				.post-content {}
			}
		}

		.sidebar {
			float: right;
			width: 100%;
			max-width: none;
			padding: 20px 0 0 0;

		}
	}

	//Swedac Magasin
	#single_section_post_swedac_magasin {
		.main {
			float: left;
			width: 100%;
			padding: 0;
			margin: 0;

			.single-post {
				padding: 20px 0 0 0;
				margin: 0;

				.post-header {
					float: left;
					width: 100%;
					max-width: none;
					padding: 0;
					margin: 0;
					background: #fff;

					h1 {
						font-size: 25px;
						line-height: 40px;
						margin: 0 0 10px;
						letter-spacing: 0.8px;
						font-weight: 400;
					}
				}

				.post-content {
					p {
						width: 100%;
						max-width: none;

						&:last-child {
							margin: 0;
						}
					}
				}

				.post-footer {
					float: left;
					width: 100%;
					margin: 30px 0 0 0;

				}
			}


			.latest_swedac_magasin {
				float: left;
				width: auto;
				margin: 40px -20px 0;

				ul {
					li {
						max-width: none;
						margin: 0 !important;
						padding: 0 !important;
						width: 100% !important;
						max-width: none !important;
						max-height: none !important;
						line-height: 0;

						.content {
							height: auto !important;
							overflow: hidden;

							.post_image {
								position: relative;

								img {
									width: 100%;
									height: auto;
									line-height: 1;
								}
							}

							.info {
								h2 {
									font-size: 35px !important;
									padding: 0 !important;
									margin: 0 0 4px !important;
									line-height: 37px !important;
									letter-spacing: 0.8px !important;
								}

								.excerpt {
									font-size: 16px !important;
									line-height: 22px;
								}
							}
						}
					}
				}
			}
		}
	}
}

@media only screen and (max-width:568px) {}

@media only screen and (max-width:450px) {}

@media only screen and (max-width:414px) {}

@media only screen and (max-width:320px) {}