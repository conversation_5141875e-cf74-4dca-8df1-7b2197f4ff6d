@media only screen and (max-width:1440px) {

}
@media only screen and (max-width:1280px) {

}
@media only screen and (max-width:1140px) {

}
@media only screen and (max-width:1024px) {
	body {}

	h1, h2, h3, h4 {}
	h1 {}
	h2 {}
	h3 {}
	h4 {}

}
@media only screen and (max-width:966px) {

}
@media only screen and (max-width:600px) {

	#header {

		#top_content {
			.top_nav {
				nav#top_navigation_left {
					ul {
						li { padding: 0 7px; list-style: none; float: left; line-height: 1;
							&:first-child {padding: 0 10px 0 0;}
						}
					}
				}

			}
			.top_toolbox {
				nav#top_navigation_right {
					ul {
						li { padding: 0 7px;
							&:first-child {padding: 0 10px 0 0;}

						}
					}
				}
			}


		}
		#logotype { 
			float: left; max-width: 100%; margin: 8px 0 15px 0;
		}

		nav#main_navigation { 
			float: left; margin: 10px 0 0;

			//Steg 1
			ul { margin: 0; padding: 0; float: right; z-index: 1000;
				li { margin:0; list-style:none; padding: 0 15px; text-align: center; float: left; position: relative; line-height: 1;
					a {font-size: 16px;}

					&#menu-item-639 {
						.sub-menu {left: -87px;}
					}
					&#menu-item-608 {
						.sub-menu {left: -90px;
							&:after {left: 56%;}
						}
					}

					//Steg 2
					.sub-menu { display: none;  position: absolute; left: -80px; z-index: 1; width: 260px; padding-top: 25px; z-index: 1000;

						li {
							.grow {

								&:before { width: 20px; height: 20px; background-size: 16px; }
								&.active {
									&:before { width: 20px; height: 20px; background-size: 16px; }
								}
							}
							.grow_top {display: none;}

							//Steg 3
							.sub-menu {
								li {
									a {}
									&:hover {}

									.sub-menu {
										li {
											a {}
										}
									}

									&:last-child {
										a {}
									}
								}
							}

							&:last-child {
								a {}
							}
							&:first-child {}
						}
					}
				}
			}
			#mobile_top_navigation_right {
				display: block !important;
			}
			.search-icon { float: right; margin:-3px 0 0 15px;
				span {position: relative;
					img {width: 30px; height: 30px;}
				}

				&:hover {cursor: pointer;}
			}
		}

		//Om headern ska dockas.
		// &.fixed {top: -112px; position: fixed; box-shadow: 0 1px 2px 0px #B5B5B5}
		// &.vissible {top: 0px; }
	}

	body.logged-in {
		#header {
			&.vissible {top: -66px; }
		}
	}
}
@media only screen and (max-width:916px) {
	.mobile-menu{
		display: block;
		position: relative;
		top: 0;
		left: 0;
		height: 64px;
		z-index: 999;
		display: flex;
		justify-content: space-between;
		padding: 0.5rem;
		background-color: white;
		align-items: center;
		&.open{
			position: fixed;
			top: 0;
			width: 100%;
			z-index: 999999;
		}
		input{
			border: none;
			background-color: transparent;
			font-size: 2rem;
			padding: 0;
			opacity: 1;
		}
		.hidden{
			display: none;
		}
		.logotype{
			height: 90%;
			width: 250px;
			a{
				display: block;
			}
		}
	}

	#header {
		padding:0;
		position: fixed;
		min-height: 100vh;
		width: 100%;
		z-index: 999;
		display: none;
		overflow: scroll;
		&.open{
			display: block;
		}
		.wrapper {
			padding:0;
		}
		.menu-wrapper{
			position: fixed;
			mine-height: 100vh;
			width: 100%;
			bottom: 0;
			background-color: white;
			z-index: 2;
			inset: 0;
			flex-flow: column;
			padding-top: 64px;
			display: flex;
			overflow: scroll;
			>.wrapper{
				height: 130px;
				margin: 0;
				background-color: white;
				.search-bar{
					width: 100%;
					justify-content: center;
					input[type=search]{
						width: 100%;
						height: 42px;
					}
					button[type=submit]{
						height: 42px;
					}
				}
			}
			#top_content {
				position: unset;
				flex-grow: 1;
				.wrapper{
					display: flex;
					flex-flow: column;
					height: 100%;
					justify-content: space-between;
					.socialmedia{
						margin: 0 auto;
						height: 100px;
						align-items: center;
						ul li a i::before{
							height: 50px;
							width: 50px;
							font-size: 2rem;
							line-height: 50px;
						}
					}
					.top_toolbox {
						nav#top_navigation_right {
							display: none;
							
						}
					}
					nav#main_navigation {
						float: left;
						width: 100%;
						margin: 0;
						flex-grow: 1;
						padding: 15px 0 2rem 0;
						max-height: fit-content;
						//Steg 1
						ul {
							width: 100%;
							float: left;
							padding: 0;
							li {
								&.menu-item-has-children button{
									height: 24px;
									width: 24px;
								}
								float: left;
								width: 100%;
								margin: 0.5rem 0;
								a {
									float: left;
									font-size: 20px;
									width: 100%;
									text-align: left;
									font-size: 1.5rem;
									width: calc(100% - 24px);
								}
			
								.grow_mobile {
									display: block;
									float: right;
									position: absolute;
									right: -14px;
									padding: 0 12px;
									color: #fff;
			
									&:hover {
										cursor: pointer;
									}
			
									&:before {
										float:left;
										width: 20px;
										height: 20px;
										background:url("../../assets/images/svg/plus-blue.svg") no-repeat center center;
										background-size: 16px;
										content: '';
										margin: 0;
									}
			
									&.active {
										&:before {
											background-size: 16px;
											background: url("../../assets/images/svg/minus-blue.svg") no-repeat center center;
											content: '';
											height: 20px;
											float: left;
											margin: 0;
											width: 20px;
										}
									}
								}
			
								&#menu-item-639 {
									.sub-menu {
										left: inherit;
									}
								}
			
								&#menu-item-608 {
									.sub-menu {left: inherit; border-bottom: none; padding:10px 0 0; margin:10px 0 0;
										&:after {left: inherit;}
			
										li {
											.sub-menu {padding:10px 0; margin: 10px 0; border-bottom: solid 1px #ddd;}
										}
									}
								}
			
								//Steg 2
								.sub-menu {
									display: none;
									margin: 10px 0;
									position: relative;
									top: inherit;
									left: inherit;
									width: 100%;
									float: left;
									padding: 10px 0;
									z-index: 1000;
									border-top: solid 1px #ddd;
									border-bottom: solid 1px #ddd;
			
									&:after {
										display: none;
									}
			
									li {
										background: transparent;
										border-bottom: 0;
										padding: 10px 0;
			
										.grow {
											&:before { width: 20px; height: 20px; background-size: 16px; }
											&.active {
												&:before { width: 20px; height: 20px; background-size: 16px; }
											}
										}
			
										a {
											border-bottom: none;
											background: none;
											color: white;
											font-size: 16px;
											width: calc(100% - 24px);
											&:focus {
												background-color: rgba(0 ,0, 0, 0.1);
												outline: 0.08em dashed $black;
											}								
										}
										button{
											border-color: white;
											&::after{
												color: white;
											}
										}
			
										//Steg 3
										.sub-menu {
											border-top: solid 1px #ddd;
											border-bottom: solid 1px #ddd;
											left: inherit;
											margin: 10px 0;
											padding: 10px 0;
											position: relative;
											top: inherit;
											width: 100%;
											z-index: 1000;
			
											li {
												background: transparent;
												padding: 8px 0;
												button{
													&::after{
														filter: none;
													}
													&[aria-expanded="true"]{
														&::after {
															filter: none;

														}
													}
													&[aria-expanded="false"]{
														&::after{
															filter: none;
														}
													}
												}
												a {
													border-bottom: 0;
													padding: 0 10px 0 0;
													color: white;
													font-size: 14px;
			
												}
			
												&:hover {
													background: none;
												}
			
												.sub-menu {
													li {
														a {border-bottom: none; padding:10px 0; color: white; font-size: 16px;background: none;}
													}
												}
			
												&:last-child {
													a {}
												}
											}
										}
			
										&:hover {
											background: none;
										}
									}
								}
			
								&.current-menu-item {
									a { 
										text-decoration: underline; color: #fbba00;
									}
								}
								&.current_page_ancestor  {
									a {
										text-decoration: underline; color: #fbba00;
									}
								}
							}
						}
			
						//Topmeny vänster och höger
						#menu-topnavigation-left,
						#menu-topnavigation-left-eng,
						#menu-topnavigation-right,
						#menu-topnavigation-right-eng {
							padding: 10px 0 0;
							margin: 10px 0 0;
							border-top: solid 1px #ddd;
			
							li {
								padding: 7px 0;
			
								a {
									font-size: 16px;
								}
			
								.sub-menu {
									li {
										padding: 8px 10px;
			
										a {
											font-size: 14px;
										}
									}
								}
							}
						}
			
					}
				}
			}
		}
		
		#logotype {
			display: none;
		}

		.search-bar {
			margin-left: 20px;
			margin-right: 20px;
		}


		//Toggla menyn
		#toggle_navigation { display: block; width: 25px;height: 25px; float: right; position: relative;transform: rotate(0deg);transition: .5s ease-in-out; cursor: pointer; top: 15px; right: 0; margin: 2px 20px 2px 20px;
			span {display: block; position: absolute;height: 1px;width: 100%; max-width:35px; background: $color-bla; border-radius: 9px;opacity: 1;left: 0;transform: rotate(0deg);transition: .25s ease-in-out;
				&:nth-child(1) {top: 0;}
				&:nth-child(2), &:nth-child(3) {top: 9px;}
				&:nth-child(4) {top: 18px;}
			}

			&.open {
				span { background: $color-bla;
					&:nth-child(1) {top: 11px;width: 0; left: 50%;}
					&:nth-child(2) {transform: rotate(45deg);}
					&:nth-child(3) {transform: rotate(-45deg);}
					&:nth-child(4) {top: 11px;width: 0; left: 50%;}
				}
			}
		}

	}

	body.logged-in {
		#header {
			&.vissible {top: 46px; }
		}
	}
}
@media only screen and (max-width:568px) {

}
@media only screen and (max-width:450px) {

}
@media only screen and (max-width:414px) {

}
@media only screen and (max-width:320px) {

}
