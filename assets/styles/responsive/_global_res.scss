@media only screen and (max-width:1024px) {
	body {

		//Om en sidebar ej finns
		#main_wrapper {
			float: left;
			width: 100%;

			.wrapper {
				.content.row {
					float: left;
					width: 100%;
					margin: 0;

					.main {
						float: left;
						width: 100%;

						.wrapper {
							padding: 0;
						}
					}
				}
			}
		}

		//Om en sidebar finns. 
		&.sidebar-active {
			#main_wrapper {
				.wrapper {
					.content.row {
						padding: 0;

						.main {
							padding: 0 20px 0 0;
							max-width: 660px;

							.wrapper {
								padding: 0;
							}
						}

						.sidebar {
							float: right;
							width: 100%;
							max-width: 300px;
							padding: 0 0 0 5px;
						}
					}
				}
			}
		}
	}

	h1,
	h2,
	h3,
	h4 {}

	h1 {}

	h2 {}

	h3 {}

	h4 {}

	.wrapper {
		padding: 0 20px;
	}

	// Rubriker med överblick
	//-------------------------------------------
	#header-content {
		padding: 40px 0 45px;

		.page-header {
			.title-page {}

			.intro {
				width: 70%;
			}
		}
	}

	#page-header-parent {
		.title {
			.wrapper {
				.title-page {}
			}
		}

		.post_thumbnail {
			.image {
				background-attachment: fixed;
				background-position: top;
				background-size: 100%;
				height: 350px;
			}
		}
	}

	// Slider
	//-------------------------------------------
	#frontpage_owl_slider {
		height: 405px;

		ul {
			li {
				height: 405px;

				.wrapper {
					padding: 4% 20px;

					.info {

						h1,
						h2 {
							font-size: 75px;
						}

						.content {
							color: #fff;
							font-size: 23px;
							line-height: 28px;
						}
					}

					.slider_links {
						ul {
							li {
								a {
									&:after {}

									&:hover {
										&:after {}
									}
								}

								&:nth-last-child(-n+4) {
									a {
										&:after {}

										&:hover {
											&:after {}
										}
									}
								}
							}
						}
					}
				}

				&.no_image {
					background: $color-da-blue;
				}
			}
		}
	}

	//Sökformuläret
	#search_form {
		float: left;
		width: 100%;
		background: #ebeff2;
		padding: 30px 0;

		.wrapper {
			form#search-form {

				.adv_search {
					#mainmenu {
						li {
							#submenu {
								width: auto;

								li {
									padding: 8px 0px;
									text-align: center;

									a {
										&:before {
											display: none;
										}
									}
								}
							}
						}
					}
				}

				#sokkallor {
					display: flex;

					.alt {
						flex-grow: 5;
						padding: 10px 0;

						input {
							width: 13px;
							padding: inherit;
							float: left;
							margin: 0 5px;
						}

						label {
							width: auto;
							float: left;
							margin: 3px 0 0;
						}

						&:last-child {}

					}
				}
			}
		}
	}

}

@media only screen and (max-width:966px) {
	body {

		//Om en sidebar ej finns
		#main_wrapper {
			float: left;
			width: 100%;

			.wrapper {
				.content.row {
					float: left;
					width: 100%;
					margin: 0;

					.main {
						float: left;
						width: 100%;

						.wrapper {
							padding: 0;
						}
					}
				}
			}
		}

		//Om en sidebar finns. 
		&.sidebar-active {
			#main_wrapper {
				.wrapper {
					.content.row {
						padding: 0;

						.main {
							padding: 0 30px 0 0;
							max-width: 630px;

							.wrapper {
								padding: 0;
							}
						}

						.sidebar {
							float: right;
							width: 100%;
							max-width: 270px;
							padding: 0 0 0 5px;
						}
					}
				}
			}
		}
	}

	h1,
	h2,
	h3,
	h4 {}

	h1 {}

	h2 {}

	h3 {}

	h4 {}

	.wrapper {
		padding: 0 20px;
	}

	// Rubriker med överblick
	//-------------------------------------------
	#header-content {
		padding: 40px 0 45px;

		.page-header {
			.title-page {}

			.intro {
				width: 80%;
			}
		}
	}

	// Slider
	//-------------------------------------------
	#frontpage_owl_slider {
		height: 405px;

		ul {
			li {
				height: 405px;

				.wrapper {
					padding: 4% 20px;

					.info {

						h1,
						h2 {
							font-size: 55px;
						}

						.content {
							font-size: 20px;
							line-height: 30px;
						}
					}

					.slider_links {
						ul {
							li {
								a {
									&:after {}

									&:hover {
										&:after {}
									}
								}

								&:nth-last-child(-n+4) {
									a {
										&:after {}

										&:hover {
											&:after {}
										}
									}
								}
							}
						}
					}
				}

				&.no_image {
					background: $color-da-blue;
				}
			}
		}
	}

	//Sökformuläret
	#search_form {
		float: left;
		width: 100%;
		background: #ebeff2;
		padding: 30px 0;

		.wrapper {
			form#search-form {
				.main_serach {
					width: 75%;
				}

				.adv_search {
					width: 25%;

					#mainmenu {
						li {
							#submenu {
								width: auto;
								z-index: 1000;

								li {
									padding: 8px 0px;
									text-align: center;

									a {
										&:before {
											display: none;
										}
									}
								}
							}
						}
					}
				}

				#sokkallor {
					display: flex;

					.alt {
						flex-grow: 5;
						font-size: 14px;

						input {
							margin: 0 0px 0 15px;
						}

						&:last-child {}

					}
				}
			}
		}
	}
}

@media only screen and (max-width:768px) {
	body {

		//Om en sidebar ej finns
		#main_wrapper {
			float: left;
			width: 100%;

			.wrapper {
				.content.row {
					float: left;
					width: 100%;
					margin: 0;

					.main {
						float: left;
						width: 100%;

						.wrapper {
							padding: 0;
						}
					}
				}
			}
		}

		//Om en sidebar finns. 
		&.sidebar-active {
			#main_wrapper {
				.wrapper {
					.content.row {
						padding: 0;

						.main {
							padding: 0;
							margin: 0;
							max-width: none;
							border-right: none;

							.wrapper {
								padding: 0;
							}
						}

						.sidebar {
							float: right;
							width: 100%;
							max-width: none;
							padding: 30px 0 0 0;
						}
					}
				}
			}
		}
	}

	h1,
	h2,
	h3,
	h4 {}

	h1 {}

	h2 {}

	h3 {}

	h4 {}

	.wrapper {
		padding: 0 20px;
	}

	// Rubriker med överblick
	//-------------------------------------------
	#header-content {
		padding: 40px 0 45px;
		margin: 104px 0 0 0;

		.page-header {
			.title-page {
				font-size: 40px;
			}

			.intro {
				width: 80%;

				p {
					font-size: 20px;
					line-height: 28px;
				}
			}
		}
	}

	#page-header-parent {
		float: left;
		width: 100%;
		margin: 104px 0 0 0;

		.title {
			.wrapper {
				.title-page {}
			}
		}

		.post_thumbnail {
			.image {
				background-attachment: fixed;
				background-position: top;
				background-size: 100%;
				height: 330px;
			}
		}
	}

	// Slider
	//-------------------------------------------
	#frontpage_owl_slider {
		height: 385px;
		margin: 104px 0 0 0;

		ul {
			li {
				height: 385px;

				.wrapper {
					padding: 4% 20px;

					.info {
						width: 55%;

						h1,
						h2 {
							font-size: 40px;
						}

						.content {
							font-size: 18px;
							line-height: 28px;
						}
					}

					.slider_links {
						width: 40%;

						ul {
							li {
								a {
									&:after {}

									&:hover {
										&:after {}
									}
								}

								&:nth-last-child(-n+4) {
									a {
										&:after {}

										&:hover {
											&:after {}
										}
									}
								}
							}
						}
					}
				}

				&.no_image {
					background: $color-da-blue;
				}
			}
		}
	}

	//Certifikat
	.certifikat_rep {
		.cert_content {
			.r_toggle_cert {}

			.cert_div {
				float: left;
				width: 100%;
				overflow-x: scroll;

				table {
					width: 1024px;

					thead {
						tr {
							th {}
						}
					}

					tbody {
						tr {
							td {
								span {}

								p {}
							}

							&:nth-child(even) {}
						}
					}
				}
			}

			&:last-child {}
		}
	}

	//Sökformuläret
	#search_form {
		float: left;
		width: 100%;
		background: #ebeff2;
		padding: 30px 0;

		.wrapper {
			form#search-form {
				.main_serach {
					width: 72%;

					.search-field {
						padding: 10px 15px 10px 50px;
						background-size: 25px 25px;
					}

					.search-submit {
						padding: 10px 0;
					}
				}

				.adv_search {
					width: 28%;

					#mainmenu {
						li {
							span {
								padding: 1.2rem 1.5rem;

								.fa-angle-down {
									top: 5px;
								}
							}

							#submenu {
								width: auto;
								z-index: 1000;

								li {
									padding: 8px 0px;
									text-align: center;

									a {
										&:before {
											display: none;
										}
									}
								}
							}
						}
					}
				}

				#sokkallor {
					display: block;

					.alt {
						flex-grow: inherit;
						font-size: 13px;
						padding: 10px 0;
						width: 24%;
						margin: 0 9px 0 0;
						float: left;

						input {
							margin: 0 0px 0 10px;
						}

						&:last-child {
							margin: 0;
							float: right;
							width: 24.2%;
						}

						&:first-child {
							width: 100%;
							margin: 0 0 7px;
						}

					}
				}
			}
		}
	}
}

@media only screen and (max-width:736px) {
	body {

		//Om en sidebar ej finns
		#main_wrapper {
			float: left;
			width: 100%;

			.wrapper {
				.content.row {
					float: left;
					width: 100%;
					margin: 0;

					.main {
						float: left;
						width: 100%;

						.wrapper {
							padding: 0;
						}
					}
				}
			}
		}

		//Om en sidebar finns. 
		&.sidebar-active {
			#main_wrapper {
				.wrapper {
					.content.row {
						padding: 0;

						.main {
							padding: 0;
							margin: 0;
							max-width: none;
							border-right: none;

							.wrapper {
								padding: 0;
							}
						}

						.sidebar {
							float: right;
							width: 100%;
							max-width: none;
							padding: 30px 0 0 0;
						}
					}
				}
			}
		}
	}

	// Ingress
	//-------------------------------------------
	.ingress {
		float: left;
		width: 100%;
		color: #666;
				margin: 0 0 20px;

		p {
			line-height: 29px;
			margin: 0;
			font-size: 18px;

		}
	}

	h1,
	h2,
	h3,
	h4 {}

	h1 {
		font-size: 35px;
	}

	h2 {
		font-size: 28px;
	}

	h3 {
		font-size: 16px;
	}

	h4 {
		font-size: 16px;
	}

	input,
	textarea,
	select {
		padding: 10px 15px;

		&.error {
			padding: 10px 15px;
		}

		&.valid {
			padding: 10px 15px;
		}

	}

	.label_select {
		position: relative;

		&:after {
			content: '\f107';
			font-family: $FA;
			font-size: 35px;
			right: 15px;
			top: 50px;
			position: absolute;
			pointer-events: none;
		}
	}

	.wrapper {
		padding: 0 20px;
	}

	// Single post info
	//-------------------------------------------
	.post_settings {
		float: left;
		width: 100%;
		max-width: 700px;

		.author {
			float: left;
			width: 100%;
			margin: 15px 0 10px;
			font-size: 16px;

			span {
				text-transform: capitalize;
				font-weight: 600;
			}
		}

		.publish_change {
			float: left;
			width: 100%;
			margin: 8px 0 10px;
			font-size: 16px;

			span {
				float: left;
				width: 100%;
				color: #aeaeae;
				margin: 0 0 3px
			}
		}

		.share_post {
			float: left;
			width: 100%;
			margin: 15px 0 0;

			.share {
				text-align: center;
				padding: 10px 0;
				border: solid 1px #ccc;
				background: #fff;

				.fa {
					font-size: 25px;
					margin: 0 5px;
				}

				span {
					position: relative;
					top: -3px;
				}

				&.facebook {
					width: 100%;
					float: left;
					color: #333;
					font-size: 14px;
					margin: 0 0 8px;

					.fa {
						color: #3B5998;
					}
				}

				&.linkedin {
					width: 100%;
					float: left;
					color: #333;
					font-size: 14px;
					margin: 0 0 8px;

					.fa {
						color: #1c87bd;
					}
				}

				&.mail {
					width: 100%;
					float: left;
					margin: 0;

					span {
						display: inline-block;
						top: -1px;
					}

					.fa {
						color: $color-da-blue;
						font-size: 20px;
						padding: 2px 0 3px;
					}
				}

				&:hover {
					cursor: pointer;
				}
			}
		}
	}


	// Rubriker med överblick
	//-------------------------------------------
	#header-content {
		padding: 30px 0;
		margin: 0;
		max-height: none;

		.page-header {
			.title-page {
				font-size: 35px;
			}

			.intro {
				width: 100%;

				p {
					font-size: 16px;
					line-height: 25px;
				}
			}
		}
	}

	#page-header-parent {
		float: left;
		width: 100%;
		margin: 0;

		.title {
			.wrapper {
				.title-page {}
			}
		}

		.post_thumbnail {
			.image {
				background-attachment: inherit;
				background-position: center;
				background-position-y: 0 !important;
				background-size: cover;
				height: 330px;
			}
		}
	}

	// Slider
	//-------------------------------------------
	#frontpage_owl_slider {
		height: auto;
		margin: 0;

		ul {
			li {
				height: auto;
				background: $color-da-blue !important;
				float: left;
				width: 100%;

				.slider-cover {
					display: none
				}

				.wrapper {
					padding: 5% 20px 5%;
					float: left;
					width: 100%;
					top: 0;

					.info {
						width: 100%;

						h1,
						h2 {
							font-size: 45px;
							margin: 0 0 6px;
							letter-spacing: 0.6px;
						}

						.content {
							font-size: 18px;
							line-height: 28px;
							color: $color-text;
						}
					}

					.slider_links {
						width: 100%;
						margin: 20px 0 0;

						ul {
							li {
								background: #fff !important;

								a {
									&:after {}

									&:hover {
										&:after {}
									}

									&.nav-item-blue {
										border: 1px solid #ffffff;
									}
								}

								&:nth-last-child(-n+3) {
									background: $color-dark-grey !important;

									a {
										&:after {}

										&:hover {
											&:after {}
										}
									}
								}
							}
						}
					}
				}

				&.no_image {
					background: $color-da-blue;
				}
			}
		}
	}

	//Sökformuläret
	#search_form {
		float: left;
		width: 100%;
		background: #ebeff2;
		padding: 30px 0;

		.wrapper {
			padding: 0 20px !important;

			form#search-form {
				.main_serach {
					width: 100%;
					padding: 0;

					.search-field {
						padding: 10px 15px 10px 50px;
						background-size: 25px 25px;
						width: 80%;
						background-position: top 7px left 8px;
					}

					.search-submit {
						padding: 10px 0;
						width: 20%;

						&:hover {}
					}
				}

				.adv_search {
					width: 100%;
					padding: 0;
					margin: 20px 0 0;

					#mainmenu {
						li {
							span {
								padding: 10px 20px;

								.fa-angle-down {
									top: 5px;
								}

								&.active {
									border-bottom-left-radius: 0;
									border-bottom-right-radius: 0;
								}
							}

							#submenu {
								width: 100%;
								z-index: 1000;
								position: relative;
								margin: 0;
								border-top-right-radius: 0;
								border-top-left-radius: 0;

								li {
									padding: 8px 19px;
									text-align: left;

									a {
										&:before {
											display: none;
										}
									}
								}
							}
						}
					}
				}

				#sokkallor {
					display: block;

					.alt {
						flex-grow: inherit;
						text-align: left;
						font-size: 13px;
						padding: 10px 0;
						width: 100%;
						margin: 0 0 5px 0;
						float: left;

						input {
							margin: 0 15px;
							transform: scale(1.2);
						}

						&:last-child {
							margin: 0;
							float: right;
							width: 100%;
						}

						&:first-child {
							width: 100%;
							margin: 0 0 5px;
						}

					}
				}
			}
		}
	}

	//Visa fler sökträffar & Avnacerad sökning
	.search_result_buttons {
		float: left;
		width: 100%;
		margin: 15px 0;

		span {
			float: left;
			width: 100%;
			padding: 10px 20px;
			margin: 0 0 8px;

			&.visa_fler_traffar {

				&.disabled {
					background: rgba(0, 112, 168, 0.75);
					pointer-events: none;
				}
			}

			&.avancerad_sokning {
				float: left;
				width: 100%;
				margin: 0;
			}

			&:hover {
				background: lighten($color-bla, 10%);
				text-decoration: none;
				cursor: pointer;
			}
		}
	}

	//Sökresultat
	.search_result {
		h2 {}

		article.search_post {
			.left {
				float: left;
				width: 100%;
			}

			.right {
				display: none;
			}

			&:last-child {
				border-bottom: none;
				padding: 25px 0 0 0;
			}
		}
	}

	//Namnstämplar
	.search_result_namnstampelregistret {
		article.search_post_namnstampel {
			.excerpt {
				.table_wrapper {
					float: left;
					width: 100%;
					overflow-x: scroll;
				}

				table {
					width: 600px;

					thead {
						tr {
							th {
								padding: 10px 30px;
								font-size: 14px;
							}
						}
					}

					tbody {
						tr {
							td {
								padding: 10px 30px;
								font-size: 13px;
							}
						}
					}
				}
			}

			&:last-child {
				border-bottom: none;
				padding: 25px 0 0 0;
			}
		}
	}

	//Organ
	.search_result_organ {
		float: left;
		width: 100%;
		margin: 10px 0 0;

		h2 {}

		article.search_post_organ {
			.excerpt {
				float: left;
				width: 100%;

				//Ackrediterade Organ
				.name {
					float: left;
					width: 50%;
					text-align: center;
					border: none;
					font-size: 14px;

					&:nth-child(odd) {
						border-left: solid 1px #acacac;
					}
				}
			}
		}
	}

	#sok_foreskriver_dokument,
	#monitor_content_for_updates {
		float: left;
		width: 100%;
		padding: 20px 0 30px;
		margin: 0 0 40px;
		background: $color-light;

		.wrapper,
		.inner-container {
			.title {
				float: left;
				width: 100%;

				span {
					float: left;
					margin: 3px 0 0 0;
				}

				img {
					display: none;
				}
			}

			#searchform_foreskrifter_dokument,
			#monitor_content_form {

				.row {
					float: left;
					width: 100%;
					margin: 0 0 8px;

					.search-field,
					.req {
						border: solid 1px #D0D0D0;
												appearance: none;
						font-size: 15px;
						padding: 10px 15px;
						float: left;
						width: 100%;
						border-radius: 4px;
					}

					select {
						margin: 0;
						padding: 10px 15px;
						float: left;
						width: 100%;
						font-size: 16px;
						border-radius: 4px;
						background: #fff;
						-webkit-appearance: none;

						option {
							padding: 0;
							list-style: none;
							float: left;
							padding: 0 30px 0 0;
							width: 100%;
							margin: 0 0 7px;
						}
					}

					&:nth-child(even) {
						float: right;
					}
				}

				.search-submit,
				.monitor-submit {
										appearance: none;
					font-size: 15px;
					transition: all .2s;
					border: none;
					float: left;
					width: 100%;
					border-radius: 4px;
					padding: 10px 20px;
					background: $color-bla;
					color: #fff;

					&:hover {
						background: lighten($color-bla, 10%);
						text-decoration: none;
						cursor: pointer;
					}
				}

				.monitor-submit {
					width: auto;
				}
			}
		}
	}

	#sok_foreskriver_dokument_page {
		float: left;
		width: 100%;
		padding: 30px 0 30px;
		margin: 0 0 20px;
		background: $color-light;

		.wrapper {
			.title {
				float: left;
				width: 100%;
				margin: 0 0 15px;

				span {
					float: left;
					margin: 3px 0 0 0;
				}

				img {
					display: none;
				}
			}

			#searchform_foreskrifter_dokument {

				.row {
					float: left;
					width: 100%;
					margin: 0 0 8px;

					.search-field {
						border: solid 1px #D0D0D0;
												appearance: none;
						font-size: 15px;
						padding: 10px 15px;
						float: left;
						width: 100%;
						border-radius: 4px;
					}

					select {
						margin: 0;
						padding: 10px 15px;
						float: left;
						width: 100%;
						font-size: 16px;
						border-radius: 4px;
						background: #fff;
						-webkit-appearance: none;

						option {
							padding: 0;
							list-style: none;
							float: left;
							padding: 0 30px 0 0;
							width: 100%;
							margin: 0 0 7px;
						}
					}

					&:nth-child(even) {
						float: right;
					}
				}

				.search-submit {
										appearance: none;
					font-size: 15px;
					transition: all .2s;
					border: none;
					float: left;
					width: 100%;
					border-radius: 4px;
					padding: 10px 20px;
					background: $color-bla;
					color: #fff;

					&:hover {
						background: lighten($color-bla, 10%);
						text-decoration: none;
						cursor: pointer;
					}
				}
			}
		}
	}



	//Senaste dokument
	.senaste_dokument {
		float: left;
		width: 100%;
		font-size: 16px;

		h3 {
			float: left;
			width: 100%;
			border-bottom: solid 1px #888;
			padding: 0 0 15px;
			margin: 0 0 20px;
			letter-spacing: normal;
		}

		.table_wrapper {
			float: left;
			width: 100%;
			overflow-x: scroll;
			box-shadow: inset -5px 0px 5px 0px rgba(0, 0, 0, 0.75);
		}

		table {
			width: 600px;

			thead {
				tr {
					th {
						padding: 10px 6px;
						font-size: 12px;
					}
				}
			}

			tbody {
				tr {
					td {
						padding: 5px 6px;
						font-size: 12px;
					}
				}
			}
		}
	}


	.senaste_dokument_result {
		.table_wrapper {
			float: left;
			width: 100%;
			overflow-x: scroll;
		}

		table {
			width: 600px;

			thead {
				tr {
					th {
						padding: 10px 30px;
					}
				}
			}

			tbody {
				tr {
					td {
						padding: 5px 30px;
						font-size: 13px;
					}
				}
			}
		}
	}

	ul.did_you_mean_suggestion_list li {
		width: 100%;

		a {
			text-align: center;
			width: inherit;
		}
	}
}