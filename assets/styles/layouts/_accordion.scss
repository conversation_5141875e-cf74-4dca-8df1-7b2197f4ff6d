.collapsible-link {
  &-black {
    &::before {
      content: '';
      width: 14px;
      height: 2px;
      background: #333;
      position: absolute;
      top: calc(50% - 1px);
      right: 22px;
      display: block;
      transition: all 0.3s;
    }
    &::after {
      content: '';
      width: 2px;
      height: 14px;
      background: #333;
      position: absolute;
      top: calc(50% - 7px);
      right: calc(1rem + 12px);
      display: block;
      transition: all 0.3s;
    }
  }
  &-white {
    &::before {
      content: '';
      width: 14px;
      height: 2px;
      background: #fff;
      position: absolute;
      top: calc(50% - 1px);
      right: 37px;
      display: block;
      transition: all 0.3s;
    }
    &::after {
      content: '';
      width: 2px;
      height: 14px;
      background: #fff;
      position: absolute;
      top: calc(50% - 7px);
      right: calc(1rem + 27px);
      display: block;
      transition: all 0.3s;
    }
  }
}

.collapsible-link-black[aria-expanded='true']::after, .collapsible-link-white[aria-expanded='true']::after {
  transform: rotate(90deg) translateX(-1px);
}
.collapsible-link-black[aria-expanded='true']::before, .collapsible-link-white[aria-expanded='true']::before {
  transform: rotate(180deg);
}
// Make accordions work on mobile devices
.paragraph_start, .r_more, .card-link {
  z-index:99;
  cursor:pointer;
}
.accordion {
  &-icon {
    padding-right: 15%;
  }
  &-text {
    font-size: 20px;
        text-decoration: none;
  }
}
.card-header:after {
  display: none;
}
.general_docs, .area_docs {
  h3, li {
      margin: 0;
  }
}
.docs {
  font-size: 16px;
  }
.doc_header {
  font-size: 18px;
  }
.doc_link {
  text-decoration: none;
}
.header_title {
  font-size: 16px;
  }
.card ul li .docs:before{
  position: absolute;
  top: 10px;
  left: 0;
  right: 0;
  bottom: 0;
  float: left;
  width: 16px;
  height: 16px;
  background: url(../../assets/images/pil-blue.png) no-repeat center center;
  background-size: 16px;
  content: '';
}
