div.sidebar {
	#sidebar_sub_menu {
		float: left;
		width: 100%;

		h2 {
			font-size: 16px;
			color: $color-da-blue;
			letter-spacing: 0.8px;
		}

		ul.submenu {
			float: left;
			width: 100%;
			margin: 0;
			padding: 0;

			li {
				padding: 0 10px 0 10px;
				list-style: none;
				border-bottom: 1px solid $color-lightgrey;
				padding: 15px 0px;

				.grow_subpage {
					float: right;
					position: absolute;
					right: 0;
					padding: 13px 0 9px 10px;

					&:hover {
						cursor: pointer;
					}

					&:before {
						float: left;
						width: 20px;
						height: 20px;
						background: url("../../assets/images/svg/plus-black.svg") no-repeat center center;
						background-size: 16px;
						content: '';
						margin: 0;
					}

					&.active {
						&:before {
							float: left;
							width: 20px;
							height: 20px;
							background: url("../../assets/images/svg/minus-black.svg") no-repeat center center;
							background-size: 16px;
							content: '';
							margin: 0;
						}
					}

				}

				a {
					color: $grey;
					text-decoration: none;
					position: relative;
					&:hover {
						color: $color-bla;
					}
				}

				//submenu
				ul.sub-menu {
					display: none;
					margin: 0;
					float: left;
					width: 100%;
					padding: 15px 0;
					border-bottom: solid 1px #DEDEDE;

					li {
						line-height: 1;

						a {
							position: relative;
							border-bottom: none;
							padding: 4px 0;
							float: left;
							width: 100%;
							line-height: 25px;

							&:before {
								content: '\f105';
								font-family: $FA;
								margin: 0px 6px 3px 0;
								float: left;
								font-size: 20px;
								font-weight: 200;
							}
						}

						.grow_subpage {
							display: none;
						}

						ul.sub-menu {
							display: none !important;
						}
					}
				}


				//Current Page
				&.current_page_item {
					.grow_subpage {}

					a {
						color: $color-da-blue;
						letter-spacing: 0.5px;
											}

					ul.sub-menu {
						li {
							line-height: 1;

							a {
								font-weight: normal;
																color: $color-bla;
								letter-spacing: normal;

								&:before {
									font-weight: 200;
								}
							}
						}
					}

					&.open {
						.grow_subpage {
							&:before {
								float: left;
								width: 20px;
								height: 20px;
								background: url("../../assets/images/svg/minus-black.svg") no-repeat center center;
								background-size: 16px;
								content: '';
								margin: 0;
							}
						}
					}
				}

				//Current page parent
				&.current_page_parent {
					a {
						color: $grey;
						&:hover {
							color: $color-bla;
						}
					}

					ul.sub-menu {
						li {
							line-height: 1;

							a {
								font-weight: normal;
																color: $color-bla;
								letter-spacing: normal;

								&:before {
									font-weight: 200;
								}
							}

							&.current_page_item {
								line-height: 1;

								a {
									color: $grey;
									&:hover {
										color: $color-bla;
									}

									&:before {
										font-weight: 200;
									}
								}
							}
						}
					}

					&.open {
						.grow_subpage {
							&:before {
								float: left;
								width: 20px;
								height: 20px;
								background: url("../../assets/images/svg/minus-black.svg") no-repeat center center;
								background-size: 16px;
								content: '';
								margin: 0;
							}
						}
					}
				}
			}

		}
	}

	//Om GD Bloggen
	.om-gd-bloggen {
		float: left;
		width: 100%;

		.profilbild {
			float: left;
			width: 100%;
			line-height: 10px;

			img {
				width: 100%;
				height: auto;
			}
		}

		.content {
			padding: 20px 25px 20px 25px;
			float: left;
			width: 100%;
			background: $color-light;

			h3 {
				font-size: 26px;
				color: #012138;
			}

			.info {
				font-size: 16px;

				p {
					line-height: 23px;
				}

				a {
					font-weight: normal;
					position: relative;
					float: left;
					width: 100%;

					&:before {
						float: left;
						width: 20px;
						height: 20px;
						background: url("../../assets/images/pil-blue.png") no-repeat center center;
						background-size: 16px;
						content: '';
						margin: 1px 8px 0 0;
					}
				}
			}
		}
	}
}

//sidebar
.sidebar {
    width: 50%;
    >h2 {
        border-bottom: 1px solid $color-lightgrey;
        padding: 10px 0 10px 10px;
        &.more-news-header {
            font-size: 16px;
        }
    }
    ul {
        margin: 0;
        li {
            padding: 0 10px 0 10px;
			list-style: none;
			border-bottom: 1px solid $color-lightgrey;
			padding: 15px 0px;
            time.updated {
                font-size: 12px;
                line-height: 16px;
            }
            h3 a {
                color: $color-bla;
            }
            a {
                color: $grey;
                text-decoration: none;
				position: relative;
				&:hover {
					color: $color-bla;
				}
            }
            &:last-child {
				border-bottom: none;
				padding-bottom: 0px;
                margin-bottom: 0px;
            }
            &.page_item_has_children > a {
                display: block;
            }
        }
        &.more-news-list li h3 {
            font-size: 16px;
        }
        &.children {
            margin-left: 10px;
            li a {
				color: $color-blue;
				&:hover {
					color: $color-bla;
				}
            }
        }
    }
}

.wrapper .sidebar ul .medarbetare {
    border-bottom: 0;
}