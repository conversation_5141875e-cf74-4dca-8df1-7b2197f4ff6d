.screen-reader-shortcut {
	position: absolute;
	top: -1000em;

	&:focus {
		left: 6px;
		top: 6px;
		height: auto;
		width: auto;
		display: block;
		font-size: 14px;
		font-weight: 600;
		padding: 15px 23px 14px;
		background: #f1f1f1;
		color: $color-bla;
		z-index: 100000;
		line-height: normal;
		box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
		text-decoration: none;
		outline: 2px solid transparent;
		outline-offset: -2px;
	}
}
body.admin-bar{
	#header{
		.menu-wrapper{
			#top_content{
				top: 32px;
			}
		}
	}
}
html.lock-height{
	max-height: 100vh;
	overflow: hidden;
}
.mobile-menu{
	display: none;

	.menu-toggle {

		button {

			background: transparent;
			border: 0;

		}

	}

}
#header {
	width: 100%;
	transition: all 0.5s;
	background: #fff;

	.menu-wrapper{
		#top_content {
			position: fixed;
			top: 0;
			width: 100%;
			z-index: 999;
			background-color: $header-top-background;
			color: white;
			.wrapper {
				display: flex;
				justify-content: flex-end;
				flex-direction: row;
				align-items: center;
				padding: 10px 0;
	
				.socialmedia {
					display: flex;
					margin-right: auto;
					ul {
						display: flex;
						margin: 0;
						gap: 0.5rem;
						li {
							list-style: none;
							a {
								color: $header-top-text;
								i{
									&::before{
										width: 25px;
										font-size: 0.8rem;
										height: 25px;
										display: block;
										text-align: center;
										border: solid 1px white;
										border-radius: 50%;
										line-height: 26px;
									}
								}
								&:focus-visible{
									outline: solid white 2px;
								}
								&:hover {
									color: #f1bd40;
								}
							}
						}
					}
				}
				.top_nav {
					nav#top_navigation_left {
						ul {
							margin: 0;
							padding: 0;
	
							li {
								padding: 0 10px;
								list-style: none;
								line-height: 1;
		
								a {
									font-size: 12px;
									color: $header-top-text;
									transition: color 0.2s;
																		&:focus-visible{
										outline: solid white 2px;
									}
									&:hover {
										color: #f1bd40;
									}
								}
							}
						}
					}
				}
				#mobile_top_navigation_right {
					display: none;
				}
	
				.top_toolbox {
					nav#top_navigation_right {
						ul {
							margin: 0;
							padding: 0;
							display: flex;
							justify-content: center;
							li {
								padding: 0 12px;
								list-style: none;
								line-height: 1;
								display: flex;
								&.menu-item-has-children{
									&:after {
										margin-left: 0.5rem;
										content: '◢';
										transform: rotate(45deg);
										color: #fbba00;
										font-size: 0.5em;
										z-index: -1;
									}	
								}
								button{
									height: 10px;
									width: 10px;
									vertical-align: middle;
									float: right;
									margin-left: 10px;
									margin-right: -17px;
									background-color: transparent;
									border: none;
									&:focus-visible{
										outline: solid white 2px;
									}
								}
								a {
									font-size: 12px;
									position: relative;
									color: white;
									transition: color 0.2s; 
									
									&:focus-visible{
										outline: solid white 2px;
									}
									&:hover {
										color: #f1bd40;
									}
	
									&:before {
										font-size: 14px;
										margin: 5px 5px 0 0;
									}
								}
	
								&.menu-item-has-children {
									position: relative;
									//Steg 2
									.sub-menu {
										display: none;
										position: absolute;
										left: -90px;
										padding-top: 25px;
										width: 260px;
										z-index: 1000;
										top: 10px;

										&:after {
											top: -5px;
											left: 50%;
											border: solid transparent;
											content: " ";
											height: 0;
											width: 0;
											position: absolute;
											pointer-events: none;
											border-color: rgba(136, 136, 136, 0);
											border-bottom-color: $color-knapp;
											border-width: 15px;
											margin-left: -15px;
										}
	
										li {
											background: $menu-step-1-background;
											border-bottom: solid 1px #7d8a90;
											text-align: left;
											padding: 12px 20px;
											margin: 0;
											transition: all 0.2s;
											position: relative;
											width: 100%;
	


											a {
												color: #fff;
												font-size: 14px;
												padding: 0;
												letter-spacing: 0.6px;
												text-decoration: none;
												line-height: 1.45;
												&:after {
													content: "";
												}
	
												&:focus {
													outline: 0.08em dashed $white;
												}
												&:hover{
													color: #f1bd40;
												}
											}
	
											&:last-child {
												border-bottom: 0;
											}
										}
	
										&.open {
											display: block;
											animation-name: slideUp;
											animation-duration: 0.5s;
											animation-fill-mode: forwards;
											animation-timing-function: ease;
										}
									}
								}
	
							}
						}
					}
				}
			}
		}

		#logotype {
			max-width: 310px;
			margin: 8px 0 15px 0;
			width: 30%;
	
			a {
				width: 100%;
	
				.logo-img {
					height: auto;
					width: 80%;
				}
			}
		}
	
		nav#main_navigation {
			width: 70%;
			display: block;
	
			//Steg 1
			>ul {
				margin: 0;
				padding: 0;
				z-index: 1000;
				justify-content: flex-end;
				>li {
					margin: 0;
					list-style: none;
					padding: 0 20px;
					text-align: center;
					position: relative;
					line-height: 1;
					&.menu-item-has-children{
						padding-right: calc(1rem + 20px);
						button{
							height: 15px;
							width: 15px;
							vertical-align: middle;
							float: right;
    						margin-right: -21px;
							background-color: transparent;
							border: none;
							&:focus-visible{
								outline: solid white 2px;
							}
						}
						&:after {
							position: absolute;
							margin-left: 0.5rem;
							content: '◢';
							transform: rotate(45deg);
							color: #fbba00;
							font-size: 0.75em;
							z-index: -1;
						}
					}
					a {
						color: white;
						font-size: 18px;
						text-decoration: none;
						@media (max-width:951px){
							font-size: 10px;
						}
						&:focus-visible{
							outline: solid white 2px;
						}
						&:hover {
							color: #f1bd40;
						}
					}

					.grow_mobile {
						display: none;
						position: absolute;
						right: 0;
						padding: 10px 12px;
						color: #fff;
	
						&:hover {
							cursor: pointer;
						}
	
						&:before {
							background: url("../../assets/images/svg/plus-white.svg") no-repeat center center;
							background-size: 16px;
							content: "";
							height: 20px;
							margin: 0;
							width: 20px;
						}
	
						&.active {
							&:before {
								content: "";
								background: url("../../assets/images/svg/minus-white.svg") no-repeat center center;
								background-size: 16px;
								height: 20px;
								margin: 0;
								width: 20px;
							}
						}
					}
	
					&#menu-item-639 {
						.sub-menu {
							left: -80px;
						}
					}
	
					//Steg 2
					.sub-menu {
						display: none;
						position: absolute;
						left: -65px;
						z-index: 1;
						width: 290px;
						padding-top: 25px;
						z-index: 1000;
						height: auto;
						max-height: 80vh;
						overflow-y: scroll;
						-ms-overflow-style: none;
						scrollbar-width: none;

						&::-webkit-scrollbar {
							display: none;
						}
	
						&:after {
							border: solid transparent;
							border-color: rgba(136, 136, 136, 0);
							border-bottom-color: $color-knapp;
							border-width: 15px;
							content: " ";
							height: 0;
							margin-left: -15px;
							top: -5px;
							left: 50%;
							position: absolute;
							pointer-events: none;
							width: 0;
						}
	
						li {
							border-bottom: solid 1px #7d8a90;
							line-height: 1;
							width: 100%;
							text-align: left;
							padding: 12px 20px;
							margin: 0;
							background: $menu-step-1-background;
							transition: all 0.2s;
							position: relative;

							.grow {
								color: #fff;
								position: absolute;
								right: 7px;
								padding: 0 12px;
	
								&:hover {
									cursor: pointer;
								}
	
								&:before {
									background-size: 16px;
									background: url("../../assets/images/svg/plus-white.svg") no-repeat center center;
									content: "";
									height: 20px;
									width: 20px;
									margin: 0;
								}
	
								&.active {
									&:before {
										background: url("../../assets/images/svg/minus-white.svg") no-repeat center center;
										background-size: 16px;
										content: "";
										height: 20px;
										margin: 0;
										width: 20px;
									}
								}
							}

							button{
								height: 20px;
								width: 20px;
								vertical-align: middle;
								float: right;
								margin-right: 0px;
								background-color: transparent;
								border-radius: 15px;
								position: relative;
								&:focus-visible{
									outline: solid white 2px;
									outline-offset: 2px;
								}
								&::after{
									background-size: cover;
									background: url("../../assets/images/svg/plus-white.svg") no-repeat center center;
									content: "";
									position: absolute;
									inset: 0;
								}
								&[aria-expanded="true"]{
									&::after {
										background: url("../../assets/images/svg/minus-white.svg") no-repeat center center;
									}
								}
								&[aria-expanded="false"]{
									&::after{
										background-size: cover;
										background: url("../../assets/images/svg/plus-white.svg") no-repeat center center;
										content: "";
										position: absolute;
										inset: 0;
									}
								}
							}
	
							a {
								color: $menu-step-1-font;
								font-size: 14px;
								letter-spacing: 0.5px;
								line-height: 18px;
							
								&:hover {
									color: #f1bd40;
								}
						
								&:focus {
									outline: 0.08em dashed $white;
								}
							}
	
							//Steg 3
							.sub-menu {
								left: 0 !important;
								margin: 10px -20px 0 0;
								padding: 0;
								position: relative;
								width: 290px;
								z-index: 1000;
								height: auto;
								max-height: 80vh;
								overflow-y: scroll;
								-ms-overflow-style: none;
								scrollbar-width: none;
								margin-left: -20px;
								margin-bottom: -20px;

								&::-webkit-scrollbar {
									display: none;
								}
	
								&:after {
									display: none;
								}
	
								li {
									line-height: 1;
									padding: 12px 20px;
									background: $menu-step-2-background;
	
									.grow {
										color: #fff;
										position: absolute;
										right: 7px;
										padding: 0 12px;
	
										&:hover {
											cursor: pointer;
										}
	
										&:before {
											filter: invert(100%) sepia(8%) saturate(7142%) hue-rotate(183deg)
												brightness(89%) contrast(90%);
											background-size: 16px;
											background: url("../../assets/images/svg/plus-white.svg") no-repeat center
												center;
											content: "";
											height: 20px;
											width: 20px;
											margin: 0;
										}
	
										&.active {
											&:before {
												background: url("../../assets/images/svg/minus-white.svg") no-repeat center
													center;
												background-size: 16px;
												content: "";
												height: 20px;
												margin: 0;
												width: 20px;
											}
										}
									}
	
									
									button{
										height: 20px;
										width: 20px;
										vertical-align: middle;
										float: right;
										margin-right: 0px;
										background-color: transparent;
										border-radius: 15px;
										position: relative;
										&:focus-visible{
											outline: solid black 2px;
											outline-offset: 2px;
										}
										&::after{
											background-size: cover;
											filter: invert(100%) sepia(8%) saturate(7142%) hue-rotate(183deg)
														brightness(89%) contrast(90%);
											background: url("../../assets/images/svg/plus-white.svg") no-repeat center center;
											content: "";
											position: absolute;
											inset: 0;
										}
										&[aria-expanded="true"]{
											&::after {
												filter: invert(100%) sepia(8%) saturate(7142%) hue-rotate(183deg)
														brightness(89%) contrast(90%);
												background: url("../../assets/images/svg/minus-white.svg") no-repeat center center;
											}
										}
										&[aria-expanded="false"]{
											&::after{
												background-size: cover;
												filter: invert(100%) sepia(8%) saturate(7142%) hue-rotate(183deg)
														brightness(89%) contrast(90%);
												background: url("../../assets/images/svg/plus-white.svg") no-repeat center center;
												content: "";
												position: absolute;
												inset: 0;
											}
										}
									}
									a {
										position: relative;
										color: $menu-step-2-font;
	
										&:before {
											content: "\f105";
											font-family: $FA;
											margin: 0 6px 1px 0;
										}
										
										&:hover {
											color: #162943;
											text-decoration: underline;
										}
										&:focus-visible{
											outline: dashed #162943 2px										
										}
										
									}
	
									&:hover {
										background: $menu-step-2-hover-background;
										color: #f1bd40;
									}
	
									.sub-menu {
										display: none;
										width: 290px;
										margin-bottom: -20px;
										li {
											line-height: 1;
											padding: 12px 20px;

											a {
												padding: 14px 10px;
												
												&:hover {
													text-decoration: underline;
												}
												
											}
										}
									}
	
									&:last-child {
										a {
											border-bottom: none;
										}
									}
								}
							}
	
							&:last-child {
								border-bottom: 0;
								margin-bottom: 0.5rem;
							}
						}
	
						&.open {
							display: block !important;
							animation-name: slideUp;
							animation-duration: 0.5s;
							animation-fill-mode: forwards;
							animation-timing-function: ease;
						}
					}
	
					&.current-menu-item,
					&.current_page_ancestor {
						>a {
							color: #fbba00;
							text-decoration: underline;
						}
					}
				}
			}
	
			.search-icon {
				margin: -3px 0 0 16px;
	
				span {
					position: relative;
	
					img {
						width: 30px;
						height: 30px;
					}
				}
	
				&:hover {
					cursor: pointer;
				}
			}
		}
		>.wrapper{
			margin-top: 51px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 1rem 0;
			.search-bar{
				form{
					display: contents;
				}
				display: flex;
				form{
					display: contents;
				}
				input{
					padding: 0;
					border: none;
					width: 450px;
					padding: .5rem 1rem;
					height: 100%;
				}
				input[type='search']{
					height: 45px;
					background-color: #e7edf0;
					border-radius: 4px 0 0 4px;
				}
				button[type='submit']{
					width: auto;
					padding: .5rem 1rem;
					display: flex;
					align-items: center;
					gap: .5rem;
					border-radius: 0 4px 4px 0;
					background-color: #005cb9;
					color: white;
					border: solid 1px #005cb9;

					&:focus {

						outline: 1px dashed #000;
						outline-offset: 2px;
	
					}
				}
			}
		}
	}
}

body.logged-in {
	#header {
		&.vissible {
			top: -80px;
		}
	}
}
