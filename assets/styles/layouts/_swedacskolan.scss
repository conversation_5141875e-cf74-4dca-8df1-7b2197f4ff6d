// SCSS for SWEDACSKOLAN  and some temporary bootstrap classes

#main_wrapper {
    overflow-x: hidden;
}

.mt-15 {
    margin-top: -12px;
}

.h-500 {
    height: 500px;
}

.video-height {
    height: 500px;
    @media only screen and (max-width:768px) {
        height: 300px;
    }
}

.h-250 {
    padding-bottom: 100% !important;
}

.text-large {
    font-size: 7rem;
}

.text-overlay {
    font-size: 28px;
}

.grow { 
    transition: all .2s ease-in-out; 
}

.grow:hover { 
    transform: scale(1.02); 
}

.overlay {
    position: absolute;
    bottom: 0;
    background: rgba(0, 112, 168, 0.75);
    width: 100%; 
    color: white !important;
    font-size: 18px;
    padding: 25px;
    text-align: center;
}

.playIcon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.responsive-background {
    max-width: 100%;
    background-position: center;
    background-size: cover;
  }

.videoPlay { 
    transition: all .2s ease-in-out; 
    cursor: pointer;
}

.videoPlay:hover { 
    opacity: 0.8;
}

.show {
    display: block !important;
}

.hide {
    display: none !important;
}
