.cardImg {
    height:300px;
    background-size: cover;
    background-position: center;
}

a#card-box:focus {

    outline: 1px dashed #fff;
    outline-offset: 2px;

}

.bg-d-blue {
    background: $color-da-blue;
}

.accordion {
    &:hover {
        cursor: pointer;
        color: $white;
        background: $color-da-blue;
    }
}

.card-header {
    &:after {
        font-family: 'FontAwesome';  
        content: "\f068";
        float: right; 
    }
    &.collapsed:after {
        content: "\f067"; 
    }
}

.button_links {
    width: 30%;
    float: right;
    position: relative;
    z-index: 2;

    a {
        line-height: 18px;
        position: relative;
        padding: 12px 15px 8px;
        float: left;
        width: 60%;
        text-align: left;
        color: $white;
        background-color: $color-da-blue;
        border-radius: 5px;
        font-size: 14px;
                text-decoration: none;
        @media only screen and (max-width:768px) {
            width: 100% !important;
        }

        &:after {
            float: right;
            width: 20px;
            height: 20px;
            background: url("../../assets/images/pil-white.png") no-repeat center center;
            background-size: 16px;
            content: '';
            margin: -2px 5px 2px 5px;
            transition: all .2s;
            color: $white;
        }

        &:hover {
            color: $white;
            background: $color-bla;
            &:after {
                margin: -2px 0 2px 0;
            }
        }
    }
}

.mb-neg-5 {
    margin-bottom: -3rem;
}
