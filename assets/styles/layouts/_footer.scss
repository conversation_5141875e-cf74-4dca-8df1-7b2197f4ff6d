#footer {
	color: #fff;
	width: 100%;
	float: left;
	position: relative;
	background: $color-da-blue;

	.footer_left {
		float: left;
		width: 50%;
		padding: 0 20px 0 0;

		.logotype {
			float: left;
			width: 100%;
			max-width: 310px;
			height: 80px;

			img {
				width: 100%;
				height: auto;
			}
		}

		.footer_text {
			float: left;
			width: 100%;
			margin: 0 0 20px;
			line-height: 22px;
			font-size: 16px;
			color: $color-text;

		}

		.footer_contact {
			float: left;
			width: 100%;

			.tel {
				float: left;
				width: 100%;
				margin: 0 0 15px;

				.left {
					float: left;
					width: 10%;

					img {}
				}

				.right {
					float: right;
					width: 85%;

					span {
						font-size: 24px;
						margin: 10px 0 0;
						float: left;
						width: 100%;

						a {
							color: $color-bla;
							&:hover{
								color: white;
							}
							&:focus-visible {
								outline: dashed $white 0.08em;
							}
						}
					}

					p {
						font-size: 12px;
						float: left;
						width: 100%;
						margin: 0;
						line-height: 1;
						display: none;
					}
				}
			}

			.epost {
				float: left;
				width: 100%;

				.left {
					float: left;
					width: 10%;

					img {}
				}

				.right {
					float: right;
					width: 85%;

					span {
						font-size: 24px;
						margin: 0;
						float: left;
						width: 100%;

						a {
							color: $color-bla;
							&:hover{
								color: white;
							}
							&:focus {
								outline: 0.08em dashed $white;
							}
						}
					}

					p {
						font-size: 12px;
						float: left;
						width: 100%;
						margin: 0;
						line-height: 1;
						display: none;
					}
				}
			}

			.adresser {
				float: left;
				width: 100%;
				margin: 20px 0 0;
				font-size: 16px;

				ul {
					margin: 0;
					padding: 0;

					li {
						list-style: none;

						b {
							color: #fff;
							margin: 0 0 5px;
							float: left;
							width: 100%;
							letter-spacing: 0.5px;
						}

						span {
							color: $color-text;
							font-size: 16px;

							p {
								line-height: 22px;
							}
						}
						h2 {
							margin: -3px 0 2px;
							color: #fff;
							font-size: 24px;
							letter-spacing: 1px;
						}
					}
				}
			}
		}
		.menu-footer-container{
			float: left;
			ul{
				li{
					list-style: none;
					a{
						color: white;
						position: relative;
						&:focus-visible {
							outline: dashed $white 0.08em;
						}
						&::before{
							position: absolute;
							content: '◢';
							left: -1.5rem;
							font-size: 0.85rem;
							transform: rotate(-45deg);
							color: #fbba00;
						}
					}
				}
			}
		}
	}

	.footer_right {
		float: right;
		width: 50%;
		padding: 0 0 0 20px;

		.socialmedia {
			float: left;
			width: 100%;
			height: 80px;

			ul {
				margin: 0;
				padding: 0;
				float: right;

				li {
					list-style: none;
					float: left;
					padding: 0 0 0 15px;

					span {
						a {
							display: inline-block;
							width: 40px;
							height: 40px;
							float: left;
							border: solid 1px #fff;
							color: #fff;
							text-align: center;
							padding: 7px 0 0 0;
							border-radius: 50em;

							.fa {
								font-size: 18px;
								transition: all .2s;
							}

							&:hover {
								.fa {
									color: $color-bla;
								}
							}

							&:focus {
								outline: 0.08em dashed $white;

								.fa {
									color: $color-bla;
								}
							}
						}
					}
				}
			}
		}

		.lankar {
			float: left;
			width: 100%;

			ul {
				margin: 0;
				padding: 0;

				li {
					list-style: none;
					float: left;
					width: 100%;
					padding: 0;
					margin: 0 0 15px;
					padding: 0 0 15px;

					.right {
						float: left;
						width: 100%;

						a {
							color: $color-text;
							float: left;
							width: 100%;

							h2 {
								margin: -3px 0 2px;
								color: #fff;
								font-size: 24px;
								letter-spacing: 1px;
							}

							&:focus {
								outline: 0.08em dashed $white;
							}
						}

						.text {
							float: left;
							width: 100%;
							color: $color-text;
							font-size: 18px;

							p {
								margin: 0;
								line-height: 22px;
								font-size: 16px;
							}
						}
					}
				}
			}
		}

		#news_letter {
			float: left;
			width: 100%;
			display: none;

			h2 {
				color: #fff;
				font-size: 24px;
				letter-spacing: 1px;
			}

			.widget_wysija {
				input {
					padding: 13px 10px;
					float: left;
					font-size: 14px;
					border: none;

					&.wysija-input {
						width: 70%;
						border-bottom-left-radius: 3px;
						border-top-left-radius: 3px;
						border-right: none;

						&#form-validation-field-0 {
							background: #FFA9A9 url(../images/error.png) no-repeat right 10px top 15px;

							&::-webkit-input-placeholder {
								color: #333;
							}
						}

					}

					&.wysija-submit {
						margin: 0;
						width: 30%;
						border-bottom-right-radius: 3px;
						border-top-right-radius: 3px;
						color: #fff;
						background: $color-bla;
						border-left: none;
						transition: all .2s;

						&:hover {
							background: lighten($color-bla, 10%);
							text-decoration: none;
							cursor: pointer;
						}
					}
				}
			}

			//Meddelande
			.wysija-msg {
				.allmsgs {
					.updated {
						background: none;
						border: none;

						ul {
							li {
								position: relative;
								padding: 5px 0;
								color: $color-text;
								float: left;
								width: 100%;

								&:before {
									content: '\f129';
									font-family: $FA;
									float: left;
									margin: 0 7px 0 0;
									font-size: 18px;
									color: lighten($color-bla, 10%);
								}

							}
						}
					}
				}
			}

			.formError {}

			.formErrorContent {
				display: none !important;
			}
		}
	}
}

body.home {
	#footer {
		margin: 0;
	}
}

#wrapper_footer {
	float: left;
	width: 100%;
	font-size: 12px;
	font-weight: 600;
	color: #666;
	transition: color .2s;
	background-color: #fff;
	margin: 40px 0 0;

	.copyright {
		width: 100%;
		max-width: 1040px;
		padding: 15px 0;
		margin: 5px auto 3px;
		text-align: center;
		display: flex;

		.copy {
			width: 30%;
			float: left;
			text-align: left;
		}

		.links {
			width: 70%;
			float: left;
			text-align: right;

			a {
				border-right: solid 1px #acacac;
				padding: 0 8px;
				&.cookie {
					border-right: none;

					i {
						color: #3D9CCC;
					}
				}
			}
		}
	}
}