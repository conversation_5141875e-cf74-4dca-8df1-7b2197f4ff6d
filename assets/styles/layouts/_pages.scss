.row {
	width: 100%;
}
#quick_links{
	width: 100%;
	float: left;
	margin: 4rem 0;
	display: flex;
	gap: 2rem;
	flex-wrap: wrap;
	list-style: none;
	li {
		list-style: none;
		text-decoration: none;
		flex: 1;
		flex-basis: 20%;

		a{
			display: block;
			color: white;
			box-shadow: 0 0 40px 20px rgba(16,42,65,0);
			transform: all 0,25s;
			&:focus-within{
				outline:solid 3px black;
				outline-offset: 2px;
	
			}
			&:hover{
				transform: scale(1.1);
				box-shadow: 0 0 40px 20px rgba(16,42,65,.16);
			}
			div{
				position: relative;
				background-color: #162943;
				color: white;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				padding: 2rem;
				height: 140px;
				&:after {
					position: absolute;
					content: '◢';
					transform: rotate(-45deg);
					color: #fbba00;
					font-size: 0.5em;
					text-align: center;
					line-height: 22px;
					right: 1rem;
					bottom: 1rem;
					width: 25px;
					height: 25px;
					border-radius: 50%;
					border: solid 1px white;
				}
			}
		}

	}

}
#slider{
	#sliderControlls{
		&.alt-colors{
			min-width: 100%;
			margin-left: 0;
			@media only screen and (max-width:782px) {
				min-width: calc(100% + 40px);
				margin-left: -20px;
			}
			float: left;
			margin-bottom: 2rem;
		}
		.carousel-control-prev{
			left: 0;
			width: 10%;
			@media only screen and (max-width:600px) {
				left: calc(50% - 40px);
				width: 35px;
				height: 35px;
				top: auto;
				bottom: 15px;
			}
			.carousel-control-prev-icon{
				background: none;

				&::after{
					position: absolute;
					content: '◢';
					color: #fbba00;
					font-size: 10px;
					text-align: center;
					line-height: 30px;
					padding-right: 4px;
					left: 50%;
					top: 50%;
					width: 35px;
					height: 35px;
					border: solid 1px #fff;
					border-radius: 50px;
					transform:translate(-50%, -50%) rotate(135deg);
				}
			}
			&:focus-visible{
				.carousel-control-prev-icon{	
					&::after{
						outline:solid 5px white;
						outline-offset: 2px;
					}
				}
			}
		}
		.carousel-control-next{
			right: 0;
			width: 10%;
			@media only screen and (max-width:600px) {
				right: calc(50% - 40px);
				width: 35px;
				height: 35px;
				top: auto;
				bottom: 15px;
			}
			.carousel-control-next-icon{
				background: none;
				&::after{
					position: absolute;
					content: '◢';
					color: #fbba00;
					font-size: 10px;
					text-align: center;
					line-height: 30px;
					padding-right: 4px;
					left: 50%;
					top: 50%;
					width: 35px;
					height: 35px;
					border: solid 1px #fff;
					border-radius: 50px;
					transform:translate(-50%, -50%) rotate(-45deg);
				}
			}
			&:focus-visible{
				.carousel-control-next-icon{	
					&::after{
						outline:solid 5px white ;
						outline-offset: 2px;
					}
				}
			}
		}
	}
	.carousel{
		&.alt-colors{
			.carousel-item{

				.slideCover{

				}
				>.wrapper{
					@media only screen and (max-width:1450px) {
						padding: 0 6rem
					}
					@media only screen and (max-width:1050px) {
						padding: 0 6rem
					}
					@media only screen and (max-width:860px) {
						padding: 0 4rem
					}
					@media only screen and (max-width:600px) {
						padding: 0 20px;
					}
					.info{
						display: inline-block;	
						max-width: 450px !important;
						width: 100%;	
					}
					.link{
						background-color: #fdb913;
						
						a{
							color: #162943;
							&::after{
								color: #162943;
							}
						}
					}
				}
			}		
		}
		.carousel-item{
			position: relative;
			height: 455px;
			.slideCover{
				position: absolute;
				inset: 0;
				background-color: rgba(0.35,0.35,0.35,0.35);
			}
			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
				@media only screen and (min-width:782px) {
					&.desktop {
						display: block !important;
					}

					&.mobile {
						display: none !important;
					}
				}

				@media only screen and (max-width:782px) {
					&.desktop {
						display: none !important;
					}

					&.mobile {
						display: block !important;
					}
				}
			}
			>.wrapper{
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				color: white;
				padding: 0 6rem;
				@media only screen and (max-width:860px) {
					padding: 0 4rem
				}
				@media only screen and (max-width:600px) {
					padding: 0 20px;
					top: 35%;
					transform: translate(-50%, -35%);
				}
				@media only screen and (max-width:260px) {
					top: 30%;
					transform: translate(-50%, -30%);
				}
				.content {
					@media only screen and (max-width:600px) {
						font-size: 17px;
						line-height: 1.3;
					}
					@media only screen and (max-width:300px) {
						font-size: 16px;
					}
					@media only screen and (max-width:260px) {
						font-size: 15px;
					}
				}
				.info{
					max-width: 600px !important;
				}
				.link{
					background-color: #162943;
					display: inline-block;
					margin-top: 0.85rem;
					a{
						display: block;
						padding: 0.5rem 2.5rem 0.5rem 1.25rem;
						color: white;
						font-size: 0.875em;
						position: relative;
						&::after{
							position: absolute;
							content: '◢';
							color: #fff;
							font-size: 10px;
							text-align: center;
							line-height: 35px;
							right: 7px;
							top: 50%;
							width: 35px;
							height: 35px;
							transform:translateY(-50%) rotate(-45deg);
						}
						&:focus-visible{
							outline:solid 3px black ;
							outline-offset: 2px;
						}
					}
				}
				h2{
					max-width: 600px !important;
					color: white;
					font-size: 3em;
					word-break: break-word;

					@media only screen and (max-width:860px) {
						font-size: 2.4em;
					}
					@media only screen and (max-width:600px) {
						font-size: 2.1em;
					}
					@media only screen and (max-width:500px) {
						font-size: 1.9em;
					}
					@media only screen and (max-width:300px) {
						font-size: 1.7em;
					}
					@media only screen and (max-width:260px) {
						font-size: 1.4em;
					}
				}
			}
		}
	}
}
.carousel-copyright {
	bottom: 25px;
	font-size: 13px; 
	color: #fff;
	position: absolute; 
	z-index: 1; 
	text-align: center; 
	width: 100%;
	opacity: .65;
	line-height: 1.2;
	padding: 0 20px;
	user-select: none;
	letter-spacing: 0.3px;
	@media only screen and (max-width:600px) {
		bottom: 60px;
	}
	@media only screen and (max-width:260px) {
		line-height: 1.15;
	}
	div {
		@media only screen and (max-width:600px) {
			position: relative;
			margin: 0 auto;
			max-width: 100%;
		}
	}
}
.image-links{
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	margin: 2rem 0;
	width: 100%;
	.image-link{
		position: relative;
		@media only screen and (max-width:768px) {
			width: 100%;
		}
		width: calc(50% - 1rem);
		aspect-ratio: 1/1;
		margin: 1rem 0;
		transition: all 0.25s;
		&:focus-within,&:hover{
			scale: 1.05;
		}
		img{
			position: absolute;
			inset: 0;
			object-fit: cover;
			height: 100%;
			width: 100%;
		}
		a{
			position: absolute;
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
			color: white;
			&:focus-visible{
				
				outline: solid black 3px;
				
			}
			.info{
				position: absolute;
				bottom: 0;
				left: 0;
				padding: 1rem;
				.title {
					font-weight: bold;
					font-size: 1.75rem;
					width: 100%;
					word-break: break-word;
					@media only screen and (max-width:600px) {
						font-size: 1.5rem;
					}
					@media only screen and (max-width:450px) {
						font-size: 1.25rem;
					}
					&::after{
						position: absolute;
						content: "◢";
						color: #fff;
						font-size: 10px;
						text-align: center;
						line-height: 48px;
						margin-left: 0.25rem;
						transform: rotate(-45deg);
					}
				}
				.flair{
					width: 100%;
					display: block;
				}
			}
		}
	}
}
#info-block{
	background-color: #e7edf0;
	float: left;
	width: 100%;
    margin: 0 0 2rem 0;
    padding: 2rem 0;
	.blocks{
		display: grid;
		grid-auto-rows: auto;
		grid-gap:  2rem 4rem;
		grid-template-columns: repeat(3, minmax(0, 1fr));
		@media only screen and (max-width:980px) {
			grid-template-columns: repeat(2, minmax(0, 1fr));
			.block.third{
				grid-column: 3/1;
			}
		}
		@media only screen and (max-width:680px) {
			grid-template-columns: repeat(1, minmax(0, 1fr));
			.block.third{
				grid-column: 1/1;
			}
		}
		.block{
			display: flex;
			flex-flow: column;
			justify-content: center;
			align-items: center;	
			img{
				width: 60%;
			}
			img+.title{
				font-size: 4rem;
			}
			.title{
				font-size: 5.5rem;
				font-weight: 500;
				color: #162943;

			}
			.text{
				text-align: center;
			}
			.link{
				background-color: #162943;
				margin-top: 0.5rem;
				a{
					display: block;
					padding: 0.85rem 2.5rem 0.85rem 1.25rem;
					color: white;
					font-size: 0.875em;
					position: relative;
					&::after{
						position: absolute;
						content: '◢';
						color: #fff;
						font-size: 10px;
						text-align: center;
						line-height: 35px;
						right: 7px;
						top: 50%;
						width: 35px;
						height: 35px;
						transform:translateY(-50%) rotate(-45deg);
					}
					&:focus-visible{
						outline:solid 3px black ;
						outline-offset: 2px;
					}
				}
			}
		}
	}
}
#frontpage_news {
	float: left;
	width: 100%;
	margin: 4rem 0;
	padding: 4rem 0;
	background: #ebeff2;

	.left {
		float: left;
		width: 65.5%;
		padding: 0 40px 0 0;

		ul {
			margin: 0;
			padding: 0;

			li {
				list-style: none;
				float: right;
				width: 100%;

				.image {
					float: left;
					margin: 0 0 15px;
					width: 100%;


					img {
						float: left;
						width: 100%;
						height: auto;
						max-height: 350px;
						object-fit: cover;
					}
					
				}

				.info {
					
					a {
						color: #333;
						display: inline-block;
						h2 {
							font-size: 46px;
							margin: 0 0 10px;
							font-weight: 400;
							line-height: 46px;
							letter-spacing: normal;
						}
						&:focus-visible{
							outline:solid 3px black ;
							outline-offset: 2px;
						}
					}
					.excerpt {
						font-size: 16px;
						line-height: 22px;
						color: #505050;
						p{
							margin: 0;
						}

					}

				}

			}
		}
	}

	.right {
		float: right;
		width: 34.5%;
		padding: 0 0 0 40px;

		img{
			max-height: 250px;
			object-fit: cover;
			float: right;
			width: 100%;
			margin: 0 0 15px;

		}


		.info {
			float: right;
			width: 100%;
			

			h2 {
				margin: 15px 0 0px;
				font-size: 22px;
				line-height: 26px;
								font-weight: 400;
				letter-spacing: normal;

			}
			.excerpt {
				font-size: 16px;
				margin: 0 0 1.5rem 0;
				p {
					margin: 0;
					line-height: 22px;
				}
			}
			a{
				display: inline-block;
				background-color: #005cb9;
				color: white;
				padding: 0.5rem 2.5rem 0.5rem 1rem;
				position: relative;
				&::after{
					position: absolute;
					right: 1.25rem;
					top: 50%;
					transform: translateY(-50%) rotate(-45deg);
					content: '◢';
					color: white;
					font-size: 0.75rem;
				}
				&:focus-visible{
					outline:solid 3px black ;
					outline-offset: 2px;
				}
			}
		}
	}
}

//Länkar på startsidan
#news_newsletter {
	float: left;
	margin: 20px 0 0;
	width: 100%;

	ul {
		margin: 0;
		padding: 0;

		li {
			padding: 0 !important;
			list-style: none;
			float: left;
			position: relative;
			margin: 0 !important;

			a {
				color: $color-bla;
				transition: all .2s;
				font-size: 16px;

				&:hover {
					color: lighten($color-bla, 10%);
					text-decoration: none;
				}

				&:before {
					float: left;
					width: 20px;
					height: 20px;
					background: url("../../assets/images/pil-blue.png") no-repeat center center;
					background-size: 16px;
					content: '';
					margin: 4px 8px 0 0;
				}
			}
		}
	}
}

//Puffar på startsidan
#frontpage_puffar {
	float: left;
	width: 100%;
	margin: 40px 0 0;
	.tag{
		margin-bottom: 0.5rem;
		font-size: 1.5rem;
		a{
			color: #162943;
			position: relative;
			&::after{
				position: absolute;
				content: '◢';
				color: #fbba00;
				font-size: 0.5em;
				text-align: center;
				line-height: 42px;
				width: 10px;
				height: 25px;
				transform: rotate(-45deg);
			}
		}
	}
	#owl-slider{
		position: relative;


		.owl-stage{

			.owl-item{
				padding: 6px;
				li{
					list-style: none;
				}
				&:hover{
					cursor: pointer;
					.image img{
						transform: scale(1.05);

					}
					a{
						text-decoration: none;
						h2{
							text-decoration: underline;
						}
						p{
							text-decoration: none;
						}
					}
				}
				&:focus-within{
					.content.item{
						outline:solid 5px black;
						outline-offset: 2px;

					}
				}
				.image{
					overflow: hidden;
					img{
						height: 200px;
						object-fit: cover;
						cursor: pointer;
						transition: transform 0.25s;
					}
				}
				a{
					text-decoration: none;
					h2{
						font-size: 1.25rem;
						color: black;
						font-weight: bold;
					}
				} 
				p{
					color: #505050;
					font-family: "Inter", sans-serif;
				}
				.timestamp{
					font-size: 0.85em;
					font-weight: 100;
					margin: 0.25rem 0;
				}
			}
		}
		.show-on-focus{
			display: none;
		}
		&:focus-visible{
			.show-on-focus{
				display: block;
			}
		}
		@media only screen and (max-width:1180px) {
			padding: 0 4rem;
			.owl-nav.disabled, .owl-nav{
				width: calc(100% - 4rem) !important;
			}
		}
		.owl-nav.disabled, .owl-nav{
			z-index: -1;
			display: flex;
			justify-content: space-between;
			position: absolute;
			width: calc(100% + 4rem);
			top: 30%;
			transform: translateY(-50%);
			margin: 0 -2rem;
			button{
				span{
					color: transparent;
					font-size: 2rem;
				}
			}
			.owl-prev{
				>span{
					position: relative;
					&::after{
						position: absolute;
						content: '◢';
						color: #fff;
						font-size: 10px;
						text-align: center;
						line-height: 35px;
						left: 50%;
						top: 50%;
						width: 35px;
						height: 35px;
						background-color: #162943;
						border-radius: 50px;
						transform: translate(-50%, -50%) rotate(135deg) ;
					}
				}
				&:focus-within{
					>span{
						&::after{
							outline:solid 3px black;
							outline-offset: 2px;
						}
					}
				}
			}
			.owl-next{
				>span{
					position: relative;
					&::after{
						position: absolute;
						content: '◢';
						color: #fff;
						font-size: 10px;
						text-align: center;
						line-height: 35px;
						left: 50%;
						top: 50%;
						width: 35px;
						height: 35px;
						background-color: #162943;
						border-radius: 50px;
						transform:translate(-50%, -50%) rotate(-45deg);
					}
	
				}
				&:focus-within{
					>span{
						&::after{
							outline:solid 3px black;
							outline-offset: 2px;

						}
					}
				}
			}
		}
	}
}

#frontpage_swedac_academy {
	float: left;
	width: 100%;
	background: $color-light;
	padding: 40px 0;
	margin: 40px 0 0;

	.wrapper {
		.content_swedac_academy {
			float: left;
			width: 65%;

			h2 {
				font-size: 22px;
				color: $color-bla;
				font-weight: 400;
				margin: 0 0 15px;
				letter-spacing: normal;
			}

			.text {
				font-size: 32px;
				font-weight: 300;
				line-height: 38px;

			}

			.lankar {
				margin: 20px 0 0;

				ul {
					margin: 0;
					padding: 0;

					li {
						padding: 0;
						list-style: none;
						float: left;
						margin: 0 20px 0 0;

						a {
							padding: 10px 25px;
							border-radius: 4px;
							background: $color-bla;
							color: #fff;
							display: inline-block;
							font-size: 16px;
							text-decoration: none;

							&:hover {
								background: lighten($color-bla, 10%);
								text-decoration: none;
								cursor: pointer;
							}
						}
					}
				}
			}
		}

		.images {
			float: right;
			width: 30%;
			padding: 0 0 0 30px;

			img {}
		}
	}
}


#swedac_magasin {
	float: left;
	width: 100%;
	margin: 2rem 0;

	
	h2 {
		margin: 0;
		font-weight: 400;
		letter-spacing: normal;
		font-size: 22px;
			a {
			color: $black;
			margin: 0 0 10px;
			width: 100%;
		}
	}
	.title{
		margin-bottom: 1rem;
		font-size: 1.5rem;
		a{
			color: #162943;
			position: relative;
			&::after{
				position: absolute;
				content: '◢';
				color: #fbba00;
				font-size: 0.5em;
				text-align: center;
				line-height: 42px;
				width: 10px;
				height: 25px;
				transform: rotate(-45deg);
			}
		}
	}

	ul {
		margin: 0;
		padding: 0;

		li {
			width: 100%;
			max-width: 230px;
			float: left;
			padding: 0;
			list-style: none;
			margin: 0 0 40px;

			.content {
				position: relative;
				height: 230px;
				transition: all 0.25s;
				box-shadow: 0 0 0px 0px rgba(16,42,65,0);

				&:hover{
					transform: scale(1.1);
					box-shadow: 0 0 40px 20px rgba(16,42,65,.16);

				}
				a {
					float: left;
					width: 100%;
					height: 100%;
					margin: 0;
					&:focus-visible{
						outline:solid 3px black ;
						outline-offset: 2px;
					}
					.cover {
						position: absolute;
						width: 100%;
						height: 100%;
						left: 0;
						top: 0;
						z-index: 1;
						background: -moz-linear-gradient(bottom, rgba(0, 0, 0, 0.65) 16%, rgba(0, 0, 0, 0.65) 17%, rgba(0, 0, 0, 0) 60%);
						background: -webkit-linear-gradient(bottom, rgba(0, 0, 0, 0.65) 16%, rgba(0, 0, 0, 0.65) 17%, rgba(0, 0, 0, 0) 60%);
						background: linear-gradient(to top, rgba(0, 0, 0, 0.65) 16%, rgba(0, 0, 0, 0.65) 17%, rgba(0, 0, 0, 0) 60%);
					}

					.post_image {
						position: absolute;
						width: 100%;
						height: 100%;

						img {    
							height: 100%;
							width: 100%;
							object-fit: cover;
						}
					}

					.info {
						position: absolute;
						z-index: 2;
						color: #fff;
						float: left;
						width: 100%;
						padding: 15px;
						bottom: 0;

						h2 {
							@media only screen and (max-width:240px) {
	
								font-size: 25px !important;

							}
							color: #fff;
							font-size: 20px;
							line-height: 24px;
							margin: 0 0 4px;
														letter-spacing: 0.8px;
						}

						.excerpt {
							p {
								font-size: 16px;
								line-height: 20px;
								margin: 0;
							}
						}
					}
				}
			}

			&:nth-child(odd) {
				float: right;
			}

			&:nth-child(even) {
				float: left;
				margin: 0 0 40px;
			}

			&:nth-last-child(-n+2) {
				margin: 0;
			}

			&:first-child {
				width: 50%;
				max-width: 500px;
				float: left !important;
				margin: 0 40px 0 0;

				.content {
					width: 100%;
					height: 500px;
					margin: 0 40px 0 0;
					position: relative;
					&:hover{
						transform: scale(1.05);
						box-shadow: 0 0 40px 20px rgba(16,42,65,.16);
					}
					.cover {
						position: absolute;
						width: 100%;
						height: 100%;
						left: 0;
						top: 0;
						z-index: 1;
						background: -moz-linear-gradient(bottom, rgba(0, 0, 0, 0.65) 16%, rgba(0, 0, 0, 0.65) 17%, rgba(0, 0, 0, 0) 60%);
						background: -webkit-linear-gradient(bottom, rgba(0, 0, 0, 0.65) 16%, rgba(0, 0, 0, 0.65) 17%, rgba(0, 0, 0, 0) 60%);
						background: linear-gradient(to top, rgba(0, 0, 0, 0.65) 16%, rgba(0, 0, 0, 0.65) 17%, rgba(0, 0, 0, 0) 60%);
					}

					.info {
						position: absolute;
						z-index: 2;
						color: #fff;
						float: left;
						width: 100%;
						padding: 15px;
						bottom: 0;

						h2 {
							color: #fff;
							font-size: 50px;
							line-height: 50px;
							margin: 0 0 10px;
														letter-spacing: 0.8px;
						}

						.excerpt {
							font-size: 16px;
							line-height: 20px;
						}
					}
				}
			}
		}
	}
}

#fordonsbesiktning {
	float: left;
	width: 100%;
	background: $color-light;
	padding: 4rem 0;
	margin: 4rem 0 2rem 0;

	.wrapper {
		display: flex;
		padding: 0;

		.left {
			float: left;
			width: 22%;

			.box {
				width: 130px;
				height: 130px;
				margin: 0 auto 0;
				text-align: center;

				img {
					width: 100%;
					height: auto;
				}
			}
		}

		.right {
			float: right;
			width: 78%;

			h2 {

				@media only screen and (max-width:240px) {
	
					font-size: 20px !important;

				}

				font-size: 25px;
				line-height: 36px;
				margin: 0 0 10px;
								letter-spacing: 0.5px;
				font-weight: 400;
			}

			.text {
				font-size: 16px;

				p {
					line-height: 23px;
					margin: 0;
				}
			}

			.lankar {
				margin: 15px 0 0;
				float: left;
				width: 100%;

				ul {
					margin: 0;
					padding: 0;

					li {
						padding: 3px 0;
						list-style: none;
						margin: 0;
						float: left;
						padding: 0 10px 0 0;

						a {
							position: relative;
							font-size: 16px;

							&:before {
								float: left;
								width: 20px;
								height: 20px;
								background: url("../../assets/images/pil-blue.png") no-repeat center center;
								background-size: 16px;
								content: '';
								margin: 2px 8px 0 0;
							}

							&:hover {}
						}
					}
				}
			}
		}
	}

}

//Template med snabbval
.page-header-custom {}

#snabbval {
	float: left;
	width: 100%;
	margin: 30px 0 0;

	ul {
		margin: 0;
		padding: 0;

		li {
			list-style: none;
			padding: 0;
			width: 100%;
			max-width: 320px;
			float: left;
			margin: 0 0 40px;
			transition: all .2s;

			a {
				float: left;
				width: 100%;

				.rubrik {
					float: left;
					width: 100%;
					background: $color-dark-grey;
					padding: 12px 15px 8px;
					line-height: 18px;
					margin: 0;
					border-radius: 5px;

					h2 {
						font-size: 16px;
						font-weight: 400;
						margin: 0;
						width: 100%;
						color: #fff;
												letter-spacing: normal;

						&:after {
							float: right;
							width: 100%;
							height: 20px;
							background: url("../../assets/images/pil-white.png") no-repeat center right;
							background-size: 20px;
							content: '';
							margin: -16px 5px 2px 5px;
							transition: all .2s;
						}
						&:hover {
							&:after {
								margin: -16px 0 2px 0;
							}
						}
					}
				}
			}

			&.middle {
				margin: 0 40px 40px;
			}
		}
	}
}

//Standard
.page-header {
	h1 {
		letter-spacing: 1.2px;
	}
}

.ackreditering_form_section {
	float: left;
	width: 100%;
	ul {
		margin: 10px 0 15px 35px;
	}

	#ansok_om_ackreditering_form {
		float: left;
		width: 100%;
		input:focus {

			outline: 1px dashed #000 !important;
			outline-offset: 2px !important;
		}
		.row_section {
			float: left;
			width: 100%;
			margin: 0 0 20px;

			&.last-child {
				border-bottom: none;
				padding: 0;
				margin: 0;
				padding: 0 0 10px;
			}

			&.with_checkboxes {
				.form-row {
					margin: 0 0 7px;
				}
			}

			span.row_title {
				float: left;
				width: 100%;
				margin: 0 0 15px;
								letter-spacing: 0.2px;
				font-weight: 500;

				b {
					float: left;
					width: 100%;
				}
			}

			.form-row {
				float: left;
				width: 100%;
				margin: 0 0 20px;
				position: relative;

				i {
					position: absolute;
					top: 18px;
					right: 10px;
					transform: scale(0);
					transition: transform .2s;

					&.fa-times {
						color: #CC002F;
						position: absolute;
						margin-top: 35px;
					}

					&.fa-check {
						color: #00925C;
						position: absolute;
						margin-top: 35px;
					}
				}

				input.inputbox {
					float: left;
					width: 100%;
				}

				input.inputbox.checkbox_annat {
					float: left;
					width: 100%;
					padding: 7px 15px;
					margin: 10px 0 0;
				}

				&.left {
					float: left;
					width: 48%;
				}

				&.right {
					float: right;
					width: 48%;
				}

				&.full {
					float: left;
					width: 100%;
				}

				&:last-child {
					margin: 0;
				}

				&.radio {
					margin: 0 0 7px;

					input.radio_input {
						float: left;
						width: 100%;
						padding: 7px 15px;
						margin: 10px 0 20px;
					}
				}

				.error~.fa-times {
					transform: scale(1);
				}

				.error~.fa-check {
					transform: scale(0);
				}

				.valid~.fa-times {
					transform: scale(0);
				}

				.valid~.fa-check {
					transform: scale(1);
				}
			}
		}

		.submit-button {
			font-size: 16px;
			margin: 15px 0 0;
			padding: 15px 30px;
			border-radius: 4px;
			color: #fff;
			background: $color-bla;
			border-left: none;
			transition: all .2s;
			float: right;

			&:hover {
				background: lighten($color-bla, 10%);
				text-decoration: none;
				cursor: pointer;
			}
			&:disabled{
				background-color: $color-dark-grey;
				&:hover {
					background: lighten($color-dark-grey, 10%);
					text-decoration: none;
					cursor: pointer;
				}
			}
		}

	}
}

// Visselblåsning
.visselblasning_form_section {
	float: left;
	width: 100%;
	ul {
		margin: 10px 0 15px 35px;
	} 

	#visselblasning_form {
		float: left;
		width: 100%;

		.row_section {
			float: left;
			width: 100%;
			margin: 0 0 20px;

			&.last-child {
				border-bottom: none;
				padding: 0;
				margin: 0;
				padding: 0 0 10px;
			}

			&.with_checkboxes {
				.form-row {
					margin: 0 0 7px;
				}
			}

			span.row_title {
				float: left;
				width: 100%;
				margin: 0 0 15px;
								letter-spacing: 0.2px;
				font-weight: 500;

				b {
					float: left;
					width: 100%;
				}
			}

			.form-row {
				float: left;
				width: 100%;
				margin: 0 0 20px;
				position: relative;

				i {
					position: absolute;
					top: 18px;
					right: 10px;
					transform: scale(0);
					transition: transform .2s;

					&.fa-times {
						color: #CC002F;
					}

					&.fa-check {
						color: #00925C;
					}
				}

				input.inputbox, textarea.inputbox {
					float: left;
					width: 100%;
				}

				input.inputbox.checkbox_annat {
					float: left;
					width: 100%;
					padding: 7px 15px;
					margin: 10px 0 0;
				}

				&.left {
					float: left;
					width: 48%;
				}

				&.right {
					float: right;
					width: 48%;
				}

				&.full {
					float: left;
					width: 100%;
				}

				&:last-child {
					margin: 0;
				}

				&.radio {
					margin: 0 0 7px;

					input.radio_input {
						float: left;
						width: 100%;
						padding: 7px 15px;
						margin: 10px 0 20px;
					}
				}

				.error~.fa-times {
					transform: scale(1);
				}

				.error~.fa-check {
					transform: scale(0);
				}

				.valid~.fa-times {
					transform: scale(0);
				}

				.valid~.fa-check {
					transform: scale(1);
				}
			}
		}

		.submit-button {
			font-size: 16px;
			margin: 15px 0 0;
			padding: 15px 30px;
			border-radius: 4px;
			color: #fff;
			background: $color-bla;
			border-left: none;
			transition: all .2s;
			float: right;

			&:hover {
				background: lighten($color-bla, 10%);
				text-decoration: none;
				cursor: pointer;
			}
			&:disabled{
				background-color: $color-dark-grey;
				&:hover {
					background: lighten($color-dark-grey, 10%);
					text-decoration: none;
					cursor: pointer;
				}
			}
		}

	}
}

// Medarbetare (Träffa swedacs medarbetare)
.medarbetare_holder {
	ul {
		margin: 0;
		float: left;
		width: 100%;

		li {
			float: left;
			width: 100%;
			border-top: solid 1px #ccc;
			padding: 15px 0 0;
			margin: 15px 0 0;
			list-style: none;

			&:last-child {}

			.medar_left {
				float: left;
				width: 30%;

				img {
					width: 100%;
					line-height: 0;
				}
			}

			.medar_right {
				float: left;
				width: 70%;
				padding: 0 0 0 20px;

				h3 {}

				.medar_ingress {
					float: left;
				}

				span.ingress {
					float: left;
					width: 100%;
				}

				.readMore {
					float: left;
					width: 100%;
					margin: 10px 0 0;

					a {
						font-size: 16px;
						margin: 0;
						padding: 10px 30px;
						border-radius: 4px;
						color: #fff;
						background: $color-bla;
						border-left: none;
						transition: all .2s;
						float: right;
						line-height: 1;

						&:hover {
							background: lighten($color-bla, 10%);
							text-decoration: none;
							cursor: pointer;
						}
					}
				}
			}

			.medar_full {
				float: left;
				width: 100%;

				h3 {}

				span.ingress {
					float: left;
					width: 100%;
				}

				.readMore {
					float: left;
					width: 100%;
					margin: 10px 0 0;

					a {
						font-size: 16px;
						margin: 0;
						padding: 10px 30px;
						border-radius: 4px;
						color: #fff;
						background: $color-bla;
						border-left: none;
						transition: all .2s;
						float: right;
						line-height: 1;

						&:hover {
							background: lighten($color-bla, 10%);
							text-decoration: none;
							cursor: pointer;
						}
					}
				}
			}

		}
	}
}


.extra_lankar {
	float: left;
	width: 100%;
	margin: 0 0 15px;

	ul {
		margin: 0;
		padding: 0;

		li {
			padding: 0;
			list-style: none;
			float: left;
			padding: 0 30px 0 0;
			width: 100%;
			margin: 0 0 7px;

			a {
				position: relative;
				color: $color-bla;
				font-size: 18px;

				&:before {
					float: left;
					width: 20px;
					height: 20px;
					background: url("../../assets/images/pil-blue.png") no-repeat center center;
					background-size: 16px;
					content: '';
					margin: 2px 8px 0 0;
				}
			}
		}
	}
}

//Ankarlänkar
.page_ankarlankar {
	float: left;
	width: 100%;
	margin: 0 0 15px;

	ul {
		margin: 0;
		padding: 0;

		li {
			padding: 0;
			list-style: none;
			float: left;
			padding: 0 30px 0 0;
			width: 100%;
			margin: 0 0 7px;

			a {
				position: relative;
				color: $color-bla;
				font-size: 18px;

				&:before {
					float: left;
					width: 20px;
					height: 20px;
					background: url("../../assets/images/pil-blue.png") no-repeat center center;
					background-size: 16px;
					content: '';
					margin: 4px 8px 0 0;
				}
			}
		}
	}
}

.page-content {
	float: left;
	width: 100%;
	margin-bottom: 30px;

	p {
		&:last-child {
			margin: 0;
		}
	}

	//Gällande föreskrifter i nummerordning
	.docs_numberordning {
		float: left;
		width: 100%;

		.menu_docs {
			float: left;
			width: 100%;

			.menu_docs_ul {
				margin: 0;
				padding: 0;
				float: left;
				width: 100%;
				border-bottom: solid 5px $color-da-blue;

				li {
					float: left;
					padding: 10px 15px;
					list-style: none;
					transition: all .2s;
					font-size: 15px;

					&.current {
						background: $color-da-blue;
						color: #fff;
					}

					&:hover {
						background: $color-da-blue;
						color: #fff;
						cursor: pointer;
					}

					&.docs_grundforfattning {
						float: left;
					}

					&.docs_all {
						float: right;
					}
				}
			}
		}

		.info {
			ul {
				margin: 0;
				padding: 0;

				.content {
					float: left;
					width: 100%;

					h2 {
						margin: 20px 0 10px 0;
						float: left;
						width: 100%;
						display: none;
					}

					li {
						float: left;
						width: 100%;
						list-style: none;
						padding: 10px 0;
						font-size: 16px;
						border-bottom: solid 1px #ccc;

						span {
							&.bet {
								float: left;
								width: 20%;
							}

							&.title {
								float: left;
								width: 80%;
								padding: 0 10px;
							}
						}

						&.andringsforfattning {
							display: none;
						}
					}

					&.first {
						h2 {
							display: block;
							padding: 10px 0 0;
						}
					}

					&:first-child {
						h2 {
							border-top: none;
						}
					}
				}
			}
		}
	}

	table {
		float: left;
		width: 100% !important;
		font-size: 16px;

		tbody {
			tr {
				td {
					padding: 5px 15px;
				}
			}
		}
	}



	//Intresseanmälan till teknisk bedömare
	#bli-teknisk-bedomare {
		float: left;
		width: 100%;
		margin: 0 0 20px;

		.top {
			.row {
				float: left;
				width: 100%;
				margin: 0 0 20px;
				position: relative;

				i {
					position: absolute;
					top: 18px;
					right: 10px;
					transform: scale(0);
					transition: transform .2s;

					&.fa-times {
						color: #CC002F;
					}

					&.fa-check {
						color: #00925C;
					}
				}

				span.text {
					float: left;
					width: 100%;
					margin: 0 0 10px;
					font-size: 16px;
				}

				label {
					display: none;
				}

				label.label {
					display: block;
					float: left;
					width: 100%;
					font-size: 15px;
					margin: 2px 0;
					padding: 2px 0;
				}

				input.inputbox {
					float: left;
					width: 100%;
				}

				textarea.textarea {
					float: left;
					width: 100%;
				}

				&.fname {
					width: 48%;
					float: left;
				}

				&.lname {
					width: 48%;
					float: right;
				}

				&.email {
					width: 48%;
					float: left;
				}

				&.phone {
					width: 48%;
					float: right;
				}

				&.analyserar_verksamhet {
					float: left;
					width: 100%;
					border-bottom: solid 1px #D0D0D0;
					padding: 0 0 15px;
				}

				&.verksamheten_utgors_av {
					float: left;
					width: 100%;
				}

				&.intresseorganisation {
					float: left;
					width: 100%;
					border-bottom: solid 1px #D0D0D0;
					padding: 0 0 15px;
				}

				&.ackrediteringssystemet {
					float: left;
					width: 100%;
					border-bottom: solid 1px #D0D0D0;
					padding: 0 0 15px;
				}

				&.ackrediteringsstandarder {}

				&.checkbox {}



				&:last-child {
					margin: 0;
				}

				.error~.fa-times {
					transform: scale(1);
				}

				.error~.fa-check {
					transform: scale(0);
				}

				.valid~.fa-times {
					transform: scale(0);
				}

				.valid~.fa-check {
					transform: scale(1);
				}
			}
		}

		.bottom {
			float: left;
			width: 100%;
			margin: 20px 0 0;

			.submit-booking {
				font-size: 16px;
				margin: 0;
				padding: 15px 30px;
				border-radius: 4px;
				color: #fff;
				background: $color-bla;
				border-left: none;
				transition: all .2s;

				&:hover {
					background: lighten($color-bla, 10%);
					text-decoration: none;
					cursor: pointer;
				}
				&:disabled{
					background-color: $color-dark-grey;
					&:hover {
						background: lighten($color-dark-grey, 10%);
						text-decoration: none;
						cursor: pointer;
					}
				}
			}

			span {
				float: left;
				width: 100%;
				font-size: 16px;
				margin: 15px 0 0;
			}
		}
	}

	// klagomål och synpunkter
	#klagomal_synpunkter {
		float: left;
		width: 100%;
		margin: 0 0 30px;
		padding: 0 0 30px;
		border-bottom: solid 1px #D0D0D0;

		.top {
			.row {
				float: left;
				width: 100%;
				margin: 0 0 20px;
				position: relative;

				i {
					position: absolute;
					top: 18px;
					right: 10px;
					transform: scale(0);
					transition: transform .2s;

					&.fa-times {
						color: #CC002F;
					}

					&.fa-check {
						color: #00925C;
					}
				}

				label {
					display: none;
				}

				input.inputbox {
					float: left;
					width: 100%;
				}

				textarea.textarea {
					float: left;
					width: 100%;
				}

				&.fname {
					width: 48%;
					float: left;
				}

				&.lname {
					width: 48%;
					float: right;
				}

				&.email {
					width: 48%;
					float: left;
				}

				&.phone {
					width: 48%;
					float: right;
				}

				&:last-child {
					margin: 0;
				}

				.error~.fa-times {
					transform: scale(1);
				}

				.error~.fa-check {
					transform: scale(0);
				}

				.valid~.fa-times {
					transform: scale(0);
				}

				.valid~.fa-check {
					transform: scale(1);
				}
			}
		}

		.bottom {
			float: left;
			width: 100%;
			margin: 20px 0 0;

			.submit-booking {
				font-size: 16px;
				margin: 0;
				padding: 15px 30px;
				border-radius: 4px;
				color: #fff;
				background: $color-bla;
				border-left: none;
				transition: all .2s;

				&:hover {
					background: lighten($color-bla, 10%);
					text-decoration: none;
					cursor: pointer;
				}
				&:disabled{
					background-color: $color-dark-grey;
					&:hover {
						background: lighten($color-dark-grey, 10%);
						text-decoration: none;
						cursor: pointer;
					}
				}
			}

			span {
				float: left;
				width: 100%;
				font-size: 16px;
				margin: 15px 0 0;
			}
		}
	}

	//Kurser
	#kurser_section {
		float: left;
		width: 100%;
		margin: 30px 0 0;

		ul {
			margin: 0;
			padding: 0;

			li {
				list-style: none;
				padding: 0;
				width: 100%;
				float: left;
				margin: 0 0 15px;
				transition: all .2s;

				.rubrik {
					float: left;
					width: 100%;
					background: $color-knapp;
					padding: 13px 13px;
					margin: 0;

					h2 {
						font-size: 20px;
						font-weight: 400;
						margin: 0;
						position: relative;
						float: left;
						width: 100%;
						color: #fff;
												letter-spacing: normal;

						&:after {
							float: right;
							width: 100%;
							height: 20px;
							background: url("../../assets/images/pil-white.png") no-repeat center right;
							background-size: 20px;
							content: '';
						}
					}
				}

				&:last-child {
					margin: 0;
				}
			}
		}
	}

	#booking_course {
		float: left;
		width: 100%;
		margin: 30px 0 0;
		padding: 30px;
		background: $color-light;

		.messange_content {
			float: left;
			width: 100%;
			margin: 0 0 30px;

			.inner {
				float: left;
				width: 100%;
				padding: 20px;

				h2 {
					margin: 0 0 15px;
					letter-spacing: normal;
				}

				p {
					margin: 0 0 0px;
				}

				b {
					letter-spacing: normal;
				}

				&.failed {
					background-color: #FDD !important;
					border: solid 1px #CC002F !important;
				}

				&.success {
					background-color: #E5FFE5 !important;
					border: solid 1px #00925C !important;
				}

			}
		}

		h3 {
			margin: 0 0 10px;
			letter-spacing: normal;
		}

		.top {
			float: left;
			width: 100%;
			margin: 0 0 30px;

			.row {
				float: left;
				width: 100%;
				margin: 0 0 20px;
				position: relative;

				select {
					margin: 0;
					padding: 0;
					padding: 15px;
					float: left;
					width: 100%;
					font-size: 16px;
					border-radius: 4px;
					background: #fff;
					-webkit-appearance: none;

					option {
						padding: 0;
						list-style: none;
						float: left;
						padding: 0 30px 0 0;
						width: 100%;
						margin: 0 0 7px;
					}

					&#swedac_education {
						margin: 0 0 15px;
					}
				}

				.kurstillfalle_section {
					display: none;
					float: left;
					width: 100%;

					select {
						margin: 0;
						padding: 0;
						padding: 15px;
						float: left;
						width: 100%;
						font-size: 16px;
						border-radius: 4px;
						background: #fff;
						-webkit-appearance: none;

						option {
							padding: 0;
							list-style: none;
							float: left;
							padding: 0 30px 0 0;
							width: 100%;
							margin: 0 0 7px;
						}
					}
				}


				i {
					position: absolute;
					top: 18px;
					right: 65px;
					transform: scale(0);
					transition: transform .2s;

					&.fa-times {
						color: #CC002F;
					}

					&.fa-check {
						color: #00925C;
					}
				}

				&.education {}

				.error~.fa-times {
					transform: scale(1);
				}

				.error~.fa-check {
					transform: scale(0);
				}

				.valid~.fa-times {
					transform: scale(0);
				}

				.valid~.fa-check {
					transform: scale(1);
				}
			}
		}

		.middle {
			float: left;
			width: 100%;
			margin: 0 0 30px;

			.row {
				float: left;
				width: 100%;
				margin: 0 0 20px;
				position: relative;

				i {
					position: absolute;
					top: 18px;
					right: 10px;
					transform: scale(0);
					transition: transform .2s;

					&.fa-times {
						color: #CC002F;
					}

					&.fa-check {
						color: #00925C;
					}
				}

				label {
					display: none;
				}

				input {
					float: left;
					width: 100%;
				}

				&.fname {
					width: 48%;
					float: left;
				}

				&.lname {
					width: 48%;
					float: right;
				}

				&:last-child {
					margin: 0;
				}

				.error~.fa-times {
					transform: scale(1);
				}

				.error~.fa-check {
					transform: scale(0);
				}

				.valid~.fa-times {
					transform: scale(0);
				}

				.valid~.fa-check {
					transform: scale(1);
				}
			}
		}

		.middle_extra {
			float: left;
			width: 100%;
			margin: 0 0 20px;

			.create_one_more {
				float: left;
				font-size: 16px;
				margin: 0;
				color: $color-bla;
				transition: all .2s;

				&:hover {
					text-decoration: none;
					cursor: pointer;
				}
			}

			#extra_section {
				float: left;
				width: 100%;

				.field_section {
					float: left;
					width: 100%;
					padding: 15px 0;
					margin: 15px 0;
					border-bottom: solid 1px #DEDEDE;
					position: relative;

					.remove_item_button {
						position: absolute;
						top: -15px;
						right: 0;

						.fa-times {
							color: #CC002F;
						}

						&:hover {
							cursor: pointer;
						}
					}

					.row {
						float: left;
						width: 100%;
						margin: 0 0 20px;
						position: relative;

						i {
							position: absolute;
							top: 18px;
							right: 10px;
							transform: scale(0);
							transition: transform .2s;

							&.fa-times {
								color: #CC002F;
							}

							&.fa-check {
								color: #00925C;
							}
						}

						label {
							display: none;
						}

						input {
							float: left;
							width: 100%;
						}

						&.fname {
							width: 48%;
							float: left;
						}

						&.lname {
							width: 48%;
							float: right;
						}

						&:last-child {
							margin: 0;
						}

						.error~.fa-times {
							transform: scale(1);
						}

						.error~.fa-check {
							transform: scale(0);
						}

						.valid~.fa-times {
							transform: scale(0);
						}

						.valid~.fa-check {
							transform: scale(1);
						}
					}

					&:last-child {
						border-bottom: none;
					}
				}
			}

			.no_deltagare {
				float: left;
				width: 100%;
				font-size: 16px;
				margin: 0 0 10px;
			}

		}

		.bottom {
			float: left;
			width: 100%;
			margin: 20px 0 0 0;

			.left {
				float: left;
				width: 100%;
				margin: 0 0 25px;

				.submit-booking {
					font-size: 16px;
					margin: 0;
					padding: 15px 30px;
					border-radius: 4px;
					color: #fff;
					background: $color-bla;
					border-left: none;
					transition: all .2s;

					&:hover {
						background: lighten($color-bla, 10%);
						text-decoration: none;
						cursor: pointer;
					}
					&:disabled{
						background-color: $color-dark-grey;
						&:hover {
							background: lighten($color-dark-grey, 10%);
							text-decoration: none;
							cursor: pointer;
						}
					}
				}
			}

			.right {
				float: left;
				width: 100%;
				padding: 0 0 10px;

				a {
					float: left;
					width: 100%;
					text-align: left;
					font-size: 16px;
				}
			}

			span {
				float: left;
				width: 100%;
				font-size: 16px;
			}
		}
	}

	// Bedömarkurs
	#reg_bedomarutbildning {
		float: left;
		width: 100%;
		display: none;

		#bedomarutbildning_course {
			float: left;
			width: 100%;
			margin: 0;
			padding: 30px;
			background: $color-light;

			h3 {
				margin: 0 0 10px;
				letter-spacing: normal;
			}

			.top {
				float: left;
				width: 100%;
				margin: 0 0 30px;

				.row {
					float: left;
					width: 100%;
					margin: 0 0 0;
					position: relative;

					select {
						margin: 0;
						padding: 15px;
						float: left;
						width: 100%;
						font-size: 16px;
						border-radius: 4px;
						background: #fff;
						-webkit-appearance: none;

						option {
							padding: 0;
							list-style: none;
							float: left;
							padding: 0 30px 0 0;
							width: 100%;
							margin: 0 0 7px;
						}
					}

					i {
						position: absolute;
						top: 18px;
						right: 65px;
						transform: scale(0);
						transition: transform .2s;

						&.fa-times {
							color: #CC002F;
						}

						&.fa-check {
							color: #00925C;
						}
					}
				}
			}

			.middle {
				float: left;
				width: 100%;
				margin: 0 0 30px;

				.row {
					float: left;
					width: 100%;
					margin: 0 0 20px;
					position: relative;

					&.left {
						width: 48%;
						float: left;
					}

					&.right {
						width: 48%;
						float: right;
					}

					&.full {
						width: 100%;
						float: left;
					}

					i {
						position: absolute;
						top: 18px;
						right: 10px;
						transform: scale(0);
						transition: transform .2s;

						&.fa-times {
							color: #CC002F;
						}

						&.fa-check {
							color: #00925C;
						}
					}

					label.error {
						display: none;
					}

					label {
						float: left;
						width: 100%;
						margin: 0 0 4px;
					}

					span {
						float: left;
						width: 100%;
						margin: 5px 0;

						input {
							float: left;
							width: auto;
							margin: 5px 5px 0 0;
						}
					}

					input {
						float: left;
						width: 100%;
					}

					textarea {
						width: 100%;
						float: left;
					}

					&:last-child {
						margin: 0;
					}
				}
			}

			.bottom {
				.submit-booking {
					font-size: 16px;
					margin: 0;
					padding: 15px 30px;
					border-radius: 4px;
					color: #fff;
					background: $color-bla;
					border-left: none;
					transition: all .2s;

					&:hover {
						background: lighten($color-bla, 10%);
						text-decoration: none;
						cursor: pointer;
					}
					&:disabled{
						background-color: $color-dark-grey;
						&:hover {
							background: lighten($color-dark-grey, 10%);
							text-decoration: none;
							cursor: pointer;
						}
					}
				}

				span {
					float: left;
					width: 100%;
					margin: 15px 0 0;
				}
			}
		}
	}

	//Snabbval
	#page_snabbval {
		float: left;
		width: 100%;
		margin: 30px 0 0;

		ul {
			margin: 0;
			padding: 0;

			li {
				list-style: none;
				padding: 0;
				width: 100%;
				float: left;
				margin: 0 0 30px;
				transition: all .2s;

				a {
					float: left;
					width: 100%;

					.rubrik {
						float: left;
						width: 100%;
						background: $color-dark-grey;
						padding: 13px 13px;
						margin: 0;

						h2 {
							font-size: 20px;
							font-weight: 400;
							margin: 0;
							position: relative;
							float: left;
							width: 100%;
							color: #fff;
														letter-spacing: normal;

							&:after {
								float: right;
								width: 100%;
								height: 20px;
								background: url("../../assets/images/pil-white.png") no-repeat center right;
								background-size: 20px;
								content: '';
							}
						}
					}
				}

				&.last-child {
					margin: 0;
				}
			}
		}
	}

	//Kontaktperson
	.kontaktperson_section {
		float: left;
		width: 100%;
		background: $color-light;
		padding: 30px 40px;
		margin: 40px 0 0;

		h2 {
			font-weight: 600;
			margin: 0;
			position: relative;
			float: left;
						letter-spacing: normal;
			font-size: 22px;
			color: $color-da-blue;
		}

		ul {
			margin: 0;
			padding: 0;

			li {
				float: left;
				width: 100%;
				list-style: none;

				.left {
					float: left;
					width: 100%;
					max-width: 80px;
					padding: 0 15px 0 0;
					// img {-webkit-filter: grayscale(100%);}
				}

				.right {
					float: left;
					width: 80%;
					padding: 5px 0 0 0;

					span {
						float: left;
						width: 100%;
						font-size: 16px;
						margin: 1px 0;

						&.namn {}

						&.telefon {}

						&.epost {}
					}
				}
			}
		}
	}

	//Dokument
	.dokument_section {
		float: left;
		width: 100%;
		background: $color-light;
		padding: 30px 40px;
		margin: 30px 0 0 0;

		h2 {
			font-weight: 600;
			margin: 0 0 10px;
			position: relative;
			float: left;
						letter-spacing: normal;
			font-size: 22px;
			color: $color-da-blue;
		}

		ul {
			margin: 0;
			padding: 0;

			li {
				float: left;
				width: 100%;
				list-style: none;
				padding: 10px 0;
				border-top: solid 1px #ccc;

				.left {
					float: left;
					width: 100%;
					max-width: 50px;
				}

				.right {
					float: left;
					width: 80%;
					padding: 0 0 0 15px;
					margin: -5px 0 0 0;

					span {
						float: left;
						width: 100%;
						font-size: 16px;
						margin: 0;
						line-height: 25px;

						&.file {}

						&.file_name {
							text-transform: lowercase;
							color: $color-text-darker;
						}

						&.file_type_size {
							text-transform: uppercase;
							color: $color-text-darker;
						}
					}
				}

				&:first-child {
					border-top: none;
					padding-top: 0;
				}
			}
		}
	}

	//Pressmeddelande
	.rss_press {
		float: left;
		width: 100%;
		margin: 20px 0 0 0;

		b {
			float: left;
			width: 100%;
			border-bottom: solid 1px #ccc;
			margin: 0 0 20px;
			padding: 0 0 10px;
		}

		.item {
			padding: 15px 0;

			h2 {
				letter-spacing: normal;
				font-size: 20px;
				font-weight: 400;
				margin: 0 0 10px;
				position: relative;
				float: left;
				width: 100%;
				color: #fff;
							}

			.description {
				p {
					font-size: 16px;
					line-height: 24px;
				}
			}
		}
	}

	//Ledning
	.ledning_page {
		float: left;
		width: 100%;

		.sektion {
			float: left;
			width: 100%;
			margin: 0 0 15px;

			h3 {
				letter-spacing: normal;
				margin: 0 0 15px;
			}

			ul {
				float: left;
				width: 100%;
				margin: 0;

				li {
					float: left;
					width: 47%;
					list-style: none;
					padding: 0;
					margin: 0 0 10px;

					&:nth-child(odd) {
						@media only screen and (min-width: 737px) {
							clear: both;
						}
					}

					.profilbild {
						float: left;
						width: 100%;
						min-height: 250px;
						margin: 0 0 5px;

						img {
							width: 100%;
							max-width: 250px;
							height: auto;
						}
					}

					.roll {
						float: left;
						width: 100%;
						line-height: 25px;
					}

					.namn {
						float: left;
						width: 100%;
						font-weight: bold;
					}

					&:nth-child(even) {
						width: 46%;
						float: right;
					}
				}
			}
		}
	}

	//Kontaktuppgifter
	.kontaktuppgifter_kontor {
		float: left;
		width: 100%;
		margin: 15px 0 10px;

		ul {
			margin: 0;

			li {
				float: left;
				list-style: none;
				padding: 0;

				.google_map {
					float: left;
					width: 100%;
					margin: 0 0 1rem;

					img {
						max-width: 100%;
						max-height: 100%;
					}
				}

				h2 {
					float: left;
					width: 100%;
					margin: 25px 0 15px;
					letter-spacing: 0.8px;
				}

				.adress {
					float: left;
					width: 100%;
					margin: 0 0 15px;

				}

				&:nth-child(even) {
					width: 46%;
					float: right;
				}
			}
		}
	}
}

//Ämnesområden
#snabbval_amnesomrodan {
	float: left;
	width: 100%;
	margin: 30px 0 0;

	#grid[data-columns]::before {
		content: '3 .column.size-1of3';
		position: absolute;
		top: -9999px;
		left: -9999px;
	}

	.column {
		float: left;
	}

	.size-1of3 {
		width: 33.333%;
		max-width: 320px;

		&:nth-child(2) {
			margin: 0 40px
		}
	}

	.category_section {
		padding: 0;
		width: 100%;
		float: left;
		margin: 0 0 40px;
		transition: all .2s;

		&.first {
			.head {
				background: $color-bla;

				h2 {
					letter-spacing: normal;
				}
			}

			.content {
				span {
					font-size: 16px;
										margin: 0 0 25px;
					float: left;
					width: 100%;
				}

				form {
					float: left;
					width: 100%;
					position: relative;
					margin-bottom: 1rem;

					.loading-icon {
						float: left;
						width: 100%;
						text-align: center;
						position: absolute;
						top: 0;
						left: 0;
						padding: 10px 0;
						display: none;

						.fa {
							font-size: 30px;
							color: $color-bla;
						}
					}

					select {
						margin: 0 0 20px;
					}
				}

				select {
					transition: all .2s;
					font-size: 16px;
										margin: 0;
					padding: 15px 50px 15px 15px;
					float: left;
					width: 100%;
					font-size: 16px;
					border-radius: 4px;
					background: #fff;
					-webkit-appearance: none;
					
					&:focus {

						outline: 1px dashed #000;
						outline-offset: 2px;
	
					}

					option {
						padding: 0;
						list-style: none;
						float: left;
						padding: 0 30px 0 0;
						width: 100%;
						margin: 0 0 7px;
					}

					&.fadeOut {
						opacity: .4;
					}
				}



			}
		}


		.head {
			float: left;
			width: 100%;
			background: $color-dark-grey;
			padding: 13px 13px 14px;
			margin: 0;

			h2 {
				letter-spacing: normal;
				font-size: 20px;
				font-weight: 400;
				margin: 0;
				position: relative;
				float: left;
				width: 100%;
				color: #fff;
							}
		}

		.content {
			float: left;
			width: 100%;
			min-height: 350px;
			background: $color-light;
			padding: 15px 20px;

			ul {
				float: left;
				width: 100%;
				margin: 0;

				li {
					padding: 1px 0;
					animation-name: slideUp;
					animation-duration: .8s;
					animation-fill-mode: forwards;
					animation-timing-function: ease;
					display: none;
					list-style: none;
					float: left;
					width: 100%;
					position: relative;

					a {
						color: $color-bla;
						font-size: 16px;
											}

					&:before {
						content: "";
						height: 20px;
						width: 20px;
						background: url('../../assets/images/pil-blue.png') no-repeat center center;
						background-size: 16px;
						margin: 4px 8px 4px 0;
						float: left;
						color: $color-bla;
					}
				}

				.show_more {
					display: none;
					position: relative;
					float: left;
					width: 100%;
					padding: 20px 0 10px;
					color: $color-bla;
					font-size: 16px;

					&:before {
						content: "";
						float: left;
						height: 20px;
						width: 20px;
						background: url('../../assets/images/plus-blue.png') no-repeat center center;
						background-size: 16px;
						margin: 1px 8px 0 0;
					}

					&.hide {
						display: none;
					}

					&.show {
						display: block;
					}

					&:hover {
						cursor: pointer;
					}
				}

				.show_less {
					position: relative;
					display: none;
					float: left;
					width: 100%;
					padding: 20px 0 10px;
					color: $color-bla;
					font-size: 16px;

					&:before {
						content: "";
						float: left;
						height: 20px;
						width: 20px;
						background: url('../../assets/images/minus-blue.png') no-repeat center center;
						background-size: 16px;
						margin: 1px 8px 0 0;
					}

					&.hide {}

					&:hover {
						cursor: pointer;
					}
				}
			}
		}

		&.last {
			.head {
				background: $color-bla;

				h2 {
					letter-spacing: normal;
				}
			}

			.content {
				ul {
					li {
						display: none;
					}
				}
			}
		}
	}
	.submit-button {

		display: inline-block;
		background-color: #005cb9;
		color: #fff;
		padding: .5rem 2.5rem .5rem 2.5rem;
		position: relative;
		margin: auto 0;

		&:focus {

			outline: 1px dashed #000 !important;
			outline-offset: 2px !important;

		}

	}

	#all_kontrollformer_form, #all_amnesomraden_form {
		select {
			option:focus {
				background-color: #005cb9;
			}
		}
	}
}

.sidebarIcon {
	height: 32px;
}

//Nyheter
#page-news-content {
	ul {
		margin: 0;
		padding: 0;

		li {
			list-style: none;
			padding: 0;
			float: left;
			width: 100%;
			border-bottom: solid 1px #cccccc;
			padding: 25px 0 20px;
			animation-name: slideUp;
			animation-duration: .5s;
			animation-fill-mode: forwards;
			animation-timing-function: ease;

			.left {
				float: left;
				width: 70%;

				.meta {
					float: left;
					width: 100%;

					.updated {
						text-transform: lowercase;
						font-size: 14px;
						color: $color-text-darker;
						float: left;
						line-height: 9px;
						margin: 0 0 10px;
					}
				}

				h2 {
										letter-spacing: normal;
					font-size: 20px;
				}

				.excerpt {
					line-height: 22px;
					font-size: 16px;
				}
			}

			.right {
				float: right;
				width: 15%;

				a {
					float: left;

					img {}
				}
			}

			&:last-child {
				border-bottom: none;
			}
		}
	}

	.search_result_buttons {
		float: left;
		width: 100%;
		margin: 15px 0;

		span {
			line-height: 17px;
			font-size: 15px;
			float: left;
			margin: 0 15px 0 0;
			border-radius: 4px;
			padding: 15px 20px;
			text-align: center;
			background: $color-bla;
			color: #fff;
			transition: all .2s;

			&.visa_fler_traffar {
				&.disabled {
					background: rgba(0, 112, 168, 0.75);
					pointer-events: none;
				}
			}

			&:hover {
				background: lighten($color-bla, 10%);
				text-decoration: none;
				cursor: pointer;
			}
		}
	}
}


//Swedac Magasin (ny kod 2020 av Cybercom)
#page-magasin-content {
	.row {
		.article {


			&:nth-child(5n+1) {
				grid-row-start: span 3;
			}

			>.content {
				position: relative;
				background-color: red;

				>.post-image {
					height: 80%;
					width: auto;
				}
			}

			>.cover {}
		}
	}
}

#swedac_magasin_latest {
	float: left;
	width: 100%;
	margin: 40px 0 0 0;

	.left {
		float: left;
		width: 20%;
		max-width: 190px;

		.omslag {
			float: left;
			width: 100%;

			img {
				width: 100%;
				height: auto;
			}
		}
	}

	.right {
		float: left;
		width: 100%;
		max-width: 850px;

		.nuvarande_info {
			float: left;
			width: 100%;
			padding: 25px 30px;
			background: $color-light;

			h2 {
				color: $color-da-blue;
				margin: 0 0 10px;
				font-size: 25px;
			}

			span {
				display: none;
				font-size: 13px;
				color: $color-text;
			}

			ul {
				margin: 0;
				padding: 0;

				li {
					float: left;
					padding: 0;
					list-style: none;

					a {
						color: $color-bla;
						position: relative;
						padding: 0 15px 0 0;
						font-size: 16px;

						&:before {
							float: left;
							width: 20px;
							height: 20px;
							background: url("../../assets/images/pil-blue.png") no-repeat center center;
							background-size: 16px;
							content: '';
							margin: 4px 8px 0 0;
						}
					}
				}
			}
		}

		.prenumerera_magasin {
			float: left;
			width: 100%;
			padding: 10px 30px 25px;
			background: $color-light;

			h2 {
				color: $color-da-blue;
				margin: 0 0 10px;
				font-size: 25px;
			}

			span {
				display: none;
				font-size: 13px;
				color: $color-text;
			}

			.prenumerera_magasin_form {
				float: left;
				width: 100%;
				margin: 10px 0 0;

				form {
					.top {
						float: left;
						width: 100%;

						.row {
							float: left;
							position: relative;

							input {
								float: left;
								width: 100%;
							}

							&.namn {
								width: 25%;
							}

							&.postadress {
								width: 55%;
								margin: 0 2%;
							}

							i {
								position: absolute;
								top: 18px;
								right: 10px;
								transform: scale(0);
								transition: transform .2s;

								&.fa-times {
									color: #CC002F;
								}

								&.fa-check {
									color: #00925C;
								}
							}

							.error~.fa-times {
								transform: scale(1);
							}

							.error~.fa-check {
								transform: scale(0);
							}

							.valid~.fa-times {
								transform: scale(0);
							}

							.valid~.fa-check {
								transform: scale(1);
							}
						}

						.send {
							float: left;
							width: 15%;

							.submit {
								width: 100%;
								font-size: 16px;
								text-align: center;
								margin: 0;
								padding: 15px 0;
								border-radius: 4px;
								color: #fff;
								background: $color-bla;
								border-left: none;
								transition: all .2s;

								&:hover {
									background: lighten($color-bla, 10%);
									text-decoration: none;
									cursor: pointer;
								}
							}
						}
					}
				}
			}

			ul {
				margin: 0;
				padding: 0;

				li {
					float: left;
					padding: 0;
					list-style: none;

					a {
						color: $color-bla;
						position: relative;
						padding: 0 15px 0 0;
						font-size: 16px;

						&:before {
							float: left;
							width: 20px;
							height: 20px;
							background: url("../../assets/images/pil-blue.png") no-repeat center center;
							background-size: 16px;
							content: '';
							margin: 4px 8px 0 0;
						}
					}
				}
			}
		}
	}


}

//Category sida
.single_posts {
	float: left;
	width: 100%;

	ul {
		margin: 0;
		padding: 0;

		li {
			list-style: none;
			padding: 0;
			float: left;
			width: 100%;
			border-bottom: solid 1px #cccccc;
			padding: 30px 0;

			.left {
				float: left;
				width: 100%;

				.meta {
					float: left;
					width: 100%;

					.updated {
						text-transform: lowercase;
						font-size: 14px;
						color: $color-text;
						float: left;
						line-height: 9px;
						margin: 0 0 10px;
					}
				}

				h2 {
					letter-spacing: normal;
				}

				.excerpt {
					line-height: 23px;
				}
			}

			&:first-child {
				padding: 0 0 30px;
			}
		}
	}
}

//GD Bloggen
#page-bloggen-content {
	.blogg-post {
		float: left;
		width: 100%;
		padding: 0 0 70px 0;

		.post-header {
			h2 {
				font-size: 40px;
				line-height: 45px;
				margin: 0 0 15px;
				letter-spacing: 1.2px;

				a {
					color: #222;
				}
			}

			.meta-info {
				font-size: small;

				.updated {
					text-transform: lowercase;
				}

				.author {}
			}
		}

		.post-content {
			p {
				&:last-child {
					margin: 0;
				}
			}
		}

		.post-footer {
			float: left;
			width: 100%;
			margin: 20px 0 0 0;

		}

		&:last-child {
			padding: 0;
		}
	}
}

// Sidmall Certifieringsorgan
.page-content .certification-container {
	width: 100%;
	float: left;

	ul,
	ol {
		margin: 0;

		li {
			list-style: none;
		}
	}

	ol li {
		width: 100%;
		float: left;

		h2 {
			cursor: pointer;
			float: left;
			font-size: 20px;
			margin-top: 10px;
			transition: all 0.25s ease-in;
			width: inherit;

			i.fa {
				color: $color-bla;
				display: block;
				font-family: $FA;
				font-style: normal;
				float: left;
				font-size: 26px;
				margin-right: 8px;
				margin-top: 2px;
				transition: all 0.15s ease-in;
				transform-origin: center center;
			}

			span {
				float: left;
				max-width: 91%;
			}

			&.active,
			&:hover {
				span {
					text-decoration: underline;
				}

				i.fa {
					color: lighten($color-bla, 5%);
				}
			}

			&.active i.fa {
				transform: rotate(90deg);
			}
		}

		ul {
			display: none;
			float: left;
			width: 100%;
			padding: 0 0 0 24px;

			li {
				float: left;
				font-style: italic;
				font-size: 16px;
				margin: 10px 0 0;
				padding: 0;
			}
		}

		&:first-child h2 {
			margin-top: 0;
		}
	}
}



footer.post-footer {
	position: relative;
}

.nf-form-content input[type=submit]:hover {

	color: #4F4F4F !important;

}