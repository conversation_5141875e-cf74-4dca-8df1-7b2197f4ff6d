#single_section_post {
	.main {
		float: left;
		width: 100%;
		max-width: 700px;
		padding: 0 60px 0 0;
		margin: 0 20px 0 0;
		border-right: solid 1px #DEDEDE;

		.single-post {
			.post-header {
				h1 {
					font-size: 50px;
					line-height: 55px;
					margin: 0 0 15px;
					letter-spacing: 1.2px;
				}

				.meta-info {
					font-size: small;

					.updated {}

					.author {}
				}
			}

			.post-content {
				p {
					&:last-child {
						margin: 0;
					}
				}
			}
		}

		//Kurser
		.kurs-post {
			.post-header {
				h1 {
					font-size: 50px;
					line-height: 55px;
					margin: 0 0 15px;
					letter-spacing: 1.2px;
				}

				.meta-info {
					font-size: small;

					.updated {}

					.author {}
				}
			}

			.post-content {
				p {
					&:last-child {
						margin: 0;
					}
				}
			}

			#reg_course {
				float: left;
				width: 100%;
				display: none;

				#booking_course {
					float: left;
					width: 100%;
					margin: 0;
					padding: 30px;
					background: $color-light;

					h3 {
						margin: 0 0 10px;
						letter-spacing: normal;
					}

					.top {
						float: left;
						width: 100%;
						margin: 0 0 30px;

						.row {
							float: left;
							width: 100%;
							margin: 0 0 0;
							position: relative;

							select {
								margin: 0;
								padding: 15px;
								float: left;
								width: 100%;
								font-size: 16px;
								border-radius: 4px;
								background: #fff;
								-webkit-appearance: none;

								option {
									padding: 0;
									list-style: none;
									float: left;
									padding: 0 30px 0 0;
									width: 100%;
									margin: 0 0 7px;
								}
							}

							i {
								position: absolute;
								top: 18px;
								right: 65px;
								transform: scale(0);
								transition: transform .2s;

								&.fa-times {
									color: #CC002F;
								}

								&.fa-check {
									color: #00925C;
								}
							}

							&.education {}
						}
					}

					.middle {
						float: left;
						width: 100%;
						margin: 0 0 30px;

						.row {
							float: left;
							width: 100%;
							margin: 0 0 20px;
							position: relative;

							i {
								position: absolute;
								top: 18px;
								right: 10px;
								transform: scale(0);
								transition: transform .2s;

								&.fa-times {
									color: #CC002F;
								}

								&.fa-check {
									color: #00925C;
								}
							}

							label.error {
								display: none;
							}

							label {
								float: left;
								width: 100%;
								margin: 0 0 4px;
							}

							textarea {
								width: 100%;
								float: left;
							}

							input {
								float: left;
								width: 100%;
							}

							&.fname {
								width: 48%;
								float: left;
							}

							&.lname {
								width: 48%;
								float: right;
							}

							&:last-child {
								margin: 0;
							}
						}
					}

					.middle_extra {
						float: left;
						width: 100%;
						margin: 0 0 30px;

						.create_one_more {
							float: left;
							font-size: 16px;
							margin: 0;
							padding: 15px 30px;
							border-radius: 4px;
							color: #fff;
							background: $color-bla;
							border-left: none;
							transition: all .2s;

							&:hover {
								background: lighten($color-bla, 10%);
								text-decoration: none;
								cursor: pointer;
							}
						}

						#extra_section {
							float: left;
							width: 100%;

							.field_section {
								float: left;
								width: 100%;
								padding: 15px 0;
								margin: 15px 0;
								border-bottom: solid 1px #DEDEDE;
								position: relative;

								.remove_item_button {
									position: absolute;
									top: -15px;
									right: 0;

									.fa-times {
										color: #CC002F;
									}
								}

								.row {
									float: left;
									width: 100%;
									margin: 0 0 20px;
									position: relative;

									i {
										position: absolute;
										top: 18px;
										right: 10px;
										transform: scale(0);
										transition: transform .2s;

										&.fa-times {
											color: #CC002F;
										}

										&.fa-check {
											color: #00925C;
										}
									}

									label {
										display: none;
									}

									input {
										float: left;
										width: 100%;
									}

									&.fname {
										width: 48%;
										float: left;
									}

									&.lname {
										width: 48%;
										float: right;
									}

									&:last-child {
										margin: 0;
									}
								}

								&:last-child {
									border-bottom: none;
								}
							}
						}

						.no_deltagare {
							float: left;
							width: 100%;
							font-size: 16px;
							margin: 0 0 10px;
						}

					}

					.bottom {
						.left {
							float: left;
							width: 50%;
							margin: 0 0 30px;

							.submit-booking {
								font-size: 16px;
								margin: 0;
								padding: 15px 30px;
								border-radius: 4px;
								color: #fff;
								background: $color-bla;
								border-left: none;
								transition: all .2s;

								&:hover {
									background: lighten($color-bla, 10%);
									text-decoration: none;
									cursor: pointer;
								}
								&:disabled{
									background-color: $color-dark-grey;
									&:hover {
										background: lighten($color-dark-grey, 10%);
										text-decoration: none;
										cursor: pointer;
									}
								}
							}
						}

						.right {
							float: left;
							width: 50%;
							padding: 15px 0;

							a {
								float: left;
								width: 100%;
								text-align: right;
							}
						}

						span {
							float: left;
							width: 100%;
						}
					}
				}
			}

			.post-footer {}
		}
	}

	.sidebar {
		float: right;
		width: 100%;
		max-width: 320px;
		padding: 0 0 0 40px;

	}
}

//Dokument
#single_section_post_dokument {
	float: left;
	width: 100%;

	.main {
		float: left;
		width: 100%;
		padding: 0;
		margin: 0;

		.single-post-dokument {
			.post-header {
				float: left;
				width: 100%;
				background: $color-light;
				padding: 40px 0;
				margin: 0;

				h1 {
					font-size: 50px;
					line-height: 55px;
					margin: 0 0 15px;
					letter-spacing: 1.2px;
				}

				.dokument_section {
					float: left;
					width: 100%;

					ul {
						margin: 0;
						padding: 0;

						li {
							float: left;
							width: 100%;
							list-style: none;
							padding: 15px 0;
							border-top: solid 1px #ccc;

							.left {
								float: left;
								width: 100%;
								max-width: 65px;
							}

							.right {
								float: left;
								width: 80%;
								padding: 0 0 0 15px;

								span {
									float: left;
									width: 100%;
									font-size: 16px;

									&.file {
										h3 {
											margin: 0;
										}
									}

									&.file_name {
										color: $color-text-darker;
									}

									&.file_type_size {
										text-transform: uppercase;
										color: $color-text-darker;
									}
								}
							}

							&:first-child {
								border-top: none;
								padding-top: 0;
							}
						}
					}
				}
			}

			.post-content {
				float: left;
				width: 100%;
				max-width: 700px;
				margin: 30px 0;

				p {
					&:last-child {
						margin: 0;
					}
				}

				h3 {
					float: left;
					width: 100%;
					border-bottom: solid 1px #DADADA;
					padding: 0 0 10px;
					margin: 0 0 10px;
					letter-spacing: normal;
				}

				.allman_info {
					float: left;
					width: 100%;
					margin: 0 0 30px;

					.table_wrapper {
						float: left;
						width: 100%;
						overflow-x: scroll;
					}

					table {
						float: left;
						width: 100%;

						tbody {
							tr {
								float: left;
								width: 100%;
								margin: 4px 0;

								td {
									&.a {
										width: 33%;
										float: left;
									}

									&.b {
										width: 66%;
										float: left;

										span {
											float: left;
											padding: 0 10px 0 0;
											margin: 0 0 3px;
										}
									}
								}

								&.doc_description {
									margin: 0 0 20px;
								}
							}
						}
					}
				}

				.relateade_omraden {
					float: left;
					width: 100%;
					margin: 0 0 15px;

					h3 {
						float: left;
						width: 100%;
						border-bottom: solid 1px #DADADA;
						padding: 0 0 10px;
						margin: 0 0 10px;
						letter-spacing: normal;
					}

					ul {
						margin: 0;
						padding: 0;

						li {
							padding: 0;
							list-style: none;
							float: left;
							padding: 0 30px 0 0;
							width: 100%;
							margin: 0 0 7px;

							a {
								position: relative;
								color: $color-bla;
								font-size: 18px;

								&:before {
									float: left;
									width: 20px;
									height: 20px;
									background: url("../../assets/images/pil-blue.png") no-repeat center center;
									background-size: 16px;
									content: '';
									margin: 2px 8px 0 0;
								}
							}
						}
					}
				}

				.post-footer {
					float: left;
					width: 100%;
					border-top: solid 1px #DADADA;
					padding: 10px 0 0;
					margin: 10px 0 0;

				}
			}
			.old_certificates{
				float: left;
				display: flex;
				flex-wrap: wrap;
				width: 100%;
				margin: 0 0 30px;
				h3{
					float: left;
					width: 100%;
					border-bottom: solid 1px #dadada;
					padding: 0 0 10px;
					margin: 0 0 10px;
					letter-spacing: normal;
				}

				h1{
					width: 100%;
				}
				>div{
					width: 100%;
										overflow-x:auto;

					table{
						width: 100%;
						white-space: nowrap;
						border-collapse: separate;
						border-spacing: 0.5rem 1rem;
						thead{
						}
						tbody{
							tr{

								border-top: solid 1px #DADADA;
								td{
								}
							}
						}
					}
				}
			}
		}
	}
}

//Certifikat
#single_section_post_certifikat {
	float: left;
	width: 100%;

	.main {
		float: left;
		width: 100%;
		padding: 0;
		margin: 0;

		.single-post-certifikat {
			.post-header {
				float: left;
				width: 100%;
				background: $color-light;
				padding: 40px 0;
				margin: 0;

				h1 {
					font-size: 50px;
					line-height: 55px;
					margin: 0 0 15px;
					letter-spacing: 1.2px;
				}

				.certifikat_section {
					float: left;
					width: 100%;

					ul {
						margin: 0;
						padding: 0;

						li {
							float: left;
							width: 100%;
							list-style: none;
							padding: 15px 0;
							border-top: solid 1px #ccc;

							.left {
								float: left;
								width: 100%;
								max-width: 65px;
							}

							.right {
								float: left;
								width: 80%;
								padding: 0 0 0 15px;

								span {
									float: left;
									width: 100%;
									font-size: 16px;

									&.file {
										h3 {
											margin: 0;
										}
									}

									&.file_name {
										color: $color-text-darker;
									}

									&.file_type_size {
										text-transform: uppercase;
										color: $color-text-darker;
									}
								}
							}

							&:first-child {
								border-top: none;
								padding-top: 0;
							}
						}
					}
				}
			}

			.post-content {
				float: left;
				width: 100%;
				max-width: 700px;
				margin: 30px 0;

				p {
					&:last-child {
						margin: 0;
					}
				}

				h3 {
					float: left;
					width: 100%;
					border-bottom: solid 1px #DADADA;
					padding: 0 0 10px;
					margin: 0 0 10px;
					letter-spacing: normal;
				}

				.allman_info {
					float: left;
					width: 100%;
					margin: 0 0 30px;

					.table_wrapper {
						float: left;
						width: 100%;
						overflow-x: scroll;
					}

					table {
						float: left;
						width: 100%;

						tbody {
							tr {
								float: left;
								width: 100%;
								margin: 4px 0;

								td {
									&.a {
										width: 33%;
										float: left;
									}

									&.b {
										width: 66%;
										float: left;

										span {
											float: left;
											padding: 0 10px 0 0;
											margin: 0 0 3px;
										}
									}
								}

								&.doc_description {
									margin: 0 0 20px;
								}
							}
						}
					}
				}

				.relateade_omraden {
					float: left;
					width: 100%;
					margin: 0 0 15px;

					h3 {
						float: left;
						width: 100%;
						border-bottom: solid 1px #DADADA;
						padding: 0 0 10px;
						margin: 0 0 10px;
						letter-spacing: normal;
					}

					ul {
						margin: 0;
						padding: 0;

						li {
							padding: 0;
							list-style: none;
							float: left;
							padding: 0 30px 0 0;
							width: 100%;
							margin: 0 0 7px;

							a {
								position: relative;
								color: $color-bla;
								font-size: 18px;

								&:before {
									float: left;
									width: 20px;
									height: 20px;
									background: url("../../assets/images/pil-blue.png") no-repeat center center;
									background-size: 16px;
									content: '';
									margin: 2px 8px 0 0;
								}
							}
						}
					}
				}

				.post-footer {
					float: left;
					width: 100%;
					border-top: solid 1px #DADADA;
					padding: 10px 0 0;
					margin: 10px 0 0;

				}
			}
		}
	}
}

//Ämnesområde (Single)
.single_section_post_amnesomrade {
	.main {
		float: left;
		width: 100%;
		max-width: 700px;
		padding: 0 60px 0 0;
		margin: 0 20px 0 0;
		border-right: solid 1px #DEDEDE;

		.document_crm {
			display: none;
		}

		.relaterad_information {
			display: none;
		}

		.single-post {
			&.amnesomraden_categories-ledningssystem {
				#hidden_section {
					display: block;
				}
			}

			.post-header {
				h1 {
					font-size: 50px;
					line-height: 45px;
					margin: 0 0 15px;
					letter-spacing: 1.2px;
				}

				.meta-info {
					font-size: small;

					.updated {}

					.author {}
				}
			}

			//Ledningssystem
			.ledningssystem-section {
				float: left;
				width: 100%;
				margin: 10px 0 15px;

				ul {
					margin: 0;
					padding: 0;

					li {
						padding: 0;
						list-style: none;
						float: left;
						padding: 0 30px 0 0;
						width: 100%;
						margin: 0 0 7px;

						a {
							position: relative;
							color: $color-bla;
							font-size: 18px;

							&:before {
								float: left;
								width: 20px;
								height: 20px;
								background: url("../../assets/images/pil-blue.png") no-repeat center center;
								background-size: 16px;
								content: '';
								margin: 2px 8px 0 0;
							}
						}
					}
				}
			}

			#hidden_section {
				display: none;

				.ledningssystem-section {
					float: left;
					width: 100%;
					margin: 10px 0 15px;
					background: $color-light;
					padding: 30px;

					select {
						margin: 0;
						padding: 0;
						padding: 15px;
						float: left;
						width: 100%;
						font-size: 16px;
						border-radius: 4px;
						background: #fff;
						-webkit-appearance: none;

						option {
							padding: 0;
							list-style: none;
							float: left;
							padding: 0 30px 0 0;
							width: 100%;
							margin: 0 0 7px;
						}
					}
				}
			}

			.post-content {
				float: left;
				width: 100%;

				h3 {
					font-size: 30px;
					font-weight: 400;
					letter-spacing: normal;
				}

				p {
					&:last-child {
						margin: 0;
					}
				}
			}

			.post-footer {
				float: left;
				width: 100%;
				margin: 30px 0 0 0;

				#spec_links {
					float: left;
					margin: 0;
					padding: 0;
					width: 100%;

					li {
						list-style: none;
						position: relative;
						float: left;
						width: 100%;
						padding: 0;
						margin: 0 0 5px;

						.head {
							float: left;
							width: 100%;
							padding: 15px 15px;
							background: $color-dark-grey;
							color: #fff;
							min-height: 80px;
							transition: all .2s;

							.images {
								text-align: center;
								float: left;
								margin: 0 20px 0 0;
								position: absolute;
								top: 21px;
								left: 20px;
								width: 32px;
								height: 42px;
								margin: auto;
								vertical-align: middle;

								img {
									width: 25px;
								}
							}

							&:hover {
								cursor: pointer;
								background: lighten($color-dark-grey, 2%)
							}
						}

						h2 {
							margin: 12px 0 0 50px;
							font-size: 25px;
							font-weight: 400;
							color: #fff;
							letter-spacing: normal;
						}

						.r_more {
							position: absolute;
							top: 27px;
							right: 20px;
							width: 30px;
							height: 30px;
							margin: auto;
							vertical-align: middle;

							div {
								&.plus {}

								&.minus {
									display: none;
								}

								&:hover {
									cursor: pointer;
								}
							}

							&.snurr {
								div {
									&.plus {
										display: none;
									}

									&.minus {
										display: block;
									}
								}
							}
						}

						.hidden_content {
							float: left;
							width: 100%;
							padding: 15px;
							display: none;
							background: #f6f7f9;

							//Dokuemnt
							.document_crm {
								display: block;
								float: left;
								padding: 0 10px;

								.doc_section {
									float: left;
									width: 100%;

									ul {
										float: left;
										width: 100%;
										margin: 0;

										li {
											float: left;
											width: 100%;
											font-size: 16px;
											margin: 0 0 8px;

											b {
												display: none;
												float: left;
												width: 100%;
												border-bottom: solid 1px #DADADA;
												padding: 0 0 10px;
												margin: 20px 0 10px;
												letter-spacing: normal;

											}

											span {
												float: left;
												width: 100%;
												position: relative;
												padding: 0 0 0 30px;

												&:before {
													position: absolute;
													top: 5px;
													left: 0;
													right: 0;
													bottom: 0;
													float: left;
													width: 20px;
													height: 20px;
													background: url("../../assets/images/pil-blue.png") no-repeat center center;
													background-size: 16px;
													content: '';
												}
											}

											&:first-child {
												b {
													display: block;
												}
											}
										}
									}
								}

							}

							#document_crm {}

							//Externa länkar
							.externa_lankar {
								float: left;
								width: 100%;

								ul {
									loat: left;
									width: 100%;
									margin: 0 0 0 5px;

									li {
										float: left;
										width: 100%;

										a {
											float: left;
											width: 100%;
											position: relative;
											padding: 0 0 0 30px;

											&:before {
												position: absolute;
												top: 5px;
												left: 0;
												right: 0;
												bottom: 0;
												float: left;
												width: 20px;
												height: 20px;
												background: url("../../assets/images/pil-blue.png") no-repeat center center;
												background-size: 16px;
												content: '';
											}
										}
									}
								}
							}

							//Relaterad information
							.relaterad_information {
								float: left;
								width: 100%;
								display: block;

								ul {
									float: left;
									width: 100%;
									margin: 0 0 0 5px;

									li {
										float: left;
										width: 100%;

										a {
											float: left;
											position: relative;
											padding: 0 0 0 30px;

											&:before {
												position: absolute;
												top: 5px;
												left: 0;
												right: 0;
												bottom: 0;
												float: left;
												width: 20px;
												height: 20px;
												background: url("../../assets/images/pil-blue.png") no-repeat center center;
												background-size: 16px;
												content: '';
											}

											span {
												font-size: 15px;
												color: $color-text;
												margin: 0 0 0 7px;
												pointer-events: none;
											}
										}
									}
								}
							}

							//Bevaka innehåll
							.starta_bevakning {
								float: left;
								width: 100%;

								span {
									float: left;
									width: 100%;
									margin: 0 0 15px;
								}

								.row {
									float: left;
									width: 48%;
									position: relative;

									input {
										width: 100%;
										float: left;
										font-size: 16px;
										border-radius: 4px;
									}

									.error-text{

										font-size: .8rem;

									}

									&.name {}

									&.email {
										float: right;
									}

									i {
										position: absolute;
										top: 18px;
										right: 15px;
										transform: scale(0);
										transition: transform .2s;

										&.fa-times {
											color: #CC002F;
										}

										&.fa-check {
											color: #00925C;
										}
									}
								}

								.submit_bevakning {
									font-size: 16px;
									margin: 25px 0 0 0;
									float: right;
									padding: 15px 30px;
									border-radius: 4px;
									color: #fff;
									background: $color-bla;
									border-left: none;
									transition: all .2s;

									&:hover {
										background: lighten($color-bla, 3.8%);
										text-decoration: none;
										cursor: pointer;
									}
								}
							}

							//Skriv ut material

						}

						&:last-child {
							margin: 0;
						}
					}
				}
			}
		}
	}

	.sidebar {
		float: right;
		width: 100%;
		max-width: 320px;
		padding: 0 0 0 40px;

	}
}

//Ämnesområde (Single Tabell)
#working_area_tabel {
	float: left;
	width: 100%;
	margin: 25px 0 0;
	padding: 25px 0 0;
	border-top: solid 1px #DEDEDE;

	.working_info {
		float: left;
		width: 100%;
		margin: 0 0 15px;

		p {
			&:last-child {
				margin: 0;
			}
		}
	}

	.table_wrapper {
		table {
			float: left;
			width: 100%;
			text-align: left;
			margin: 8px 0 0;
			border-collapse: collapse;
			border-spacing: 0;

			thead {
				tr {
					background: #333333;
					color: #fff;

					th {
						padding: 10px 10px;
						font-size: 13px;
						font-weight: normal;
						letter-spacing: 0.5px;
											}
				}
			}

			tbody {
				background: #fff;
				border-collapse: separate;
				border-spacing: 0;

				tr {
					background: #fff;

					td {
						padding: 10px 10px;
						font-size: 15px;

						span {
							float: left;
							width: 100%;
						}

						p {
							margin: 0;
							line-height: 23px;
						}
					}

					&:nth-child(even) {
						background: #E8ECED;
					}
				}
			}
		}
	}
}

//Swedac Magasin
#single_section_post_swedac_magasin {
	.main {
		float: left;
		width: 100%;
		padding: 0;
		margin: 0;

		.single-post {
			padding: 0 0 0 40px;
			margin: 0 0 0 -40px;

			.post-header {
				float: left;
				width: 100%;
				max-width: 760px;
				padding: 20px 30px 0 30px;
				margin: -55px 0 0 -30px;
				background: #fff;

				h1 {
					font-size: 50px;
					line-height: 55px;
					margin: 0 0 15px;
					letter-spacing: 1.2px;
				}

				.meta-info {
					font-size: small;

					.updated {}

					.author {}
				}
			}

			.post-content {
				width: 100%;
				max-width: 700px;
				p {
					&:last-child {
						margin: 0;
					}
				}
			}

			.post-footer {
				float: left;
				width: 100%;
				margin: 30px 0 0 0;

				#spec_links {
					float: left;
					margin: 0;
					padding: 0;
					width: 100%;

					li {
						list-style: none;
						position: relative;
						float: left;
						width: 100%;
						padding: 0;
						margin: 0 0 5px;

						.head {
							float: left;
							width: 100%;
							padding: 15px 15px;
							background: $color-knapp;
							color: #fff;
							min-height: 80px;

							.images {
								text-align: center;
								float: left;
								margin: 0 20px 0 0;
								position: absolute;
								top: 30px;
								left: 20px;
								width: 30px;
								height: 30px;
								margin: auto;
								vertical-align: middle;

								img {
									width: 25px;
								}
							}
						}

						h2 {
							margin: 8px 0 0 50px;
							font-size: 25px;
							font-weight: 400;
							color: #fff;
							letter-spacing: normal;
						}


						&:last-child {
							margin: 0;
						}
					}
				}
			}
		}


		.latest_swedac_magasin {
			float: left;
			width: 100%;
			margin: 40px 0 0;

			ul {
				margin: 0;
				padding: 0;

				li {
					width: 100%;
					max-width: 230px;
					float: left;
					padding: 0;
					list-style: none;
					margin: 0 0 40px;

					.content {
						position: relative;
						height: 230px;

						.cover {
							position: absolute;
							width: 100%;
							height: 100%;
							left: 0;
							top: 0;
							z-index: 1;
							background: -moz-linear-gradient(bottom, rgba(0, 0, 0, 0.65) 16%, rgba(0, 0, 0, 0.65) 17%, rgba(0, 0, 0, 0) 60%);
							background: -webkit-linear-gradient(bottom, rgba(0, 0, 0, 0.65) 16%, rgba(0, 0, 0, 0.65) 17%, rgba(0, 0, 0, 0) 60%);
							background: linear-gradient(to top, rgba(0, 0, 0, 0.65) 16%, rgba(0, 0, 0, 0.65) 17%, rgba(0, 0, 0, 0) 60%);
						}

						.post_image {
							position: absolute;
							width: 100%;

							img {}
						}

						.info {
							position: absolute;
							z-index: 2;
							color: #fff;
							float: left;
							width: 100%;
							padding: 15px;
							bottom: 0;

							h2 {
								color: #fff;
								font-size: 20px;
								line-height: 24px;
								margin: 0 0 4px;
																letter-spacing: 0.8px;
							}

							.excerpt {
								font-size: 16px;
							}
						}
					}

					&:nth-child(odd) {
						float: right;
					}

					&:nth-child(even) {
						float: left;
						margin: 0 0 40px;
					}

					&:nth-last-child(-n+2) {
						margin: 0;
					}

					&:first-child {
						width: 50%;
						max-width: 500px;
						float: left !important;
						margin: 0 40px 0 0;

						.content {
							width: 100%;
							height: 500px;
							margin: 0 40px 0 0;
							position: relative;

							.cover {
								position: absolute;
								width: 100%;
								height: 100%;
								left: 0;
								top: 0;
								z-index: 1;
								background: -moz-linear-gradient(bottom, rgba(0, 0, 0, 0.65) 16%, rgba(0, 0, 0, 0.65) 17%, rgba(0, 0, 0, 0) 60%);
								background: -webkit-linear-gradient(bottom, rgba(0, 0, 0, 0.65) 16%, rgba(0, 0, 0, 0.65) 17%, rgba(0, 0, 0, 0) 60%);
								background: linear-gradient(to top, rgba(0, 0, 0, 0.65) 16%, rgba(0, 0, 0, 0.65) 17%, rgba(0, 0, 0, 0) 60%);
							}

							.info {
								position: absolute;
								z-index: 2;
								color: #fff;
								float: left;
								width: 100%;
								padding: 15px;
								bottom: 0;

								h2 {
									color: #fff;
									font-size: 50px;
									line-height: 50px;
									margin: 0 0 10px;
																	}

								.excerpt {
									font-size: 16px;
								}
							}
						}
					}
				}
			}
		}
	}
}

//kalender
#single_section_posts_kalender {
	h1 {
		font-size: 50px;
		line-height: 55px;
		margin: 0 0 25px;
		letter-spacing: 1.2px;
	}

	.single_section_post_kalender {
		float: left;
		width: 100%;
		background: #f6f7f9;
		padding: 40px;
		margin: 0 0 40px;

		.kalender_date {
			float: left;
			width: 15%;
			padding: 35px 20px;
			background-color: $color-knapp;
			color: #fff;

			span {
				float: left;
				width: 100%;
				text-align: center;

				&.start_date {
					font-weight: 200;
					font-size: 52px;
					line-height: 1;
					margin: 0 0 3px;

					.lastWord {
						text-transform: uppercase;
						margin: 4px 0 8px;
						font-size: 16px;
											}
				}

			}
		}

		.kalender_info {
			float: right;
			width: 80%;
			margin: -4px 0 0 0;

			h2 {
				color: $color-bla;
				transition: all .2s;
				font-size: 22px;

				&:hover {
					color: lighten($color-bla, 10%);
					text-decoration: none;
					cursor: pointer;
				}

				&.active {
					color: #222;
				}
			}

			table {
				font-size: 16px;

				tr {
					td {
						&.first {
							color: $color-text;
							padding: 0 15px 5px 0;
						}

						&.last {
														letter-spacing: 0.2px;
							padding: 0 0 5px 0;

							span {
								display: none;
							}
						}
					}
				}
			}

			.addtocalendar {
				position: relative;
				font-size: 16px;
				color: $color-bla;
				float: left;
				width: 100%;
				margin: 10px 0 0;

				&:before {
					float: left;
					width: 20px;
					height: 20px;
					background: url("../../assets/images/pil-blue.png") no-repeat center center;
					background-size: 16px;
					content: '';
					margin: 1px 8px 0 0;
				}

				.atc_event {
					display: none;
				}
			}

			.kalender_url {
				position: relative;
				font-size: 16px;
				float: left;
				width: 100%;
				margin: 10px 0 0;

				&:before {
					float: left;
					width: 20px;
					height: 20px;
					background: url("../../assets/images/pil-blue.png") no-repeat center center;
					background-size: 16px;
					content: '';
					margin: 1px 8px 0 0;
				}

				.success_copy {
					width: 80px !important;
					text-align: center;
					transition: all .2s;
					position: relative;
					display: none;
					padding: 3px 12px;
					background: #AAF5AA;
					font-size: 13px;
					margin: 0 0 0 20px;

					&:before {
						right: 100%;
						top: 50%;
						border: solid transparent;
						content: " ";
						height: 0;
						width: 0;
						position: absolute;
						pointer-events: none;
						border-color: rgba(170, 245, 170, 0);
						border-right-color: #AAF5AA;
						border-width: 7px;
						margin-top: -7px;
					}

					&.vissible {
						display: inline-block;
						animation-name: slideLeft;
						animation-duration: 2s;
						animation-fill-mode: forwards;
						animation-timing-function: ease;
					}
				}

				input {
					position: absolute;
					top: -99999px;
					left: -99999px;
				}
			}
		}

		.entry-summary {
			float: left;
			width: 70%;
			margin: 15px 0 0;
			display: none;
			clear: both;

			p {
				margin: 0 0 10px;

				&:last-child {
					margin: 0;
				}
			}
		}

		&.last-child {
			margin: 0;
		}
	}

	//Single sida
	.main {
		float: left;
		width: 100%;
		max-width: 700px;
		padding: 0 60px 0 0;
		margin: 0 20px 0 0;

		.single-post-kalender {
			float: left;
			width: 100%;

			.kalender_date {
				float: left;
				width: 25%;
				padding: 20px 20px;
				background-color: $color-knapp;
				color: #fff;

				span {
					float: left;
					width: 100%;
					text-align: center;

					&.start_date {
						font-weight: 200;
						font-size: 52px;
						line-height: 1;
						margin: 0 0 3px;

						.lastWord {
							text-transform: uppercase;
							margin: 4px 0 8px;
							font-size: 16px;
													}
					}

				}
			}

			.kalender_info {
				float: right;
				width: 70%;
				margin: -4px 0 0 0;

				h2 {
					color: $color-bla;
					transition: all .2s;
										letter-spacing: normal;

					&:hover {
						color: lighten($color-bla, 10%);
						text-decoration: none;
						cursor: pointer;
					}

					&.active {
						color: #222;
					}
				}

				table {
					font-size: 16px;

					tr {
						td {
							&.first {
								color: $color-text;
								padding: 0 15px 5px 0;
							}

							&.last {
								font-weight: bold;
								padding: 0 0 5px 0;
							}
						}
					}
				}

				.addtocalendar {
					position: relative;
					font-size: 16px;
					color: $color-bla;
					float: left;
					width: 100%;
					margin: 10px 0 0;

					&:before {
						float: left;
						width: 20px;
						height: 20px;
						background: url("../../assets/images/pil-blue.png") no-repeat center center;
						background-size: 16px;
						content: '';
						margin: 1px 8px 0 0;
					}

					.atc_event {
						display: none;
					}
				}
			}

			.post-content {
				float: left;
				width: 100%;
				margin: 15px 0 0;
				clear: both;

				p {
					margin: 0 0 10px;

					&:last-child {
						margin: 0;
					}
				}
			}

			&.last-child {
				margin: 0;
			}
		}
	}

	.sidebar {
		float: right;
		width: 100%;
		max-width: 320px;
		padding: 0 0 0 40px;

	}
}

.video-container {
	position: relative;
	padding-bottom: 56.25%;
	/*16:9*/
	padding-top: 0;
	height: 0;
	overflow: hidden;
}

.video-container iframe,
.video-container object,
.video-container embed {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}