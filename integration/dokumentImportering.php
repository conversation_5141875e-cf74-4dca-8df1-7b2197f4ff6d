<?php
// #1: Add CLI check at the very top
// Ensure this script is run from the command line and not via a web browser
if (php_sapi_name() !== 'cli' && php_sapi_name() !== 'cgi-fcgi') { // 'cgi-fcgi' can sometimes be used by cron
    // Send a 403 Forbidden header (important for web browsers/crawlers)
    header('HTTP/1.0 403 Forbidden');
    // Output a message (optional, but good practice)
    // You can customize this message or make it more generic
    exit('This script is designed for command-line execution only and cannot be accessed via a web browser.');
}

// If we're here, it's a CLI execution.

// IMPORTANT for wp-load.php in CLI:
// wp-load.php needs ABSPATH to be defined.
// Determine the WordPress root path based on the script's location.
// Adjust this path if your script is located elsewhere relative to the WP root.
// Assuming this script is in /wp-content/plugins/your-plugin-name/cron-scripts/your-script.php
// Then '../../../../' goes up to the WordPress root.
if ( ! defined( 'ABSPATH' ) ) {
    define( 'ABSPATH', __DIR__ . '/../../../../' ); // Adjust if your script's path depth is different
}

require_once ABSPATH . 'wp-load.php';
require_once __DIR__ . '/../vendor/autoload.php';

class Dokument
{
    private $title = "";
    private $name = "";
    private $reference = "";
    private $type = "";
    private $areaHeading = array();
    private $heading = "";
    private $description = "";
    private $authorization = "";
    private $calexnumber = "";
    private $repealed = false;
    private $currentUri = "";
    private $orginalUri = "";
    private $amendentUri = array();
    private $areaHeadingIndex = 0;
    private $modifiedDate = '1970-01-01';
    //2021
    private $isControlForm = false;
    private $controlFormTitles = array();

    private $webCategories = array();
    private $webCategoriesIndex = 0;

    public function getName()
    {
        return $this->name;
    }

    public function getTitle()
    {
        return $this->title;
    }

    public function getReference()
    {
        return $this->reference;
    }

    public function getType()
    {
        return $this->type;
    }

    public function getAreaHeading()
    {
        return $this->areaHeading;
    }

    public function getWebCategories()
    {
        return $this->webCategories;
    }

    public function getHeading()
    {
        return $this->heading;
    }

    public function getDescription()
    {
        return $this->description;
    }

    public function getAuthorization()
    {
        return $this->authorization;
    }

    public function getCalexnumber()
    {
        return $this->calexnumber;
    }

    public function getRepealed()
    {
        return $this->repealed;
    }

    public function getCurrentUri()
    {
        return $this->currentUri;
    }

    public function getOriginalUri()
    {
        return $this->orginalUri;
    }

    public function getAmendentUri()
    {
        return $this->amendentUri;
    }

    public function getModifiedDate()
    {
        return $this->modifiedDate;
    }

    public function getIsControlForm()
    {
        return $this->isControlForm;
    }

    public function getControlFormTitles()
    {
        return $this->controlFormTitles;
    }

    /**
     * Reads the content from the given PDF and returns it as a formatted string (ready to be saved in the database).
     *
     * @link https://developer.wordpress.org/reference/functions/sanitize_text_field/
     *
     * @param  int $index  Index number of the current document (used to create the temporary PDF)
     * @return string      Text from the PDF
     */
    public function getPdfText($index)
    {
        try {
            $docContent = '';
            $fileContent = '';

            if (!empty($this->getCurrentUri())) {
                $url = $this->getCurrentUri();
                $contextOptions = [
                    'http' => ['method' => 'GET', 'timeout' => 30],
                    'ssl' => ['verify_peer' => false, 'verify_peer_name' => false],
                ];
                $context = stream_context_create($contextOptions);
                $headers = @get_headers($url, false, $context);

                if ($headers && preg_grep('/^Content-Type:\s*application\/pdf/i', $headers)) {
                    $fileContent = @file_get_contents($url, false, $context);
                    if ($fileContent === false) {
                        // Log this specific error, perhaps with the URL
                        DokumentIntegration::setDebug("Failed to fetch PDF content from: $url. HTTP request failed.");
                        return ''; // Or throw an exception if you want to halt on this
                    }
                } else {
                    DokumentIntegration::setDebug('The URL is not a PDF file or headers not retrieved: ' . $url);
                    return '';
                }
            } else {
                //DokumentIntegration::setDebug('PDF URL is empty for document index: ' . $index);
                return ''; // No URL, nothing to process
            }

            if (!empty($fileContent)) {
                // Define a temporary file path. Ensure the 'temp' directory exists and is writable.
                // It's better to use WordPress's temp directory functions if possible,
                // or a dedicated, known-writable temp path.
                $tempDir = ABSPATH . 'temp/'; // Or wp_get_temp_dir() if you prefer
                if (!is_dir($tempDir)) {
                    wp_mkdir_p($tempDir); // Create if it doesn't exist
                }
                $tmpPdfPath = $tempDir . 'tmp_pdf_' . $index . '_' . time() . '.pdf'; // Add time() for more uniqueness if needed

                if (file_put_contents($tmpPdfPath, $fileContent) === false) {
                    DokumentIntegration::setDebug('Could not write content to temporary PDF file: ' . $tmpPdfPath);
                    // Attempt to clean up if write failed but file was created (e.g., partial write)
                    if (file_exists($tmpPdfPath)) { unlink($tmpPdfPath); }
                    return '';
                }

                if (!is_readable($tmpPdfPath)) {
                    DokumentIntegration::setDebug('Temporary PDF file is not readable: ' . $tmpPdfPath);
                    // No need to unlink if it's not readable/didn't write correctly
                    return '';
                }

                // Use pdftotext
                // The '-' tells pdftotext to output to stdout
                // escapeshellarg is crucial for security if $tmpPdfPath could ever contain user-influenced data (less likely here)
                $escapedPath = escapeshellarg($tmpPdfPath);
                $command = "pdftotext " . $escapedPath . " - -enc UTF-8"; // Output to stdout
                $docContent = shell_exec($command);

                // Check for errors from shell_exec (pdftotext might return non-zero on error,
                // or output to stderr which shell_exec doesn't capture by default)
                // A more robust check might involve checking pdftotext's exit code.
                if ($docContent === null || $docContent === false) {
                    DokumentIntegration::setDebug('shell_exec failed for pdftotext or pdftotext produced no output. Command: ' . $command . ' for PDF: ' . $url);
                    $docContent = '';
                } else {
                    // 1. Remove NULL bytes (critical)
                    $docContent = str_replace("\0", "", $docContent);

                    // 2. Remove Form Feed characters (common from PDFs)
                    $docContent = str_replace("\f", "", $docContent);

                    // 3. Remove Soft Hyphens (optional, but good for cleanliness)
                    $docContent = str_replace("\u{00AD}", "", $docContent);

                    // 4. Convert Non-Breaking Spaces to regular spaces (optional, often good)
                    $docContent = str_replace("\u{00A0}", " ", $docContent);

                    // 5. Correct specific "SWEDAC TYPE version" pattern
                    // To make the regex case-insensitive, add an 'i' flag at the end: /.../i
                    // And ensure the callback handles casing appropriately for the output.
                    // For now, assuming input "SWE DA C", "INF O" etc. is uppercase.
                    $docContent = preg_replace_callback(
                        // Regex:
                        // ((?:S\s*W\s*E\s*DA\s*C)|(?:SWE\s*DA\s*C))  -- SWEDAC part (Group 1)
                        // \s+                                       -- separator
                        // ((?:D\s*O\s*C)|(?:I\s*N\s*F\s*O)|(?:R\s*E\s*P)) -- TYPE part (Group 2)
                        // \s+                                       -- separator
                        // ([\d\s:]+)                                -- VERSION part (Group 3)
                        // Lookahead:
                        // (?=
                        //   \s+ (?: \d{4}-\d{2}-\d{2} | XX:XX | [A-ZÅÄÖ][a-zA-ZÅÄÖåäö\s:]*\b | [^:\d\s].* )
                        //   |
                        //   (?:\s*)$
                        // )
                        '/((?:S\s*W\s*E\s*DA\s*C)|(?:SWE\s*DA\s*C))\s+((?:D\s*O\s*C)|(?:I\s*N\s*F\s*O)|(?:R\s*E\s*P))\s+([\d\s:]+)(?=\s+(?:\d{4}-\d{2}-\d{2}|XX:XX|[A-ZÅÄÖ][a-zA-ZÅÄÖåäö\s:]*\b|[^:\d\s].*)|(?:\s*)$)/',
                        function ($matches) {
                            // $matches[1] = SWEDAC part
                            // $matches[2] = TYPE part
                            // $matches[3] = VERSION part

                            $swedacPart = "SWEDAC"; // Desired output

                            $typeInput = str_replace(' ', '', strtoupper($matches[2])); // Clean the matched TYPE part
                            $typePart = $typeInput; // Default if no specific mapping
                            if ($typeInput === "INFO") {
                                $typePart = "INFO";
                            } elseif ($typeInput === "DOC") {
                                $typePart = "DOC";
                            } elseif ($typeInput === "REP") {
                                $typePart = "REP";
                            }
                            // Add other type mappings if they exist, e.g., STYRDOK, etc.

                            $versionPart = str_replace(' ', '', $matches[3]); // Remove all spaces from version

                            return $swedacPart . ' ' . $typePart . ' ' . $versionPart;
                        },
                        $docContent
                    );

                    // 6. Consolidate multiple whitespace characters (including newlines from PDF) into single spaces
                    // This effectively "joins" lines from the PDF into a single flowing text block.
                    $docContent = preg_replace('/\s+/', ' ', $docContent);

                    // 7. WordPress sanitization (which also trims)
                    // The trim() here is now somewhat redundant due to preg_replace and sanitize_text_field,
                    // but it's harmless.
                    return sanitize_text_field(trim($docContent));
                }

                // Remove the temporary PDF file
                if (file_exists($tmpPdfPath)) { // Check if it exists before unlinking
                    if (!unlink($tmpPdfPath)) {
                        DokumentIntegration::setDebug('Could not remove temporary PDF file: ' . $tmpPdfPath);
                        // This is not critical for functionality but good for cleanup.
                    }
                }
            }

            return sanitize_text_field(trim($docContent)); // Trim whitespace from pdftotext output

        } catch (ErrorException $e) {
            // Log the full exception details including file and line for ErrorException
            DokumentIntegration::setNewExceptionError($e, "Exception in getPdfText for index: " . $index . " URL: " . (isset($this->currentUri) ? $this->currentUri : 'N/A'));
            return '';
        }
    }

    public function getPdfTextOld($index)
    {
        try {
            // Analyze PDF file and build necessary objects
            $parser = new \Smalot\PdfParser\Parser();

            $docContent = '';
            $fileContent = '';

            // Check if the URL to the PDF is set
            if (!empty($this->getCurrentUri())) {
                $url = $this->getCurrentUri();

                // Set up a stream context with a timeout (30 seconds)
                $contextOptions = [
                    'http' => [
                        'method'  => 'GET',
                        'timeout' => 30, // Set timeout to 30 seconds
                    ],
                    'ssl' => [
                        'verify_peer'      => false,
                        'verify_peer_name' => false,
                    ],
                ];
                $context = stream_context_create($contextOptions);

                // Get headers with timeout protection
                $headers = @get_headers($url, false, $context);

                if ($headers && preg_grep('/^Content-Type:\s*application\/pdf/i', $headers)) {
                    // Fetch the PDF content with timeout
                    $fileContent = @file_get_contents($url, false, $context);

                    if ($fileContent === false) {
                        throw new ErrorException("Failed to fetch PDF content from: $url");
                    }
                } else {
                    DokumentIntegration::setDebug('The URL is not a PDF file: ' . $url);
                    return '';
                }
            }

            if (!empty($fileContent) && $fileContent != false) {
                $tmpFile = file_put_contents(ABSPATH . 'temp/tmp_pdf_' . $index . '.pdf', $fileContent);

                if ($tmpFile == false) {
                    throw new ErrorException('Could not write the content to the temporary PDF file.');
                }

                if (!is_readable(ABSPATH . 'temp/tmp_pdf_' . $index . '.pdf')) {
                    throw new ErrorException('Temporary PDF file with index ' . $index . ' is not readable.');
                }

                /**
                 * Reads the content from the temporary PDF file.
                 *
                 * @var [string] Returned content from the PDF
                 *
                 * @link https://github.com/smalot/pdfparser
                 */
                $pdf = $parser->parseFile(ABSPATH . 'temp/tmp_pdf_' . $index . '.pdf');

                // Retrieve all pages from the PDF
                $pages = $pdf->getPages();

                $docContent = '';

                // Loop over all pages to extract text
                foreach ($pages as $page) {
                    $docContent .= ' ' . $page->getText();
                }

                // Remove the PDF file
                if (!unlink(ABSPATH . 'temp/tmp_pdf_' . $index . '.pdf')) {
                    throw new ErrorException('The temporary PDF file with index ' . $index . ' is not readable.');
                }
            }

            return sanitize_text_field($docContent);
        } catch (ErrorException $e) {
            DokumentIntegration::setNewExceptionError($e);
            return ''; // Return an empty string if an error occurs
        }
    }

    public function setName($nyttValue)
    {
        $this->name = $nyttValue;
    }

    public function setTitle($nyttValue)
    {
        $this->title = $nyttValue;
    }

    public function setReference($nyttValue)
    {
        $this->reference = $nyttValue;
    }

    public function setType($nyttValue)
    {
        $this->type = $nyttValue;
    }

    public function setAreaHeading($area, $heading)
    {
        $this->areaHeading[$this->areaHeadingIndex]["omradesnamn"] = $area;
        $this->areaHeading[$this->areaHeadingIndex]["heading"] = $heading;
        $this->areaHeadingIndex++;
    }

    public function setAreaHeadingNull()
    {
        $this->areaHeading = null;
    }

    public function setWebCategories($title, $accOrder, $cfArea, $cfHeadings)
    {
        $this->webCategories[$this->webCategoriesIndex]["webCategories_title"] = $title;
        $this->webCategories[$this->webCategoriesIndex]["webCategories_acc_order"] = $accOrder;
        $this->webCategories[$this->webCategoriesIndex]["webCategories_area"] = $cfArea;
        $this->webCategories[$this->webCategoriesIndex]["webCategories_headings"] = $cfHeadings;
        $this->webCategoriesIndex++;
    }

    public function setWebCategoriesNull()
    {
        $this->webCategories = null;
    }

    public function setHeading($nyttValue)
    {
        $this->heading = $nyttValue;
    }

    public function setDescription($nyttValue)
    {
        $this->description = $nyttValue;
    }

    public function setAuthorization($nyttValue)
    {
        $this->authorization = $nyttValue;
    }

    public function setCalexnumber($nyttValue)
    {
        $this->calexnumber = $nyttValue;
    }

    public function setRepealed($nyttValue)
    {
        $this->repealed = $nyttValue;
    }

    public function setCurrentUri($nyttValue)
    {
        $this->currentUri = $nyttValue;
    }

    public function setOriginalUri($nyttValue)
    {
        $this->orginalUri = $nyttValue;
    }

    public function setAmendentUri($nyttValue)
    {
        $this->amendentUri[] = $nyttValue;
    }

    public function setModifiedDate($modifiedDate)
    {
        $this->modifiedDate = $modifiedDate;
    }

    public function setIsControlForm($nyttValue)
    {
        $this->isControlForm = $nyttValue;
    }

    public function setControlFormTitles($nyttValue)
    {
        $this->controlFormTitles[] = $nyttValue;
    }
}

class DokumentIntegration
{
    // https://api-test.swedac.se/swagger/ui/index#!/Documents/Documents_GetAll
    // private $defaultUrl = "https://swedac-api-test.azurewebsites.net/";
    // private $defaultUrl = "https://api-test.swedac.se/";
    private $defaultUrl = "https://api.swedac.se/";


    // Adds the error message to a text file and stops the script.
    public static function setNewExceptionError($error)
    {
        date_default_timezone_set('Europe/Stockholm');
        $date = date('Y-m-d H:i:s');
        error_log($date . " " . strip_tags($error) . "\n", 3, __DIR__ . "/../../../documents_debug.log");
    }

    // Adds the debug message to a text file.
    public static function setDebug($string)
    {
        date_default_timezone_set('Europe/Stockholm');
        $date = date('Y-m-d H:i:s');
        error_log($date . " " . $string . "\n", 3, __DIR__ . "/../../../documents_debug.log");
    }

    /**
     * Empties the database of posts with post_type 'dokument' and the monitoring table for cron jobs that have already run.
     *
     * @return [array] Returns an empty array
     */
    public function resetPostTypes()
    {
        global $wpdb;

        // count dokument posts before deletion
        $myrows = $wpdb->get_results("SELECT COUNT(*) FROM `wp_posts` WHERE `post_type` = 'dokument'");
        if ($wpdb->last_error) { self::setDebug("Error in wpdb while counting dokument posts: " . $wpdb->last_error); }
        self::setDebug("Swedish documents deleted: " . $myrows[0]->{"COUNT(*)"});

        // count document_eng posts before deletion
        $myrows = $wpdb->get_results("SELECT COUNT(*) FROM `wp_posts` WHERE `post_type` = 'document_eng'");
        if ($wpdb->last_error) { self::setDebug("Error in wpdb while counting document_eng posts: " . $wpdb->last_error); }
        self::setDebug("English documents deleted: " . $myrows[0]->{"COUNT(*)"});

        // delete dokument posts
        $myrows = $wpdb->get_results("DELETE FROM `wp_posts` WHERE `post_type` = 'dokument'");
        if ($wpdb->last_error) { self::setDebug("Error in wpdb while deleting dokument posts: " . $wpdb->last_error); }

        // delete _crm_doc_% postmeta
        $myrows = $wpdb->get_results("DELETE FROM `wp_postmeta` WHERE meta_key LIKE '_crm_doc_%'");
        if ($wpdb->last_error) { self::setDebug("Error in wpdb while deleting _crm_doc_% postmeta: " . $wpdb->last_error); }

        // delete crm_doc_% postmeta
        $myrows = $wpdb->get_results("DELETE FROM `wp_postmeta` WHERE meta_key LIKE 'crm_doc_%'");
        if ($wpdb->last_error) { self::setDebug("Error in wpdb while deleting crm_doc_% postmeta: " . $wpdb->last_error); }

        // delete document_eng posts
        $myrows = $wpdb->get_results("DELETE FROM `wp_posts` WHERE `post_type` = 'document_eng'");
        if ($wpdb->last_error) { self::setDebug("Error in wpdb while deleting document_eng posts: " . $wpdb->last_error); }

        // clear subscribers_document_run table
        $myrows = $wpdb->get_results("DELETE FROM `subscribers_document_run` WHERE `run` = '0' OR `run` = '1'");
        if ($wpdb->last_error) { self::setDebug("Error in wpdb while clearing subscribers_document_run table: " . $wpdb->last_error); }

        return $myrows;
    }

    /**
     * Empties the Redis object cache.
     * 
     * @return void
     */
    public function resetRedisCache()
    {
        global $wp_object_cache;

        if (method_exists($wp_object_cache, 'flush')) {
            $wp_object_cache->flush();
        }
    }

    public function postNewData($objectifiedDoc)
    {
        global $wpdb;

        $postIds = array();

        try {
            $i = 0;
            $ny = 0;

            $processedReferences = [];

            foreach ($objectifiedDoc as $index => $dokument) {

                if (in_array($dokument->getReference(), $processedReferences)) {
                    DokumentIntegration::setDebug("Skipping duplicate reference in current batch: " . $dokument->getReference());
                    continue; // Skip to next document
                }

                $ny++;

                $args = array(
                    'post_type' => 'dokument',
                    'post_status' => 'publish',
                    'post_title' => $dokument->getTitle(),
                    'post_content' => '',
                    'meta_input' => array(
                        'crm_doc_title' => $dokument->getTitle(),
                        'crm_doc_heading' => $dokument->getAreaHeading(),
                        'crm_doc_description' => $dokument->getDescription(),
                        'crm_doc_dokumentbeteckning' => $dokument->getReference(),
                        'crm_doc_celexnumber' => $dokument->getCalexnumber(),
                        'crm_doc_type' => $dokument->getType(),
                        'crm_doc_repealed' => $dokument->getRepealed(),
                        'crm_doc_originalver' => $dokument->getOriginalUri(),
                        'crm_doc_doc' => $dokument->getCurrentUri(),
                        'crm_doc_authorization' => $dokument->getAuthorization(),
                        'crm_doc_text_content' => $dokument->getPdfText($index),
                        'crm_doc_iscontrolform' => $dokument->getIsControlForm(),
                    ),
                );

                $postID = wp_insert_post($args);

                $insertUpdatedDoc = $wpdb->insert(
                    'subscribers_document_run',
                    array(
                        'document_name' => $dokument->getReference(),
                        'modified_date' => date($dokument->getModifiedDate()),
                        'post_id' => $postID,
                        'run' => 0,
                    )
                );

                if ($insertUpdatedDoc === false) {
                    throw new ErrorException('$wpdb->insert(); failed inserting into subscribers_document_run table.');
                }

                $postIds[] = $postID;

                $field_key_amendment = "field_5729e2a54644c";
                $amendmentUri = $dokument->getAmendentUri();
                $amendmentArray = array();

                foreach ($amendmentUri as $uri) {
                    $amendmentArray[]["forfattning"] = $uri;
                }

                update_field($field_key_amendment, $amendmentArray, $postID);

                // Area
                $field_key = "field_5729e023ec28e";
                $areaArray = $dokument->getAreaHeading();
                $value = array();

                if ($areaArray != null) {
                    foreach ($areaArray as $indexAreaHeading => $area) {
                        if (!empty($area)) {
                            $checkIfExists = get_field("crm_doc_area", $postID);
                            $value[$indexAreaHeading]["omradesnamn"] = $area["omradesnamn"];
                            $value[$indexAreaHeading]["heading"] = $area["heading"];
                        }
                    }
                    update_field($field_key, $value, $postID);
                }

                // Insert getwebCategories
                if ($dokument->getwebCategories() != null) {
                    foreach ($dokument->getwebCategories() as $cf) {

                        $headings = [];
                        foreach ($cf['webCategories_headings'] as $heading ) {
                            $headings[] = ['heading' => $heading];
                        }

                        $data = array(
                            'webCategories_title' => $cf['webCategories_title'],
                            'webCategories_acc_order' => $cf['webCategories_acc_order'],
                            'webCategories_area' => $cf['webCategories_area'],
                            'webCategories_headings' => $headings,
                        );
                        add_row('crm_doc_webCategories', $data, $postID);
                    }
                }

                // Insert control form titles
                if ($dokument->getControlFormTitles() != null) {
                    foreach ($dokument->getControlFormTitles() as $cft) {
                        add_row('crm_doc_controlformtitle', ['control_form_title' => $cft], $postID);
                    }
                }

                $processedReferences[] = $dokument->getReference();
            }
        } catch (ErrorException $e) {
            self::setNewExceptionError($e);
        }
        return $postIds;
    }

    public function postNewDataEng($objectifiedDoc)
    {
        $postIds = array();

        try {
            $areaArray = array();
            $i = 0;

            $processedReferences = [];

            foreach ($objectifiedDoc as $index => $dokument) {
                // Query - checks if there is any document with the current calexnumber,
                if (substr($dokument->getTitle(), 0, 11) != "In Swedish:") {
                    $i++;

                    if (in_array($dokument->getReference(), $processedReferences)) {
                        DokumentIntegration::setDebug("Skipping duplicate reference in current batch: " . $dokument->getReference());
                        continue; // Skip to next document
                    }

                    // Check if the query has any content, if empty, we add.
                    $args = array(
                        'post_type' => 'document_eng',
                        'post_status' => 'publish',
                        'post_title' => $dokument->getTitle(),
                        'post_content' => '',
                        'meta_input' => array(
                            'crm_doc_title_en' => $dokument->getTitle(),
                            'crm_doc_heading_en' => $dokument->getAreaHeading(),
                            'crm_doc_description_en' => $dokument->getDescription(),
                            'crm_doc_dokumentbeteckning_en' => $dokument->getReference(),
                            'crm_doc_celexnumber_en' => $dokument->getCalexnumber(),
                            'crm_doc_type_en' => $dokument->getType(),
                            'crm_doc_repealed_en' => $dokument->getRepealed(),
                            'crm_doc_originalver_en' => $dokument->getOriginalUri(),
                            'crm_doc_doc_en' => $dokument->getCurrentUri(),
                            'crm_doc_authorization_en' => $dokument->getAuthorization(),
                            'crm_doc_text_content' => $dokument->getPdfText($index),
                            'crm_doc_iscontrolform' => $dokument->getIsControlForm(),
                        ),
                    );

                    $postID = wp_insert_post($args);

                    $postIds[] = $postID;

                    // $field_key_amendment = "field_57594ab7eba7f"; // Old english references
                    $field_key_amendment = "field_5729e2a54644c";
                    $amendmentUri = $dokument->getAmendentUri();
                    $amendmentArray = array();

                    foreach ($amendmentUri as $uri) {
                        $amendmentArray[]["forfattning"] = $uri;
                    }

                    update_field($field_key_amendment, $amendmentArray, $postID);

                    // $field_key = "field_57594a86eba7c"; // Old english references
                    $field_key = "field_5729e023ec28e";
                    $areaArray = $dokument->getAreaHeading();
                    $value = array();

                    if ($areaArray != null) {
                        foreach ($areaArray as $indexAreaHeading => $area) {
                            if (!empty($area)) {
                                // $checkIfExists = get_field("crm_doc_area_en", $postID);
                                $checkIfExists = get_field("crm_doc_area", $postID);
                                $value[$indexAreaHeading]["omradesnamn"] = $area["omradesnamn"];
                                $value[$indexAreaHeading]["heading"] = $area["heading"];
                            }
                        }

                        update_field($field_key, $value, $postID);
                    }

                    // Insert getwebCategories
                    if ($dokument->getwebCategories() != null) {
                        foreach ($dokument->getwebCategories() as $cf) {

                            $headings = [];
                            foreach ($cf['webCategories_headings'] as $heading ) {
                                $headings[] = ['heading' => $heading];
                            }

                            $data = array(
                                'webCategories_title' => $cf['webCategories_title'],
                                'webCategories_acc_order' => $cf['webCategories_acc_order'],
                                'webCategories_area' => $cf['webCategories_area'],
                                'webCategories_headings' => $headings,
                            );
                            add_row('crm_doc_webCategories', $data, $postID);
                        }
                    }

                    // Insert control form titles
                    if ($dokument->getControlFormTitles() != null) {
                        foreach ($dokument->getControlFormTitles() as $cft) {
                            add_row('crm_doc_controlformtitle', ['control_form_title' => $cft], $postID);
                        }
                    }

                    $processedReferences[] = $dokument->getReference();
                }
            }
        } catch (ErrorException $e) {
            self::setNewExceptionError($e);
        }
        return $postIds;
    }

    public function trim($text)
    {
        $patterns = array(
            '/\W+/', // match any non-alpha-numeric character sequence, except underscores
            '/\d+/', // match any number of decimal digits
            '/_+/', // match any number of underscores
            '/\s+/', // match any number of white spaces
        );

        $replaces = array(
            '', // remove
            '', // remove
            '', // remove
            '', // leave only 1 space     Elmätare, ,
        );

        $result = trim(preg_replace($patterns, $replaces, $text));
        $result = preg_replace('/\s+/', '', $result);

        return $result;
    }

    // Creates an object from the return of the API call.
    public function objektify($rawDoc)
    {
        try {
            // Objectify raw data.
            $returnArray = array();
            // for each raw data row as row.

            foreach ($rawDoc as $index => $line) {
                if (!empty($line["title"])) {
                    $document = new Dokument();

                    // sets all attributes
                    $document->setReference($line["reference"]);
                    $document->setTitle($line["title"]);
                    $document->setType($line["type"]);
                    $document->setHeading($line["heading"]);
                    $document->setDescription($line["description"]);
                    $document->setAuthorization($line["authorization"]);
                    $document->setCalexnumber($line["celexNumber"]);
                    $document->setRepealed($line["repealed"]);
                    $document->setCurrentUri($line["currentUri"]);
                    $document->setOriginalUri($line["originalUri"]);
                    $document->setIsControlForm($line["isControlForm"]);

                    // Control form title
                    if ($line["controlFormTitles"] != null) {
                        foreach ($line["controlFormTitles"] as $cft) {
                            $document->setControlFormTitles($cft);
                        }
                    }

                    // Date of the last modification of the document
                    if ($line["updatedDate"] != null) {
                        $document->setModifiedDate($line["updatedDate"]);
                    }

                    if ($line["amendentUris"] != null) {
                        foreach ($line["amendentUris"] as $uri) {
                            $document->setAmendentUri($uri);
                        }
                    }

                    if (!empty($line["categories"])) {
                        foreach ($line["categories"] as $areaHeading) {
                            $document->setAreaHeading($areaHeading["area"], $areaHeading["heading"]);
                        }
                    } else {
                        $document->setAreaHeadingNull();
                    }

                    if (!empty($line["webCategories"])) {
                        foreach ($line["webCategories"] as $webCategories) {
                            $document->setwebCategories($webCategories["controlFormTitle"], $webCategories["accOrder"], $webCategories["area"], $webCategories["headings"]);
                        }
                    } else {
                        $document->setWebCategoriesNull();
                    }


                    // Save the document in the array.
                    $returnArray[] = $document;
                }
            }

            // Returns the objectified data stored in returnArray.
            return $returnArray;
        } catch (ErrorException $e) {
            self::setNewExceptionError($e);
        }
    }

    /**
     * Cleans up orphaned post meta data from the wp_postmeta table.
     * Specifically targets known orphaned meta keys if provided, otherwise general orphaned meta.
     *
     * @param array $specific_meta_keys (Optional) An array of specific meta keys to target for cleanup.
     *                                    If empty, will attempt to clean all orphaned meta.
     * @return void
     */
    public function cleanupOrphanedPostMeta($specific_meta_keys = [])
    {
        global $wpdb;
        self::setDebug("Starting orphaned post meta cleanup.");

        $deleted_count = 0;

        if (!empty($specific_meta_keys)) {
            // Target specific meta keys
            foreach ($specific_meta_keys as $meta_key_to_clean) {
                $query = $wpdb->prepare(
                    "DELETE pm
                     FROM {$wpdb->postmeta} pm
                     LEFT JOIN {$wpdb->posts} p ON pm.post_id = p.ID
                     WHERE p.ID IS NULL AND pm.meta_key = %s",
                    $meta_key_to_clean
                );
                $result = $wpdb->query($query);

                if ($result === false) {
                    self::setDebug("Error deleting orphaned meta for key '{$meta_key_to_clean}': " . $wpdb->last_error);
                } elseif ($result > 0) {
                    $deleted_count += $result;
                    self::setDebug("Deleted {$result} orphaned post meta entries for key '{$meta_key_to_clean}'.");
                }
            }
        } else {
            // General cleanup for all orphaned meta (use with caution on very large sites without testing)
            // This query can be heavy on very large postmeta tables.
            $query = "DELETE pm
                      FROM {$wpdb->postmeta} pm
                      LEFT JOIN {$wpdb->posts} p ON pm.post_id = p.ID
                      WHERE p.ID IS NULL";
            $result = $wpdb->query($query);

            if ($result === false) {
                self::setDebug("Error deleting general orphaned meta: " . $wpdb->last_error);
            } elseif ($result > 0) {
                $deleted_count = $result; // In this case, $result is the total count
                self::setDebug("Deleted {$result} general orphaned post meta entries.");
            }
        }
        
        if ($deleted_count > 0) {
            self::setDebug("Total orphaned post meta entries deleted: {$deleted_count}.");
        } else {
            self::setDebug("No orphaned post meta entries found or deleted for the specified criteria.");
        }
    }

    /**
     * Main function to execute the import process.
     * 
     * @return void
     * @throws ErrorException
     */
    public function main()
    {

        // Use distinct option keys if you might have other CLI scripts or web triggers
        $running_option_key = 'documents_import_running'; 
        $last_successful_option_key = 'documents_last_successful_import';

        // Check if the import is already running
        if (get_option($running_option_key)) {
            self::setDebug("CLI Import already running, exiting.");
            return;
        }

        // Check if the import already ran successfully today
        $today = date('Y-m-d');
        if (get_option($last_successful_option_key) == $today) {
            self::setDebug("CLI Import already ran successfully today, exiting.");
            return;
        }
        
        // Set the import running flag
        update_option($running_option_key, 1);
        self::setDebug("-----------------------------------");
        self::setDebug("Starting CLI import process. Import running flag SET.");

        $cache_invalidation_was_suspended_by_this_run = false;
        if (function_exists('wp_suspend_cache_invalidation')) {
            wp_suspend_cache_invalidation(true);
            $cache_invalidation_was_suspended_by_this_run = true;
            self::setDebug("Cache invalidation SUSPENDED.");
        }

        $import_succeeded = false; // Flag to track if the core import logic was successful

        try {
            // Fetch data from the API - Swedish
            $rawDoc = $this->getApi("v1/documents");
            if ($rawDoc === null || $rawDoc === false) { // Check if API call failed
                throw new ErrorException("Failed to fetch Swedish documents from API.");
            }
            $rawDoc = json_decode($rawDoc, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new ErrorException("Failed to decode JSON for Swedish documents: " . json_last_error_msg());
            }
            $objectifiedDoc = $this->objektify($rawDoc);

            // Fetch data from the API - English
            $rawDocEng = $this->getApi("v1/documents?lang=en");
            if ($rawDocEng === null || $rawDocEng === false) { // Check if API call failed
                throw new ErrorException("Failed to fetch English documents from API.");
            }
            $rawDocEng = json_decode($rawDocEng, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new ErrorException("Failed to decode JSON for English documents: " . json_last_error_msg());
            }
            $objectifiedDocEng = $this->objektify($rawDocEng);

            self::setDebug("Number of Swedish documents from API: " . count($objectifiedDoc));
            self::setDebug("Number of English documents from API: " . count($objectifiedDocEng));

            // Process only if there's data from at least one language
            if (count($objectifiedDoc) > 0 || count($objectifiedDocEng) > 0) {
                $this->resetPostTypes(); // Deletes old posts of 'dokument' and 'document_eng'
                // The initial $this->resetRedisCache(); after resetPostTypes can be moved to finally
                // or kept here if you specifically want to clear cache immediately after deletes
                // For simplicity with suspend_cache_invalidation, one final flush is often enough.
                // Let's remove it from here and rely on the final one for now.

                $batch_size = 50;
                $successfullyImportedSwedish = 0;
                $successfullyImportedEnglish = 0;

                if (count($objectifiedDoc) > 0) {
                    self::setDebug("Processing Swedish documents...");
                    foreach (array_chunk($objectifiedDoc, $batch_size) as $batch_index => $batch) {
                        self::setDebug("Processing Swedish batch " . ($batch_index + 1));
                        $imported = $this->postNewData($batch); // postNewData returns array of inserted post IDs
                        if ($imported && is_array($imported)) {
                            $successfullyImportedSwedish += count($imported);
                        }
                        unset($batch);
                        if (function_exists('gc_collect_cycles')) gc_collect_cycles();
                    }
                }

                if (count($objectifiedDocEng) > 0) {
                    self::setDebug("Processing English documents...");
                    foreach (array_chunk($objectifiedDocEng, $batch_size) as $batch_index => $batch) {
                        self::setDebug("Processing English batch " . ($batch_index + 1));
                        $imported = $this->postNewDataEng($batch); // postNewDataEng returns array of inserted post IDs
                        if ($imported && is_array($imported)) {
                            $successfullyImportedEnglish += count($imported);
                        }
                        unset($batch);
                        if (function_exists('gc_collect_cycles')) gc_collect_cycles();
                    }
                }

                self::setDebug("Number of Swedish documents imported: " . $successfullyImportedSwedish);
                self::setDebug("Number of English documents imported: " . $successfullyImportedEnglish);
                $import_succeeded = true; // Mark as successful if we got this far with data processing
            } else {
                self::setDebug("No documents found in API for either Swedish or English. No data imported.");
                $import_succeeded = true; // Still considered a "successful" run, just no data.
            }

        } catch (Throwable $e) { // Catch ErrorException, Exception, and other Errors (PHP 7+)
            // For PHP 5, you might have 'catch (ErrorException $e)' and 'catch (Exception $e)'
            $error_message = "CRITICAL ERROR during import: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine() . "\nTrace: " . $e->getTraceAsString();
            // Create a new ErrorException if you want to pass it to your existing logger
            self::setNewExceptionError(new ErrorException($error_message)); 
            self::setDebug("Import process FAILED due to an error.");
            $import_succeeded = false; // Explicitly mark as failed
            // The 'echo $e;' is removed, as setNewExceptionError handles logging.
        } finally {
            // This block executes regardless of whether an exception occurred or not.

            // Call to cleanup orphaned meta
            $meta_keys_to_target = ['_wpml_word_count']; // Add other known orphaned keys if any
            $this->cleanupOrphanedPostMeta($meta_keys_to_target);
            
            // Resume cache invalidation if it was suspended
            if ($cache_invalidation_was_suspended_by_this_run && function_exists('wp_suspend_cache_invalidation')) {
                wp_suspend_cache_invalidation(false);
                self::setDebug("Cache invalidation RESUMED.");
            }

            // STEP 1: Flush the underlying WordPress Object Cache (Redis)
            // This ensures that any subsequent operations by WordPress or plugins
            // will fetch fresh data directly from the database.
            $this->resetRedisCache(); // This is your existing Redis flush
            self::setDebug("Redis object cache flushed.");

            // STEP 2: Clear WP Rocket's caches (Page cache, etc.)
            // WP Rocket will now rebuild its caches using data fetched fresh from the DB
            // (since the object cache was just cleared).
            if (function_exists('rocket_clean_domain')) {
                rocket_clean_domain();
                self::setDebug("WP Rocket cache cleared.");
            }

            // Clear the import running flag
            delete_option($running_option_key);
            self::setDebug("Import running flag CLEARED.");

            // Save date of last successful import to options table ONLY if import_succeeded
            if ($import_succeeded) {
                update_option($last_successful_option_key, $today);
                self::setDebug("Import process deemed successful. Last successful import date updated.");
            } else {
                self::setDebug("Import process deemed unsuccessful or errored. Last successful import date NOT updated.");
            }

            if ($import_succeeded){
                self::setDebug("Import process script finished successfully.");
            } else {
                self::setDebug("Import process script finished with errors.");
            }
        }
    }

    public function getApi($cutUrl)
    {
        try {
            // Fetches the URL, defaultUrl is a class attribute with the value http://api.swedac.se/.
            // while cutUrl has the value we pass from each respective function
            // this has two possibilities, v1/stamps (gets the name stamps)
            // v1/accreditations (gets the accredited bodies).
            $url = $this->defaultUrl . $cutUrl;
            $ch = curl_init();

            // set curl limits
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 20); // 20 seconds to connect
            curl_setopt($ch, CURLOPT_TIMEOUT, 120); // 120 seconds for the entire operation

            // want application/json back
            curl_setopt($ch, CURLOPT_HTTPHEADER, array('Accept: application/json'));

            // specifies which URL
            curl_setopt($ch, CURLOPT_URL, $url);

            // specify that we want a return value
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            // Debug - want to receive HEADER_OUT data on how the call went.
            curl_setopt($ch, CURLINFO_HEADER_OUT, true);

            // Execute the request
            $result = curl_exec($ch);

            if ($result === false) {
                throw new ErrorException(curl_error($ch), curl_errno($ch));
            }

            // Get information about how the call went.
            $info = curl_getinfo($ch);

            // obtain an HTTP code.
            $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            // Close the curl connection
            curl_close($ch);

            return $result;
        } catch (ErrorException $e) {
            self::setNewExceptionError($e);
        }
    }
}

// error handler, defines custom exceptions (similar to C#, Java, etc.).
function exception_error_handler($errno, $errstr, $errfile, $errline)
{
    throw new ErrorException($errstr, $errno, 0, $errfile, $errline);
}

set_error_handler("exception_error_handler");

// The script will only reach here if run via CLI due to the check at the top.
$class = new DokumentIntegration();
$class->main();

// Exit with 0 for success for cron
exit(0);