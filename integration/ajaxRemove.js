
jQuery(".remove i" ).click(function() {

jQuery("body").css({ opacity: 0.5 });
jQuery.ajax({

		timeout:15000,
		type:"GET",
		dataType: "text",
		url:"truncateErrorlogs.php",

		success: function(ajaxReturnData){
				console.log(ajaxReturnData);	
			

		jQuery(".error-wrapper").empty();					
		jQuery(".error-wrapper").append("<h4>"+ajaxReturnData+"</h4>");
					
			
		},	
		error : function(xhr,status,error){
			jQuery("body").css({ opacity: 1 });
			window.alert(xhr.statusText+ " : " + status +  " : " +error);
		},
		complete: function(xhr,status){
			jQuery("body").css({ opacity: 1 });
		}
	});
});