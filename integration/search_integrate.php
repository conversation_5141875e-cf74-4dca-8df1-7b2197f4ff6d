<?php
/*
Integration mellan Swedac sökfunktion och dess hemsida.
För specifik information, referera till dokumentet search_integrate.docx.

Klasser:

Bolag - representerar ett bolag som kan innehålla företag som är ackrediterade.

SearchItems - representerar enskilda bolag som är ackrediterade
search_integrate -- binder ihop dessa klasser och omvandlar api-anrop returen till egen definerade objekt som loopas ut.

*/

class apiCall
{
     /*
         	*	var $defaultUrl - erhåller en referens till swedacs api standard-url. Denna är en del av den fullständiga       url:en.
         	*/
     private $defaultUrl = "https://api.swedac.se/";
     public function get($cutUrl)
     {
          // skaffar url, default-url är ett klassattribut som har värdet http://api.swedac.se/.
          // medans cutUrl har det väret vi skickar in ifrån respektive funktion
          //denna har två möjligheter, v1/stamps (får ut namnstämplarna)
          //v1/accrediations (får ut accrediterade organ).
          $url = $this->defaultUrl . $cutUrl;
          $ch = curl_init();
          //vill ha application/json tillbaka
          curl_setopt($ch, CURLOPT_HTTPHEADER, array('Accept: application/json'));

          // specificerar vilken url
          curl_setopt($ch, CURLOPT_URL, $url);
          //definerar att vi vill ha ett returvärde
          curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
          //debug - vill ta emot HEADER_OUT data, om hur anropet gick.
          curl_setopt($ch, CURLINFO_HEADER_OUT, true);
          //executar anropet
          $result = curl_exec($ch);
          //skaffar information om hur anropet gick.
          $info = curl_getinfo($ch);
          // erhåller en http-kod.
          $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
          // stänger ned curl-connection
          curl_close($ch);
          //returnerar resultatet.
          return $result;
     }
} // slut klass API

class bolag
{
     /*
               *Klassen Bolag - representerar ett bolag, innehåller attribut nedan. Innehåller främst bolagets namn samt         alla företag       som
               *representerar detta företag. Tex.(Svensk bilprovning AB - Alingsås, Örebro, Karlstad , etc).
               * var $accNo - Swedac API
               * var $orgName - Namn på organisation.
               * var $searchItems - Array där det sparas enskilda firmor tillhörande bolag.
               * var href - idans url (search.swedac.se?)
               */
     private $accNo = 0;
     private $orgName = "";
     private $searchItems = array();
     private $href = "";

     /**
      * set-functions - sätter nytt värde på klass-attribut
      *
      * @param $nyttValue - det nya värdet attributet ifråga skall erhålla
      *
      * @return void
      */
     public function setOrgName($nyttValue)
     {

          $this->orgName = $nyttValue;
     }
     public function setAccNo($nyttValue)
     {

          $this->accNo = $nyttValue;
     }
     public function setSearchItems($nyttValue)
     {

          $this->searchItems = $nyttValue;
     }
     public function setHref($nyttValue)
     {
          $this->href = $nyttValue;
     }
     /**
      * get-functions -
      *
      *
      *
      * @return värdet i aktuellt attribut.
      */
     public function getAccNo()
     {

          return $this->accNo;
     }
     public function getOrgName()
     {

          return $this->orgName;
     }
     public function getSearchItems()
     {

          return $this->searchItems;
     }
     public function getHref()
     {

          return $this->href;
     }
}
class searchItems
{
     /*
              *Klassen searchItems - ett enskilt företag inom detta bolag.
              * var $id - Swedac API:s ID
              * var $name - Namn på orten där företaget brukar.
              * var $count - Swedac API:s Count
              */
     private $id = 0;
     private $name = "";
     private $count = 0;
     private $totalCount = "";
     //get

     /**
      * get-functions -
      *
      *
      *
      * @return värdet i aktuellt attribut.
      */
     public function getId()
     {
          return $this->id;
     }
     public function getName()
     {
          return $this->name;
     }
     public function getCount()
     {
          return $this->count;
     }
     //set
     /**
      * set-functions - sätter nytt värde på klass-attribut
      *
      * @param $nyttValue - det nya värdet attributet ifråga skall erhålla
      *
      * @return void
      */
     public function setId($nyttValue)
     {
          $this->id = $nyttValue;
     }
     public function setName($nyttValue)
     {
          $this->name = $nyttValue;
     }
     public function setCount($nyttValue)
     {
          $this->count = $nyttValue;
     }
}


// klass search_integrate
class search_integrate
{
     private $mapCoords = "";
     private $queryString = "";
     /**
      * utför och Objektifierar API-anrop för ackrediteringar.
      *
      * @param null
      *
      * @return objektiferad array med associativa värden för bolag.
      */
     public function getMapCoords()
     {

          return $this->mapCoords;
     }

     public function accreditations()
     {
          // $url = "v1/accreditations?q=".$_GET["s"].'&$top=20';

          global $wp_query;
          $this->queryString = urlencode($_GET["s"]);
          $queryString = urlencode($_GET["s"]);


          // Ajax
          // if(isset($_GET["lang"])){
          //      if($_GET["lang"]==="en"){

          //           $url = "v1/accreditations-map?q=".$queryString."&lang=en&top=20&count";
          //      }
          // }
          // else{
          //           $url = "v1/accreditations-map?q=".$queryString."&top=20&count";
          // }



         if (isset($_GET["lang"])) {
            if ($_GET["lang"] === "en") {

            $url = "v1/accreditations?q=" . $queryString . "&lang=en&count";
            }
         } else {
            $url = "v1/accreditations?q=" . $queryString . "&count";
          }

          $apiCall = new apiCall();
          $foretag = $apiCall->get($url);
          $foretag = json_decode($foretag, true);
          //    var_dump($foretag);

          $foretagArray = array();
          if ( isset( $foretag ) ) {
               // foreach ($foretag["mapCoordinates"] as $index => $coordinate) {
               //      $printCoord[$index]["ID"] = $coordinate["id"];
               //      $printCoord[$index]["Name"] = $coordinate["name"];
               //      $printCoord[$index]["AccNo"] = $coordinate["accNo"];
               //      $printCoord[$index]["GeoLatitude"] = $coordinate["geoLatitude"];
               //      $printCoord[$index]["GeoLongitude"] = $coordinate["geoLongitude"];
               //      $printCoord[$index]["MultipleOffices"] = $coordinate["multipleOffices"];
               // }

               // $printCoord = json_encode($printCoord);
               // // wp_cache_set("map_acc".$_GET["s"],$printCoord);
               // $this->mapCoords = $printCoord;

               foreach ($foretag as $foretagItem) {
                    $bolag = new bolag();
                    $bolag->setHref($foretagItem["_links"][0]["href"]);
                    $bolag->setOrgName($foretagItem["orgName"]);
                    $bolag->setAccNo($foretagItem["accNo"]);
                    $bolag->setSearchItems($foretagItem["officeSummaries"]);
                    $foretagArray[] = $bolag;
               }
               
               return $foretagArray;
          } else {
               return array();
          }
     }

     public function initiateAjax($skip, $search, $lang)
     {
          //utför ajax anrop,
          //definerar vår URL
          if ($lang === "eng") {

               $url = "v1/accreditations?q=" . $search . '&lang=en'; //.$skip;
          } else {

               $url = "v1/accreditations?q=" . $search; //.$skip;

          }

          $apiCall = new apiCall();
          //utför en get på api-anropet.
          $foretag = $apiCall->get($url);
          // kollar så det ej är tomt.
          if ($foretag != "") {
               return $foretag;
          } else {
               return "fel";
          }
     }



     /**
      * Bestämmer ifall anropet skall köras, returnerar den slutgiltiga arrayen med värden, objektifierade så som associativa.
      *
      * @param null
      *
      * @return objektiferad array med associativa värden för bolag och searchitems.
      */

     public function ackrediterande_organ()
     {
          $bolag = array();
          // kollar ifall sökalternativet är ackrediterande_organ
          if (isset($_GET["ackrediterande_organ"])) {
               $searchItemArray = wp_cache_get("ackrediterande_organ" . $_GET["s"]);
               if ($searchItemArray === false) {

                    //gör ett api-anrop, och returnerar en egendefinerad klass. för mer information se funktionen accreditations().
                    $bolag = $this->accreditations();
                    //instansierar ny array. Denna skall innehålla alla råa sökalternativ ifrån API-anropet.
                    $searchItemArray = array();


                    // för varje bolag .
                    foreach ($bolag as $index => $bolagItem) {
                         //skaffar alla enskilda företag i den specifika bolagsinstansieringen
                         $searchArray = $bolagItem->getSearchItems();
                         // skapar en ny array
                         // skapar en ny array vid varje anrop. denna utgör arrayens multidimensionella del,
                         $objectSearchArray = array();
                         //nestlad foreach, objektifierar rådata från API-anrop. Denna utgör enskilda firmor.
                         foreach ($searchArray as $key => $item) {
                              //instansierar klassen
                              $searchItemsClass = new searchItems();
                              $searchItemsClass->setId($item["id"]);
                              $searchItemsClass->setName($item["name"]);
                              $searchItemsClass->setCount($item["count"]);
                              // kör diverse anrop för att spara data i arrayen deklarerad nedan.
                              $objectSearchArray[$key] = $searchItemsClass;
                         }
                         //lägger till associerande text i array - orgnamn och href, och till sist alla enskilda firmor som är  objektifierade.
                         $searchItemArray[$index]["organisationsnamn"] = $bolagItem->getOrgName();
                         $searchItemArray[$index]["href"] = $bolagItem->getHref();
                         $searchItemArray[$index]["accNo"] = $bolagItem->getAccNo();
                         $searchItemArray[$index][] = $objectSearchArray;
                         // wp_cache_set("ackrediterande_organ".$_GET["s"],$searchItemArray);

                    }
               }
               $coords = wp_cache_get("map_acc" . $_GET["s"]);
               if ($coords != false) {
                    $this->mapCoords = $coords;
               }
               //skriver ut svar
               $this->printAckrediterade_organ($searchItemArray);
          }
     }
     public function printAckrediterade_organ($result)
     {
          global $wp_query;

          $url = "http://search.swedac.se/sv/ackrediteringar/";
          if (isset($_GET["lang"])) {
               if ($_GET["lang"] === "en") {
                    $url = "http://search.swedac.se/en/accreditations/";
               }
          }

          if ( isset( $this->totalCount ) ) {

               echo '<input type="hidden" id="totalCountAccred" name="totalCount" value="' . $this->totalCount . '" />';
          }
          echo '<div id="ackrediteradeorganResults" class="searchResultSection"><div class="wrapper"><div class="search_result_organ">';

          if (ICL_LANGUAGE_CODE == 'sv') :
               echo '<h2>Ackrediterade organ</h2>';
          elseif (ICL_LANGUAGE_CODE == 'en') :
               echo '<h2>Accreditation register</h2>';
          endif;

          if (!empty($result)) {
               // require_once("wp-content/themes/swedac_theme/integration/karta/karta.php");
               sort($result);
               foreach ($result as $searchItem) {
                    echo '<article class="search_post_organ"> <div class="excerpt">
			   <h3> ' . $searchItem["organisationsnamn"] . '</h3>';
                    foreach ($searchItem[0] as $searchItems) {
                         echo '<div class="name">  <a target="_blank" href=' . $url . $searchItem["accNo"] . "/" . $searchItems->getId() . ' > ' . $searchItems->getName() . ' </a> </div>';
                    }
                    echo "</div> </article>";
               }
               $tal = intval($this->totalCount) - 20;

               if ($tal < 0) {
                    $classAddDisabled = "disabled";
               } else {
                    $classAddDisabled = "";
               }

               if (ICL_LANGUAGE_CODE == 'sv') :
                    $rowCount = count($result);
                    echo '

                <div id="ackrediterade_organ_load_container" class="search_result_buttons ' . $classAddDisabled . '">
                   <small class="smallDocs_total" style="display:block">Visar alla ' . $rowCount . ' träffar</small>

                     <a href="http://search.swedac.se/sv/ackrediteringar?s=' . $this->queryString . '" target="_blank">
                          <span class="avancerad_sokning">
                               <i class="fa fa-external-link" aria-hidden="true"></i> Avancerad sökning
                          </span>
                     </a>
                </div>
           '; ?>

                    <input type="hidden" id="ackrediteringCountResults" value="<?php echo $rowCount; ?><?php if ($rowCount == 1) {
                                                                                                              echo " träff";
                                                                                                         } else {
                                                                                                              echo " träffar";
                                                                                                         } ?>">

               <?php
               elseif (ICL_LANGUAGE_CODE == 'en') :
                    $rowCount = count($result);
                    echo '

                <div id="ackrediterade_organ_load_container" class="search_result_buttons ' . $classAddDisabled . '">
                   <small class="smallDocs_total" style="display:block">Showing all ' . $rowCount . ' results</small>
                   <a href="http://search.swedac.se/sv/ackrediteringar?s=' . $this->queryString . '" target="_blank">
                      <span class="avancerad_sokning">
                           <i class="fa fa-external-link" aria-hidden="true"></i> Advanced search
                      </span>
                   </a>
                </div>
           ';
               ?>

                    <input type="hidden" id="ackrediteringCountResults" value="<?php echo $rowCount; ?><?php if ($rowCount == 1) {
                                                                                                              echo " result";
                                                                                                         } else {
                                                                                                              echo " results";
                                                                                                         } ?>">

               <?php
               endif;
          } else { ?>


               <div class="alert alert-warning">
                    <?php if (ICL_LANGUAGE_CODE == 'sv') : ?>
                         <input type="hidden" id="ackrediteringCountResults" value="0 träffar">
                         <strong>Inget hittades</strong>
                         <em><?php echo sprintf(__('Sökordet '), $wp_query->found_posts);
                              echo '<span class="term">' . get_search_query() . '</span> gav dessvärre ingen träff i sökkällan Ackrediterade organ.'; ?></em>
                    <?php elseif (ICL_LANGUAGE_CODE == 'en') : ?>
                         <input type="hidden" id="ackrediteringCountResults" value="0 results">
                         <strong>Nothing found</strong>
                         <em><?php echo sprintf(__('The keyword  '), $wp_query->found_posts);
                              echo '<span class="term">' . get_search_query() . '</span> ave unfortunately no hit in the search source Accreditation register.'; ?></em>
                    <?php endif; ?>
               </div>

          <?php }

          echo '</div> <!-- //search_result  --> <br class="clear" /> </div> <!-- //wrapper  --> </div> <!-- //searchResultSection  -->';
     }

     /**
      * Bestämmer ifall anropet för stamps skall köras, returnerar den slutgiltiga arrayen med värden, objektifierade så som associativa.
      *
      * @param null
      *
      * @return objektiferad array med associativa värden för bolag och searchitems.
      */



     /**
      * Utför ett api-anrop till Swedac. Returnerar ett JSON-värde på detta.
      *
      * @param cutUrl - Andra delen av URL:en, generell metod för att utföra api-anrop. Param skall ha ett värde av tex :
      *v1/accreditations?q=kontrollbesktining, för att utföra ett anrop för att få ut alla kontrollbesiktningar.
      *
      * @return json-objekt av queryn.
      */
}


class stamp
{

     private $id = "";
     private $stampNo = "";
     private $orgName = "";
     private $adress = "";
     private $zipcode = "";
     private $city = "";
     private $country = "";
     private $href = "";
     private $unRegYear = "";
     private $regYear = "";
     //get
     public function getId()
     {
          return $this->id;
     }
     public function getStampNo()
     {
          return $this->stampNo;
     }

     public function getOrgName()
     {
          return $this->name;
     }
     public function getAdress()
     {
          return $this->adress;
     }

     public function getZipcode()
     {
          return $this->zipcode;
     }

     public function getCity()
     {
          return $this->city;
     }

     public function getCountry()
     {
          return $this->country;
     }

     public function getHref()
     {
          return $this->href;
     }
     public function getRegYear()
     {
          return $this->regYear;
     }
     public function getUnRegYear()
     {
          return $this->unRegYear;
     }


     // set
     public function setId($nyttValue)
     {
          $this->id = $nyttValue;
     }
     public function setStampNo($nyttValue)
     {
          $this->stampNo = $nyttValue;
     }

     public function setOrgName($nyttValue)
     {
          $this->name = $nyttValue;
     }
     public function setAdress($nyttValue)
     {
          $this->adress = $nyttValue;
     }

     public function setZipcode($nyttValue)
     {
          $this->zipcode = $nyttValue;
     }

     public function setCity($nyttValue)
     {
          $this->city = $nyttValue;
     }

     public function setCountry($nyttValue)
     {
          $this->country = $nyttValue;
     }

     public function setHref($nyttValue)
     {
          $this->href = $nyttValue;
     }
     public function setRegYear($nyttValue)
     {
          $this->regYear = $nyttValue;
     }
     public function setUnRegYear($nyttValue)
     {
          $this->unRegYear = $nyttValue;
     }
}


class search_stamps
{

     private $queryString = "";

     public function initiateStamps()
     {
          if ((isset($_GET["namnstampelregistret"])) && ($_GET["namnstampelregistret"] != "")) {
               return $this->main();
          }
     }


     public function initiateAjax($skip, $search)
     {
          $url = "v1/stamps?name=" . $search; //.$skip;
          $apiCall = new apiCall();
          $stamps = $apiCall->get($url);
          if ($stamps != "") {
               return $stamps;
          } else {
               return null;
          }
     } // slut func
     public function printTheStamps($stamps)
     {
          global $wp_query;
          echo '<div id="namnstampelregistretResults" class="searchResultSection"><div class="wrapper"><div class="search_result_namnstampelregistret">'; ?>

          <?php if (ICL_LANGUAGE_CODE == 'sv') : ?>
               <h2>Namnstämpelregistret</h2>
          <?php elseif (ICL_LANGUAGE_CODE == 'en') : ?>
               <h2>Stamps register</h2>
          <?php endif; ?>


          <?php if (!empty($stamps)) { ?>
               <article class="search_post_namnstampel">
                    <div class="excerpt">
                         <div class="table_wrapper">
                              <table id="namnstamplar_table">
                                   <thead>
                                        <tr>
                                             <?php if (ICL_LANGUAGE_CODE == 'sv') : ?>
                                                  <th>Namnstämpel</th>
                                                  <th>Reg.</th>
                                                  <th>Avreg.</th>
                                                  <th>Namn</th>
                                                  <th>Adress</th>
                                             <?php elseif (ICL_LANGUAGE_CODE == 'en') : ?>
                                                  <th>Name mark</th>
                                                  <th>Reg.</th>
                                                  <th>Unreg.</th>
                                                  <th>Name</th>
                                                  <th>Address</th>
                                             <?php endif; ?>
                                        </tr>
                                   </thead>
                                   <tbody>
                                        <?php foreach ($stamps as $stamp) { ?>

                                             <tr class="namnstampel_row">
                                                  <td><?php echo $stamp->getStampNo(); ?></td>
                                                  <td><?php echo $stamp->getRegYear(); ?></td>
                                                  <td><?php echo $stamp->getUnRegYear(); ?></td>
                                                  <td><?php echo $stamp->getOrgName(); ?></td>
                                                  <td>
                                                       <?php if ($stamp->getAdress()) {
                                                            echo $stamp->getAdress() . '<br/>';
                                                       } ?>
                                                       <?php if ($stamp->getZipcode()) {
                                                            echo $stamp->getZipcode() . '<br/>';
                                                       } ?>
                                                       <?php if ($stamp->getCity()) {
                                                            echo $stamp->getCity();
                                                       } ?>
                                                       <?php if ($stamp->getCountry()) {
                                                            echo $stamp->getCountry();
                                                       } ?>

                                                  </td>
                                             </tr>

                                        <?php } ?>
                                   </tbody>
                              </table>
                         </div>
                    </div>
               </article>
               <?php if (ICL_LANGUAGE_CODE == 'sv') : $rowCount = count($stamps); ?>

                    <div class="search_result_buttons">
                         <?php if ($rowCount < 10) { ?>
                              <small class="smallDocs">Visar 1-<?php echo $rowCount; ?> av <?php echo $rowCount; ?> <?php if ($rowCount == 1) {
                                                                                                                             echo " träff";
                                                                                                                        } else {
                                                                                                                             echo " träffar";
                                                                                                                        } ?></small>
                         <?php } else { ?>
                              <small class="smallDocs">Visar 1-10 av <?php echo $rowCount; ?> träffar</small>
                         <?php } ?>
                         <input type="hidden" id="namnstampCountResults" value="<?php echo $rowCount; ?><?php if ($rowCount == 1) {
                                                                                                              echo " träff";
                                                                                                         } else {
                                                                                                              echo " träffar";
                                                                                                         } ?>">
                         <small class="smallDocs_total">Visar alla <?php echo $rowCount; ?> träffar</small>
                         <span id="loadMoreNamnstamplar" class="visa_fler_traffar"><i class="fa fa-spinner fa-spin loading-icon" aria-hidden="true"></i> Visa alla</span>
                         <a href="http://search.swedac.se/sv/namnstamplar?r=1&name=<?php echo $this->queryString; ?>&stampnumber=&zipcode=&city=&mode=all&ob=&ps=" target="_blank">
                              <span class="avancerad_sokning">
                                   <i class="fa fa-external-link" aria-hidden="true"></i> Avancerad sökning
                              </span>
                         </a>
                    </div>
               <?php elseif (ICL_LANGUAGE_CODE == 'en') : $rowCount = count($stamps); ?>
                    <div class="search_result_buttons">
                         <?php if ($rowCount < 10) { ?>
                              <small class="smallDocs">Shows 1-<?php echo $rowCount; ?> of <?php echo $rowCount; ?> <?php if ($rowCount == 1) {
                                                                                                                             echo " result";
                                                                                                                        } else {
                                                                                                                             echo " results";
                                                                                                                        } ?></small>
                         <?php } else { ?>
                              <small class="smallDocs">Shows 1-10 of <?php echo $rowCount; ?> results</small>
                         <?php } ?>
                         <input type="hidden" id="namnstampCountResults" value="<?php echo $rowCount; ?><?php if ($rowCount == 1) {
                                                                                                              echo " result";
                                                                                                         } else {
                                                                                                              echo " results";
                                                                                                         } ?>">
                         <small class="smallDocs_total">Showing all <?php echo $rowCount; ?> results</small>
                         <span id="loadMoreNamnstamplar" class="visa_fler_traffar"><i class="fa fa-spinner fa-spin loading-icon" aria-hidden="true"></i> Show all</span>
                         <a href="http://search.swedac.se/sv/namnstamplar?r=1&name=<?php echo $this->queryString; ?>&stampnumber=&zipcode=&city=&mode=all&ob=&ps=" target="_blank">
                              <span class="avancerad_sokning">
                                   <i class="fa fa-external-link" aria-hidden="true"></i> Advanced search</span>
                         </a>
                    </div>
               <?php endif; ?>
          <?php } else { ?>

               <div class="alert alert-warning">
                    <?php if (ICL_LANGUAGE_CODE == 'sv') : ?>
                         <input type="hidden" id="namnstampCountResults" value="0 träffar">
                         <strong>Inget hittades</strong>
                         <em><?php echo sprintf(__('Sökordet '), $wp_query->found_posts);
                              echo '<span class="term">' . get_search_query() . '</span> gav dessvärre ingen träff i sökkällan Namnstämpelregistret'; ?></em>
                    <?php elseif (ICL_LANGUAGE_CODE == 'en') : ?>
                         <input type="hidden" id="namnstampCountResults" value="0 results">
                         <strong>Nothing found</strong>
                         <em><?php echo sprintf(__('The keyword  '), $wp_query->found_posts);
                              echo '<span class="term">' . get_search_query() . '</span> ave unfortunately no hit in the search source Stamps register '; ?></em>
                    <?php endif; ?>
               </div>

<?php }

          echo ' </div> <!-- //search_result  --> <br class="clear" /> </div> <!-- //wrapper  --> </div> <!-- //searchResultSection  -->';
     }
     public function main()
     {
          $this->queryString = urlencode($_GET["s"]);
          $queryString = urlencode($_GET["s"]);

          $returnArray = wp_cache_get("stamps_" . $_GET["s"]);
          if ($returnArray === false) {
               $url = "v1/stamps?name=" . $queryString . '&count';

               $apiCall = new apiCall();
               $stamps = $apiCall->get($url);

               $stamps = json_decode($stamps, true);
               if (isset($stamps[0]["id"])) {
                    $returnArray = array();
                    foreach ($stamps as $stamp) {
                         $stampItem = new stamp();
                         $stampItem->setId($stamp["id"]);
                         $stampItem->setStampNo($stamp["stampNo"]);
                         $stampItem->setOrgName($stamp["orgName"]);
                         $stampItem->setAdress($stamp["address"]["addressLine1"]);
                         $stampItem->setZipcode($stamp["address"]["zipCode"]);
                         $stampItem->setCity($stamp["address"]["city"]);
                         $stampItem->setCountry($stamp["address"]["country"]);
                         $stampItem->setHref($stamp["_links"][0]["href"]);
                         $stampItem->setUnRegYear($stamp["unregYear"]);
                         $stampItem->setRegYear($stamp["regYear"]);
                         $returnArray[] = $stampItem;
                    } // foreach
                    $myexpire = 60 * 60 * 24; // Cache data for one day (86400 seconds)
                    $group = "stamps";
                    // wp_cache_set("stamps_".$_GET["s"],$returnArray);
               }
          }

          $this->printTheStamps($returnArray);
     } // slut func
} // slut klass
// accrederings ajax
if (isset($_GET["ajaxAcc"])) {

     if (isset($_POST["skip"])) {

          $skip = $_POST["skip"];
     } else {
          echo json_encode("No skip is set.");
          return null;
     }
     if (isset($_POST["search"])) {

          $search = $_POST["search"];
     } else {

          echo json_encode("No Query-word is set");
          return null;
     }
     if (isset($_POST["languageActivator"])) {
          if ($_POST["languageActivator"] == true) {

               $language = true;
               $searchAcc = new search_integrate();
               $result = $searchAcc->initiateAjax($skip, $search, $language);
          }
     } else {
          $language = false;
          $searchAcc = new search_integrate();
          $result = $searchAcc->initiateAjax($skip, $search, $language);
     }



     if ($result != null) {
          header('Content-Type: application/json');
          echo json_encode($result);
     }
}
if (isset($_GET["ajaxStamp"])) {
     if (isset($_POST["skip"])) {
          $skip = $_POST["skip"];
     } else {
          echo json_encode("Fel vid skip");
          return null;
     }
     if (isset($_POST["search"])) {
          $search = $_POST["search"];
     } else {
          echo json_encode("fel vid query-string");
          return null;
     }

     $searchStamps = new search_stamps();
     $result = $searchStamps->initiateAjax($skip, $search);
     if ($result != null) {
          header('Content-Type: application/json');
          echo json_encode($result);
     }
}
