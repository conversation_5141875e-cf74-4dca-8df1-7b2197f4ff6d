<?php
require_once('../../../../wp-load.php');

/**
 * Handles data and functions for each accreditation area
 */
class AccreditationArea
{
    private $title;
    private $areaSubTitles;

    public function __construct($title) {
        $this->title = $title;
    }

    public function getTitle() {
        return $this->title;
    }

    /**
     * Calls Swedac's API and searches for data
     *
     * @param [string] $filter [filters the request]
     * @param [array] $args [wp_remote_get parameters]
     * @return [array]      [Received data from the API call]
     */
    private function callData($filter = 'levels=5', $args = ['timeout' => 10])
    {
        $received = wp_remote_get('https://api.swedac.se/v1/accreditationareas/filter?typeofbusiness=Certifiering%20av%20' . $this->title . '&' . $filter, $args);
        $receivedDecoded = json_decode($received['body'])[0]->values;
        return $receivedDecoded;
    }

    /**
     * Calls Swedac's API and retrieves all certification schemes for each area.
     *
     * @return [array]       [Contains title and description for all certification schemes]
     */
    public function getRemoteAreas() {
        $data = $this->callData();

        return $data;
    }

    public function setAreas($obj) {
        foreach ($obj as $value) {
            $this->areaSubTitles[] = $value->title;
        }
    }

    public function getAreas() {
        return $this->areaSubTitles;
    }

    /**
     * Calls Swedac's API and retrieves specific information for a certification scheme.
     *
     * @param  [string] $areaTitle [The title of the certification scheme]
     * @return [array]            [Contains all titles and descriptions for the specifications applicable to the certification scheme]
     */
    private function getRemoteSpecs($areaTitle) {
        $data = $this->callData('levels=7&filter-level5=' . $areaTitle);

        return $data;
    }

    /**
     * Creates WordPress posts in the specified post_type.
     * Associates the post with the correct category in the specified taxonomy.
     * Saves the specifications for the certification scheme in an ACF repeater field.
     *
     * @param  [array] $areaArray [Contains titles for the certification schemes]
     * @param  [string] $termSlug  [description]
     * @return [bool]            [Returns true if no errors occurred]
     */
    public function insertAreas($areaArray, $termSlug) {
        foreach ($areaArray as $title) {
            $areaSpecs = $this->getRemoteSpecs($title);

            $args = [
                'post_title' => $title,
                'post_status' => 'publish',
                'post_type' => 'kravspecifikationer',
            ];

            // Skapa post
            $postID = wp_insert_post($args);

            if (is_wp_error($postID)) {
                return 'WP Error Posts: ' . $postID->get_error_message();
            }

            $values = [];

            // Loopa igenom array och lägg till data i ACF upprepningsfält
            foreach ($areaSpecs as $areaSpec) {
                $values[] = [
                    "field_5d6e62c2772fa" => $areaSpec->title,
                    "field_5d6e62c9772fb" => $areaSpec->description,
                ];
            }

            update_field('field_5d6e6297772f9', $values, $postID);

            // Koppla post mot rätt kategori
            $termID = term_exists($termSlug, 'kravspec_tax');
            $term = wp_set_post_terms($postID, $termID, 'kravspec_tax');

            if (is_wp_error($term)) {
                return 'WP Error Terms: ' . $term->get_error_message();
            }
        }

        return true;
    }

    /**
     * Deletes all posts and their associated data from the database for the specified post_type.
     *
     * @param  [string] $postType [The name of the post_type from which all posts should be removed]
     * @return [bool]           [Returns true if all posts were deleted]
     * @link https://gist.github.com/jazibsawar/a8c94a7361b47f507a23c2620e69b3c3#file-delete_custom_post_type-php-L4
     */
    public static function removeAreas($postType) {
        global $wpdb;

        $result = $wpdb->query(
           $wpdb->prepare("
               DELETE posts,pt,pm
               FROM wp_posts posts
               LEFT JOIN wp_term_relationships pt ON pt.object_id = posts.ID
               LEFT JOIN wp_postmeta pm ON pm.post_id = posts.ID
               WHERE posts.post_type = %s
               ",
               $postType
           )
       );

        return $result!==false;
    }
}

/**
 * Uses the AccreditationArea class to remove existing posts,
 * fetch data from Swedac's API, and then save the data in WordPress.
 *
 * @return [bool] [Returns true if no errors occurred]
 */
function swedac_get_accreditation_areas()
{
    // Områden - Kategorier
    $certOrgan = [
        'Produkter',
        'Ledningssystem',
        'Personer',
    ];

    // Check if there is a field in the database indicating that posts have been saved from a previous fetch
    $postExists = boolval(get_option('swedac_krav_spec_inserted', false));

    // If there are previous posts fetched, delete these first
    if ($postExists) {
        AccreditationArea::removeAreas('kravspecifikationer');

        update_option('swedac_krav_spec_inserted', false);
    }

    foreach ($certOrgan as $certOrg) {
        $accAreaObj = new AccreditationArea($certOrg);

        $accAreaRemote = $accAreaObj->getRemoteAreas();

        $accAreaObj->setAreas($accAreaRemote);

        $accAreaObj->insertAreas($accAreaObj->getAreas(), strtolower($certOrg));
    }

    // Add field in wp_options to indicate that the posts have been saved in the database
    update_option('swedac_krav_spec_inserted', true, null);

    return true;
}

// Runs when the file is called directly via the browser, this will later be handled by a cron script
swedac_get_accreditation_areas();
