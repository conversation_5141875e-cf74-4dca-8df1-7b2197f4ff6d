<?php
// #1: Add CLI check at the very top
// Ensure this script is run from the command line and not via a web browser
if (php_sapi_name() !== 'cli' && php_sapi_name() !== 'cgi-fcgi') { // 'cgi-fcgi' can sometimes be used by cron
    // Send a 403 Forbidden header (important for web browsers/crawlers)
    header('HTTP/1.0 403 Forbidden');
    // Output a message (optional, but good practice)
    // You can customize this message or make it more generic
    exit('This script is designed for command-line execution only and cannot be accessed via a web browser.');
}

// If we're here, it's a CLI execution.

// IMPORTANT for wp-load.php in CLI:
// wp-load.php needs ABSPATH to be defined.
// Determine the WordPress root path based on the script's location.
// Adjust this path if your script is located elsewhere relative to the WP root.
// Assuming this script is in /wp-content/plugins/your-plugin-name/cron-scripts/your-script.php
// Then '../../../../' goes up to the WordPress root.
if ( ! defined( 'ABSPATH' ) ) {
    define( 'ABSPATH', __DIR__ . '/../../../../' ); // Adjust if your script's path depth is different
}

require_once ABSPATH . 'wp-load.php';

function documentImportAfterSetExceptionError($error)
{
    date_default_timezone_set('Europe/Stockholm');
    $date = date('Y-m-d H:i:s');
    error_log($date . " " . strip_tags($error) . "\n", 3, __DIR__ . "/../../../documents_debug.log");
}

function documentImportAfterSetDebug($string) {
    date_default_timezone_set('Europe/Stockholm');
    $date = date('Y-m-d H:i:s');
    error_log($date . " " . $string . "\n", 3, __DIR__ . "/../../../documents_debug.log");
}

try {

    // Checks if the document import was successful
    $today = date('Y-m-d');
    $last_successful_import = get_option('documents_last_successful_import');
    if ($last_successful_import != $today) {
        // the import was not successful
        // send an email to the admin
        $to = '<EMAIL>, <EMAIL>';
        $subject = 'Swedac - Document import failed (' . $today . ')';
        $message = 'Hello, the document import on Swedac website failed. <br><br>Please check this URL: <br>' . get_site_url() . '/?fritext=&s=&document_type=&site_section=document_pt&post_type=dokument';
        $headers = array('Content-Type: text/html; charset=UTF-8');

        if (wp_mail($to, $subject, $message, $headers)) {
            documentImportAfterSetDebug("Failure notification email sent to: " . $to);
        } else {
            documentImportAfterSetDebug("Failed to send failure notification email to: " . $to);
        }
    }

} catch (ErrorException $e) {
    documentImportAfterSetExceptionError($e);
}

// Exit with 0 for success for cron
exit(0);