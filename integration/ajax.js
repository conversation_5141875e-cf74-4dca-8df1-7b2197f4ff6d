
// jQuery("#loadMoreNamnstamplar" ).click(function() {
//
// var skipElement = jQuery("#valueOfSkipStamp");
// skip = skipElement.attr("value");
// var search = jQuery("#searchString").attr("value");
// var number = parseInt(skip);
// var skipNewValue = number+20;
// skipElement.attr("value",skipNewValue);
//
// jQuery.ajax({
//
// 		timeout:15000,
// 		type:"post",
// 		data: {
// 			skip,
// 			search,
// 		},
// 		dataType: "json",
// 		url:"wp-content/themes/swedac_theme/integration/search_integrate.php?ajaxStamp",
//
// 		success: function(ajaxReturnData){
//
// 			var obj = jQuery.parseJSON(ajaxReturnData);
// 			$.each( obj, function() {
//
// 				if(this.unregYear!=null){
//
// 					unregYear = this.unregYear;
// 				}
// 				else{
//
// 					unregYear = "Null";
// 				}
//
// 				$("#namnstamplar_table").append('<tr class="namnstampel_row"><td>'+this.stampNo+'</td><td>'+this.regYear+'</td> <td>'+unregYear+'</td><td>'+this.orgName+'</td><td>'+this.address.addressLine1+'<br/> '+this.address.zipCode+'<br/> '+this.address.city+' '+this.address.country+'</td>');
//
//
// 			});
//
//
//
//
// 		},
// 		error : function(xhr,status,error){
//
// 			window.alert(xhr.statusText+ " : " + status +  " : " +error);
// 		},
// 		complete: function(xhr,status){
//
// 		}
// 	});
// });

function isJson(str) {
    try {
         JSON.parse(str);
    } catch (e) {
        return false;
    }
    return true;
}
function parseSecond(val) {
    var result = null,
        tmp = [];
    var items = location.search.substr(1).split("&");
    for (var index = 0; index < items.length; index++) {
        tmp = items[index].split("=");
        if (tmp[0] === val) result = decodeURIComponent(tmp[1]);
    }
    return result;
}

jQuery("#loadMoreOrgan" ).click(function() {

var queryStrings = parseSecond("lang");
if(queryStrings==="en"){
var languageActivator = true;
}
var skipElement = jQuery("#valueOfSkip");
skip = skipElement.attr("value");

var totalCount = jQuery("#totalCountAccred");
var placeholder = totalCount.attr("value");
var placeholder = parseInt(placeholder);
placeholder = placeholder-20;
totalCount.attr("value",placeholder.toString());
if(placeholder<0){

	jQuery("#loadMoreOrgan").addClass("disabled");
}


var search = jQuery("#searchString").attr("value");
var number = parseInt(skip);
var skipNewValue = number+20;
skipElement.attr("value",skipNewValue);
jQuery.ajax({

		timeout:15000,
		type:"post",
		data: {
			skip,
			search,
			languageActivator,
		},
		dataType: "json",
		url:"wp-content/themes/swedac_theme/integration/search_integrate.php?ajaxAcc",

		success: function(ajaxReturnData){

			console.log(languageActivator);
			if(languageActivator===true)
			{
				var hrefUrl = "http://search.swedac.se/en/accreditations/";
			}
			else{
				var hrefUrl = "http://search.swedac.se/sv/ackrediteringar/";
			}

			 var obj = jQuery.parseJSON(ajaxReturnData);
			var target = $("#ackrediterade_organ_load_container");

				$.each( obj, function() {
                	var nameItem = "";
                	var accNo = this.accNo;
                	$.each(this.officeSummaries,function(){
                		nameItem +="<div class='name'><a target='_blank' href='"+hrefUrl+accNo+"/"+this.id+"'>"+this.name+"</a></div>";
                	});
				target.before("<article class='search_post_organ'> <div class='excerpt'>  <h3>"+this.orgName+"</h3> "+nameItem+"</div></article>");

				});




		},
		error : function(xhr,status,error){

			window.alert("Något gick fel. Försök igen, om problemet kvarstår så vänligen avvakta och försök igen senare.");
		},
		complete: function(xhr,status){

		}
	});
});

function isJson(str) {
    try {
         JSON.parse(str);
    } catch (e) {
        return false;
    }
    return true;
}
