
# Swedac
Swedacs wordpress theme

## Environment Variables
To run this project, you will need to add the following environment variables to your wp-config file
`API_DOCUMENTATION_INTEGRATION_DEFAULT_URL`

You can set it as either
`https://api-test.swedac.se/` or `https://api.swedac.se/`

## Development
1. Download the repo
2. Run `npm install`
3. Run `composer install`
4. Configure BrowserSync in `gulpfile.js` according to your local environment
5. Run `npm run watch` to start developing

## Compliling for production
1. Run `npm run build`

## Doployment
Automatic deployment: upload and building.
