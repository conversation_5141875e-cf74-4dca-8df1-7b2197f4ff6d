<?php
/**
 * Template Name: Template 8 - Föreskrifter & Dokument
 */
?>

<div id="sok_foreskriver_dokument">
	<div class="wrapper">
		<div class="title">
			<img alt="Sök" data-src="<?php echo get_template_directory_uri(); ?>/assets/images/icon-search.png">

			<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
			<span>
				<h1>Sök föreskrifter & dokument</h1>
			</span>
			<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
			<span>
				<h1>Search regulations and documents</h1>
			</span>
			<?php endif;?>
		</div>

		<form id="searchform_foreskrifter_dokument" action="<?php echo get_site_url(); ?>" method="get">
			<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
			<div class="row">
				<input type="search" class="search-field"
					placeholder="<?php if(isset($fritext)) {echo "Du sökte på ". $fritext;} else {echo "Fritext";} ?>" value=""
					name="fritext" title="<?php if(isset($fritext)) {echo "Du sökte på ". $fritext;} else {echo "Fritext";} ?>">
			</div>

			<div class="row">
				<input type="text" class="search-field"
					placeholder="<?php if( isset($doc_nr)) {echo "Du sökte på ". $doc_nr;} else {echo "Dokumentnummer";} ?>" name="s"
					title="<?php if(isset($doc_nr)) {echo "Du sökte på ". $doc_nr;} else {echo "Dokumentnummer";} ?>">
			</div>
			<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
			<div class="row">
				<input type="search" class="search-field"
					placeholder="<?php if(isset($fritext)) {echo "You searched for ". $fritext;} else {echo "Text search";} ?>" value=""
					name="fritext" title="<?php if(isset($fritext)) {echo "You searched for ". $fritext;} else {echo "Text search";} ?>">
			</div>

			<div class="row">
				<input type="text" class="search-field"
					placeholder="<?php if(isset($doc_nr)) {echo "You searched for ". $doc_nr;} else {echo "Document number";} ?>"
					name="s" title="<?php if(isset($doc_nr)) {echo "You searched for ". $doc_nr;} else {echo "Document number";} ?>">
			</div>
			<?php endif;?>

			<div class="row">
				<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
				<?php $all_omraden = array(
						'post_type' 		=> 'amnesomraden',
						'posts_per_page' 	=> -1,
						'orderby'			=> 'title',
						'order' 			=> 'ASC'
					);

					$query_all = new WP_Query($all_omraden);
					if($query_all->have_posts()): ?>
				<label class="label_select" for="all_omraden_select">Område</label>
				<select id="all_omraden_select" name="omraden">
					<option selected disabled>Välj område</option>
					<?php while($query_all->have_posts()):$query_all->the_post(); ?>
					<option value="<?php the_title(); ?>"><?php the_title(); ?></option>
					<?php endwhile; ?>
				</select>
				<?php else: ?>

				<label class="label_select" for="all_omraden_select">Område</label>
				<select id="all_omraden_select" name="omraden">
					<option selected disabled>Inga områden finns</option>
					<?php while($query_all->have_posts()):$query_all->the_post(); ?>
					<option value="<?php the_title(); ?>"><?php the_title(); ?></option>
					<?php endwhile; ?>
				</select>
				<?php endif; wp_reset_query(); ?>
				<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
				<?php $all_omraden = array(
						'post_type' 		=> 'amnesomraden',
						'posts_per_page' 	=> -1,
						'orderby'			=> 'title',
						'order' 			=> 'ASC'
					);

					$query_all = new WP_Query($all_omraden);
					if($query_all->have_posts()): ?>
				<label class="label_select" for="all_omraden_select">Area</label>
				<select id="all_omraden_select" name="omraden">
					<option selected disabled>Choose area</option>
					<?php while($query_all->have_posts()):$query_all->the_post(); ?>
					<option value="<?php the_title(); ?>"><?php the_title(); ?></option>
					<?php endwhile; ?>
				</select>
				<?php else: ?>
				<label class="label_select" for="all_omraden_select">Area</label>
				<select id="all_omraden_select" name="omraden">
					<option selected disabled>No areas available</option>
					<?php while($query_all->have_posts()):$query_all->the_post(); ?>
					<option value="<?php the_title(); ?>"><?php the_title(); ?></option>
					<?php endwhile; ?>
				</select>
				<?php endif; wp_reset_query(); ?>
				<?php endif;?>
			</div>

			<div class="row">
				<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
				<label class="label_select" for="typ_av_dokument">Dokumenttyp</label>
				<select id="typ_av_dokument" name="document_type">
					<option value="" selected>Alla typer av dokument</option>
					<option value="stafs">STAFS (Gällande föreskrift)</option>
					<option value="stafs-upphavd">STAFS (Upphävd föreskrift)</option>
					<option value="doc">DOC (Väglednings-/tolkningsdokument)</option>
					<option value="rep">REP (Rapport)</option>
					<option value="info">INFO (Informationsmaterial)</option>
				</select>
				<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
				<label class="label_select" for="typ_av_dokument">Type of document</label>
				<select id="typ_av_dokument" name="document_type">
					<option value="" selected>All types of documents</option>
					<option value="stafs">STAFS (Current regulation)</option>
					<option value="stafs-upphavd">STAFS (Repealed regulation)</option>
					<option value="doc">DOC (Guidance document)</option>
					<option value="rep">REP (Report)</option>
					<option value="info">INFO (Information material)</option>
				</select>
				<?php endif;?>
			</div>

			<input name="site_section" type="hidden" value="document_pt" />

			<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
			<input type="hidden" name="lang" value="sv">
			<input type="hidden" name="post_type" value="dokument" />
			<input type="submit" class="search-submit" value="Sök">
			<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
			<input type="hidden" name="lang" value="en">
			<input type="hidden" name="post_type" value="document" />
			<input type="submit" class="search-submit" value="Search">
			<?php endif;?>
		</form>
	</div>
</div>

<div class="wrapper">
	<div class="senaste_dokument">
		<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
		<h3>Senaste publicerade dokument</h3>
		<?php $paged = get_query_var( 'paged' ) ? get_query_var( 'paged' ) : 1;
			$args = array(
				'post_status' 		=> 'publish',
				'post_type' 		=> 'dokument',
				'paged' 			=> $paged,
				'posts_per_page' 	=> 10,
				'orderby' 			=> 'date',
				'meta_query' => array (
				    array(
				        'key' => 'crm_doc_doc',
				        'value' => '.pdf',
				        'compare' => 'LIKE'
				    )
				)
			);

			$query = new WP_Query($args);
			if($query->have_posts()): ?>
		<div class="table_wrapper">
			<table>
				<thead>
					<tr>
						<th>DOK.NR</th>
						<th>RUBRIK</th>
						<th>TYP AV DOKUMENT</th>
						<th>DATUM</th>
					</tr>
				</thead>
				<tbody>
					<?php while($query->have_posts()):$query->the_post(); ?>
					<tr>
						<td><?php echo get_field('crm_doc_dokumentbeteckning'); ?></td>
						<td><a href="<?php the_permalink(); ?>" title="<?php the_title(); ?>"><?php the_title(); ?></a></td>
						<td><?php echo get_field('crm_doc_type'); ?></td>
						<td><?php the_time('Y-m-d'); ?></td>
					</tr>
					<?php endwhile; ?>
				</tbody>
			</table>
		</div>
		<?php if(function_exists('easy_wp_pagenavigation')) { 
			echo easy_wp_pagenavigation( $query ); 
		} ?>
		<?php endif; wp_reset_postdata(); ?>
		<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
		<h3>Latest published documents</h3>
		<?php $paged = get_query_var( 'paged' ) ? get_query_var( 'paged' ) : 1;
			$args = array(
				'post_status' 		=> 'publish',
				'post_type' 		=> 'document_eng',
				'paged' 			=> $paged,
				'posts_per_page' 	=> 10,
				'orderby' 			=> 'date',
				'meta_query' => array (
				    array(
				        'key' => 'crm_doc_doc_en',
				        'value' => '.pdf',
				        'compare' => 'LIKE'
				    )
				)
			);

			$query = new WP_Query($args);
			if($query->have_posts()): ?>
		<div class="table_wrapper">
			<table>
				<thead>
					<tr>
						<th>DOC.NR</th>
						<th>TITLE</th>
						<th>TYPE OF DOCUMENT</th>
						<th>DATE</th>
					</tr>
				</thead>
				<tbody>
					<?php while( $query->have_posts() ) : $query->the_post(); ?>
					<tr>
						<td><?php echo get_field('crm_doc_dokumentbeteckning_en'); ?></td>
						<td><a href="<?php the_permalink(); ?>" title="<?php the_title(); ?>"><?php the_title(); ?></a></td>
						<td><?php echo get_field('crm_doc_type_en'); ?></td>
						<td><?php the_time('Y-m-d'); ?></td>
					</tr>
					<?php endwhile; ?>
				</tbody>
			</table>
		</div>
		<?php echo easy_wp_pagenavigation( $query ); ?>
		<?php endif; wp_reset_postdata(); ?>
		<?php endif;?>
	</div>

	<div class="dokument_genvagar">
		<?php if(get_field('foreskrifter_dokument_genvagar')): ?>
		<h3>Genvägar</h3>
		<ul>
			<?php while(has_sub_field('foreskrifter_dokument_genvagar')): ?>
			<li>
				<a href="<?php echo get_sub_field('lankmal'); ?>" title=""><?php echo get_sub_field('lanknamn'); ?></a>
			</li>
			<?php endwhile; ?>
		</ul>
		<?php endif; ?>
	</div>
</div>