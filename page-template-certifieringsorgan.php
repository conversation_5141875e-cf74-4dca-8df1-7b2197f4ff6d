<?php
/**
 * Template Name: Certifieringsorgan
 */

while (have_posts()) : the_post();
	?>
	<div id="page-header-parent">
		<div class="title">
			<div class="wrapper">
				<?php
				function wps_parent_post ()
				{
					global $post;

					if ($post->post_parent) {
						$ancestors = get_post_ancestors($post->ID);
						$root = count($ancestors) - 1;
						$parent = $ancestors[$root];
					} elseif ($post->post_ancestors) {
						$ancestors = get_post_ancestors($post->ID);
						$root = count($ancestors) - 1;
						$parent = $ancestors[$root];
					} else {
						$parent = $post->ID;
					}

					if ($post->ID != $parent) {
						echo '<a href="'.get_permalink($parent).'" class="parent-post">' . get_page(array_pop(get_post_ancestors($post->ID)))->post_title . '</a>';
					}
				}
				?>

				<span class="title-page">
					<?php wps_parent_post(); ?>
				</span>
			</div>
		</div>
		<?php cc_breadcrumb_renderer(true); ?>
	</div>

	<?php
	get_template_part('templates/page', 'header');
	?>
	<div class="wrapper">
		<div class="page-content">
			<?php get_template_part('templates/content', 'page-certifieringsorgan'); ?>
		</div>
	</div>
	<?php 
endwhile;
