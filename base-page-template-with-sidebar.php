<?php

use Roots\Sage\Setup;
use Roots\Sage\Wrapper;

?>
<!doctype html>
<html <?php language_attributes(); ?>>
    <?php get_template_part('templates/head'); ?>
    <body <?php body_class(); ?>>
        <!--[if IE]>
	        <div class="alert-ie-appeared">
	            <?php _e('Du använder <strong>Internet Explorer</strong> som webbläsare. Internet Explorer har från och med januari 2016 slutat få säkerhetsuppdateringar utav Microsoft Corporation. Så för att uppnå den bästa upplevelsen av vår webbplats, var god uppdatera till en annan <a href="http://browsehappy.com/" target="_blank">webbläsare</a>.'); ?>
	        </div>
    	<![endif]-->

	    <div id="main_wrapper">
	        <div class="" role="document">
	            <?php do_action('get_header'); get_template_part('templates/header');?>
				
				<section>
		            <div id="page-header-parent">
						<div class="title">
							<div class="wrapper">
								<?php
								function wps_parent_post()
								{
									global $post;

									if ($post->post_parent){
										$ancestors=get_post_ancestors($post->ID);
										$root=count($ancestors)-1;
										$parent = $ancestors[$root];
									} elseif ($post->post_ancestors){
										$ancestors=get_post_ancestors($post->ID);
										$root=count($ancestors)-1;
										$parent = $ancestors[$root];
									} else {
										$parent = $post->ID;
									}

									if ($post->ID != $parent) {
										$post_ancestors = get_post_ancestors($post->ID);
										$parent_post_id = (is_array($post_ancestors) && !empty($post_ancestors)) ? array_pop($post_ancestors) : false;
										if ($parent_post_id) {
											echo get_page($parent_post_id)->post_title;
										}
									}
								}
								?>
								<span class="title-page">
									<?php wps_parent_post(); ?>
								</span>

							</div>
						</div>
						<?php cc_breadcrumb_renderer(true); ?>
					</div>

					<div class="wrapper">
		           		<main class="content row">
			           	    <div class="main">
			           	        <?php include Wrapper\template_path(); ?>
			           	    </div>

			           	    <?php if (Setup\display_sidebar()) : ?>
			           	        <div class="sidebar">
			           	            <?php include Wrapper\sidebar_path(); ?>
			           	        </div>
			           	    <?php endif; ?>
			           	</main>
		           	</div>
				</section>
				<?php
				if(is_page(10416) || is_page(9770)){
					?>
					<div id="sok_certifikat">
						<div class="wrapper">
							<div class="title">
								<img alt="Sök" data-src="<?php echo get_template_directory_uri(); ?>/assets/images/icon-search.png">
								<span>
									<h2><?= __('Sök Certifikat och Godkännanden', 'certificates')?></h2>
								</span>
							</div>
							<form id="searchform_certifikat" action="<?= get_site_url();?>" method="get">
								<div class="row">
									<label><?= __('Fritext', 'certificates')?></label>
									<input type="search" class="search-field" value="" name="fritext" title="<?= __('Fritext', 'certificates')?>">
								</div>
								<div class="row">
									<label><?= __('Certifikatnummer', 'certificates')?></label>
									<input type="text" class="search-field" name="s" title="<? __('Certifikatnummer', 'certificates')?>">
								</div>
								<div class="row">
									<?php $all_omraden = array(
										'post_type' 		=> 'amnesomraden',
										'posts_per_page' 	=> -1,
										'orderby'			=> 'title',
										'order' 			=> 'ASC',
										'tax_query'         => array(
											array(
												'taxonomy' 	=> 'amnesomraden_categories',
												'field' 	=> 'slug',
												'terms' 	=> 'reglerad-matteknik'
											)
										)
									);
									$query_all = new WP_Query($all_omraden);
									// var_dump($query_all);
									if ($query_all->have_posts()) : ?>
										<label class="label_select" for="all_omraden_select"><?= __('Område', 'certificates'); ?></label>
										<select id="all_omraden_select" name="omraden">
											<option selected disabled><?= __('Välj område', 'certificates')?></option>
											<?php while ($query_all->have_posts()) : $query_all->the_post(); ?>
												<option value="<?php the_ID(); ?>"><?php the_title(); ?></option>
											<?php endwhile; ?>
										</select>
									<?php endif;
									wp_reset_query(); ?>


								</div>
								<div class="row">
									<label class="label_select" for="typ_av_certifikat"><?= __('Certifikattyp', 'certificates'); ?></label>
									<select id="typ_av_certifikat" name="certifikat_type">
										<option value="" selected><?= __('Alla typer av certifikat', 'certificates'); ?></option>
										<option value="cottt"><?= __('Certifikat om tillsatsanordningar till taxametrar', 'certificates'); ?></option>
										<option value="cumtawg"><?= __('Certifikat utfärdade med stöd av WELMEC Guide 8.8', 'certificates'); ?></option>
										<option value="eoemk"><?= __('EU-typintyg och EU-intyg om konstruktionskontroll (MID 2014/32/EU)', 'certificates'); ?></option>
										<option value="gakmd"><?= __('Godkännanden av kvalitetssystem, modul D', 'certificates'); ?></option>
									</select>
								</div>
								<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
									<input name="site_section" type="hidden" value="certificate_pt" />
									<input type="hidden" name="lang" value="sv">
									<input type="hidden" name="post_type" value="certifikat" />
									<input type="submit" class="search-submit" value="Sök">
								<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
									<input name="site_section" type="hidden" value="certificate_pt" />
									<input type="hidden" name="lang" value="en">
									<input type="hidden" name="post_type" value="certifikat" />
									<input type="submit" class="search-submit" value="Search">
								<?php endif;?>
							</form>
						</div>
						</div>

						<div class="wrapper">
						<div class="senaste_dokument">
							<h3><?= __('Senaste publicerade dokument', 'certificates'); ?></h3>
							<?php
							$args = array(
								'post_status' 		=> 'publish',
								'post_type' 		=> 'certifikat',
								'paged' 			=> $paged,
								'posts_per_page' 	=> 10,
								'orderby' 			=> 'date',
								'meta_query' => array(
									array(
										'key' => 'crm_cert_doc',
										'value' => '.pdf',
										'compare' => 'LIKE'
									)
								)
							);

							$query = new WP_Query($args);
							if ($query->have_posts()) : ?>
								<div class="table_wrapper">
									<table>
										<thead>
											<tr>
												<th><?=  __('DOK.NR', 'certificates') ?></th>
												<th><?=  __('RUBRIK', 'certificates') ?></th>
												<th><?=  __('TYP AV DOKUMENT', 'certificates') ?></th>
												<th><?=  __('DATUM', 'certificates') ?></th>
											</tr>
										</thead>
										<tbody>
											<?php while ($query->have_posts()) : $query->the_post(); ?>
												<tr>
													<td><?php echo get_field('crm_cert_dokumentbeteckning'); ?></td>
													<td><a href="<?php the_permalink(); ?>" title="<?php the_title(); ?>"><?php the_title(); ?></a></td>
													<td><?php echo get_field('crm_cert_type'); ?></td>
													<td><?php the_time('Y-m-d'); ?></td>
												</tr>
											<?php endwhile; ?>
										</tbody>
									</table>
								</div>
							<?php endif;
							wp_reset_postdata(); ?>
						</div>
					</div>
					<?php
				}
				?>														   
	            <?php do_action('get_footer');  get_template_part('templates/footer'); wp_footer(); ?>
	        	<br class="clear">
	        </div>
	    </div>

    </body>
</html>
