<?php
/**
 * Sage includes
 *
 * The $sage_includes array determines the code library included in your theme.
 * Add or remove files to the array as needed. Supports child theme overrides.
 *
 * Please note that missing files will produce a fatal error.
 *
 * @link https://github.com/roots/sage/pull/1042
 */

// Add to wp-config.php (outside public_html if possible)
define('FRIENDLY_CAPTCHA_API_KEY', 'A14K405OMN814AT5EOSEREEN107MAI7UNNK6N2S4FGKSJ8SNL317I4D9K5');
define('FRIENDLY_CAPTCHA_SITEKEY', 'FCMHCLG9N0S4NF1M');


$sage_includes = [
  'lib/acf-fields.php',// Theme actions
  'lib/actions.php',   // Theme actions
  'lib/assets.php',    // Scripts and stylesheets
  'lib/extras.php',    // Custom functions
  'lib/setup.php',     // Theme setup
  'lib/titles.php',    // Page titles
  'lib/wrapper.php',   // Theme wrapper class
  'lib/customizer.php', // Theme customizer
  'lib/shortcodes.php', // Custom shortcodes (2020)
  'lib/friendly-captcha.php' // Friendly Captcha (2025)
];

foreach ($sage_includes as $file) {
  if (!$filepath = locate_template($file)) {
    trigger_error(sprintf(__('Error locating %s for inclusion', 'sage'), $file), E_USER_ERROR);
  }

  require_once $filepath;
}
unset($file, $filepath);


// function attachment_search( $query ) {
//     if ( $query->is_search ) {
//        $query->set( 'post_type', array( 'attachment' ) );
//        $query->set( 'post_status', array( 'publish', 'inherit' ) );
//        $query->set( 'post_mime_type', array( 'application/pdf' ) );
//     }
//    return $query;
// }
// add_filter( 'pre_get_posts', 'attachment_search' );

add_action('admin_menu', 'cc_add_custom_menu_pages');
/**
 * Lägg till egenbyggda sidor i wp admin menyn.
 */
function cc_add_custom_menu_pages()
{
    add_menu_page(
        'Sökresultat - Log',
        'Söklogg',
        'manage_options',
        'custom-search-log',
        'custom_search_log',
        'dashicons-search',
    );

    add_menu_page(
        'Hantera prenumeranter',
        'Prenumeranter',
        'administrator',
        'manage-subscribers',
        'cc_manage_subscribers_admin_page',
    );
}

global $wpdb;

if (isset($_GET['change_row'])) { // Uppdaterar rad i databsen
    $table = 'wp_serach_log_forslag';
    $lang = '';
    if (ICL_LANGUAGE_CODE == 'en') {
        $table = 'wp_serach_log_forslag_en';
        $lang = '&lang=en';
    }

    $input_forslag = utf8_decode($_POST['input_forslag']);
    $row_id = $_POST['row_id'];

    $values = array();

    //Loop through added fields
    foreach ($_POST['fields'] as $key => $value) {
        $value = utf8_decode($value);
        $values[] = "('{$row_id}', '{$value}')";
    }

    $joinedValues = implode(',', $values);

    $sql = "INSERT INTO $table (row_id, forslag) VALUES $joinedValues";
    $wpdb->query($sql);

    header("Location: /wp-admin/admin.php?page=custom-search-log$lang&success_changed_row");
    exit;
}

if (isset($_GET['delete_row'])) { // Tar bort rad från databasen
    $row_id = $_POST['row_id'];

    $table = 'wp_serach_log';
    $lang = '';
    if (ICL_LANGUAGE_CODE == 'en') {
        $table = 'wp_serach_log_en';
        $lang = '&lang=en';
    }

    $wpdb->query($wpdb->prepare("DELETE FROM $table WHERE id = %s", $row_id));

    header("Location: /wp-admin/admin.php?page=custom-search-log$lang&success_deleted_row");
    exit;
}

if (isset($_GET['delete_forslag'])) { // Tar bort förslag från databasen
    $mainId = $_POST['mainId'];
    $forslagId = $_POST['forslagId'];

    $table = 'wp_serach_log_forslag';
    $lang = '';
    if (ICL_LANGUAGE_CODE == 'en') {
        $table = 'wp_serach_log_forslag_en';
        $lang = '&lang=en';
    }

    $prepared = $wpdb->prepare("DELETE FROM $table WHERE id = %s AND row_id = %s", $mainId, $forslagId);
    $wpdb->query($prepared);

    header("Location: /wp-admin/admin.php?page=custom-search-log$lang&success_deleted_forslag");
    exit;
}

function custom_search_log() {
    global $wpdb;

    if(isset($_GET['success_changed_row'])) {
        echo "
            <div class='message_box'>
                <div class='inner_box success'>
                    <p><i class='fa fa-check' aria-hidden='true'></i> Raden har blivit uppdaterad i databasen.</p>
                </div>
            </div>
        ";
    }

    if(isset($_GET['success_deleted_row'])) {
        echo "
            <div class='message_box'>
                <div class='inner_box success'>
                    <p><i class='fa fa-check' aria-hidden='true'></i> Den valda raden har tagits bort från databasen.</p>
                </div>
            </div>
        ";
    }

    if(isset($_GET['success_deleted_forslag'])) {
        echo "
            <div class='message_box'>
                <div class='inner_box success'>
                    <p><i class='fa fa-check' aria-hidden='true'></i> Det valda förslaget har tagits bort från databasen.</p>
                </div>
            </div>
        ";
    }
?>

<div id="custom_search_log" class="wrap">
    <div class="custom_search_log_inner">

        <?php if(ICL_LANGUAGE_CODE=='sv') { ?>
            <h1>Sökresultat - Logg (Svenska)</h1>
        <?php } else { ?>
            <h1>Sökresultat - Logg (Engelska)</h1>
        <?php } ?>

        <p>Här nedan listas alla sökningar som har gjorts i sökfunktionen på swedac.se där man har använt sökkällan
            <strong>Swedac.se</strong>.</p>

        <p>Ange sökförslag som är baserat på söktermen. För att lägga till en synonym så klickar ni på knappen
            "<strong>Lägg till synonym</strong>" och sedan klickar ni på "Spara".<br />Flera synonymer kan läggas till
            på samma gång genom att klicka upprepade gånger på "<strong>Lägg till synonym</strong>"</p>

        <ul id="ul_header">
            <li class="row_1">Träffar</li>
            <li class="row_2">Sökterm</li>
            <li class="row_3">Antalet sökningar</li>
            <li class="row_4">Förslag</li>
            <li class="row_5">URL (Öppnas i ny flik)</li>
            <li class="row_6">Ta bort</li>
        </ul>

        <ul id="ul_body">

            <?php
            $pagenum = isset( $_GET['pagenum'] ) ? absint( $_GET['pagenum'] ) : 1;

            $limit = 300; // number of rows in page
            $offset = ( $pagenum - 1 ) * $limit;

            if(ICL_LANGUAGE_CODE=='sv') {
                $total = $wpdb->get_var( "SELECT COUNT(`id`) FROM wp_serach_log" );
            } else {
                $total = $wpdb->get_var( "SELECT COUNT(`id`) FROM wp_serach_log_en" );
            }

            $num_of_pages = ceil( $total / $limit );

            if(ICL_LANGUAGE_CODE=='sv') {
                $entries = $wpdb->get_results( "SELECT * FROM wp_serach_log ORDER BY antal_sokningar DESC LIMIT $offset, $limit");
            } else {
                $entries = $wpdb->get_results( "SELECT * FROM wp_serach_log_en ORDER BY antal_sokningar DESC LIMIT $offset, $limit");
            }

            foreach ( $entries as $item ) {
                            $traffar            = $item->traffar;
                            $id                 = $item->id;
                            $term               = $item->term;
                            $antal_sokningar    = $item->antal_sokningar;
                            $forlsag            = $item->forslag;
                            $org_url            = $item->url;

                            $traffar = intval($traffar);

                            //Hämta förslag
                            if(ICL_LANGUAGE_CODE=='sv') {
                                $search_log_forslag = $wpdb->get_results ( "SELECT * FROM wp_serach_log_forslag WHERE row_id = '$id'" );
                            } else {
                                $search_log_forslag = $wpdb->get_results ( "SELECT * FROM wp_serach_log_forslag_en WHERE row_id = '$id'" );
                            }

                            if($traffar == 0) { $noResults = "noResults"; } else {$noResults = "";}


                        ?>
            <div class="content <?php echo $noResults; ?>">
                <li class="row_1"><?php echo $traffar; ?> st</li>
                <li class="row_2"><?php echo $term; ?></li>
                <li class="row_3"><?php echo $antal_sokningar; ?> st</li>
                <li class="row_4">
                    <?php foreach ( $search_log_forslag as $forslag ) {
                                        $mainId         = $forslag->id;
                                        $forslagId      = $forslag->row_id;
                                        $forslag        = $forslag->forslag;

                                        // var_dump($search_log_forslag);
                                    ?>
                    <form class="forslag_form" action="/wp-admin/admin.php?page=custom-search-log&delete_forslag"
                        method="post">
                        <span class="forslag"><?php echo $forslag; ?></span>
                        <input type="hidden" name="mainId" value="<?php echo $mainId; ?>">
                        <input type="hidden" name="forslagId" value="<?php echo $forslagId; ?>">
                        <button type="submit"><i class="fa fa-times" aria-hidden="true"></i></button>
                    </form>
                    <?php } ?>
                    <form action="/wp-admin/admin.php?page=custom-search-log&change_row" class="change_row_form"
                        method="post">
                        <!-- <input class="input" type="text" name="input_forslag" placeholder="Ange sökförslag" value="<?php echo $forlsag; ?>">
                                    <input class="submit" type="submit" value="Spara"> -->

                        <div class="addNewBox" data-newRow="<?php echo $i; ?>">
                            <span class="add_row" data-newButton="<?php echo $i; ?>">Lägg till synonym <i
                                    class="fa fa-plus" aria-hidden="true"></i></span>
                        </div>
                        <input class="submit" type="submit" value="Spara">
                        <input type="hidden" name="row_id" value="<?php echo $id; ?>">
                    </form>
                </li>
                <li class="row_5"><a href="<?php echo $org_url ?>"
                        title="Orginal sökadress för termen <?php echo $term; ?>"
                        target="_blank"><?php echo $org_url ?></a></li>
                <li class="row_6">
                    <form action="/wp-admin/admin.php?page=custom-search-log&delete_row" method="post"><input
                            class="submit" type="submit" value="Ta bort rad"><input type="hidden" name="row_id"
                            value="<?php echo $id; ?>"></form>
                </li>
            </div>
            <?php $i++; } ?>
        </ul>

        <?php

        $page_links = paginate_links( array(
            'base' => add_query_arg( 'pagenum', '%#%' ),
            'format' => '',
            'prev_text' => __( '&laquo;', 'text-domain' ),
            'next_text' => __( '&raquo;', 'text-domain' ),
            'total' => $num_of_pages,
            'current' => $pagenum
        ) );

        if ( $page_links ) {
            echo '<div class="tablenav"><div class="tablenav-pages" style="text-align:center; float:none; font-size:1.5em; margin:1em 0;">' . $page_links . '</div></div>';
        }
        ?>

        </table>
    </div>
</div>

<?php

}

add_filter('body_class', 'cc_kravspecifikationer_body_class');
/**
 * Lägg till en klass på bodyn om vi är på en sida som använder sidmallen Certifieringsorgan
 */
function cc_kravspecifikationer_body_class($classes)
{
    if (!is_page_template('page-template-certifieringsorgan.php')) {
        return $classes;
    }

    return array_merge($classes, ['certifieringsorgan']);
}

add_action('template_redirect', 'cc_kravspecifikationer_redirect');
/**
 * Dirigera om sidan om besökare försöker komma åt en singlesida för kravspecifikationer post_typen
 */
function cc_kravspecifikationer_redirect()
{
    global $post;

    if (!is_singular('kravspecifikationer')) {
        return;
    }

    $slug = get_the_terms($post->ID, 'kravspec_tax')[0]->slug;

    $view = '';

    if ($slug == 'produkter') {
        $pageID = 4022961;
    } else if ($slug == 'personer') {
        $pageID = 4022955;
    } else if ($slug == 'ledningssystem') {
        $pageID = 4022958;
    } else {
        $pageID = null;
    }

    if (!empty($pageID)) {
        $view .= '?view=' . $post->post_name;
    }

    wp_redirect(get_the_permalink($pageID) . $view);
    exit;
}


add_action('init', 'cc_create_sub_document_table');
/**
 * Kollar om databastabellen vid namn subscribers_document finns.
 * Om den inte finns skapas den.
 *
 * @link https://codex.wordpress.org/Creating_Tables_with_Plugins#Creating_or_Updating_the_Table
 * @link https://codex.wordpress.org/Class_Reference/wpdb#SELECT_a_Variable
 * @link https://wordpress.stackexchange.com/questions/11641/checking-if-database-table-exists
 */
function cc_create_sub_document_table()
{
    global $wpdb;

    $tableName = 'subscribers_document';

    $queryResult = $wpdb->get_var("SHOW TABLES LIKE '$tableName'");

    if ($queryResult === null) {
        $charsetCollate = $wpdb->get_charset_collate();

        $createTableQuery = "CREATE TABLE $tableName (
          id int(11) NOT NULL AUTO_INCREMENT,
          hash varchar(250) NOT NULL,
          subscriber_id int(11) NOT NULL,
          document_name varchar(250) NOT NULL,
          paired_post_ids varchar(250) NOT NULL,
          subscribed_date date NOT NULL,
          last_notified date NOT NULL,
          PRIMARY KEY  (id)
        ) $charsetCollate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        dbDelta($createTableQuery);
    }
}

add_action('init', 'cc_create_sub_document_run_table');
/**
 * Kollar om databastabellen vid namn subscribers_document_run finns.
 * Om den inte finns skapas den.
 *
 * @link https://codex.wordpress.org/Creating_Tables_with_Plugins#Creating_or_Updating_the_Table
 * @link https://codex.wordpress.org/Class_Reference/wpdb#SELECT_a_Variable
 * @link https://wordpress.stackexchange.com/questions/11641/checking-if-database-table-exists
 */
function cc_create_sub_document_run_table()
{
    global $wpdb;

    $tableName = 'subscribers_document_run';

    $queryResult = $wpdb->get_var("SHOW TABLES LIKE '$tableName'");

    if ($queryResult === null) {
        $charsetCollate = $wpdb->get_charset_collate();

        $createTableQuery = "CREATE TABLE $tableName (
          id int(11) NOT NULL AUTO_INCREMENT,
          document_name varchar(250) NOT NULL,
          modified_date date NOT NULL,
          post_id int(11) NOT NULL,
          run int(11) NOT NULL,
          PRIMARY KEY  (id)
        ) $charsetCollate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        dbDelta($createTableQuery);
    }
}
/**
 * TODO: När alla dokument importeras kollar vi om dokumenttypen är STAFS, om den är det lägger vi till den i databastabellen subscribers_document_run.
 * Vi sätter ID run till 0, då kommer det att köras ett till cron-jobb på natten som gör en kontroll och kollar om något av dokumenten i listan har uppdateras sen en användare
 * har bevakat dess innehåll, vi uppdaterar sedan run värdet till 1. Sen sker samma som ovan, vi läser in dokumenten igen och uppdaterar fältet modified_date.
 */
add_action('init', 'cc_preview_sub_doc_run');
/**
 * Skapar temporärt data i tabellen subscribers_document_run
 * @return [type] [description]
 */
function cc_preview_sub_doc_run()
{
    if (is_admin()) {
        return;
    }

    global $wpdb;

    $sql = $wpdb->prepare("SELECT * FROM subscribers_document_run WHERE run = %d", 0);

    $result = $wpdb->get_row($sql);

    if ($result !== null) {
        return;
    }

    $docRun = [];

    $args = [
        'post_type' => 'dokument',
        'posts_per_page' => -1,
    ];

    $query = new WP_Query($args);

    if ( $query->have_posts() ) {
        while ( $query->have_posts() ) {
            $query->the_post();

            $postID = get_the_ID();

            $docRun1 = [];
            $docRun1[] = get_field('crm_doc_dokumentbeteckning', $postID);
            $docRun1[] = date('Y-m-d');
            $docRun1[] = $postID;

            $docRun[] = $docRun1;
        }
    }

    foreach ($docRun as $doc) {
        $sql = $wpdb->prepare(
            "INSERT INTO subscribers_document_run (document_name, modified_date, post_id, run) VALUES (%s, %s, %d, %d)",
            $doc[0],
            $doc[1],
            $doc[2],
            0
        );

        $result = $wpdb->query($sql);
    }
}

add_action('acf/save_post', 'cc_update_choosen_post', 5);
/**
 * Tillåt bara ett inlägg att vara utvald, de andra sätts till ej utvalda.
 *
 * @link https://support.advancedcustomfields.com/forums/topic/there-can-only-be-one-field-checked/
 */
function cc_update_choosen_post($post_id)
{
    if (get_post_type($post_id) !== 'news') {
        return;
    }

    $fieldName = 'upphavd_nyhet_sv';
    $fieldKey = 'field_575921f4edcaa';
    $metaValue = 'true';
    $fieldVal = 'false';

    if (ICL_LANGUAGE_CODE === 'en') {
        $fieldName = 'upphavd_nyhet_en';
        $fieldKey = 'field_575925a3a50b9';
    }

    $newValue = $_POST['acf'][$fieldKey] ?? 'false';

    // Om vi inte valt detta inlägg som utvalt, fortsätt inte.
    if ($newValue === 'false') {
        return;
    }

    global $wpdb;

    $wpdb->query(
        $wpdb->prepare(
            "UPDATE wp_postmeta SET meta_value = 'false' WHERE meta_key = %s",
            $fieldName
        )
    );
}

add_action('pre_get_posts', 'cc_recalc_posts_per_page_sticky_post');
/**
 * Körs på startsidan i högra sektionen för inlägg (nyheter). Om det finns ett sticky inlägg så uppdaterar vi posts_per_page till 2 för att bara få ut 3.
 * Tack till Benjamin som hjälpte till att förbättra funktionen. :)
 *
 * @link https://wordpress.stackexchange.com/questions/76620/sticky-posts-exceed-posts-per-page-limit
 * @link https://wordpress.stackexchange.com/questions/134765/prevent-pre-get-posts-filter-on-specific-post-type
 * @link https://developer.wordpress.org/reference/classes/wp_query/get/
 * @link https://www.php.net/manual/en/function.max.php
 *
 * @return [int] [Antal inlägg som loopen ska returnera (exkl sticky inlägg)].
 */
function cc_recalc_posts_per_page_sticky_post($query)
{
    if ($query->get('post_type') === 'news' && $query->get('posts_per_page') === 3) {
        $stickyPosts = get_option('sticky_posts');

        $numStickyPosts = count($stickyPosts);

        if (!empty($stickyPosts)) {
            $query->set('posts_per_page', max(0, 3 - $numStickyPosts));
        }
    }
}

add_filter('wpseo_breadcrumb_single_link', 'cc_custom_breadcrumb_link', 10, 2);
/**
 * Lägger till title attribut på brödsmullänkar
 */
function cc_custom_breadcrumb_link($outputLink, $link)
{
    $text = 'Return to';

    if (ICL_LANGUAGE_CODE == 'sv') {
        $text = 'Gå till';
    }

    return str_replace('<a', '<a title="' . $text . ' ' . $link['text'] . '"', $outputLink);
}

/**
 * Wrappar breadcrumbs i ett nav element med rätt aria-label för respektive språk
 */
function cc_breadcrumb_renderer($wrapper = false)
{
    $bcAriaLabel = 'Breadcrumb';

    if (ICL_LANGUAGE_CODE == 'sv') {
        $bcAriaLabel = 'Brödsmulor';
    }

    // Om Yoast är installerat så hämtar vi bredcrumb
    if (function_exists('yoast_breadcrumb')) {
        if ($wrapper === true) {
            return yoast_breadcrumb('<div class="wrapper"><nav aria-label="' . $bcAriaLabel . '" id="breadcrumbs">','</nav></div>');
        }

        return yoast_breadcrumb('<nav aria-label="' . $bcAriaLabel . '" id="breadcrumbs">','</nav>');
    }
}

/**
 * Funktion som encodar en sträng med base64-tecken men ersätter reserverade tecken för URL.
 *
 * @link https://stackoverflow.com/questions/1374753/passing-base64-encoded-strings-in-url
 */
function cc_base64_url_encode($input)
{
    return strtr(base64_encode($input), '+/=,', '._-~');
}

/**
 * Funktion som decodar en sträng med base64-tecken men ersätter reserverade tecken för URL.
 *
 * @link https://stackoverflow.com/questions/1374753/passing-base64-encoded-strings-in-url
 */
function cc_base64_url_decode($input)
{
    return base64_decode(strtr($input, '._-~', '+/=,'));
}

/**
 * Funktion som körs på hantera prenumeranter sidan och hanterar queries för prenumeranter.
 */
function cc_subscribers_wpdb_queries(int $currentPage, int $postsPerPage, string $search = null)
{
    global $wpdb;

    $currentPageOffset = $currentPage * $postsPerPage;

    $result = [
        'subscribers' => [],
        'subscribers_total_num' => 0,
    ];

    $result['subscribers_total_num'] = $wpdb->get_var("SELECT COUNT(*) FROM `subscribers`");
    $query = "SELECT * FROM `subscribers` LIMIT $currentPageOffset, $postsPerPage";
    if ($search !== null) {
        $query = $wpdb->prepare(
            "SELECT * FROM `subscribers` WHERE `namn` LIKE %s OR `mail` LIKE %s LIMIT $currentPageOffset, $postsPerPage",
            '%' . $search . '%',
            '%' . $search . '%'
        );
    }

    $result['subscribers'] = $wpdb->get_results($query);

    return $result;
}

/**
 * Innehåll för hantera prenumeranter sidan.
 */
function cc_manage_subscribers_admin_page()
{
    global $wpdb;

    // Konstant för Posts Per Page eller dyl. // Benjamin
    $postsPerPage = 100;
    $currentPage = empty($_GET['paged']) ? 0 : $_GET['paged'];

    $result = cc_subscribers_wpdb_queries($currentPage, $postsPerPage);

    if (isset($_GET['search']) && $_GET['search'] === 'found-results') {
        $result = cc_subscribers_wpdb_queries($currentPage, $postsPerPage, cc_base64_url_decode($_GET['keyword']));
    }

    $getSubs = $result['subscribers'];
    $getSubsTotal = $result['subscribers_total_num'];
    $maxPages = ceil($getSubsTotal / $postsPerPage);
    ?>
<div class="wrap">
    <h1>Hantera prenumeranter</h1>

    <?php
        if (isset($_GET['search']) && $_GET['search'] === 'no-results') :
            ?>
    <div class="notice notice-error">
        <p><?php echo esc_attr('Vi kunde inte hitta några prenumeranter med sökordet "' . cc_base64_url_decode($_GET['keyword']) . '".'); ?>
        </p>
    </div>
    <?php
        elseif (isset($_GET['search']) && $_GET['search'] === 'found-results') :
            ?>
    <div class="notice notice-info is-dismissible">
        <p><?php echo esc_attr('Sökordet "'. cc_base64_url_decode($_GET['keyword']) .'" gav ' . count($result['subscribers']) . ' träffar.'); ?>
        </p>
    </div>
    <?php
        endif;

        if (isset($_GET['delete-user']) && $_GET['delete-user'] === 'complete') :
            ?>
    <div class="notice notice-success">
        <p><?php echo esc_attr('Prenumeranten och dess tillhörande data har raderats.'); ?></p>
    </div>
    <?php
        endif;

        if (isset($_GET['search'])) :
            ?>
    <a class="button-primary" href="admin.php?page=manage-subscribers"
        title="Visa alla prenumeranter"><?php echo esc_attr('Visa alla prenumeranter'); ?></a>
    <?php
        endif;

        if (!empty($getSubs) && $getSubs !== null) :
            ?>
    <form action="?page=manage-subscribers" method="POST">
        <p>Sök efter prenumeranter, du kan söka efter namn och e-postadresser.</p>
        <input class="regular-text" type="text"
            <?php if (isset($_POST['submit-search-subscribers']) && !empty($_POST['search'])) { echo 'value="' . $_POST['search'] . '"'; } ?>
            name="search" required>
        <br>
        <input class="button-secondary" type="submit" name="submit-search-subscribers"
            value="<?php echo esc_attr('Sök'); ?>">
    </form>

    <table class="widefat">
        <thead>
            <tr>
                <th class="row-title"><?php echo esc_attr('Nummer'); ?></th>
                <th><?php echo esc_attr('Namn'); ?></th>
                <th><?php echo esc_attr('E-postadress'); ?></th>
                <th><?php echo esc_attr('Ämnesområden'); ?></th>
                <th><?php echo esc_attr('Föreskrifter'); ?></th>
            </tr>
        </thead>

        <tbody>
            <?php
                    $i = 0 + $currentPage * $postsPerPage;

                    foreach ($getSubs as $sub) :
                        $i++;
                        if ($i % 2 == 0) {
                            $alternativeClass = 'class="alternate"';
                        } else {
                            $alternativeClass = '';
                        }

                        $subID = $sub->id;
                        $encodedId = base64_encode($subID);

                        $subCountDoc = $wpdb->get_results($wpdb->prepare("SELECT COUNT(*) FROM `subscribers_document` WHERE `subscriber_id` = %s", $subID), ARRAY_N);
                        $subCountPost = $wpdb->get_results($wpdb->prepare("SELECT COUNT(*) FROM `subscribers_post` WHERE `subscriber_id` = %s", $subID), ARRAY_N);
                        ?>
            <tr <?php echo $alternativeClass; ?>>
                <td>
                    <label><?php echo esc_html($i); ?></label>
                </td>

                <td>
                    <label><?php echo esc_attr($sub->namn); ?></label>
                    <div class="row-actions">
                        <span class="edit"><button id="<?php echo esc_attr($encodedId); ?>"
                                class="edit-subscriber button-link editinline">Redigera</button> | </span>
                        <span class="cancel"><button id="<?php echo esc_attr($encodedId); ?>"
                                class="cancel-edit-subscriber button-link editinline">Avbryt</button> | </span>
                        <span class="trash"><a data-user-id="<?php echo esc_attr($encodedId); ?>"
                                class="delete-subscriber">Ta bort</a></span>
                    </div>
                </td>

                <td>
                    <label><?php echo esc_attr( $sub->mail); ?></label>
                </td>

                <td class="areas" data-existing-subs="<?php echo esc_attr($subCountPost[0][0]); ?>">
                    <label><?php echo esc_attr($subCountPost[0][0] . ' st'); ?></label>
                </td>

                <td class="docs" data-existing-subs="<?php echo esc_attr($subCountDoc[0][0]); ?>">
                    <label><?php echo esc_attr($subCountDoc[0][0] . ' st'); ?></label>
                </td>
            </tr>
            <?php
                    endforeach;
                    ?>
        </tbody>

        <tfoot>
            <tr>
                <th class="row-title"><label><?php echo esc_attr('Nummer'); ?></label></th>
                <th><?php echo esc_attr('Namn'); ?></th>
                <th><?php echo esc_attr('E-postadress'); ?></th>
                <th><?php echo esc_attr('Områden'); ?></th>
                <th><?php echo esc_attr('Föreskrifter'); ?></th>
            </tr>
        </tfoot>
    </table>

    <div class="tablenav">
        <div class="tablenav-pages">
            <span class="displaying-num">
                <?php echo esc_attr($getSubsTotal . ' prenumeranter'); ?>
            </span>

            <?php
                    if ($currentPage > 0) :
                        ?>
            <a class='first-page disabled' title='Gå till första sidan'
                href='admin.php?page=manage-subscribers&paged=0'>&laquo;</a>
            <a class='prev-page disabled' title='Gå till förgående sida'
                href='admin.php?page=manage-subscribers&paged=<?php echo urlencode($currentPage - 1); ?>'>&lsaquo;</a>
            <?php
                    endif;
                    ?>
            <span class="paging-input">
                <span class='current-page'><?php echo urlencode($currentPage + 1); ?> av </span>
                <span class='total-pages'><?php echo $maxPages; ?></span>
            </span>
            <?php
                    if ($currentPage + 1 < $maxPages) :
                        ?>
            <a class='next-page' title='Gå till nästa sida'
                href='admin.php?page=manage-subscribers&paged=<?php echo urlencode($currentPage + 1); ?>'>&rsaquo;</a>
            <a class='last-page' title='Gå till sista sidan'
                href='admin.php?page=manage-subscribers&paged=<?php echo urlencode($maxPages - 1); ?>'>&raquo;</a>
            <?php
                    endif;
                    ?>
        </div>
    </div>
    <?php
        endif;
        ?>
</div>
<?php

add_filter('jpeg_quality', function($arg){
    return 100;
  });

} // END function cc_manage_subscribers_admin_page()


function wpse27856_set_content_type(){
    return "text/html";
}
add_filter( 'wp_mail_content_type','wpse27856_set_content_type' );


// SKAPA META BOX I ADMIN FÖR UTSKICK
function amnesomraden_form_meta_boxes( $post ) {
    add_meta_box( 'form_meta_box', __( 'Ämnesområden Utskick', 'amnesomraden_form' ), 'amnesomraden_meta_box', 'amnesomraden', 'side', 'high' );
}
add_action( 'add_meta_boxes_amnesomraden', 'amnesomraden_form_meta_boxes' );

function amnesomraden_meta_box( $post ) {
    $postid = get_the_id();
    global $wpdb;
    $dbTable    = 'wp_subscribe_amnesomraden_send';
    $query      = "SELECT * FROM $dbTable WHERE post_id = '$postid'";
    $check      = $wpdb->get_results($query);
    $sent       = $check[0]->sent;

    if($check) { // OM POSTEN FINNS I DATABASEN ?>
        <?php if($sent == 0) { // OM INTE POSTEN HAR SKICKATS ?>
            <div class="custom_mail_function">
                <span class="send_to_subscribers_status">
                    <i class="fa fa-info-circle" aria-hidden="true"></i><?php _e(' Skicka ett mail till alla prenumeranter som finns registerade för utskick om ämnesområden'); ?>
                </span>
                </br>
                </br>
                <a href="/sender.php?send_amnesomraden&postid=<?php echo $postid; ?>" class="button button-primary button-small"><?php _e('Skicka'); ?></a>
                <br style="clear:both;" />
            </div>
        <?php } else { ?>
            <div class="custom_mail_function">
                <span class="send_to_subscribers_status">
                    <i class="fa fa-info-circle" aria-hidden="true"></i><?php _e(' Du har redan gjort ett utskick för detta inlägg tidigare.'); ?>
                </span>
                <br style="clear:both;" />
            </div>
        <?php } ?>
    <?php } else { // OM POSTEN INTE FINNS I DATABASEN ?>
        <div class="custom_mail_function">
            <span class="send_to_subscribers_status">
                <i class="fa fa-info-circle" aria-hidden="true"></i><?php _e(' Du måste spara det aktuella inlägget innan ett utskick kan göras.'); ?>
            </span>
            <br style="clear:both;" />
        </div>
    <?php } ?>
<?php }

add_action('init', 'cc_create_amnesomraden_send_table');
/**
 * Kollar om databastabellen vid namn wp_subscribe_amnesomraden_send finns.
 * Om den inte finns skapas den.
 */
function cc_create_amnesomraden_send_table()
{
    global $wpdb;

    $tableName = 'wp_subscribe_amnesomraden_send';

    $queryResult = $wpdb->get_var("SHOW TABLES LIKE '$tableName'");

    if ($queryResult === null) {
        $charsetCollate = $wpdb->get_charset_collate();

        $createTableQuery = "CREATE TABLE $tableName (
          id int(11) NOT NULL AUTO_INCREMENT,
          post_id int(11) NOT NULL,
          sent int(11),
          PRIMARY KEY  (id)
        ) $charsetCollate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        dbDelta($createTableQuery);
    }
}

// PLACERAR POST ID I DATABASEN NÄR EN POST SPARAS I POSTTYPEN POSTS
function save_amnesomraden_meta( $post_id, $post, $update ) {
    global $wpdb;
    $post_type = get_post_type($post_id);

    // var_dump($post_type);
    if ( "amnesomraden" != $post_type ) {
        return;
    } else {
        $post_info = get_post($post_id);
        if($post_info->post_status != 'auto-draft') {
            $dbTable    = 'wp_subscribe_amnesomraden_send';
            $query      = "SELECT * FROM $dbTable WHERE post_id = '$post_id'";
            $check      = $wpdb->get_results($query);

            if(empty($check)) {
                $dbTable    = 'wp_subscribe_amnesomraden_send';
                $query      = "INSERT INTO $dbTable (post_id) VALUES ('$post_id')";
                $check      = $wpdb->get_results($query);
            }
        }
        if($update && $post_info->post_status != 'auto-draft'){
            $dbTable    = 'wp_subscribe_amnesomraden_send';
            $query      = "UPDATE $dbTable SET sent = 0 WHERE post_id = '$post_id'";
            $check      = $wpdb->get_results($query);
        }
    }
}
add_action( 'save_post', 'save_amnesomraden_meta', 10, 3 );



// SKAPA META BOX I ADMIN FÖR UTSKICK
function dokument_form_meta_boxes( $post ) {
    add_meta_box( 'form_meta_box', __( 'Dokument Utskick', 'dokument_form' ), 'dokument_meta_box', 'dokument', 'side', 'high' );
}
add_action( 'add_meta_boxes_dokument', 'dokument_form_meta_boxes' );

function dokument_meta_box( $post ) {
    $postid = get_the_id();
    $postName = get_field('crm_doc_dokumentbeteckning',$postid);
    global $wpdb;
    $dbTable    = 'wp_subscribe_dokument_send';
    $query      = "SELECT * FROM $dbTable WHERE post_id = '$postid'";
    $check      = $wpdb->get_results($query);
    $sent       = $check[0]->sent;

    if($check) { // OM POSTEN FINNS I DATABASEN ?>
        <?php if($sent == 0) { // OM INTE POSTEN HAR SKICKATS ?>
            <div class="custom_mail_function">
                <span class="send_to_subscribers_status">
                    <i class="fa fa-info-circle" aria-hidden="true"></i><?php _e(' Skicka ett mail till alla prenumeranter som finns registerade för utskick om dokument.'); ?>
                </span>
                </br>
                </br>
                <a href="/sender.php?send_document&postid=<?php echo $postid; ?>&postName=<?php echo $postName; ?>" class="button button-primary button-small"><?php _e('Skicka'); ?></a>
                <br style="clear:both;" />
            </div>
        <?php } else { ?>
            <div class="custom_mail_function">
                <span class="send_to_subscribers_status">
                    <i class="fa fa-info-circle" aria-hidden="true"></i><?php _e(' Du har redan gjort ett utskick för detta inlägg tidigare.'); ?>
                </span>
                <br style="clear:both;" />
            </div>
        <?php } ?>
    <?php } else { // OM POSTEN INTE FINNS I DATABASEN ?>
        <div class="custom_mail_function">
            <span class="send_to_subscribers_status">
                <i class="fa fa-info-circle" aria-hidden="true"></i><?php _e(' Du måste spara det aktuella inlägget innan ett utskick kan göras.'); ?>
            </span>
            <br style="clear:both;" />
        </div>
    <?php } ?>
<?php }

add_action('init', 'cc_create_dokument_send_table');
/**
 * Kollar om databastabellen vid namn wp_subscribe_dokument_send finns.
 * Om den inte finns skapas den.
 */
function cc_create_dokument_send_table()
{
    global $wpdb;

    $tableName = 'wp_subscribe_dokument_send';

    $queryResult = $wpdb->get_var("SHOW TABLES LIKE '$tableName'");

    if ($queryResult === null) {
        $charsetCollate = $wpdb->get_charset_collate();

        $createTableQuery = "CREATE TABLE $tableName (
          id int(11) NOT NULL AUTO_INCREMENT,
          post_id int(11) NOT NULL,
          sent int(11),
          PRIMARY KEY  (id)
        ) $charsetCollate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        dbDelta($createTableQuery);
    }
}

// PLACERAR POST ID I DATABASEN NÄR EN POST SPARAS I POSTTYPEN POSTS
function save_dokument_meta( $post_id, $post, $update ) {
    global $wpdb;
    $post_type = get_post_type($post_id);

    // var_dump($post_type);
    if ( "dokument" != $post_type ) {
        return;
    } else {
        $post_info = get_post($post_id);

        if($post_info->post_status != 'auto-draft') {
            $dbTable    = 'wp_subscribe_dokument_send';
            $query      = "SELECT * FROM $dbTable WHERE post_id = '$post_id'";
            $check      = $wpdb->get_results($query);

            if(empty($check)) {
                $dbTable    = 'wp_subscribe_dokument_send';
                $query      = "INSERT INTO $dbTable (post_id) VALUES ('$post_id')";
                $check      = $wpdb->get_results($query);
            }
        }
        if($update && $post_info->post_status != 'auto-draft'){
            $dbTable    = 'wp_subscribe_dokument_send';
            $query      = "UPDATE $dbTable SET sent = 0 WHERE post_id = '$post_id'";
            $check      = $wpdb->get_results($query);
        }
    }
}
add_action( 'save_post', 'save_dokument_meta', 10, 3 );

function sortById($x, $y) {
    return $y['crm_doc_dokumentbeteckning'] <=> $x['crm_doc_dokumentbeteckning'];
}

/**
 * Disable support for comments and trackbacks in post types.
 */
function disable_comments_post_types_support() {
	$post_types = get_post_types();
	foreach ( $post_types as $post_type ) {
		if ( post_type_supports( $post_type, 'comments' ) ) {
			remove_post_type_support( $post_type, 'comments' );
			remove_post_type_support( $post_type, 'trackbacks' );
		}
	}
}

add_action( 'admin_init', 'disable_comments_post_types_support' );

/**
 * Close comments on the front-end.
 */
function disable_comments_status() {
	return false;
}

add_filter( 'comments_open', 'disable_comments_status', 20, 2 );
add_filter( 'pings_open', 'disable_comments_status', 20, 2 );

/**
 * Hide existing comments.
 *
 * @param array $comments the comments.
 */
function disable_comments_hide_existing_comments( $comments ) {
	$comments = array();
	return $comments;
}

add_filter( 'comments_array', 'disable_comments_hide_existing_comments', 10, 2 );

/**
 * Remove comments page in menu.
 */
function disable_comments_admin_menu() {
	remove_menu_page( 'edit-comments.php' );
}
add_action( 'admin_menu', 'disable_comments_admin_menu' );

/**
 * Redirect any user trying to access comments page.
 */
function disable_comments_admin_menu_redirect() {
	global $pagenow;
	if ( 'edit-comments.php' === $pagenow ) {
		wp_safe_redirect( admin_url() );
		exit;
	}
}

add_action( 'admin_init', 'disable_comments_admin_menu_redirect' );

/**
 * Remove comments metabox from dashboard.
 */
function disable_comments_dashboard() {
	remove_meta_box( 'dashboard_recent_comments', 'dashboard', 'normal' );
}

add_action( 'admin_init', 'disable_comments_dashboard' );

/**
 * Remove comments links from admin bar.
 */
function disable_comments_admin_bar() {
	if ( is_admin_bar_showing() ) {
		remove_action( 'admin_bar_menu', 'wp_admin_bar_comments_menu', 60 );
	}
}

add_action( 'init', 'disable_comments_admin_bar' );

add_action( 'admin_menu', 'remove_default_post_type' );

function remove_default_post_type() {
    remove_menu_page( 'edit.php' );
}

function remove_version() {
    return '';
}
add_filter('the_generator', 'remove_version');

// disable xmlrpc
function remove_xmlrpc_methods( $methods ) {
  return array();
}
add_filter( 'xmlrpc_methods', 'remove_xmlrpc_methods' );

remove_action('wp_head', array ( $sitepress, 'meta_generator_tag'));

/**
 * Parses the content from the new stle of page templates.
 */
function swedac_parse_the_content( $content ){
    foreach($content as $partial){
        $partial_name = str_replace("content_group_", "", $partial['acf_fc_layout'] );
        if ( file_exists( get_stylesheet_directory() . '/content_partials/' . $partial_name . '.php' ) ) {
            require( get_stylesheet_directory() . '/content_partials/' . $partial_name . '.php' );
        }            
    }
}
//Add the support for your cpt in the Widget Activity of the Admin Dashboard
if ( is_admin() ) {
	add_filter( 'dashboard_recent_posts_query_args', 'add_page_to_dashboard_activity' );

	function add_page_to_dashboard_activity( $query_args ) {
		if ( is_array( $query_args[ 'post_type' ] ) ) {
			//Set yout post type
			$query_args[ 'post_type' ][] = 'news';
		} else {
			$temp = array( $query_args[ 'post_type' ], 'news' );
			$query_args[ 'post_type' ] = $temp;
		}
		return $query_args;
	}

}

/**
 * Hide the latest comments from the Activity Dashboard widget (approach #6)
 */
add_action( 'admin_head-index.php', function()
{
    print '<style>#latest-comments{ display:none; }</style>';
} );

/**
 * Fetches XML content from a given URL.
 *
 * This function initializes a cURL session to retrieve XML data from the specified URL.
 * It sets the necessary cURL options to return the transfer as a string and to use a specific
 * user agent string to mimic a browser request.
 *
 * @param string $url The URL from which to fetch the XML content.
 * @return string|false The XML content as a string on success, or false on failure.
 */
function get_xml_from_url($url){
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:********) Gecko/20080311 Firefox/********');

    $xmlstr = curl_exec($ch);
    curl_close($ch);

    return $xmlstr;
}

/**
* Filter WP_Mail Function to Add Multiple Admin Emails
*
* @param array $args A compacted array of wp_mail() arguments, including the "to" email, subject, message, headers, and attachments values.
* @return array
*/
function custom_to_admin_emails($args) {

    // This assumes that admin emails are sent with only the admin email
    // used in the to argument.
    if (is_array($args['to'])) return $args;

    $admin_email = get_option('admin_email');

    // Check if admin email is in string, as plugins/themes could have changed format (ie. Administrator <<EMAIL>> )
    if ($admin_email !== '<EMAIL>' && strpos($args['to'], $admin_email) !== FALSE) {

        // Create array in case there are multiple emails defined in CSV format
        $emails = explode(',', $args['to']);
        
        /**
        * Add any additional emails to the array
        *
        * All email addresses supplied to wp_mail() as the $to parameter must comply with RFC 2822. Some valid examples:
        * <EMAIL>
        * User <<EMAIL>>
        */
        // $emails[] = 'User <<EMAIL>>';
        $emails[] = '<EMAIL>';

        $args['to'] = $emails;
    }

    return $args;
}
/**
 *
 * WARNING: this only checks if the email is being sent to the same email as admin_email option.
 * If for some reason another email is sent to that same email address, but it's not meant as an "admin email"
 * this filter will still add those additional emails, just something to keep in mind.
*/
add_filter('wp_mail', 'custom_to_admin_emails');