<?php use Roots\Sage\Titles; ?>
<div class="wrapper">
	<div class="page-header">
		<?php if( isset($_GET['success_subscribing']) ) { ?>
			<div class="header_message">
				<div class="wrapper">
					<div class="inner success">
						<span>Du bevakar nu alla <b><?php echo lcfirst(get_the_title()); ?></b>. När någonting ändras här kommer du få ett mail.</span>
					</div>
				</div>
			</div>
		<?php } ?>

		<?php if( isset($_GET['failed_subscribing']) ) { ?>
			<div class="header_message">
				<div class="wrapper">
					<div class="inner failed">
						<span>Du bevakar redan dessa <b><?php echo lcfirst(get_the_title()); ?></b>.</span>
					</div>
				</div>
			</div>
		<?php } ?>

		<h1><?= Titles\title(); ?></h1>
	</div>

	<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
		<?php if(get_field('page_ingress_subpage_sv')) { ?>
			<div class="ingress">
				<?php echo get_field('page_ingress_subpage_sv'); ?> 
			</div>
		<?php } ?>
	<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
		<?php if(get_field('page_ingress_subpage_en')) { ?>
			<div class="ingress">
				<?php echo get_field('page_ingress_subpage_en'); ?> 
			</div>
		<?php } ?>
	<?php endif;?>

	<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
		<?php if(get_field('page_ankarlankar')): ?>
			<div class="page_ankarlankar">
				<h2 class="nav-header">Snabbnavigation</h2>
				<ul>
					<?php while(has_sub_field('page_ankarlankar')): ?>
						<?php
							$lank_lower = get_sub_field('lank');
							$lank_lower = strtolower($lank_lower);

							$find  = array('?', '!');
							$replace = array('', '');
							$lankClean = str_replace($find, $replace, $lank_lower);
						?>
						<li>
							<a href="<?php echo $lankClean; ?>" title="Gå till <?php echo get_sub_field('namn'); ?>">
								<?php echo get_sub_field('namn'); ?>
							</a>
						</li>
					<?php endwhile; ?>
				</ul>
			</div>
		<?php endif; ?>
	<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
		<?php if(get_field('page_ankarlankar_en')): ?>
			<div class="page_ankarlankar">
				<h2 class="nav-header">Quick Navigation</h2>
				<ul>
					<?php while(has_sub_field('page_ankarlankar_en')): ?>
						<?php 
							$lank_lower = get_sub_field('lank');
							$lank_lower = strtolower($lank_lower);

							$find  = array('?', '!');
							$replace = array('', '');
							$lankClean = str_replace($find, $replace, $lank_lower);
						?>
						<li>
							<a href="<?php echo $lankClean; ?>" title="Go to <?php echo get_sub_field('namn'); ?>">
								<?php echo get_sub_field('namn'); ?>
							</a>
						</li>
					<?php endwhile; ?>
				</ul>
			</div>
		<?php endif; ?>
	<?php endif;?>


	<?php if ( has_post_thumbnail()) : ?>
		<?php $get_post_image = wp_get_attachment_image_src( get_post_thumbnail_id($post->ID), 'large' );
		$image_url = $get_post_image['0']; 
		$img_id = get_post_thumbnail_id(get_the_ID());
		$alt_text = get_post_meta($img_id , '_wp_attachment_image_alt', true); 
		$thumbnail_image = get_posts(array('p' => $img_id, 'post_type' => 'attachment'));
		$caption = ($thumbnail_image && isset($thumbnail_image[0])) ? $thumbnail_image[0]->post_excerpt : false;
		?>
		<?php if ($caption) : ?>
			<figure class="page_thumbnail">
				<img data-src="<?php echo $image_url; ?>" alt="<?php echo $alt_text; ?>" />
				<figcaption class="wp-caption-text"><?php echo $caption; ?></figcaption>
			</figure>
		<?php else : ?>
			<div class="page_thumbnail">
				<img data-src="<?php echo $image_url; ?>" alt="<?php echo $alt_text; ?>" />
			</div>
		<?php endif; ?>
	<?php endif; ?>
	
</div>