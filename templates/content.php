<li <?php post_class(); ?>>
	<?php $get_post_image = wp_get_attachment_image_src( get_post_thumbnail_id($post->ID), 'large' );
	$image_url = $get_post_image['0']; 
	$img_id = get_post_thumbnail_id(get_the_ID());
	$alt_text = get_post_meta($img_id , '_wp_attachment_image_alt', true);
	?>
	
	<div class="left">
		<div class="meta">
			<?php get_template_part('templates/entry-meta') ?>
		</div>
    	<h2><a href="<?php the_permalink(); ?>" alt="<?php the_title(); ?>"><?php the_title(); ?></a></h2>
    	<div class="excerpt">
    		<?php the_excerpt(); ?>
    	</div>
	</div>
	<div class="right">
		<?php if ( has_post_thumbnail() ) { ?>
			<a href="<?php the_permalink(); ?>" alt="<?php the_title(); ?>">
				<img data-src="<?php echo $image_url; ?>" alt="<?php echo $alt_text; ?>" />
			</a>
		<?php } ?>
	</div>
</li>