<div id="page-header-parent">
	<div class="title">
		<div class="wrapper">
			<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
				<span class="title-page">Generaldirektörens blogg</span>
			<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
				<span class="title-page">Director General's blog</span>
			<?php endif;?>
		</div>
	</div>
	<?php cc_breadcrumb_renderer(true); ?>
</div>

<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
	<div class="wrapper">
		<div id="single_section_post">
			<div class="main">
				<?php while (have_posts()) : the_post(); ?>
				<article <?php post_class('single-post'); ?>>
					<header class="post-header">
						<h1><?php the_title(); ?></h1>
						
						<div class="ingress">
							<?php echo get_field('page_ingress_subpage_sv'); ?> 
						</div>

						<?php if ( has_post_thumbnail()) : ?>
							<?php $get_post_image = wp_get_attachment_image_src( get_post_thumbnail_id($post->ID), 'large' );
							$image_url = $get_post_image['0']; 
							$img_id = get_post_thumbnail_id(get_the_ID());
							$alt_text = get_post_meta($img_id , '_wp_attachment_image_alt', true);
							$thumbnail_image = get_posts(array('p' => $img_id, 'post_type' => 'attachment'));
							$caption = ($thumbnail_image && isset($thumbnail_image[0])) ? $thumbnail_image[0]->post_excerpt : false;
							?>
							<?php if ($caption) : ?>
								<figure class="page_thumbnail">
									<img data-src="<?php echo $image_url; ?>" alt="<?php echo $alt_text; ?>" />
									<figcaption class="wp-caption-text"><?php echo $caption; ?></figcaption>
								</figure>
							<?php else : ?>
								<div class="page_thumbnail">
									<img data-src="<?php echo $image_url; ?>" alt="<?php echo $alt_text; ?>" />
								</div>
							<?php endif; ?>
						<?php endif; ?>
					</header>
					<div class="post-content">
						<?php the_content(); ?>
					</div>
					<footer class="post-footer">
						<div class="post_settings">
							<?php if((get_field('gd_blogg_skribent_namn')) && (get_field('gd_blogg_skribent_epost'))) { ?>
								<div class="author">
									<span><a href="mailto:<?php echo antispambot(get_field('gd_blogg_skribent_epost')); ?>" title="Skicka ett mail till <?php echo get_field('gd_blogg_skribent_namn'); ?>"><?php echo get_field('gd_blogg_skribent_namn'); ?></a></span>
								</div>
							<?php } elseif (get_field('gd_blogg_skribent_namn')) { ?>
								<div class="author">
									<span><?php echo get_field('gd_blogg_skribent_namn'); ?></span>
								</div>
							<?php } else { ?>

							<?php } ?>
							<div class="publish_change">
								<span>Publicerad: <?php echo get_the_date('j F Y'); ?>, kl. <?php the_time('H:i') ?></span>  
								<span>Senast uppdaterad: <?php the_modified_date('j F Y'); ?>, kl. <?php the_modified_time('H:i') ?></span>
							</div>
							<div class="share_post">
								<!-- FACEBOOK -->
								<a class="share facebook" onclick="window.open('http://www.facebook.com/sharer/sharer.php?u=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>', 'shareFacebook', 'width=650, height=270, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
									<i class="fa fa-facebook"></i> <span>Dela på Facebook</span>
								</a>
								<!-- FACEBOOK END -->

								<!-- LINKEDIN -->
									<a class="share linkedin" onclick="window.open('http://www.linkedin.com/shareArticle?mini=true&url=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>&source=[SOURCE/DOMAIN]', 'shareLinkedIn', 'width=610, height=480, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
										<i class="fa fa-linkedin-square"></i> <span>Dela på LinkedIn</span>
									</a>
								<!-- LINKEIND END -->


								<!-- MAIL -->
								<a class="share mail" href="mailto:?subject=<?php the_title(); ?>&body=Hej, jag tror att du skulle gilla detta. Kolla in detta <?php echo get_permalink(); ?>">
									<i class="fa fa-envelope" aria-hidden="true"></i> <span>Skicka som e-post</span>
								</a>
								<!-- MAIL END -->
							</div>
						</div>
					</footer>
				</article>
			<?php endwhile; ?>
			</div>

			<div class="sidebar">
				<div class="om-gd-bloggen">
					<div class="profilbild">
						<?php $profilbild_gd_bloggen = get_field('gd_bloggen_profilbild', 1022); ?>
						<?php if($profilbild_gd_bloggen) { ?>
							<img data-src="<?php echo $profilbild_gd_bloggen['sizes']['medium']; ?>" alt="" />
						<?php } ?>
					</div>
					<div class="content">
						<?php if (!empty(get_field('gd_bloggen_rubrik', 1022))): ?>
							<h3><?php echo get_field('gd_bloggen_rubrik', 1022); ?></h3>
						<?php endif; ?>
						<div class="info">
							<?php echo get_field('gd_bloggen_beskrivning', 1022); ?>

							<?php if(get_field('gd_bloggen_epost')) { ?>
								<a href="mailto:<?php echo antispambot(get_field('gd_bloggen_epost', 1022)); ?>">Maila mig</a>
							<?php } ?>

						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
	<div class="wrapper">
		<div id="single_section_post">
			<div class="main">
				<?php while (have_posts()) : the_post(); ?>
				<article <?php post_class('single-post'); ?>>
					<header class="post-header">
						<h1><?php the_title(); ?></h1>
						
						<div class="ingress">
							<?php echo get_field('page_ingress_subpage_en'); ?> 
						</div>

						<?php if ( has_post_thumbnail()) : ?>
							<?php $get_post_image = wp_get_attachment_image_src( get_post_thumbnail_id($post->ID), 'large' );
							$image_url = $get_post_image['0']; 
							?>

							<div class="page_thumbnail">
								<img data-src="<?php echo $image_url; ?>" alt="<?php the_title(); ?>" />
								
							</div>
						<?php endif; ?>
					</header>
					<div class="post-content">
						<?php the_content(); ?>
					</div>
					<footer class="post-footer">
						<div class="post_settings">
							<?php if((get_field('gd_blogg_skribent_namn_en')) && (get_field('gd_blogg_skribent_epost_en'))) { ?>
								<div class="author">
									<span><a href="mailto:<?php echo antispambot(get_field('gd_blogg_skribent_epost_en')); ?>" title="Send an email to <?php echo get_field('gd_blogg_skribent_namn_en'); ?>"><?php echo get_field('gd_blogg_skribent_namn_en'); ?></a></span>
								</div>
							<?php } elseif (get_field('gd_blogg_skribent_namn_en')) { ?>
								<div class="author">
									<span><?php echo get_field('gd_blogg_skribent_namn_en'); ?></span>
								</div>
							<?php } else { ?>

							<?php } ?>
							<div class="publish_change">
								<span>Published: <?php echo get_the_date('j F Y'); ?>, kl. <?php the_time('H:i') ?></span>  
								<span>Last updated: <?php the_modified_date('j F Y'); ?>, kl. <?php the_modified_time('H:i') ?></span>
							</div>
							<div class="share_post">
								<!-- FACEBOOK -->
								<a class="share facebook" onclick="window.open('http://www.facebook.com/sharer/sharer.php?u=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>', 'shareFacebook', 'width=650, height=270, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
									<i class="fa fa-facebook"></i> <span>Share on Facebook</span>
								</a>
								<!-- FACEBOOK END -->

								<!-- LINKEDIN -->
									<a class="share linkedin" onclick="window.open('http://www.linkedin.com/shareArticle?mini=true&url=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>&source=[SOURCE/DOMAIN]', 'shareLinkedIn', 'width=610, height=480, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
										<i class="fa fa-linkedin-square"></i> <span>Share on LinkedIn</span>
									</a>
								<!-- LINKEIND END -->


								<!-- MAIL -->
								<a class="share mail" href="mailto:?subject=<?php the_title(); ?>&body=Hej, jag tror att du skulle gilla detta. Kolla in detta <?php echo get_permalink(); ?>">
									<i class="fa fa-envelope" aria-hidden="true"></i> <span>Send as email</span>
								</a>
								<!-- MAIL END -->
							</div>
						</div>
					</footer>
				</article>
			<?php endwhile; ?>
			</div>

			<div class="sidebar">
				<div class="om-gd-bloggen">
					<div class="profilbild">
						<?php $profilbild_gd_bloggen = get_field('gd_bloggen_profilbild_en', 9827); ?>
						<?php if($profilbild_gd_bloggen) { ?>
							<img data-src="<?php echo $profilbild_gd_bloggen['sizes']['medium']; ?>" alt="" />
						<?php } ?>
					</div>
					<div class="content">
						<?php if (!empty(get_field('gd_bloggen_rubrik_en', 9827))): ?>
							<h3><?php echo get_field('gd_bloggen_rubrik_en', 9827); ?></h3>
						<?php endif; ?>
						<div class="info">
							<?php echo get_field('gd_bloggen_beskrivning_en', 9827); ?>

							<?php if(get_field('gd_bloggen_epost_en')) { ?>
								<a href="mailto:<?php echo antispambot(get_field('gd_bloggen_epost_en', 9827)); ?>">Send me a email</a>
							<?php } ?>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
<?php endif;?>