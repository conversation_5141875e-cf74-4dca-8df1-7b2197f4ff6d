<div id="page-header-parent">
	<div class="title">
		<div class="wrapper">
			<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
				<span class="title-page">Medarbetare</span>
			<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
				<span class="title-page">Employees</span>
			<?php endif;?>
		</div>
	</div>
	<div class="wrapper">
		<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
			<nav aria-label="Brödsmulor" id="breadcrumbs"><span xmlns:v="http://rdf.data-vocabulary.org/#"><span typeof="v:Breadcrumb"><a href="https://www.swedac.se/" title="Gå till Hem" rel="v:url" property="v:title">Hem</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="https://www.swedac.se/om-swedac/" title="Gå till Om Swedac" rel="v:url" property="v:title">Om Swedac</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="https://www.swedac.se/om-swedac/arbeta-pa-swedac/" title="Gå till Arbeta på Swedac" rel="v:url" property="v:title">Arbeta på Swedac</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="https://www.swedac.se/om-swedac/arbeta-pa-swedac/att-jobba-pa-swedac/" title="Gå till Träffa Swedacs medarbetare" rel="v:url" property="v:title">Träffa Swedacs medarbetare</a> / <span class="breadcrumb_last"><?php the_title(); ?></span></span></span></span></span></span></nav>
		<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
			<!-- <nav aria-label="Breadcrumb" id="breadcrumbs"><span xmlns:v="http://rdf.data-vocabulary.org/#">
				<span typeof="v:Breadcrumb"><a href="" title="Return to Home" rel="v:url" property="v:title">Home</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="/services/?lang=en" title="Return to Services" rel="v:url" property="v:title">Services</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="/services/swedac-academy/?lang=en" title="Return to Swedac Academy" rel="v:url" property="v:title">Swedac Academy</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="/services/swedac-academy/courses-2/?lang=en" title="Return to Courses" rel="v:url" property="v:title">Courses</a> / <span class="breadcrumb_last"><?php the_title(); ?></span></span></span></span>
			</nav> -->
		<?php endif;?>
	</div>
</div>

<div class="wrapper">
	<div id="single_section_post">
		<div class="main">
			<?php while (have_posts()) : the_post(); ?>
			<article <?php post_class('single-post'); ?>>
				<header class="post-header">
					<h1><?php the_title(); ?></h1>

					<div class="ingress">
						<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
							<?php echo get_field('medarbetare_ingress_sv'); ?>
						<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
							<?php echo get_field('medarbetare_ingress_en'); ?>
						<?php endif;?>
					</div>

					<?php if ( has_post_thumbnail()) : ?>
						<?php 
						$get_post_image = wp_get_attachment_image_src( get_post_thumbnail_id($post->ID), 'large' );
						$image_url = $get_post_image['0'];
						$img_id = get_post_thumbnail_id(get_the_ID());
						$alt_text = get_post_meta($img_id , '_wp_attachment_image_alt', true);
						$thumbnail_image = get_posts(array('p' => $img_id, 'post_type' => 'attachment'));
						$caption = ($thumbnail_image && isset($thumbnail_image[0])) ? $thumbnail_image[0]->post_excerpt : false;
						?>
						<?php if ($caption) : ?>
							<figure class="page_thumbnail">
								<img data-src="<?php echo $image_url; ?>" alt="<?php echo $alt_text; ?>" />
								<figcaption class="wp-caption-text"><?php echo $caption; ?></figcaption>
							</figure>
						<?php else : ?>
							<div class="page_thumbnail">
								<img data-src="<?php echo $image_url; ?>" alt="<?php echo $alt_text; ?>" />
							</div>
						<?php endif; ?>
					<?php endif; ?>
				</header>
				<div class="post-content">
					<?php the_content(); ?>
				</div>

				<footer class="post-footer">
					<div class="post_settings">
						<?php if(ICL_LANGUAGE_CODE=='sv'): ?>



							<div class="publish_change">
								<span>Publicerad: <?php echo get_the_date('j F Y'); ?>, kl. <?php the_time('H:i') ?></span>
								<span>Senast uppdaterad: <?php the_modified_date('j F Y'); ?>, kl. <?php the_modified_time('H:i') ?></span>
							</div>
							<div class="share_post">
								<a class="share facebook" onclick="window.open('http://www.facebook.com/sharer/sharer.php?u=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>', 'shareFacebook', 'width=650, height=270, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
									<i class="fa fa-facebook"></i> <span>Dela på Facebook</span>
								</a>
								<a class="share linkedin" onclick="window.open('http://www.linkedin.com/shareArticle?mini=true&url=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>&source=[SOURCE/DOMAIN]', 'shareLinkedIn', 'width=610, height=480, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
									<i class="fa fa-linkedin-square"></i> <span>Dela på LinkedIn</span>
								</a>
								<a class="share mail" href="mailto:?subject=<?php the_title(); ?>&body=Hej, jag tror att du skulle gilla detta. Kolla in detta <?php echo get_permalink(); ?>">
									<i class="fa fa-envelope" aria-hidden="true"></i> <span>Skicka som e-post</span>
								</a>
							</div>
						<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>

							<div class="publish_change">
								<span>Published: <?php echo get_the_date('j F Y'); ?>, kl. <?php the_time('H:i') ?></span>
								<span>Last updated: <?php the_modified_date('j F Y'); ?>, kl. <?php the_modified_time('H:i') ?></span>
							</div>
							<div class="share_post">
								<a class="share facebook" onclick="window.open('http://www.facebook.com/sharer/sharer.php?u=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>', 'shareFacebook', 'width=650, height=270, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
									<i class="fa fa-facebook"></i> <span>Share on Facebook</span>
								</a>
								<a class="share linkedin" onclick="window.open('http://www.linkedin.com/shareArticle?mini=true&url=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>&source=[SOURCE/DOMAIN]', 'shareLinkedIn', 'width=610, height=480, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
									<i class="fa fa-linkedin-square"></i> <span>Share on LinkedIn</span>
								</a>
								<a class="share mail" href="mailto:?subject=<?php the_title(); ?>&body=Hej, jag tror att du skulle gilla detta. Kolla in detta <?php echo get_permalink(); ?>">
									<i class="fa fa-envelope" aria-hidden="true"></i> <span>Send as email</span>
								</a>
							</div>
						<?php endif;?>
					</div>
				</footer>
			</article>
		<?php endwhile; ?>
		</div>

		<div class="sidebar">
			<?php get_template_part('templates/sidebar', 'page'); ?>
		</div>
	</div>
</div>
