<div id="page-header-parent">
	<div class="title">
		<div class="wrapper">

			<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
				<span class="title-page">Tj<PERSON><PERSON>er</span>
			<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
				<span class="title-page">Services</span>
			<?php endif;?>
		</div>
	</div>
	<div class="wrapper">
		<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
			<nav aria-label="Brödsmulor" id="breadcrumbs"><span xmlns:v="http://rdf.data-vocabulary.org/#">
				<span typeof="v:Breadcrumb"><a href="" title="Gå till Hem" rel="v:url" property="v:title">Hem</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="/tjanster/" title="Gå till Tjänster" rel="v:url" property="v:title">Tj<PERSON><PERSON>er</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="/tjanster/swedac-academy/" title="Gå till Swedac Academy" rel="v:url" property="v:title">Swedac Academy</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="/tjanster/swedac-academy/kurser/" title="Gå till Kurser" rel="v:url" property="v:title">Kurser</a> / <span class="breadcrumb_last"><?php the_title(); ?></span></span></span></span>
			</nav>
		<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
			<nav aria-label="Breadcrumb" id="breadcrumbs"><span xmlns:v="http://rdf.data-vocabulary.org/#">
				<span typeof="v:Breadcrumb"><a href="" title="Return to Home" rel="v:url" property="v:title">Home</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="/services/?lang=en" title="Return to Services" rel="v:url" property="v:title">Services</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="/services/swedac-academy/?lang=en" rel="v:url" property="v:title">Swedac Academy</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="/services/swedac-academy/courses-2/?lang=en" title="Return to Courses" rel="v:url" property="v:title">Courses</a> / <span class="breadcrumb_last"><?php the_title(); ?></span></span></span></span>
			</nav>
		<?php endif;?>
	</div>
</div>

<div class="wrapper">
	<div id="single_section_post">
		<div class="main">
			<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
				<?php while (have_posts()) : the_post(); ?>
					<article <?php post_class('kurs-post'); ?>>
						<header class="post-header">
							<?php if( isset($_GET['success_booking']) ) {
								$firstname = $_GET['firstname'];
								$lastname = $_GET['lastname'];
								$education = $_GET['education']; ?>

								<div class="messange_box">
									<div class="inner success">
										<h2>Tack för din anmälan, <?php echo $firstname; ?>!</h2>
										<p>Du har blivit anmäld till kursen:</p>
										<p><strong><?php echo $education; ?></strong></p>
									</div>
								</div>

							<?php } if( isset($_GET['failed_booking']) ) { ?>

								<div class="messange_box">
									<div class="inner failed">
										<h2>Din anmälan har <strong>inte</strong> skickats.</h2>
										<p>Det verkar som att något gick fel. Vänligen försök en gång till.</p>
										<p>Skulle samma fel uppstå en gång till så är du välkommen att kontakta oss istället.</p>
									</div>
								</div>
							<?php } ?>
							<?php if( isset($_GET['human_validation_failed']) ) { ?>
							<div class="messange_content">
								<div class="inner failed">
									<h2>Något gick fel!</h2>
									<p>Vi kunde inte verifiera att du är en människa. Var god försök igen.</p>
									<p>Skulle problemet kvarstå så är du välkommen att kontakta oss via telefon.</p>
								</div>
							</div>
							<?php } ?>
							<h1><?php the_title(); ?></h1>
							<?php if ( has_post_thumbnail()) : ?>
								<?php $get_post_image = wp_get_attachment_image_src( get_post_thumbnail_id($post->ID), 'large' );
								$image_url = $get_post_image['0'];
								$img_id = get_post_thumbnail_id(get_the_ID());
								$alt_text = get_post_meta($img_id , '_wp_attachment_image_alt', true);
								$thumbnail_image = get_posts(array('p' => $img_id, 'post_type' => 'attachment'));
								$caption = ($thumbnail_image && isset($thumbnail_image[0])) ? $thumbnail_image[0]->post_excerpt : false;
								?>
								<?php if ($caption) : ?>
									<figure class="page_thumbnail">
										<img data-src="<?php echo $image_url; ?>" alt="<?php echo $alt_text; ?>" />
										<figcaption class="wp-caption-text"><?php echo $caption; ?></figcaption>
									</figure>
								<?php else : ?>
									<div class="page_thumbnail">
										<img data-src="<?php echo $image_url; ?>" alt="<?php echo $alt_text; ?>" />
									</div>
								<?php endif; ?>
							<?php endif; ?>
						</header>
						<div class="post-content">
							<?php the_content(); ?>
						</div>

						<span class="symple-button reg_course">Registrera dig till denna kurs</span>

						<div id="reg_course">
							<div id="booking_course">

								<form id="booking_course_form" class="form clear" name="booking_course_form" method="post" action="/sender.php?booking_course_form" novalidate="novalidate" autocomplete="false">

									<div class="top">
										<h3>Välj kurstillfälle</h3>
										<div class="row education">
									        <?php if(get_field('swedac_academy_kurstillfallen')) { ?>
									        	<label class="label_select">
				    								<select class="req" name="swedac_education_kurstillfalle" id="swedac_education_kurstillfalle" >
				    									<option value="" disabled selected>
				    							    		Välj kurstillfälle
				    							    	</option>
				    									<?php while(has_sub_field('swedac_academy_kurstillfallen')): ?>
				    										<option>
				    											<?php echo get_sub_field('datum'); ?> - <?php echo get_sub_field('tid'); ?>
				    										</option>
				    									<?php endwhile; ?>
				    								</select>
			    								</label>
											<?php } else { ?>
												<p>Inga kurstillfällen finns upplagda. Men du kan fortfarande skicka in en anmälan för att visa ditt intresse.</p>
			    							<?php } ?>


										  	<i class="fa fa-check"></i>
										  	<i class="fa fa-times"></i>
										</div>
									</div>

									<div class="middle">
										<h3>Personlig information</h3>
										<div class="row title">
											<input type="text" class="inputbox req" name="title" id="title" placeholder="Titel *" >
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>
										<div class="row fname">
											<input type="text" class="inputbox req" name="firstname" id="firstname" placeholder="Förnamn *" >
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>
										<div class="row lname">
											<input type="text" class="inputbox req" name="lastname" id="lastname" placeholder="Efternamn *" >
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>
										<div class="row company">
											<input type="text" class="inputbox req" name="company" id="company" placeholder="Företag *" >
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>
										<div class="row anstllning">
											<input type="text" class="inputbox req" name="anstallning_position" id="anstallning_position" placeholder="Anställning/position *" >
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>
										<div class="row adress">
											<input type="text" class="inputbox req" name="adress" id="adress" placeholder="Företagsadress *" >
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>
										<div class="row land">
											<input type="text" class="inputbox req" name="country" id="country" placeholder="Land *" >
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>
										<div class="row phone">
											<input type="tel" class="inputbox req" name="phone" id="phone" onkeypress="return event.charCode >= 48 && event.charCode <= 57" placeholder="Telefon *">
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>
										<div class="row email">
											<input type="email" class="inputbox req" name="email" id="email" placeholder="E-postadress *">
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>

										<div class="row skraddarsy_kurs">
											<label for="skraddarsy_kurs">Jag skulle vilja ha en skräddarsydd kurs eller studiebesök med följande innehåll:</label>
											<textarea name="skraddarsy_kurs" id="skraddarsy_kurs" class="textarea" placeholder="" rows="5"></textarea>
										</div>

										<div class="row speciella_onskemal">
											<label for="speciella_onskemal">Ytterligare information eller speciella önskemål</label>
											<textarea name="speciella_onskemal" id="speciella_onskemal" class="textarea" placeholder="" rows="5"></textarea>
										</div>
									</div>
									<div class="middle_extra">
										<h3>Övrig deltagare</h3>

										<div id="extra_section"></div>
										<span class="no_deltagare">Inga övriga deltagare</span>
										<span class="create_one_more" id="create_one_more">
											Lägg till deltagare
										</span>

									</div>
									<div class="frc-captcha" data-sitekey="FCMHCLG9N0S4NF1M" data-lang="<?php echo strtolower(ICL_LANGUAGE_CODE); ?>"></div>
									<div class="bottom">
										<div class="left">
											<input class="submit-booking" type="submit" value="Skicka anmälan" disabled>
										</div>
										<div class="right">
											<!-- <a href="#">Se Swedac Academys villkor</a> -->
										</div>

										<span>* Obligatioriska fält</span>
									</div>
									<input type="hidden" name="swedac_education" value="<?php the_title(); ?>">
									<input type="hidden" name="url" value="<?php echo strtok($_SERVER["REQUEST_URI"],'?');?>"/>
								</form>
								<script>
									document.addEventListener('frc-captcha-solved', function(event) {
										jQuery('#booking_course_form .submit-booking').prop('disabled', false)
									});
								</script>
							</div>
						</div>
						<footer class="post-footer">
							<?php get_template_part('templates/author', 'page');?>
						</footer>
					</article>
				<?php endwhile; ?>
			<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
				<?php while (have_posts()) : the_post(); ?>
					<article <?php post_class('kurs-post'); ?>>
						<header class="post-header">
							<?php if( isset($_GET['success_booking']) ) {
								$firstname = $_GET['firstname'];
								$lastname = $_GET['lastname'];
								$education = $_GET['education']; ?>

								<div class="messange_box">
									<div class="inner success">
										<h2>Thank you for registration, <?php echo $firstname; ?>!</h2>
										<p>You have been registered for the course:</p>
										<p><strong><?php echo $education; ?></strong></p>
									</div>
								</div>

							<?php } if( isset($_GET['failed_booking']) ) { ?>

								<div class="messange_box">
									<div class="inner failed">
										<h2>Your registration has <strong>not</strong> been send.</h2>
										<p>It seems as that something went wrong. Please try again.</p>
										<p>If the same error occur again, you are welcome to contact us instead.</p>
									</div>
								</div>
							<?php } ?>
							<?php if( isset($_GET['human_validation_failed']) ) { ?>
							<div class="messange_content">
								<div class="inner failed">
									<h2>Something went wrong!</h2>
									<p>We could not verify that you are human. Please try again.</p>
									<p>Should the issue remain, you are welcome to contact us instead.</p>
								</div>
							</div>
							<?php } ?>
							<h1><?php the_title(); ?></h1>
							<?php if ( has_post_thumbnail()) : ?>
								<?php $get_post_image = wp_get_attachment_image_src( get_post_thumbnail_id($post->ID), 'large' );
								$image_url = $get_post_image['0'];						
								$img_id = get_post_thumbnail_id(get_the_ID());
								$alt_text = get_post_meta($img_id , '_wp_attachment_image_alt', true); ?>
		
								<div class="page_thumbnail">
									<img data-src="<?php echo $image_url; ?>" alt="<?php echo $alt_text; ?>" />
								</div>
							<?php endif; ?>
						</header>
						<div class="post-content">
							<?php the_content(); ?>
						</div>

						<span class="symple-button reg_course">Register for this course</span>

						<div id="reg_course">
							<div id="booking_course">

								<form id="booking_course_form" class="form clear" name="booking_course_form" method="post" action="/sender.php?booking_course_form" novalidate="novalidate" autocomplete="false">

									<div class="top">
										<h3>Select occasion</h3>
										<div class="row education">
									        <?php if(get_field('swedac_academy_kurstillfallen_en')) { ?>
									        	<label class="label_select">
				    								<select class="req" name="swedac_education_kurstillfalle" id="swedac_education_kurstillfalle" >
				    									<option value="" disabled selected>
				    							    		Select occasion
				    							    	</option>
				    									<?php while(has_sub_field('swedac_academy_kurstillfallen_en')): ?>
				    										<option>
				    											<?php echo get_sub_field('datum'); ?> - <?php echo get_sub_field('tid'); ?>
				    										</option>
				    									<?php endwhile; ?>
				    								</select>
			    								</label>
											<?php } else { ?>
												<p>No course dates are registered. But you can still submit an application to show your interest.</p>
			    							<?php } ?>


										  	<i class="fa fa-check"></i>
										  	<i class="fa fa-times"></i>
										</div>
									</div>

									<div class="middle">
										<h3>Personal Information</h3>
										<div class="row title">
											<input type="text" class="inputbox req" name="title" id="title" placeholder="Title *" >
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>
										<div class="row fname">
											<input type="text" class="inputbox req" name="firstname" id="firstname" placeholder="First name *" >
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>
										<div class="row lname">
											<input type="text" class="inputbox req" name="lastname" id="lastname" placeholder="Last name *" >
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>
										<div class="row company">
											<input type="text" class="inputbox req" name="company" id="company" placeholder="Company *" >
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>

										<div class="row var_number">
											<input type="text" class="inputbox" name="var_number" id="var_number" placeholder="VAT registration number" >
										</div>
										<div class="row position">
											<input type="text" class="inputbox req" name="anstallning_position" id="anstallning_position" placeholder="Position *" >
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>

										<div class="row adress">
											<input type="text" class="inputbox req" name="adress" id="adress" placeholder="Company address *" >
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>

										<div class="row country">
											<input type="text" class="inputbox req" name="country" id="country" placeholder="Country *" >
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>

										<div class="row phone">
											<input type="tel" class="inputbox req" name="phone" id="phone" onkeypress="return event.charCode >= 48 && event.charCode <= 57" placeholder="Phone *">
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>
										<div class="row email">
											<input type="email" class="inputbox req" name="email" id="email" placeholder="Email *">
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>

										<div class="row skraddarsy_kurs">
											<label for="skraddarsy_kurs">I would like a customized course or study visit with the following content:</label>
											<textarea name="skraddarsy_kurs" id="skraddarsy_kurs" class="textarea" placeholder="" rows="5"></textarea>
										</div>

										<div class="row speciella_onskemal">
											<label for="speciella_onskemal">Further information or special requirements</label>
											<textarea name="speciella_onskemal" id="speciella_onskemal" class="textarea" placeholder="" rows="5"></textarea>
										</div>

									</div>
									<div class="middle_extra">
										<h3>Other participants</h3>

										<div id="extra_section"></div>
										<span class="no_deltagare">No other participants</span>
										<span class="create_one_more" id="create_one_more">
											Add participants
										</span>

									</div>
									<div class="frc-captcha" data-sitekey="FCMHCLG9N0S4NF1M" data-lang="<?php echo strtolower(ICL_LANGUAGE_CODE); ?>"></div>
									<div class="bottom">
										<div class="left">
											<input class="submit-booking" type="submit" value="Send application" disabled>
										</div>
										<div class="right">
											<!-- <a href="#">Se Swedac Academys villkor</a> -->
										</div>

										<span>* Required fields</span>
									</div>
									<input type="hidden" name="swedac_education" value="<?php the_title(); ?>">
									<input type="hidden" name="url" value="<?php echo strtok($_SERVER["REQUEST_URI"],'?');?>"/>
								</form>
								<script>
									document.addEventListener('frc-captcha-solved', function(event) {
										jQuery('#booking_course_form .submit-booking').prop('disabled', false)
									});
								</script>
							</div>
						</div>
						<footer class="post-footer">
							<?php get_template_part('templates/author', 'page');?>
						</footer>
					</article>
				<?php endwhile; ?>
			<?php endif;?>
		</div>

		<div class="sidebar">
			<?php get_template_part('templates/sidebar', 'page'); ?>
		</div>
	</div>
</div>
<?php
/**
 * Template part for displaying single course content
 * 
 * HONESTLY DON"T KNOW WHY THIS IS HERE
 */
?>

<!-- <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
    <div class="post-content">
        <div class="course-header">
            <h1 class="entry-title"><?php the_title(); ?></h1>
        </div>

        <div class="course-details">
            <?php if(get_field('course_date')): ?>
                <div class="course-date">
                    <span class="label"><?php _e('Datum:', 'swedac_theme'); ?></span>
                    <span class="value"><?php the_field('course_date'); ?></span>
                </div>
            <?php endif; ?>

            <?php if(get_field('course_location')): ?>
                <div class="course-location">
                    <span class="label"><?php _e('Plats:', 'swedac_theme'); ?></span>
                    <span class="value"><?php the_field('course_location'); ?></span>
                </div>
            <?php endif; ?>

            <?php if(get_field('course_price')): ?>
                <div class="course-price">
                    <span class="label"><?php _e('Pris:', 'swedac_theme'); ?></span>
                    <span class="value"><?php the_field('course_price'); ?></span>
                </div>
            <?php endif; ?>
        </div>

        <div class="course-content">
            <?php the_content(); ?>
        </div>

        <?php if(get_field('course_registration_open')): ?>
            <div class="course-registration">
                <a href="<?php echo esc_url(get_permalink(ICL_LANGUAGE_CODE == 'en' ? 3509 : 118)); ?>" class="reg_course symple-button">
                    <?php _e('Anmäl dig till kursen', 'swedac_theme'); ?>
                </a>
            </div>
        <?php endif; ?>
    </div>
</article> -->
