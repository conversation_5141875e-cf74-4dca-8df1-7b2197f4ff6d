	<div id="page-header-parent">
		<div class="title">
			<div class="wrapper">
				<span class="title-page">Law &amp; Order</span>
			</div>
		</div>

		<div class="wrapper">
			<nav aria-label="Breadcrumb" id="breadcrumbs">
				<span xmlns:v="http://rdf.data-vocabulary.org/#"><span typeof="v:Breadcrumb"><a href="https://www.swedac.se" title="Return to Home" rel="v:url" property="v:title">Home</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="https://www.swedac.se/law-order?lang=en" title="Return to Law & Order" rel="v:url" property="v:title">Law &amp; Order</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="https://www.swedac.se/law-order/swedacs-regulations/" title="Return to Swedac’s regulations">Swedac’s regulations</a> / <span class="breadcrumb_last"><?php the_title(); ?></span></span></span></span></span>
			</nav>
		</div>
	</div>

	<div id="single_section_post_dokument">

			<div class="main">
				<?php while (have_posts()) : the_post(); ?>
				<article <?php post_class('single-post-dokument'); ?>>
					<header class="post-header">
						<div class="wrapper">
							<h1><?php echo get_field('crm_doc_dokumentbeteckning_en'); ?> - <?php the_title(); ?></h1>

							<div class="dokument_section">

									<?php
										$url = get_field('crm_doc_doc_en');
										if($url) {
											$parts = explode('/', $url);
											$value = $parts[count($parts) - 1];

											$search  = array('-', '_', '.pdf');
											$replace = array(' ', ':', '');
											$valueFormated = str_replace($search, $replace, $value);

										?>
										<ul>
											<li>
												<div class="left">
													<img data-src="<?php echo get_template_directory_uri(); ?>/assets/images/icon-document-large.png" alt="Dokument" />
												</div>
												<div class="right">
													<span class="file">
														<h3>
															<a href="<?php echo $url; ?>" title="" target="_blank">Download document</a>
														</h3>
													</span>
													<span class="file_name"><?php the_title(); ?> - <?php echo $valueFormated; ?></span>
													<span class="file_type_size">PDF</span>
												</div>
											</li>
										</ul>
									<?php } ?>

							</div>

						</div>
					</header>
					<div class="wrapper">
						<div class="post-content">
							<h3>Information about the regulations</h3>
							<div class="allman_info">
								<table>
									<tbody>
										<?php if(get_field('crm_doc_description_en')) { ?>
											<tr class="doc_description">
												<td class="a"><strong>Description</strong>:</td>
												<td class="b"><?php echo get_field('crm_doc_description_en'); ?></a></td>
											</tr>
										<?php } ?>
										<?php if(get_field('crm_doc_originalver_en')) {

											$url = get_field('crm_doc_originalver_en');
											$parts = explode('/', $url);
											$value = $parts[count($parts) - 1];

											$search  = array('-', '_konsol', '_', '.pdf');
											$replace = array(' ', '', ':', '');
											$valueFormated = str_replace($search, $replace, $value); ?>
											<tr>
												<td class="a"><strong>Basic Constitutional</strong>:</td>
												<td class="b">
													<?php $args = array(
														'status'			=> 'publish',
														'post_type' 		=> 'dokument_eng',
														'posts_per_page' 	=> 1,
														'meta_key'			=> 'crm_doc_dokumentbeteckning_en',
														'meta_value'		=> $valueFormated,
													);
													$query = new WP_Query($args);
													if($query->have_posts()): ?>
												    	<?php while($query->have_posts()):$query->the_post(); ?>
															<a href="<?php the_permalink(); ?>" title="<?php the_title(); ?>">
																<?php echo get_field('crm_doc_dokumentbeteckning_en'); ?>
															</a>
												    	<?php endwhile; ?>
													<?php endif;
													wp_reset_postdata(); ?>
												</td>
											</tr>
										<?php } ?>

										<?php if(get_field('crm_doc_amendmentver_en')): ?>
											<tr>
												<td class="a"><strong>Change Legislation</strong>:</td>
												<td class="b">
													<?php if(get_field('crm_doc_amendmentver_en')): ?>
															<?php while(has_sub_field('crm_doc_amendmentver_en')): ?>

																<?php if(get_sub_field('forfattning')) {
																	$url = get_sub_field('forfattning');
																	$parts = explode('/', $url);
																	$value = $parts[count($parts) - 2];
																} ?>
																<span>
																	<a href="<?php echo get_sub_field('forfattning'); ?>" target="_blank"><?php echo $value; ?> </a>
																</span>
															<?php endwhile; ?>
													<?php endif; ?>
												</td>
											</tr>
										<?php endif; ?>
										<?php if(get_field('crm_doc_authorization_en')) { ?>
											<tr>
												<td class="a"><strong>Authorization</strong>:</td>
												<td class="b"><?php echo get_field('crm_doc_authorization_en'); ?></td>
											</tr>
										<?php } ?>
										<?php if(get_field('crm_doc_celexnumber_en')) { ?>
											<tr>
												<td class="a"><strong>Celexnummer</strong>:</td>
												<td class="b"><?php echo get_field('crm_doc_celexnumber_en'); ?></td>
											</tr>

										<?php } ?>

									</tbody>
								</table>
							</div>

							<div class="relateade_omraden">
								<?php if(get_field('crm_doc_area_en')):
										//$rows = get_field('crm_doc_area');
										$doc_area = get_field('crm_doc_area_en');
										$doc_area = array_map(function ($omrade){
											return $omrade['omradesnamn'];
										}, $doc_area);

										$doc_area = array_reduce($doc_area, function($carry, $item) {
											if(empty($carry)) {
												$carry .= $item;
											} else {
												$carry .= ", $item";
											}
											return $carry;
										});
									endif;

									$findMatchedArea = array(
										'post_type' 		=> array('amnesomraden'),
										'posts_per_page' 	=> -1,
										'post_status' 		=> 'publish',
										'orderby' 			=> 'date',
										'order'				=> 'ASC',
										's' 				=> $doc_area
									);

								$areaFoundQuery = new WP_Query($findMatchedArea);
								if($areaFoundQuery->have_posts()): ?>
									<h3>Related areas</h3>
									<ul class="suggestion_posts">
									    <?php while($areaFoundQuery->have_posts()):$areaFoundQuery->the_post(); ?>
									        <li <?php post_class(); ?>>
									        	<a href="<?php the_permalink(); ?>" title="See <?php the_title(); ?>">
									        		<?php the_title(); ?>
									        	</a>
									     	</li>
									    <?php endwhile; ?>
								  	</ul>
								<?php endif; wp_reset_postdata(); ?>
							</div>
						</div>
					</div>
				</article>
			<?php endwhile; ?>

				<div id="sok_foreskriver_dokument">

					<div class="wrapper">
							<div class="title">
								<img alt="Sök" data-src="<?php echo get_template_directory_uri(); ?>/assets/images/icon-search.png">
								<span><h2>Search regulations and documents</h2></span>
							</div>
							<form id="searchform_foreskrifter_dokument"action="<?= get_site_url();?>" method="get">
								<div class="row">
									<input type="search" class="search-field" placeholder="Text search" value="" name="s" title="Text search">
								</div>
								<div class="row">
									<input type="text" class="search-field" placeholder="Document number" name="doc_nr" title="Document number">
								</div>
								<div class="row">
									<?php $all_omraden = array(
										'post_type' 		=> 'amnesomraden',
										'posts_per_page' 	=> -1,
										'orderby'			=> 'title',
										'order' 			=> 'ASC'
									);

									$query_all = new WP_Query($all_omraden);
									if($query_all->have_posts()): ?>
										<label class="label_select" for="all_omraden_select">Area</label>

										<select id="all_omraden_select" name="omraden">
										    <option selected disabled>Choose area</option>
										    <?php while($query_all->have_posts()):$query_all->the_post(); ?>
										       <option value="<?php the_title(); ?>"><?php the_title(); ?></option>
										    <?php endwhile; ?>
									  	</select>
									<?php else:  ?>
										<label class="label_select" for="all_omraden_select">Area</label>

										<select id="all_omraden_select" name="omraden">
										    <option selected disabled>No areas exists</option>
									  	</select>
									<?php endif; wp_reset_query(); ?>
								</div>
								<div class="row">
									<label class="label_select" for="typ_av_dokument">Type of document</label>
										<select id="typ_av_dokument" name="document_type">
											<option value="" selected>All types of documents</option>
											<option value="stafs">STAFS</option>
											<option value="doc">DOC</option>
											<option value="rep">REP</option>
											<option value="info">INFO</option>
										</select>
								</div>
								<input name="site_section" type="hidden" value="document_pt" />

								<input type="hidden" name="lang" value="en">
								<input type="hidden" name="post_type" value="document_eng" />
								<input type="submit" class="search-submit" value="Search">

							</form>

						</div>

				</div>


				<div class="wrapper">
					<div class="senaste_dokument">

						<h3>Latest published documents</h3>
						<?php $paged = get_query_var( 'paged' ) ? get_query_var( 'paged' ) : 1;
						$args = array(
							'post_status' 		=> 'publish',
							'post_type' 		=> 'document_eng',
							'paged' 			=> $paged,
							'posts_per_page' 	=> 10,
							'orderby' 			=> 'date',
							'meta_query' => array (
							    array(
							        'key' => 'crm_doc_doc_en',
							        'value' => '.pdf',
							        'compare' => 'LIKE'
							    )
							)
						);

						$query = new WP_Query($args);
						if($query->have_posts()): ?>
							<div class="table_wrapper">
								<table>
									<thead>
										<tr>
											<th>DOC.NR</th>
											<th>TITLE</th>
											<th>TYPE OF DOCUMENT</th>
											<th>DATE</th>
										</tr>
									</thead>
									<tbody>
									   	<?php while( $query->have_posts() ) : $query->the_post(); ?>
							          		<?php if(get_field('crm_doc_doc_en')) { ?>
				          			      		<tr>
				          							<td><?php echo get_field('crm_doc_dokumentbeteckning_en'); ?></td>
				          							<td><a href="<?php the_permalink(); ?>" title="<?php the_title(); ?>"><?php the_title(); ?></a></td>
				          							<td><?php echo get_field('crm_doc_type_en'); ?></td>
				          							<td><?php the_time('Y-m-d'); ?></td>
				          			      		</tr>
							          		<?php } ?>
									    <?php endwhile; ?>
								   </tbody>
								</table>
						  	</div>
						<?php endif; wp_reset_postdata(); ?>
					</div>
				</div>

			</div>
	</div>
