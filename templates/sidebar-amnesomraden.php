<div id="sidebar_amnesomraden">
	<?php if (ICL_LANGUAGE_CODE == 'sv'): ?>
		<h2 class="sidebar_title">Innehåll</h2>

		<?php
		$ids = get_field('show_links_on_amnesomrade', false, false);

		$postRelated = get_field('relaterad_information');

		wp_reset_query();

		if (!empty($ids)):
			$args = array(
				'post_type' => 'intern_lankningar',
				'posts_per_page' => -1,
				'post__in' => $ids,
				'orderby' => 'date',
				'order' => 'asc',
			);

			$query = new WP_Query($args);
			?>
			<ul>
				<?php
				if ($query->have_posts()):
					$t = 1;
					while ($query->have_posts()):
						$query->the_post();

						$post_slug = $post->post_name;
						?>
						<li <?php post_class('interna_lankmal ' . $post_slug); ?>>
							<a class="d-inline-flex" href="#target_<?php echo $t; ?>">
								<img class="sidebarIcon" data-src="<?php echo get_field('lankar_ikon_blue'); ?>" alt="">
								<p class="ml-3 m-0"><?php the_title(); ?></p>
							</a>
						</li>
						<?php
						$t++;
					endwhile;
				endif;

				wp_reset_postdata();
				?>
				<li class="interna_lankmal">
					<a class="d-inline-flex" href="/print-content.php?print=<?php echo $wp_query->post->ID; ?>"
						target="_blank">
						<img class="sidebarIcon" alt=""
							data-src="https://www.swedac.se/wp-content/uploads/2016/04/icon-print-blue.svg">
						<p class="ml-3 m-0">Skriv ut material</p>
					</a>
				</li>
			</ul>
			<?php
		endif;
		?>

		<!-- Visa ackrediterade företag -->
		<?php if (get_field("ackrediterade_foretag")): ?>
			<div class="pt-3 ackrediterade_foretag_serach">
				<strong>Se alla ackrediterade företag/organisationer</strong>
				<ul>
					<?php while (has_sub_field("ackrediterade_foretag")):
						$name = get_sub_field("namn");
						$sokurl = get_sub_field("sokurl");
						?>

						<li>
							<a href="<?php echo $sokurl; ?>" title="<?php echo $name; ?>">
								<i class="fa fa-angle-right" aria-hidden="true"></i> <?php echo $name; ?>
							</a>
						</li>
					<?php endwhile; ?>
				</ul>
			</div>
		<?php endif; ?>

		<?php if (get_field("certifieringsorgan_lankar")): ?>
			<div class="ackrediterade_foretag_serach">
				<strong>Se alla kravspecifikationer för <?php echo get_the_title(); ?></strong>
				<ul>
					<?php while (has_sub_field("certifieringsorgan_lankar")):
						$name = get_sub_field("namn");
						$sokurl = get_sub_field("sokurl");
						?>

						<li>
							<a href="<?php echo $sokurl; ?>" title="<?php echo $name; ?>">
								<i class="fa fa-angle-right" aria-hidden="true"></i> <?php echo $name; ?>
							</a>
						</li>
					<?php endwhile; ?>
				</ul>
			</div>
		<?php endif; ?>

	<?php elseif (ICL_LANGUAGE_CODE == 'en'): ?>
		<h2 class="sidebar_title">Content</h2>

		<?php
		$ids = get_field('show_links_on_amnesomrade_en', false, false);

		if ($ids) {
			$args = array(
				'post_type' => 'intern_lankningar',
				'posts_per_page' => 8,
				'post__in' => $ids,
				'orderby' => 'date',
				'order' => 'asc'
			);

			$query = new WP_Query($args);
			if ($query->have_posts()): ?>
				<ul>
					<?php if ($query->have_posts()): ?>
						<?php $t = 1;
						while ($query->have_posts()):
							$query->the_post();
							$post_slug = $post->post_name; ?>
							<li <?php post_class('interna_lankmal ' . $post_slug); ?>>
								<a class="d-inline-flex" href="#target_<?php echo $t; ?>">
									<img class="sidebarIcon" alt="" data-src="<?php echo get_field('lankar_ikon_blue_en'); ?>">
									<p class="ml-3 m-0"><?php the_title(); ?></p>
								</a>
							</li>
							<?php $t++; ?>
						<?php endwhile; ?>
					<?php endif;
					wp_reset_postdata(); ?>
					<li class="interna_lankmal">
						<a class="d-inline-flex" href="/print-content.php?print=<?php echo $wp_query->post->ID; ?>"
							target="_blank">
							<img class="sidebarIcon" alt=""
								data-src="https://www.swedac.se/wp-content/uploads/2016/04/icon-print-blue.svg">
							<p class="ml-3 m-0">Print material</p>
						</a>
					</li>
				</ul>
			<?php endif;
			wp_reset_postdata(); ?>

		<?php } else { ?>
			<ul>
				<li class="interna_lankmal">
					<a class="d-inline-flex" href="/print-content.php?print=<?php echo $wp_query->post->ID; ?>"
						target="_blank">
						<img class="sidebarIcon" alt=""
							data-src="https://www.swedac.se/wp-content/uploads/2016/04/icon-print-blue.svg">
						<p class="ml-3 m-0">Print material</p>
					</a>
				</li>
			</ul>
		<?php } ?>
	<?php endif; ?>

</div>