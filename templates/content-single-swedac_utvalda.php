<div id="page-header-parent">
	<div class="title">
		<div class="wrapper">
			<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
				<span class="title-page">Swedacskolan</span>
			<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
				<span class="title-page">Swedac school</span>
			<?php endif;?>

		</div>
	</div>

	<?php
		$post_id = get_queried_object_id();
		if ( has_post_thumbnail( $post_id ) ) :
		    $image_array = wp_get_attachment_image_src( get_post_thumbnail_id( $post_id ), 'swedac_magasin_single' );
		    $image = $image_array[0];
		endif;
	?>

	<?php $swedacMagasinrektangulark = get_field('swedac_magasin_rektangular');
		$imageSwedacMagasin = $swedacMagasinrektangulark['sizes']['swedac_magasin_single']; ?>

	<div class="post_thumbnail">
		<?php if($imageSwedacMagasin) { ?>
			<div class="image" style="background-image: url(<?php echo $imageSwedacMagasin; ?>)"></div>
		<?php } else { ?>
			<div class="image" style="background-image: url(<?php echo $image; ?>)"></div>
		<?php } ?>
	</div>

</div>

<div class="wrapper">
	<div id="single_section_post_swedac_magasin">
		<div class="main">
			<?php while (have_posts()) : the_post(); ?>
				<article <?php post_class('single-post'); ?>>
					<header class="post-header">
						<h1><?php the_title(); ?></h1>
						<div class="ingress">
							<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
								<?php echo get_field('page_ingress_subpage_sv'); ?>
							<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
								<?php echo get_field('page_ingress_subpage_en'); ?>
							<?php endif;?>
						</div>
					</header>
					<div class="post-content">
						<?php the_content(); ?>
					</div>

					<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
						<?php // Hämta innehåll som är relaterat till den aktuella sidan men också skriva ut relatert information
						$inner_args = get_posts( array(
							'status'			=> 'publish',
							'post_type' 		=> array('news', 'page', 'swedac_magasin', 'amnesomraden'),
							'posts_per_page' 	=> 10,
							'orderby' 			=> 'date',
							'order' 			=> 'asc',
							'meta_query' => array(
								array(
									'key' => 'relaterad_information', // or whatever your custom post type field is called
									'value' => '"' . get_the_ID() . '"', // will work only if the main loop has started
									'compare' => 'LIKE'
								)
							)
						));
							$posts = get_field('relaterad_information');
							if(($inner_args) && ($posts) ) { ?>
								<div class="relaterad_information_custom">
									<h2>Relaterat</h2>
									<ul>
										<?php foreach($inner_args as $inner_arg): ?>
											<?php $postType = get_post_type_object(get_post_type($inner_arg->ID));
											if ($postType) {
												$postIn = esc_html($postType->labels->singular_name);
											} ?>
											<li>
												<a href="<?php echo get_the_permalink( $inner_arg->ID ); ?>"><?php echo get_the_title( $inner_arg->ID ); ?></a> <span>(<?php echo $postIn; ?>)</span>
											</li>
										<?php endforeach; ?>
										<?php foreach( $posts as $post): // variable must be called $post (IMPORTANT) ?>
											<?php setup_postdata($post); ?>
											<li>
												<a href="<?php the_permalink(); ?>"><?php the_title(); ?></a> <span>(<?php echo $postIn; ?>)</span>
											</li>
										<?php endforeach; ?>
									</ul>
								</div>
							<?php } elseif ($posts) { ?>
								<div class="relaterad_information_custom">
									<h2>Relaterat</h2>
									<ul>
										<?php foreach( $posts as $post): // variable must be called $post (IMPORTANT) ?>
											<?php setup_postdata($post); ?>
											<?php $postType = get_post_type_object(get_post_type());
											if ($postType) {
												$postIn = esc_html($postType->labels->singular_name);
											} ?>

											<li>
												<a href="<?php the_permalink(); ?>"><?php the_title(); ?></a> <span>(<?php echo $postIn; ?>)</span>
											</li>
										<?php endforeach; ?>
									</ul>
								</div>
							<?php } elseif ($inner_args) { ?>
								<div class="relaterad_information_custom">
									<h2>Relaterat</h2>
									<ul>
										<?php foreach($inner_args as $inner_arg): ?>
											<?php $postType = get_post_type_object(get_post_type($inner_arg->ID));
											if ($postType) {
												$postIn = esc_html($postType->labels->singular_name);
											} ?>
											<li>
												<a href="<?php echo get_the_permalink( $inner_arg->ID ); ?>"><?php echo get_the_title( $inner_arg->ID ); ?></a> <span>(<?php echo $postIn; ?>)</span>
											</li>
										<?php endforeach; ?>
									</ul>
								</div>
							<?php } else { ?>

							<?php } ?>
						<?php wp_reset_query();  ?>
					<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
						<?php // Hämta innehåll som är relaterat till den aktuella sidan men också skriva ut relatert information
						$inner_args = get_posts( array(
							'status'			=> 'publish',
							'post_type' 		=> array('news', 'page', 'swedac_magasin', 'amnesomraden'),
							'posts_per_page' 	=> 10,
							'orderby' 			=> 'date',
							'order' 			=> 'asc',
							'meta_query' => array(
								array(
									'key' => 'relaterad_information_en', // or whatever your custom post type field is called
									'value' => '"' . get_the_ID() . '"', // will work only if the main loop has started
									'compare' => 'LIKE'
								)
							)
						));
							$posts = get_field('relaterad_information_en');
							if(($inner_args) && ($posts) ) { ?>
								<div class="relaterad_information_custom">
									<h2>Related</h2>
									<ul>
										<?php foreach($inner_args as $inner_arg): ?>
											<?php $postType = get_post_type_object(get_post_type($inner_arg->ID));
											if ($postType) {
												$postIn = esc_html($postType->labels->singular_name);
											} ?>
											<li>
												<a href="<?php echo get_the_permalink( $inner_arg->ID ); ?>"><?php echo get_the_title( $inner_arg->ID ); ?></a> <span>(<?php echo $postIn; ?>)</span>
											</li>
										<?php endforeach; ?>
										<?php foreach( $posts as $post): // variable must be called $post (IMPORTANT) ?>
											<?php setup_postdata($post); ?>
											<li>
												<a href="<?php the_permalink(); ?>"><?php the_title(); ?></a> <span>(<?php echo $postIn; ?>)</span>
											</li>
										<?php endforeach; ?>
									</ul>
								</div>
							<?php } elseif ($posts) { ?>
								<div class="relaterad_information_custom">
									<h2>Related</h2>
									<ul>
										<?php foreach( $posts as $post): // variable must be called $post (IMPORTANT) ?>
											<?php setup_postdata($post); ?>
											<?php $postType = get_post_type_object(get_post_type());
											if ($postType) {
												$postIn = esc_html($postType->labels->singular_name);
											} ?>

											<li>
												<a href="<?php the_permalink(); ?>"><?php the_title(); ?></a> <span>(<?php echo $postIn; ?>)</span>
											</li>
										<?php endforeach; ?>
									</ul>
								</div>
							<?php } elseif ($inner_args) { ?>
								<div class="relaterad_information_custom">
									<h2>Related</h2>
									<ul>
										<?php foreach($inner_args as $inner_arg): ?>
											<?php $postType = get_post_type_object(get_post_type($inner_arg->ID));
											if ($postType) {
												$postIn = esc_html($postType->labels->singular_name);
											} ?>
											<li>
												<a href="<?php echo get_the_permalink( $inner_arg->ID ); ?>"><?php echo get_the_title( $inner_arg->ID ); ?></a> <span>(<?php echo $postIn; ?>)</span>
											</li>
										<?php endforeach; ?>
									</ul>
								</div>
							<?php } else { ?>

							<?php } ?>
						<?php wp_reset_query();  ?>
					<?php endif;?>

					<footer class="post-footer">
						<div class="post_settings">
							<?php if(ICL_LANGUAGE_CODE=='sv'): ?>

								<div class="share_post">
									<!-- FACEBOOK -->
									<a class="share facebook" onclick="window.open('http://www.facebook.com/sharer/sharer.php?u=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>', 'shareFacebook', 'width=650, height=270, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
										<i class="fa fa-facebook"></i> <span>Dela på Facebook</span>
									</a>
									<!-- FACEBOOK END -->

									<!-- LINKEDIN -->
										<a class="share linkedin" onclick="window.open('http://www.linkedin.com/shareArticle?mini=true&url=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>&source=[SOURCE/DOMAIN]', 'shareLinkedIn', 'width=610, height=480, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
											<i class="fa fa-linkedin-square"></i> <span>Dela på LinkedIn</span>
										</a>
									<!-- LINKEIND END -->


									<!-- MAIL -->
									<a class="share mail" href="mailto:?subject=<?php the_title(); ?>&body=Hej, jag tror att du skulle gilla detta. Kolla in detta <?php echo get_permalink(); ?>">
										<i class="fa fa-envelope" aria-hidden="true"></i> <span>Skicka som e-post</span>
									</a>
									<!-- MAIL END -->
								</div>
							<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>

								<div class="share_post">
									<!-- FACEBOOK -->
									<a class="share facebook" onclick="window.open('http://www.facebook.com/sharer/sharer.php?u=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>', 'shareFacebook', 'width=650, height=270, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
										<i class="fa fa-facebook"></i> <span>Share on Facebook</span>
									</a>
									<!-- FACEBOOK END -->

									<!-- LINKEDIN -->
										<a class="share linkedin" onclick="window.open('http://www.linkedin.com/shareArticle?mini=true&url=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>&source=[SOURCE/DOMAIN]', 'shareLinkedIn', 'width=610, height=480, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
											<i class="fa fa-linkedin-square"></i> <span>Share on LinkedIn</span>
										</a>
									<!-- LINKEIND END -->


									<!-- MAIL -->
									<a class="share mail" href="mailto:?subject=<?php the_title(); ?>&body=Hej, jag tror att du skulle gilla detta. Kolla in detta <?php echo get_permalink(); ?>">
										<i class="fa fa-envelope" aria-hidden="true"></i> <span>Send as email</span>
									</a>
									<!-- MAIL END -->
								</div>
							<?php endif;?>
						</div>
					</footer>
				</article>

			<?php endwhile; ?>
		</div>
	</div>
</div>
