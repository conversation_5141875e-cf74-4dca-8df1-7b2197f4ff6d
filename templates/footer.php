<footer id="footer" class="pt-5 mt-5">
	<div class="wrapper">
		<div class="footer_left">
			<div class="logotype">
				 <img data-src="<?php echo get_template_directory_uri(); ?>/assets/images/swedac-logotype-neg.svg" alt="Logotyp för <?php bloginfo('name'); ?>" class="logo-img">
			</div>
			<div class="footer_text">

				<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
					<?php echo get_field('footer_about', 10); ?>
				<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
					<?php echo get_field('footer_about_en', 2652); ?>
				<?php endif;?>
			</div>



			<div class="footer_contact">
				<div class="tel">
					<div class="left">
						<img src="<?php echo get_template_directory_uri(); ?>/assets/images/icon-telephone.png" alt="Phone Icon" class="">
					</div>
					<div class="right">
						<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
							<span><a href="tel:<?php echo antispambot(get_field('footer_phonenumber', 10)); ?>"><?php echo antispambot(get_field('footer_phonenumber', 10)); ?></a></span>

						<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
							<span><a href="tel:<?php echo antispambot(get_field('footer_phonenumber_en', 2652)); ?>"><?php echo antispambot(get_field('footer_phonenumber_en', 2652)); ?></a></span>
						<?php endif;?>
					</div>
				</div>
				<div class="epost">
					<div class="left">
						<img src="<?php echo get_template_directory_uri(); ?>/assets/images/icon-mail.png" alt="Email Icon" class="">
					</div>
					<div class="right">
						<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
							<span><a href="mailto:<?php echo antispambot(get_field('footer_epost', 10)); ?>"><?php echo antispambot(get_field('footer_epost', 10)); ?></a></span>
						<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
							<span><a href="mailto:<?php echo antispambot(get_field('footer_epost_en', 2652)); ?>"><?php echo antispambot(get_field('footer_epost_en', 2652)); ?></a></span>
						<?php endif;?>

					</div>
				</div>

				<div class="adresser">
					<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
						<?php if(get_field('footer_adress_sv', 10)): ?>
							<ul>
								<?php while(has_sub_field('footer_adress_sv', 10)): ?>
									<li>
										<h2><?php echo get_sub_field('omrade'); ?></h2>
										<span><?php echo get_sub_field('adress'); ?></span>
									</li>
								<?php endwhile; ?>
							</ul>
						<?php endif; ?>
					<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
						<?php if(get_field('footer_adress_en', 2652)): ?>
							<ul>
								<?php while(has_sub_field('footer_adress_en', 2652)): ?>
									<li>
										<strong><?php echo get_sub_field('omrade'); ?></strong>
										<span><?php echo get_sub_field('adress'); ?></span>
									</li>
								<?php endwhile; ?>
							</ul>
						<?php endif; ?>
					<?php endif;?>
				</div>

			</div>
			<nav class="menu-footer-container">
				<?php wp_nav_menu(['theme_location' => 'footerNav', 'menu_class' => '', 'container' => '']); ?>
			</nav>
		</div>

		<div class="footer_right">
			
			<div class="lankar">
				<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
					<?php if(get_field('footer_lankar_sv', 10)): ?>
						<ul>
							<?php while(has_sub_field('footer_lankar_sv', 10)): ?>
								<li>
									<div class="right">
										<?php if (get_sub_field('externt_lankmal', 10)) { ?>
											<a href="<?php echo get_sub_field('externt_lankmal', 10); ?>" target="_blank" title="Se <?php echo get_sub_field('rubrik'); ?>">
												<h2><?php echo get_sub_field('rubrik', 10); ?></h2>
											</a>
										<?php } else { ?>
											<a href="<?php echo get_sub_field('lankmal', 10); ?>" title="Läs mer om <?php echo get_sub_field('rubrik'); ?>">
												<h2><?php echo get_sub_field('rubrik', 10); ?></h2>
											</a>
										<?php } ?>

										<div class="text">
											<?php echo get_sub_field('beskrivning', 10); ?>
										</div>
									</div>
								</li>
							<?php endwhile; ?>
						</ul>
					<?php endif; ?>
				<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
					<?php if(get_field('footer_lankar_en', 2652)): ?>
						<ul>
							<?php while(has_sub_field('footer_lankar_en', 2652)): ?>
								<li>
									<div class="right">
										<?php if (get_sub_field('externt_lankmal', 2652)) { ?>
											<a href="<?php echo get_sub_field('externt_lankmal', 2652); ?>" target="_blank" title="See <?php echo get_sub_field('rubrik'); ?>">
												<h2><?php echo get_sub_field('rubrik', 2652); ?></h2>
											</a>
										<?php } else { ?>
											<a href="<?php echo get_sub_field('lankmal', 2652); ?>" title="Read more about <?php echo get_sub_field('rubrik'); ?>">
												<h2><?php echo get_sub_field('rubrik', 2652); ?></h2>
											</a>
										<?php } ?>

										<div class="text">
											<?php echo get_sub_field('beskrivning', 2652); ?>
										</div>
									</div>
								</li>
							<?php endwhile; ?>
						</ul>
					<?php endif; ?>
				<?php endif;?>
			</div>

			<div id="news_letter">
				<h2>Prenumerera på Swedacs nyhetsbrev</h2>
				<?php echo do_shortcode('[wysija_form id="1"]'); ?>
			</div>
		</div>
	</div>

	<div id="wrapper_footer">
		<div class="copyright">
			<span class="copy">Copyright &copy; <?php echo date('Y'); ?> <?php bloginfo('name'); ?> </span>
			<span class="links">
				<?php if(ICL_LANGUAGE_CODE=='sv'):
					$aboutSwedacName = get_field('om_swedac_namn_footer',10);
					$aboutSwedacUrl = get_field('om_swedac_lank_footer',10);
					?>
				<?php if(ICL_LANGUAGE_CODE=='sv'):
					$policyName = get_field('policy_namn_footer',10);
					$policyUrl = get_field('policy_lank_footer',10);
				?>
					<a class="about" href="<?php echo $policyUrl; ?>" title="integritetspolicy"><?php echo $policyName; ?></a>
				<?php endif ?>
				<?php if(ICL_LANGUAGE_CODE=='sv'):
					$wcagName = get_field('wcag_namn_footer',10);
					$wcagUrl = get_field('wcag_lank_footer',10);
				?>
					<a class="about" href="<?php echo $wcagUrl; ?>" title="Tillgänglighetsrapport"><?php echo $wcagName; ?></a>
				<?php endif ?>
				<?php if($aboutSwedacName && $aboutSwedacUrl) { ?>
					<a class="about" href="<?php echo $aboutSwedacUrl; ?>" title="Swedac.se"><?php echo $aboutSwedacName; ?></a>
				<?php } ?>
					<a class="api" href="https://www.swedac.se/psidata/" title="Öppna data - API">Öppna data - API</a><a class="rss" href="https://www.swedac.se/category/nyheter/feed/" title="RSS - Feed" target="_blank">RSS <i class="fa fa-rss" aria-hidden="true"></i></a><a class="cookie" href="https://www.swedac.se/om-swedac/cookies/" title="Cookies">Cookies</a>
				<?php elseif(ICL_LANGUAGE_CODE=='en'):
						$aboutSwedacName = get_field('om_swedac_namn_footer_en',2652);
						$aboutSwedacUrl = get_field('om_swedac_lank_footer_en',2652);
					?>
					<!-- <?php if($aboutSwedacName && $aboutSwedacUrl) { ?>
					<?php } ?> -->
					<a class="rss" href="https://www.swedac.se/category/news/feed/?lang=en" title="RSS - Feed" target="_blank">RSS feed <i class="fa fa-rss" aria-hidden="true"></i></a><a class="cookie" href="https://www.swedac.se/about-swedac/cookies?lang=en" title="Cookies">Cookies</a>
				<?php endif;?>

			</span>
		</div>
	</div>
</footer>
