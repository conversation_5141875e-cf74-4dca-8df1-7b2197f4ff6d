<div class="page-content-custom">
	
	<div id="snabbval">
		<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
			<?php if(get_field('snabbval_template_2_sv')): ?>
				<ul>
					<?php $i = 2; ?>
					<?php while(has_sub_field('snabbval_template_2_sv')): $image = get_sub_field('bild'); ?>
						<li <?php if($i %3 == 0) {echo 'class="middle" ';} ?>>
							<a href="<?php echo get_sub_field('lankmal'); ?>" title="<?php echo get_sub_field('rubrik'); ?>">
								<div class="image">
									<img data-src="<?php echo $image['sizes']['puff_news'];  ?>" />
								</div>
								<div class="rubrik">
									<h2 class="title-cuter"><?php echo get_sub_field('rubrik'); ?></h2>
								</div>
							</a>
						</li>

					<?php $i++; endwhile; ?>
				</ul>
			<?php endif; ?>
		<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
			<?php if(get_field('snabbval_template_2_en')): ?>
				<ul>
					<?php $i = 2; ?>
					<?php while(has_sub_field('snabbval_template_2_en')): $image = get_sub_field('bild'); ?>
						<li <?php if($i %3 == 0) {echo 'class="middle" ';} ?>>
							<a href="<?php echo get_sub_field('lankmal'); ?>" title="<?php echo get_sub_field('rubrik'); ?>">
								<div class="image">
									<img data-src="<?php echo $image['sizes']['puff_news'];  ?>" />
								</div>
								<div class="rubrik">
									<h2 class="title-cuter"><?php echo get_sub_field('rubrik'); ?></h2>
								</div>
							</a>
						</li>

					<?php $i++; endwhile; ?>
				</ul>
			<?php endif; ?>
		<?php endif;?>

	</div>



	<?php the_content(); ?>
	
	<?php //wp_link_pages(['before' => '<nav class="page-nav"><p>' . __('Pages:', 'sage'), 'after' => '</p></nav>']); ?>
</div>