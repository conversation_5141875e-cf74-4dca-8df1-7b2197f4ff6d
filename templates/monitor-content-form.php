<?php
if (empty($docArray)) {
	$docArray = array();
	$docArray[] = get_field('crm_doc_dokumentbeteckning');
}

$outerClass = 'foreskrifter';
$innerClass = 'inner-container';
$formText = 'När något uppdateras i föreskriftslistan skickas en notifikation.';

if (is_singular('dokument')) {
	$outerClass = 'foreskrift';
	$innerClass = 'wrapper';
	$formText = 'När krav- eller vägledningsdokumentet uppdateras skickas en notifikation till angiven e-postadress.';
}
?>

<div class="<?php echo $outerClass; ?>" id="monitor_content_for_updates">
	<div class="<?php echo $innerClass; ?>">
		<div class="title">
			<img src="<?php echo home_url('wp-content/themes/swedac_theme/assets/images/icon-follow-blue.png'); ?>">
			<span>
				<h2><PERSON>va<PERSON> innehåll</h2>
			</span>
		</div>

		<p><?php echo $formText; ?></p>

		<form id="monitor_content_form" class="form clear" name="monitor_content" method="POST"
			action="/subscribers.php?add_subscriber_doc" novalidate="novalidate" autocomplete="false">
			<div class="row name">
				<input class="req" type="text" name="name" placeholder="Namn *">
			</div>

			<div class="row email">
				<input class="req" type="email" name="email" placeholder="E-postadress *">
			</div>


			<div class="row mx-0 overflow-hidden">
				<div class="frc-captcha" data-sitekey="FCMHCLG9N0S4NF1M" data-lang="<?php echo strtolower(ICL_LANGUAGE_CODE); ?>"></div>
			</div>

			<input type="hidden" name="doc_bet" value='<?php echo json_encode($docArray); ?>'>
			<input type="hidden" name="url" value="<?php echo strtok($_SERVER["REQUEST_URI"], '?'); ?>">

			<div class="row">
				<button class="monitor-submit" type="submit" name="add_subscriber_doc_submit">Bevaka</button>
			</div>
		</form>
	</div>
</div>