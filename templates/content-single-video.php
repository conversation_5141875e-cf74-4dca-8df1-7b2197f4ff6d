<?php 
$video_bild = get_field('video_poster');
$video_rubrik = get_field('video_title');
$video = get_field('video_url');
$segment = 30;

if ($video) : ?>
    <div class="wrapper py-3 py-md-5 px-0">
        <div class="container px-md-0">
            <div class="row mx-0">
                <div class="col-12 col-md-10 mx-auto">
                    <div class="responsive-background position-relative video-height imageDiv<?=$segment ?>" style=" background-image: url('<?php echo $video_bild['url'] ?>');">
                        <svg class="playIcon videoPlay" onclick="showVideo(<?=$segment ?>);" height="85" width="85" viewBox="0 0 30 30">
                            <defs>
                                <style>.cls-1{fill:#fff;}</style>
                            </defs>
                            <circle class="cls-1" cx="15" cy="15" r="15"/>
                            <path d="M20,14.4,13.8,9.9a.75.75,0,0,0-.8-.1.67.67,0,0,0-.4.7v9.1a1,1,0,0,0,.4.7.37.37,0,0,0,.3.1c.2,0,.3,0,.4-.1l6.2-4.5a.71.71,0,0,0,.3-.6A.72.72,0,0,0,20,14.4Z"/>
                        </svg>
                        <div class="overlay">
                            <h2 class="text-center text-white mt-3 text-overlay"><?php echo $video_rubrik; ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-8 mx-auto hide videoDiv<?=$segment ?>"> <?php echo $video; ?> </div>
            </div>
        </div>
    </div>
<?php endif; ?>