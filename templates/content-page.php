<div class="page-content">

	<?php if (ICL_LANGUAGE_CODE == 'sv'): ?>

		<?php if (is_page(page: 46)) { ?>

			<?php if (get_field('kontaktuppgifter_kontor', 46)): ?>
				<div class="kontaktuppgifter_kontor">
					<ul>
						<?php while (has_sub_field('kontaktuppgifter_kontor', 46)):
							$location = get_sub_field('karta'); ?>
							<li>
								<div class="google_map">
									<img class="marker" src="<?= $location['url'] ?>" />
								</div>
								<?php if (!empty(get_field('typ_av_kontor'))): ?>
									<h2><?php echo get_sub_field('typ_av_kontor'); ?></h2>
								<?php endif; ?>
								<div class="adress">
									<strong>Besöksadress</strong>
									<?php echo get_sub_field('besoksadress'); ?>
								</div>
								<div class="adress">
									<strong>Postadress</strong>
									<?php echo get_sub_field('postadress'); ?>
								</div>
							</li>
						<?php endwhile; ?>
					</ul>
				</div>
			<?php endif; ?>
		<?php } ?>
	<?php elseif (ICL_LANGUAGE_CODE == 'en'): ?>
		<?php if (is_page(2645)) { ?>
			<?php if (get_field('kontaktuppgifter_kontor_en', 2645)): ?>
				<div class="kontaktuppgifter_kontor">
					<ul>
						<?php while (has_sub_field('kontaktuppgifter_kontor_en', 2645)):
							$location = get_sub_field('karta'); ?>
							<li>
								<div class="google_map">
									<img class="marker" src="<?= $location['url'] ?>" />
								</div>
								<?php if (!empty(get_field('typ_av_kontor'))): ?>
									<h2><?php echo get_sub_field('typ_av_kontor'); ?></h2>
								<?php endif; ?>
								<div class="adress">
									<strong>Visiting address</strong>
									<?php echo get_sub_field('besoksadress'); ?>
								</div>
								<div class="adress">
									<strong>Mailing address</strong>
									<?php echo get_sub_field('postadress'); ?>
								</div>
							</li>
						<?php endwhile; ?>
					</ul>
				</div>
			<?php endif; ?>
		<?php } ?>
	<?php endif; ?>

	<?php the_content(); ?>

	<?php if (ICL_LANGUAGE_CODE == 'sv'): ?>
		<?php if (is_page(105)) { // Hem / Tjänster / Ackreditering / Ansök om ackreditering / B171 ?>
			<div class="wrapper">
				<?php get_template_part('templates/apply-form'); ?>
			</div>
		<?php } ?>
	<?php elseif (ICL_LANGUAGE_CODE == 'en'): ?>
		<?php if (is_page(11632)) // Home / Services / Accreditation / Apply for accreditation ?>
		<div class="wrapper">
			<?php get_template_part('templates/apply-form'); ?>
		</div>
		<?php { ?>

		<?php } ?>
	<?php endif; ?>

	<?php if (ICL_LANGUAGE_CODE == 'sv'): ?>

		<?php //Gällande föreskrifter i nummerordning
			if (is_page(137)) { ?>
			<div class="docs_numberordning">
				<div class="menu_docs">
					<ul class="menu_docs_ul">
						<li class="docs_all sort_docs" data-docs="all">Visa alla</li>
						<li class="docs_grundforfattning current sort_docs" data-docs="grundforfattning">Visa
							grundförfattningar</li>
					</ul>
				</div>

				<?php
				$array = array();

				$args = array(
					'status' => 'publish',
					'post_type' => 'dokument',
					'posts_per_page' => -1,
					'meta_query' => array(
						'relation' => 'AND', //Om fler fält ska användas
						array(
							'key' => 'crm_doc_repealed',
							'value' => '1',
							'compare' => '!=',
						),
						array(
							'key' => 'crm_doc_doc',
							'value' => '.pdf',
							'compare' => 'LIKE',
						),
						array(
							'key' => 'crm_doc_type',
							'value' => 'STAFS',
							'compare' => '=',
						)
					)
				);

				$docArray = array();

				$posts = get_posts($args);
				foreach ($posts as $post) {
					// Lägg till alla dokumentbeteckningar i en array för att bevaka dem
					$docArray[] = get_field('crm_doc_dokumentbeteckning', $post->ID);

					//Hämtar fält
					$subject = get_field('crm_doc_dokumentbeteckning', $post->ID);
					$subject2 = get_field('crm_doc_dokumentbeteckning', $post->ID);

					// Explodar efter STAFS och : samt hämtar ut årdet
					$explode = explode('STAFS ', $subject2);
					$field = end($explode);
					$field = substr($field, 0, 4);

					$explode = explode(':', $subject);
					$result = end($explode);

					//Lägger till 0 innan nummer som är lägre än 10
					if ($result < 10) {
						$result = '0' . $result;
					}

					//Slår ihop variabler
					$result = $field . $result;

					//Lägger in allt i en array med värden ovan
					array_push($array, array(
						"title" => get_the_title($post->ID),
						"id" => get_the_id($post->ID),
						"link" => get_permalink($post->ID),
						"crm_doc_dokumentbeteckning" => $result,
						"crm_doc_doc" => get_field('crm_doc_doc', $post->ID)
					));

				}
				//Sorterar på Störst > Lägst ex. 2016-1993
		
				usort($array, 'sortById');
				?>
				<div class="info">
					<ul>
						<?php foreach ($array as $item) {

							$title = $item['title'];
							$id = $item['id'];
							$url = $item['link'];
							$doc = $item['crm_doc_doc'];

							$doc_bet = get_field('crm_doc_dokumentbeteckning', $id);
							$doc_bet_2 = substr($doc_bet, 6); // returns "de"
							$year_title = substr($doc_bet_2, 0, 4);  // returns "abcde"
				
							if (strpos($title, ' ändring ') == true) {
								$xClass = " andringsforfattning";
							} else {
								$xClass = " grundforfattning";
							}
							?>
							<div class="content li_<?php echo $year_title; ?>">
								<h2><?php echo $year_title; ?></h2>
								<li class="<?php echo $xClass; ?>">
									<span class="bet"><a
											href="<?php echo $url; ?>"><?php echo get_field('crm_doc_dokumentbeteckning', $id); ?></a></span>
									<span class="title"><?php echo $title; ?></span>
								</li>
							</div>
						<?php } ?>
					</ul>
				</div>
			</div>
			<?php
			/**
			 * @link https://mekshq.com/passing-variables-via-get_template_part-wordpress/
			 */
			include(locate_template('templates/monitor-content-form.php', false, false));
			} ?>

	<?php elseif (ICL_LANGUAGE_CODE == 'en'): ?>

		<?php //Regulatory information in numerical order
			if (is_page(3533)) { ?>
			<div class="docs_numberordning">
				<div class="menu_docs">
					<ul class="menu_docs_ul">
						<li class="docs_all sort_docs" data-docs="all">Show all</li>
						<li class="docs_grundforfattning current sort_docs" data-docs="grundforfattning">Show basic regulations
						</li>
					</ul>
				</div>
				<?php $array = array();
				$args = array(
					'status' => 'publish',
					'post_type' => 'document_eng',
					'posts_per_page' => -1,

					'meta_query' => array(
						'relation' => 'AND', //Om fler fält ska användas
						array(
							'key' => 'crm_doc_repealed_en',
							'value' => '1',
							'compare' => '!=',
						),
						array(
							'key' => 'crm_doc_doc_en',
							'value' => '.pdf',
							'compare' => 'LIKE',
						),
						array(
							'key' => 'crm_doc_type_en',
							'value' => 'STAFS',
							'compare' => '=',
						)
					)
				);


				$posts = get_posts($args);
				foreach ($posts as $post) {

					//Hämtar fält
					$subject = get_field('crm_doc_dokumentbeteckning_en', $post->ID);
					$subject2 = get_field('crm_doc_dokumentbeteckning_en', $post->ID);

					// Explodar efter STAFS och : samt hämtar ut årdet
					$field = end(explode('STAFS ', $subject2));
					$field = substr($field, 0, 4);
					$result = end(explode(':', $subject));

					//Lägger till 0 innan nummer som är lägre än 10
					if ($result < 10) {
						$result = '0' . $result;
					}

					//Slår ihop variabler
					$result = $field . $result;

					//Lägger in allt i en array med värden ovan
					array_push($array, array(
						"title" => get_the_title($post->ID),
						"id" => get_the_id($post->ID),
						"link" => get_permalink($post->ID),
						"crm_doc_dokumentbeteckning_en" => $result,
						"crm_doc_doc_en" => get_field('crm_doc_doc_en', $post->ID)
					));

				}
				//Sorterar på Störst > Lägst ex. 2016-1993
		
				usort($array, 'sortById');


				?>
				<div class="info">
					<ul>
						<?php foreach ($array as $item) {

							$title = $item['title'];
							$id = $item['id'];
							$url = $item['link'];
							$doc = $item['crm_doc_doc'];

							$doc_bet = get_field('crm_doc_dokumentbeteckning_en', $id);
							$doc_bet_2 = substr($doc_bet, 6); // returns "de"
							$year_title = substr($doc_bet_2, 0, 4);  // returns "abcde"
				
							if (strpos($title, ' changes ') == true) {
								$xClass = " andringsforfattning";
							} else {
								$xClass = " grundforfattning";
							}
							?>
							<div class="content li_<?php echo $year_title; ?>">
								<h2><?php echo $year_title; ?></h2>
								<li class="<?php echo $xClass; ?>">
									<span class="bet"><a
											href="<?php echo $url; ?>"><?php echo get_field('crm_doc_dokumentbeteckning_en', $id); ?></a></span>
									<span class="title"><?php echo $title; ?></span>
								</li>
							</div>
						<?php } ?>
					</ul>
				</div>
			</div>
		<?php } ?>

	<?php endif; ?>

	<?php if (ICL_LANGUAGE_CODE == 'sv'): ?>

		<?php // Hem / Om Swedac / Arbeta på Swedac / Träffa Swedacs medarbetare
			if (is_page(2090)) { ?>

			<div class="medarbetare_holder">
				<?php $args = array(
					'status' => 'publish',
					'post_type' => 'medarbetare',
					'posts_per_page' => -1,
					'orderby' => 'date',
					'order' => 'desc'
				);
				$query = new WP_Query($args);
				if ($query->have_posts()): ?>
					<ul>
						<?php while ($query->have_posts()):
							$query->the_post(); ?>
							<li>
								<?php if (has_post_thumbnail()): ?>
									<?php $get_post_image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'ledning');
									$image_url = $get_post_image['0'];
									?>

									<div class="medar_left">
										<img data-src="<?php echo $image_url; ?>" alt="<?php the_title(); ?>" />
									</div>

									<div class="medar_right">
										<h3><?php the_title(); ?></h3>
										<span class="medar_ingress">
											<?php echo get_field('medarbetare_ingress_sv'); ?>
										</span>
										<div class="readMore">
											<a href="<?php the_permalink(); ?>" title="Läs mer om <?php the_title(); ?>">
												Läs mer
											</a>
										</div>
									</div>
								<?php else: ?>
									<div class="medar_full">
										<h3><?php the_title(); ?></h3>
										<span class="medar_ingress">
											<?php echo get_field('medarbetare_ingress_sv'); ?>
										</span>

										<div class="readMore">
											<a href="<?php the_permalink(); ?>" title="Läs mer om <?php the_title(); ?>">
												Läs mer
											</a>
										</div>
									</div>

								<?php endif; ?>


							</li>
						<?php endwhile; ?>
					</ul>
				<?php endif;
				wp_reset_postdata(); ?>
			</div>

		<?php } ?>

	<?php elseif (ICL_LANGUAGE_CODE == 'en'): ?>

	<?php endif; ?>

	<?php if (ICL_LANGUAGE_CODE == 'sv'): ?>
		<?php  //Sida Intresseanmälan till teknisk bedömare
			if (is_page(2126)) { ?>

			<?php if (isset($_GET['success_bli_teknisk_bedomare'])): ?>
				<div class="messange_box">
					<div class="inner success">
						<h2>Tack för din intresseanmälan!</h2>
						<p>Din intresseanmälan för att bli teknisk bedömare har skickats till oss.</p>
					</div>
				</div>
			<?php elseif (isset($_GET['human_validation_failed'])): ?>
				<div class="messange_box">
					<div class="inner failed">
						<h2><?php _e('Något gick fel!', 'applyform') ?></h2>
						<p><?php _e('Vi kunde inte verifiera att du är en människa. Var god försök igen.', 'applyform') ?></p>
						<p><?php _e('Skulle problemet kvarstå så är du välkommen att kontakta oss via telefon.', 'applyform') ?>
						</p>
					</div>
				</div>
			<?php elseif (isset($_GET['failed_bli_teknisk_bedomare'])): ?>
				<div class="messange_box">
					<div class="inner failed">
						<h2>Din intresseanmälan har <strong>inte</strong> skickats.</h2>
						<p>Det verkar som att något gick fel. Vänligen försök en gång till.</p>
						<p>Skulle samma fel uppstå en gång till så är du välkommen att kontakta oss.</p>
					</div>
				</div>
			<?php endif; ?>

			<div id="bli-teknisk-bedomare">
				<form id="bli_teknisk_bedomare_form" class="form clear" name="bli_teknisk_bedomare" method="post"
					action="/sender.php?bli_teknisk_bedomare" autocomplete="off" enctype="multipart/form-data">
					<div class="top">
						<div class="row fname">
							<input type="text" class="inputbox req" name="firstname" id="firstname" placeholder="Förnamn *"
								title="Förnamn" required>
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row lname">
							<input type="text" class="inputbox req" name="lastname" id="lastname" placeholder="Efternamn *"
								title="Efternamn" required>
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row fodelsear">
							<input type="text" class="inputbox req" name="fodelsear" id="fodelsear" placeholder="Födelseår *"
								title="Födelseår" required>
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row email">
							<input type="email" class="inputbox req" name="email" id="email" placeholder="E-postadress *"
								title="E-postadress" required>
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row phone">
							<input type="tel" class="inputbox req" name="phone" id="phone"
								onkeypress="return event.charCode >= 48 && event.charCode <= 57" placeholder="Telefon *"
								title="Telefon" required>
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row nuvarande_anstallning">
							<input type="text" class="inputbox req" name="nuvarande_anstallning" id="nuvarande_anstallning"
								placeholder="Nuvarande anställning *" title="Nuvarande anställning" required>
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>

						<div class="row analyserar_verksamhet">
							<span class="text">Jag utför, analyserar eller leder verksamhet:</span>
							<label class="label" for="analyserar_verksamhet_1"><input type="checkbox"
									id="analyserar_verksamhet_1" name="analyserar_verksamhet_1" value="Utför"> Utför <br />
							</label>
							<label class="label" for="analyserar_verksamhet_2"><input type="checkbox"
									id="analyserar_verksamhet_2" name="analyserar_verksamhet_2" value="Analyserar"> Analyserar
								<br /> </label>
							<label class="label" for="analyserar_verksamhet_3"><input type="checkbox"
									id="analyserar_verksamhet_3" name="analyserar_verksamhet_3" value="Leder"> Leder <br />
							</label>
						</div>

						<div class="row verksamheten_utgors_av">
							<span class="text">Verksamheten utgörs av:</span>
							<label class="label" for="verksamheten_utgors_av_1"><input type="checkbox"
									id="verksamheten_utgors_av_1" name="verksamheten_utgors_av_1"
									value="Laboratorieundersökningar"> Laboratorieundersökningar <br /> </label>
							<label class="label" for="verksamheten_utgors_av_2"><input type="checkbox"
									id="verksamheten_utgors_av_2" name="verksamheten_utgors_av_2" value="Kalibrering">
								Kalibrering <br /> </label>
							<label class="label" for="verksamheten_utgors_av_3"><input type="checkbox"
									id="verksamheten_utgors_av_3" name="verksamheten_utgors_av_3" value="Inspektion">
								Inspektion <br /> </label>
							<label class="label" for="verksamheten_utgors_av_4"><input type="checkbox"
									id="verksamheten_utgors_av_4" name="verksamheten_utgors_av_4" value="Certifiering">
								Certifiering <br /> </label>
						</div>

						<div class="row tidigare_anstallningar">
							<input type="text" class="inputbox req" name="anstallningar" id="anstallningar"
								placeholder="Tidigare anställningar *" title="Tidigare anställningar" required>
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row utbildning">
							<input type="text" class="inputbox req" name="utbildning" id="utbildning"
								placeholder="Högsta/senaste utbildning *" title="Högsta/senaste utbildning" required>
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row intresseorganisation">
							<fieldset>
								<legend>Jag är medlem i intresseorganisation inom mitt teknikområde/områden:</legend>
								<label class="label" for="intresseorganisation_ja"><input type="radio"
										id="intresseorganisation_ja" name="intresseorganisation" value="Ja"> Ja <br /> </label>
								<label class="label" for="intresseorganisation_nej"><input type="radio"
										id="intresseorganisation_nej" name="intresseorganisation" value="Nej"> Nej <br />
								</label>
							</fieldset>
						</div>
						<div class="row ackrediteringssystemet">
							<fieldset>
								<legend>Jag har kunskap om ackrediteringssystemet:</legend>
								<label class="label" for="ackrediteringssystemet_ja"><input type="radio"
										id="ackrediteringssystemet_ja" name="ackrediteringssystemet" value="Ja"> Ja <br />
								</label>
								<label class="label" for="ackrediteringssystemet_nej"><input type="radio"
										id="ackrediteringssystemet_nej" name="ackrediteringssystemet" value="Nej"> Nej <br />
								</label>
							</fieldset>
						</div>
						<div class="row ackrediteringsstandarder">
							<span class="text">Jag har kunskap om följande ackrediteringsstandarder:</span>

							<label class="label" for="ackrediteringsstandarder_1"><input type="checkbox"
									id="ackrediteringsstandarder_1" name="ackrediteringsstandarder_1" value="ISO/IEC 15189">
								ISO/IEC 15189 <br /> </label>
							<label class="label" for="ackrediteringsstandarder_2"><input type="checkbox"
									id="ackrediteringsstandarder_2" name="ackrediteringsstandarder_2" value="ISO/IEC 17020">
								ISO/IEC 17020 <br /> </label>
							<label class="label" for="ackrediteringsstandarder_3"><input type="checkbox"
									id="ackrediteringsstandarder_3" name="ackrediteringsstandarder_3" value="ISO/IEC 17021">
								ISO/IEC 17021 <br /> </label>
							<label class="label" for="ackrediteringsstandarder_4"><input type="checkbox"
									id="ackrediteringsstandarder_4" name="ackrediteringsstandarder_4" value="ISO/IEC 17024">
								ISO/IEC 17024 <br /> </label>
							<label class="label" for="ackrediteringsstandarder_5"><input type="checkbox"
									id="ackrediteringsstandarder_5" name="ackrediteringsstandarder_5" value="ISO/IEC 17025">
								ISO/IEC 17025 <br /> </label>
							<label class="label" for="ackrediteringsstandarder_6"><input type="checkbox"
									id="ackrediteringsstandarder_6" name="ackrediteringsstandarder_6" value="ISO/IEC 17029">
								ISO/IEC 17029 <br /> </label>
							<label class="label" for="ackrediteringsstandarder_7"><input type="checkbox"
									id="ackrediteringsstandarder_7" name="ackrediteringsstandarder_7" value="ISO/IEC 17043">
								ISO/IEC 17043 <br /> </label>
							<label class="label" for="ackrediteringsstandarder_8"><input type="checkbox"
									id="ackrediteringsstandarder_8" name="ackrediteringsstandarder_8" value="ISO/IEC 17065">
								ISO/IEC 17065 <br /> </label>
						</div>
						<div class="row kompetensomraden">
							<textarea name="kompetensomraden" id="" class="textarea req"
								placeholder="Mina tekniska kompetensområden är... *" rows="5"
								title="Mina tekniska kompetensområden är..." required></textarea>
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row omradesspecifika_regelverk mb-3">
							<textarea name="omradesspecifika_regelverk" id="" class="textarea req"
								placeholder="Jag har kännedom om följande områdesspecifika regelverk (EU:s regelverk, förordningar, föreskrifter) *"
								rows="5"
								title="Jag har kännedom om följande områdesspecifika regelverk (EU:s regelverk, förordningar, föreskrifter)"
								required></textarea>
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row samtyckande py-3">
							<span>Jag samtycker till att Swedac sparar och behandlar mina personuppgifter:&nbsp;
								<input type="checkbox" id="samtyckande" name="samtyckande" style="vertical-align:middle;"
									required>
							</span>
						</div>
						<div class="row mx-0 attachment-row pb-3">
							<span>Bifoga ditt CV i rutan här under, då det behövs för att styrka din kompetens och
								expertområden.</span>
							<input type="file" name="attachment[]" id="attachments" onchange="fileType(this)">
							<img onclick="addMoreAttachment()"
								src="<?php echo get_template_directory_uri() ?>/assets/images/plus-black.png"
								alt="<?php _e('Lägg till fler filer') ?>">
						</div>
					</div>

					<div class="row mx-0 overflow-hidden">
						<div class="frc-captcha" data-sitekey="FCMHCLG9N0S4NF1M" data-lang="<?php echo strtolower(ICL_LANGUAGE_CODE); ?>"></div>
					</div>

					<div class="row bottom">
						<input class="submit-booking" type="submit" value="Skicka" title="Skicka" disabled>
						<span>* Obligatoriska fält</span>
					</div>


					<input type="hidden" name="url" value="<?php echo strtok($_SERVER["REQUEST_URI"], '?'); ?>" />
				</form>
			</div>
		<?php } ?>
	<?php elseif (ICL_LANGUAGE_CODE == 'en'): ?>
		<?php  //Sida Intresseanmälan till teknisk bedömare
			if (is_page(3518)) { ?>

			<?php if (isset($_GET['success_bli_teknisk_bedomare'])): ?>
				<div class="messange_box">
					<div class="inner success">
						<h2>Thank you for your expression of interest!</h2>
						<p>Your expression of interest to become technical analysts have been sent to us.</p>
					</div>
				</div>
			<?php elseif (isset($_GET['failed_bli_teknisk_bedomare'])): ?>
				<div class="messange_box">
					<div class="inner failed">
						<h2>Your expression of interest has <strong>not</strong> sent.</h2>
						<p>It seems that something went wrong. Please try again.</p>
						<p>If the same error occurs again, you are welcome to contact us.</p>
					</div>
				</div>
			<?php endif; ?>

			<div id="bli-teknisk-bedomare">
				<form id="bli_teknisk_bedomare_form" class="form clear" name="bli_teknisk_bedomare" method="post"
					action="/sender.php?bli_teknisk_bedomare" novalidate="novalidate" autocomplete="false"
					enctype="multipart/form-data">
					<div class="top">
						<div class="row fname">
							<input type="text" class="inputbox req" name="firstname" id="firstname" placeholder="First name *"
								title="First name">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row lname">
							<input type="text" class="inputbox req" name="lastname" id="lastname" placeholder="Last name *"
								title="Last name">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row fodelsear">
							<input type="text" class="inputbox req" name="fodelsear" id="fodelsear"
								placeholder="Year of birth *" title="Year of birth">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row email">
							<input type="email" class="inputbox req" name="email" id="email" placeholder="Email *"
								title="Email">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row phone">
							<input type="tel" class="inputbox req" name="phone" id="phone"
								onkeypress="return event.charCode >= 48 && event.charCode <= 57" placeholder="Phone *"
								title="Phone">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row nuvarande_anstallning">
							<input type="text" class="inputbox req" name="nuvarande_anstallning" id="nuvarande_anstallning"
								placeholder="Current employment *" title="Current employment">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>

						<div class="row analyserar_verksamhet">
							<span class="text">I perform, analyze, or leading activities:</span>
							<label class="label" for="analyserar_verksamhet_1"><input type="checkbox"
									id="analyserar_verksamhet_1" name="analyserar_verksamhet_1" value="Performing"> Performing
								<br /> </label>
							<label class="label" for="analyserar_verksamhet_2"><input type="checkbox"
									id="analyserar_verksamhet_2" name="analyserar_verksamhet_2" value="Analyzes"> Analyzes
								<br /> </label>
							<label class="label" for="analyserar_verksamhet_3"><input type="checkbox"
									id="analyserar_verksamhet_3" name="analyserar_verksamhet_3" value="Leads"> Leads <br />
							</label>
						</div>

						<div class="row verksamheten_utgors_av">
							<span class="text">The operations consists of:</span>
							<label class="label" for="verksamheten_utgors_av_1"><input type="checkbox"
									id="verksamheten_utgors_av_1" name="verksamheten_utgors_av_1"
									value="Laboratory investigations"> Laboratory investigations <br /> </label>
							<label class="label" for="verksamheten_utgors_av_2"><input type="checkbox"
									id="verksamheten_utgors_av_2" name="verksamheten_utgors_av_2" value="Calibration">
								Calibration <br /> </label>
							<label class="label" for="verksamheten_utgors_av_3"><input type="checkbox"
									id="verksamheten_utgors_av_3" name="verksamheten_utgors_av_3" value="Inspection">
								Inspection <br /> </label>
							<label class="label" for="verksamheten_utgors_av_4"><input type="checkbox"
									id="verksamheten_utgors_av_4" name="verksamheten_utgors_av_4" value="Certification">
								Certification <br /> </label>
						</div>

						<div class="row tidigare_anstallningar">
							<input type="text" class="inputbox req" name="anstallningar" id="anstallningar"
								placeholder="Previous employment *" title="Previous employment">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row utbildning">
							<input type="text" class="inputbox req" name="utbildning" id="utbildning"
								placeholder="Highest / last education *" title="Highest / last education">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row intresseorganisation">
							<fieldset>
								<legend>I am a member of the association in my technical area/areas:</legend>
								<label class="label" for="intresseorganisation_ja"><input type="radio"
										id="intresseorganisation_ja" name="intresseorganisation" value="Yes"> Yes <br />
								</label>
								<label class="label" for="intresseorganisation_nej"><input type="radio"
										id="intresseorganisation_nej" name="intresseorganisation" value="No"> No <br />
								</label>
							</fieldset>
						</div>
						<div class="row ackrediteringssystemet">
							<fieldset>
								<legend>I have knowledge of the accreditation system:</legend>
								<label class="label" for="ackrediteringssystemet_ja"><input type="radio"
										id="ackrediteringssystemet_ja" name="ackrediteringssystemet" value="Yes"> Yes <br />
								</label>
								<label class="label" for="ackrediteringssystemet_nej"><input type="radio"
										id="ackrediteringssystemet_nej" name="ackrediteringssystemet" value="No"> No <br />
								</label>
							</fieldset>
						</div>
						<div class="row ackrediteringsstandarder">
							<span class="text">I have knowledge of the following Accreditation standards:</span>

							<label class="label" for="ackrediteringsstandarder_1"><input type="checkbox"
									id="ackrediteringsstandarder_1" name="ackrediteringsstandarder_8" value="ISO/IEC 15189">
								ISO/IEC 15189 <br /> </label>
							<label class="label" for="ackrediteringsstandarder_2"><input type="checkbox"
									id="ackrediteringsstandarder_2" name="ackrediteringsstandarder_1" value="ISO/IEC 17020">
								ISO/IEC 17020 <br /> </label>
							<label class="label" for="ackrediteringsstandarder_3"><input type="checkbox"
									id="ackrediteringsstandarder_3" name="ackrediteringsstandarder_2" value="ISO/IEC 17021">
								ISO/IEC 17021 <br /> </label>
							<label class="label" for="ackrediteringsstandarder_4"><input type="checkbox"
									id="ackrediteringsstandarder_4" name="ackrediteringsstandarder_3" value="ISO/IEC 17024">
								ISO/IEC 17024 <br /> </label>
							<label class="label" for="ackrediteringsstandarder_5"><input type="checkbox"
									id="ackrediteringsstandarder_5" name="ackrediteringsstandarder_4" value="ISO/IEC 17025">
								ISO/IEC 17025 <br /> </label>
							<label class="label" for="ackrediteringsstandarder_6"><input type="checkbox"
									id="ackrediteringsstandarder_6" name="ackrediteringsstandarder_5" value="ISO/IEC 17029">
								ISO/IEC 17029 <br /> </label>
							<label class="label" for="ackrediteringsstandarder_7"><input type="checkbox"
									id="ackrediteringsstandarder_7" name="ackrediteringsstandarder_6" value="ISO/IEC 17043">
								ISO/IEC 17043 <br /> </label>
							<label class="label" for="ackrediteringsstandarder_8"><input type="checkbox"
									id="ackrediteringsstandarder_8" name="ackrediteringsstandarder_7" value="ISO/IEC 17065">
								ISO/IEC 17065 <br /> </label>
						</div>
						<div class="row kompetensomraden">
							<textarea name="kompetensomraden" id="" class="textarea req"
								placeholder="My technical skills areas ... *" rows="5"
								title="My technical skills areas ..."></textarea>
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row omradesspecifika_regelverk">
							<textarea name="omradesspecifika_regelverk" id="" class="textarea req"
								placeholder="I have knowledge of the following area-specific regulations (EU regulations, ordinances, regulations) *"
								rows="5"
								title="I have knowledge of the following area-specific regulations (EU regulations, ordinances, regulations)"></textarea>
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>

						<div class="row mx-0 attachment-row pb-3">
							<input type="file" name="attachment[]" id="attachments" onchange="fileType(this)">
							<img onclick="addMoreAttachment()"
								src="<?php echo get_template_directory_uri() ?>/assets/images/plus-black.png"
								alt="<?php _e('Lägg till fler filer') ?>">
						</div>
					</div>

					<div class="row mx-0 overflow-hidden">
						<div class="frc-captcha" data-sitekey="FCMHCLG9N0S4NF1M" data-lang="<?php echo strtolower(ICL_LANGUAGE_CODE); ?>"></div>
					</div>


					<div class="bottom">
						<input class="submit-booking" type="submit" value="Send" title="Send" disabled>
						<span>* Required fields</span>
					</div>

					<input type="hidden" name="url" value="<?php echo strtok($_SERVER["REQUEST_URI"], '?'); ?>" />
				</form>
			</div>
		<?php } ?>
	<?php endif; ?>

	<?php if (ICL_LANGUAGE_CODE == 'sv'): ?>
		<!-- Sidan Ledning -->
		<?php if (is_page(page: 52)) { ?>

			<?php if (have_rows('ledning_avdelning')): ?>
				<div class="ledning_page">
					<?php while (have_rows('ledning_avdelning')):
						the_row(); ?>
						<div class="sektion">
							<?php
							if (!empty(get_sub_field('avdelningstitel'))):
								?>
								<h3><?php echo get_sub_field('avdelningstitel'); ?></h3>
								<?php
							endif;

							if (have_rows('personer')): ?>
								<ul>
									<?php while (have_rows('personer')):
										the_row();
										$profilbild = get_sub_field('bild'); ?>
										<li>
											<?php if ($profilbild) { ?>
												<div class="profilbild">
													<img data-src="<?php echo $profilbild['sizes']['ledning']; ?>"
														alt="Bild på <?php echo get_sub_field('namn'); ?>" />
												</div>
											<?php } ?>
											<div class="namn">
												<span><?php echo get_sub_field('namn'); ?></span>
											</div>
											<div class="roll">
												<span><?php echo get_sub_field('roll'); ?></span>
											</div>
											<?php if (get_sub_field('epost')) { ?>
												<div class="email">
													<span><a
															href="mailto:<?php echo antispambot(get_sub_field('epost')); ?>"><?php echo get_sub_field('epost'); ?></a></span>
												<?php } ?>
										</li>
									<?php endwhile; ?>
								</ul>
							<?php endif; ?>
						</div>
					<?php endwhile; ?>
				</div>
			<?php endif; ?>

		<?php } ?>
	<?php elseif (ICL_LANGUAGE_CODE == 'en'): ?>
		<!-- Sidan Ledning -->
		<?php if (is_page(3505)) { ?>

			<?php if (have_rows('ledning_avdelning_en')): ?>
				<div class="ledning_page">
					<?php while (have_rows('ledning_avdelning_en')):
						the_row(); ?>
						<div class="sektion">
							<?php
							if (!empty(get_sub_field('avdelningstitel'))):
								?>
								<h3><?php echo get_sub_field('avdelningstitel'); ?></h3>
								<?php
							endif;

							if (have_rows('personer')): ?>
								<ul>
									<?php while (have_rows('personer')):
										the_row();
										$profilbild = get_sub_field('bild'); ?>
										<li>
											<?php if ($profilbild) { ?>
												<div class="profilbild">
													<img data-src="<?php echo $profilbild['sizes']['ledning']; ?>"
														alt="Bild på <?php echo get_sub_field('namn'); ?>" />
												</div>
											<?php } ?>

											<div class="roll">
												<span><?php echo get_sub_field('roll'); ?></span>
											</div>
											<div class="namn">
												<span><?php echo get_sub_field('namn'); ?></span>
											</div>
											<?php if (get_sub_field('epost')) { ?>
												<div class="email">
													<span><a
															href="mailto:<?php echo antispambot(get_sub_field('epost')); ?>"><?php echo get_sub_field('epost'); ?></a></span>
												<?php } ?>
										</li>
									<?php endwhile; ?>
								</ul>
							<?php endif; ?>
						</div>
					<?php endwhile; ?>
				</div>
			<?php endif; ?>

		<?php } ?>
	<?php endif; ?>


	<?php if (ICL_LANGUAGE_CODE == 'sv'): ?>
		<?php if (is_page(48)) { ?>

			<div class="rss_press">
				<strong>Pressmeddelande</strong>
				<?php
				$url = 'https://www.mynewsdesk.com/se/rss/source/2353/pressrelease';
				$contents = get_xml_from_url($url);

				$rss = simplexml_load_string($contents);
				$feed = array();
				foreach ($rss->channel->item as $item) {
					$feedItem = array(
						'title' => (string) $item->title,
						'desc' => (string) $item->description,
						'link' => (string) $item->link,
						'date' => (string) $item->pubDate,
					);
					array_push($feed, $feedItem);
				}
				$limit = 5;
				for ($x = 0; $x < $limit; $x++) {
					echo '<div class="item">';
					$title = str_replace(' & ', ' &amp; ', $feed[$x]['title']);
					$link = $feed[$x]['link'];
					$description = $feed[$x]['desc'];
					$date = date('j F Y H:i', strtotime($feed[$x]['date']));
					echo '<h2><a href="' . $link . '" title="' . $title . '" target="_blank">' . $title . '</a></h2>';
					echo '<div class="meta"><span>' . $date . '</span></div>';
					echo '<div class="description"><p>' . $description . '</p></div>';
					echo '</div>';
				}
				?>

				<a href="https://www.mynewsdesk.com/se/swedac/latest_news" class="symple-button default black   "
					target="_blank" title="Visit Site"><span class="symple-button-inner" style="border-radius:3px">Gå till
						Swedacs pressrum på Mynewsdesk</span></a>


			</div>

		<?php } elseif (is_page(2762869)) {  // Kurser
					?>
			<div id="kurser_section">
				<?php $args = array(
					'post_type' => 'kurser',
					'posts_per_page' => -1,
					'orderby' => 'title',
					'order' => 'asc'
				);
				$query = new WP_Query($args);
				if ($query->have_posts()): ?>
					<ul>
						<?php while ($query->have_posts()):
							$query->the_post(); ?>
							<li>
								<a href="<?php the_permalink(); ?>" title="<?php the_title(); ?>">
									<div class="rubrik">
										<h2 class="title-cuter"><?php the_title(); ?></h2>
									</div>
								</a>
							</li>
						<?php endwhile; ?>
					</ul>
				<?php endif;
				wp_reset_postdata(); ?>
			</div>
		<?php } elseif (is_page(118)) { ?>
			<div id="booking_course">
				<?php if (isset($_GET['success_booking'])):
					$firstname = $_GET['firstname'];
					$lastname = $_GET['lastname'];
					$education = $_GET['education'];
					?>

					<div class="messange_content">
						<div class="inner success">
							<h2>Tack för din anmälan, <?php echo $firstname; ?>!</h2>
							<p>Du har blivit anmäld till kursen:</p>
							<p><strong><?php echo $education; ?></strong></p>
						</div>
					</div>

				<?php elseif (isset($_GET['human_validation_failed'])): ?>
					<div class="messange_content">
						<div class="inner failed">
							<h2>Något gick fel!</h2>
							<p>Vi kunde inte verifiera att du är en människa. Var god försök igen.</p>
							<p>Skulle problemet kvarstå så är du välkommen att kontakta oss via telefon.</p>
						</div>
					</div>
				<?php elseif (isset($_GET['failed_booking'])): ?>
					<div class="messange_content">
						<div class="inner failed">
							<h2>Din anmälan har <strong>inte</strong> skickats.</h2>
							<p>Det verkar som att något gick fel. Vänligen försök en gång till.</p>
							<p>Skulle samma fel uppstå en gång till så är du välkommen att kontakta oss istället.</p>
						</div>
					</div>
				<?php endif; ?>
				<form id="booking_course_form" class="form clear" name="booking_course_form" method="post"
					action="/sender.php?booking_course_form" novalidate="novalidate" autocomplete="false">

					<div class="top">
						<h3>Välj utbildning</h3>
						<div class="row education">
							<?php $args = array(
								'post_type' => 'kurser',
								'posts_per_page' => -1,
								'orderby' => 'title',
								'order' => 'asc'
							);
							$query = new WP_Query($args);
							if ($query->have_posts()): ?>
								<label class="label_select">
									<select class="req" name="swedac_education" id="swedac_education">
										<option value="" disabled selected>
											Välj kurs
										</option>
										<?php while ($query->have_posts()):
											$query->the_post(); ?>
											<option value="<?php the_title(); ?>" data-kurs="<?php the_ID(); ?>">
												<?php the_title(); ?>
											</option>
										<?php endwhile; ?>
									</select>
								</label>
								<i class="fa fa-check"></i>
								<i class="fa fa-times"></i>


							<?php else: ?>
								<label class="label_select">
									<select class="req" name="swedac_education" id="swedac_education">
										<option value="" disabled selected>
											Ingen kurs finns tillgänlig
										</option>
									</select>
								</label>
								<i class="fa fa-check"></i>
								<i class="fa fa-times"></i>
							<?php endif;
							wp_reset_postdata(); ?>

							<?php
							// $ID_kurs = get_ID();
							$args = array(
								'post_type' => 'kurser',
								'posts_per_page' => -1,
								'orderby' => 'title',
								'order' => 'asc'
							);
							$query = new WP_Query($args);
							if ($query->have_posts()): ?>

								<?php while ($query->have_posts()):
									$query->the_post(); ?>
									<div class="kurstillfalle_section <?php the_ID(); ?>">
										<?php if (get_field('swedac_academy_kurstillfallen')) { ?>
											<label class="label_select">
												<select class="kurs_select req <?php the_ID(); ?>" name="swedac_education_kurstillfalle">
													<option value="" disabled selected>
														Välj kurstillfälle
													</option>
													<?php while (has_sub_field('swedac_academy_kurstillfallen')): ?>
														<option
															value="<?php echo get_sub_field('datum'); ?> - <?php echo get_sub_field('tid'); ?>">
															<?php echo get_sub_field('datum'); ?> - <?php echo get_sub_field('tid'); ?>
														</option>
													<?php endwhile; ?>
												</select>
											</label>
										<?php } else { ?>
											<p>Inga kurstillfällen finns upplagda. Men du kan fortfarande skicka in en anmälan för att visa
												ditt intresse.</p>
										<?php } ?>
									</div>
								<?php endwhile; ?>

								<i class="fa fa-check"></i>
								<i class="fa fa-times"></i>
							<?php endif;
							wp_reset_postdata(); ?>
						</div>
					</div>

					<div class="middle">
						<h3>Personlig information</h3>
						<div class="row title">
							<input type="text" class="inputbox req" name="title" id="title" placeholder="Titel *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row fname">
							<input type="text" class="inputbox req" name="firstname" id="firstname" placeholder="Förnamn *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row lname">
							<input type="text" class="inputbox req" name="lastname" id="lastname" placeholder="Efternamn *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row company">
							<input type="text" class="inputbox req" name="company" id="company" placeholder="Företag *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row anstllning">
							<input type="text" class="inputbox req" name="anstallning_position" id="anstallning_position"
								placeholder="Anställning/position *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row adress">
							<input type="text" class="inputbox req" name="adress" id="adress" placeholder="Företagsadress *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row country">
							<input type="text" class="inputbox req" name="country" id="country" placeholder="Land *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row phone">
							<input type="tel" class="inputbox req" name="phone" id="phone"
								onkeypress="return event.charCode >= 48 && event.charCode <= 57" placeholder="Telefon *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row email">
							<input type="email" class="inputbox req" name="email" id="email" placeholder="E-postadress *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>

						<div class="row skraddarsy_kurs">
							<label for="skraddarsy_kurs">Jag skulle vilja ha en skräddarsydd kurs eller studiebesök med
								följande innehåll:</label>
							<textarea name="skraddarsy_kurs" id="skraddarsy_kurs" class="textarea" placeholder=""
								rows="5"></textarea>
						</div>

						<div class="row speciella_onskemal">
							<label for="speciella_onskemal">Ytterligare information eller speciella önskemål</label>
							<textarea name="speciella_onskemal" id="speciella_onskemal" class="textarea" placeholder=""
								rows="5"></textarea>
						</div>
					</div>
					<div class="middle_extra">
						<h3>Övrig deltagare</h3>

						<div id="extra_section"></div>
						<span class="no_deltagare">Inga övriga deltagare</span>
						<span class="create_one_more" id="create_one_more">
							Lägg till deltagare
						</span>

					</div>

					<div class="frc-captcha" data-sitekey="FCMHCLG9N0S4NF1M" data-lang="<?php echo strtolower(ICL_LANGUAGE_CODE); ?>"></div>

					<div class="bottom">
						<div class="left">
							<input class="submit-booking" type="submit" value="Skicka" disabled>
						</div>
						<div class="right">
							<!-- <a href="#">Se Swedac Academys villkor</a> -->
						</div>

						<span>* Obligatoriska fält</span>
					</div>

					<input type="hidden" name="url" value="<?php echo strtok($_SERVER["REQUEST_URI"], '?'); ?>" />
				</form>
			</div>
		<?php } ?>
	<?php elseif (ICL_LANGUAGE_CODE == 'en'): ?>
		<?php if (is_page(2762871)) { ?>
			<div id="kurser_section">
				<?php $args = array(
					'post_type' => 'kurser',
					'posts_per_page' => -1,
					'orderby' => 'title',
					'order' => 'asc'
				);
				$query = new WP_Query($args);
				if ($query->have_posts()): ?>
					<ul>
						<?php while ($query->have_posts()):
							$query->the_post(); ?>
							<li>
								<a href="<?php the_permalink(); ?>" title="<?php the_title(); ?>">
									<div class="rubrik">
										<h2 class="title-cuter"><?php the_title(); ?></h2>
									</div>
								</a>
							</li>
						<?php endwhile; ?>
					</ul>
				<?php endif;
				wp_reset_postdata(); ?>
			</div>
		<?php } elseif (is_page(3509)) { ?>
			<div id="booking_course">
				<?php if (isset($_GET['success_booking'])):
					$firstname = $_GET['firstname'];
					$lastname = $_GET['lastname'];
					$education = $_GET['education'];
					?>

					<div class="messange_content">
						<div class="inner success">
							<h2>Thanks for your registration, <?php echo $firstname; ?>!</h2>
							<p>You have been registered for the course:</p>
							<p><strong><?php echo $education; ?></strong></p>
						</div>
					</div>
				<?php elseif (isset($_GET['human_validation_failed'])): ?>
					<div class="messange_content">
						<div class="inner failed">
							<h2>Something went wrong!</h2>
							<p>We could not verify that you are human. Please try again.</p>
							<p>Should the issue remain, you are welcome to contact us instead.</p>
						</div>
					</div>
				<?php elseif (isset($_GET['failed_booking'])): ?>
					<div class="messange_content">
						<div class="inner failed">
							<h2>Your registration has <strong>not</strong> sent.</h2>
							<p>It seems that something went wrong. Please try again.</p>
							<p>If the same error occurs again, you are welcome to contact us instead.</p>
						</div>
					</div>
				<?php endif; ?>
				<form id="booking_course_form" class="form clear" name="booking_course_form" method="post"
					action="/sender.php?booking_course_form" novalidate="novalidate" autocomplete="false">

					<div class="top">
						<h3>Choose education</h3>
						<div class="row education">
							<?php $args = array(
								'post_type' => 'kurser',
								'posts_per_page' => -1,
								'orderby' => 'title',
								'order' => 'asc'
							);
							$query = new WP_Query($args);
							if ($query->have_posts()): ?>
								<label class="label_select">
									<select class="req" name="swedac_education" id="swedac_education">
										<option value="" disabled selected>
											Choose course
										</option>
										<?php while ($query->have_posts()):
											$query->the_post(); ?>
											<option value="<?php the_title(); ?>" data-kurs="<?php the_ID(); ?>">
												<?php the_title(); ?>
											</option>
										<?php endwhile; ?>
									</select>
								</label>
								<i class="fa fa-check"></i>
								<i class="fa fa-times"></i>


							<?php else: ?>
								<label class="label_select">
									<select class="req" name="swedac_education" id="swedac_education">
										<option value="" disabled selected>
											No courses available
										</option>
									</select>
								</label>
								<i class="fa fa-check"></i>
								<i class="fa fa-times"></i>
							<?php endif;
							wp_reset_postdata(); ?>

							<?php
							// $ID_kurs = get_ID();
							$args = array(
								'post_type' => 'courses',
								'posts_per_page' => -1,
								'orderby' => 'title',
								'order' => 'asc'
							);
							$query = new WP_Query($args);
							if ($query->have_posts()): ?>

								<?php while ($query->have_posts()):
									$query->the_post(); ?>
									<div class="kurstillfalle_section <?php the_ID(); ?>">
										<?php if (get_field('swedac_academy_kurstillfallen_en')) { ?>
											<label class="label_select">
												<select class="kurs_select req <?php the_ID(); ?>" name="swedac_education_kurstillfalle">
													<option value="" disabled selected>
														Select occasion
													</option>
													<?php while (has_sub_field('swedac_academy_kurstillfallen_en')): ?>
														<option
															value="<?php echo get_sub_field('datum'); ?> - <?php echo get_sub_field('tid'); ?>">
															<?php echo get_sub_field('datum'); ?> - <?php echo get_sub_field('tid'); ?>
														</option>
													<?php endwhile; ?>
												</select>
											</label>
										<?php } else { ?>
											<p>No occasion. But you can still submit an application to show your interest.</p>
										<?php } ?>
									</div>
								<?php endwhile; ?>

								<i class="fa fa-check"></i>
								<i class="fa fa-times"></i>
							<?php endif;
							wp_reset_postdata(); ?>
						</div>
					</div>

					<div class="middle">
						<h3>Personal information</h3>
						<div class="row title">
							<input type="text" class="inputbox req" name="title" id="title" placeholder="Title *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row fname">
							<input type="text" class="inputbox req" name="firstname" id="firstname" placeholder="First name *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row lname">
							<input type="text" class="inputbox req" name="lastname" id="lastname" placeholder="Last name *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row company">
							<input type="text" class="inputbox req" name="company" id="company" placeholder="Company *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row var_number">
							<input type="text" class="inputbox" name="var_number" id="var_number"
								placeholder="VAT registration number">
						</div>
						<div class="row position">
							<input type="text" class="inputbox req" name="anstallning_position" id="anstallning_position"
								placeholder="Position *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>

						<div class="row adress">
							<input type="text" class="inputbox req" name="adress" id="adress" placeholder="Company address *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>

						<div class="row country">
							<input type="text" class="inputbox req" name="country" id="country" placeholder="Country *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>

						<div class="row phone">
							<input type="tel" class="inputbox req" name="phone" id="phone"
								onkeypress="return event.charCode >= 48 && event.charCode <= 57" placeholder="Phone *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>
						<div class="row email">
							<input type="email" class="inputbox req" name="email" id="email" placeholder="Email *">
							<i class="fa fa-check"></i>
							<i class="fa fa-times"></i>
						</div>

						<div class="row skraddarsy_kurs">
							<label for="skraddarsy_kurs">I would like a customized course or study visit with the following
								content:</label>
							<textarea name="skraddarsy_kurs" id="skraddarsy_kurs" class="textarea" placeholder=""
								rows="5"></textarea>
						</div>

						<div class="row speciella_onskemal">
							<label for="speciella_onskemal">Further information or special requirements</label>
							<textarea name="speciella_onskemal" id="speciella_onskemal" class="textarea" placeholder=""
								rows="5"></textarea>
						</div>
					</div>
					<div class="middle_extra">
						<h3>Additional participants</h3>

						<div id="extra_section"></div>
						<span class="no_deltagare">No additional participants</span>
						<span class="create_one_more" id="create_one_more">
							Add one more
						</span>

					</div>

					<div class="frc-captcha" data-sitekey="FCMHCLG9N0S4NF1M" data-lang="<?php echo strtolower(ICL_LANGUAGE_CODE); ?>"></div>


					<div class="bottom">
						<div class="left">
							<input class="submit-booking" type="submit" value="Send" disabled>
						</div>
						<div class="right">
							<!-- <a href="#">See Swedac Academy conditions</a> -->
						</div>

						<span>* Required fields</span>
					</div>

					<input type="hidden" name="url" value="<?php echo strtok($_SERVER["REQUEST_URI"], '?'); ?>" />
				</form>
			</div>
		<?php } ?>
	<?php endif; ?>

	<?php if (ICL_LANGUAGE_CODE == 'sv'): ?>
		<?php if (is_page(116)) { // Bedömarutbildning
					?>

			<?php if (isset($_GET['success_bedomarutbildning'])): ?>
				<div class="messange_box">
					<div class="inner success">
						<h2>Tack för din anmälan!</h2>
						<p>Din anmälan har skickats till oss på Swedac.</p>
					</div>
				</div>
			<?php elseif (isset($_GET['human_validation_failed'])): ?>
				<div class="messange_box">
					<div class="inner failed">
						<h2><?php _e('Något gick fel!', 'applyform') ?></h2>
						<p><?php _e('Vi kunde inte verifiera att du är en människa. Var god försök igen.', 'applyform') ?></p>
						<p><?php _e('Skulle problemet kvarstå så är du välkommen att kontakta oss via telefon.', 'applyform') ?>
						</p>
					</div>
				</div>
			<?php elseif (isset($_GET['failed_bedomarutbildning'])): ?>
				<div class="messange_box">
					<div class="inner failed">
						<h2>Din anmälan har <strong>inte</strong> skickats.</h2>
						<p>Det verkar som att något gick fel. Vänligen försök en gång till.</p>
						<p>Skulle samma fel uppstå en gång till så är du välkommen att kontakta oss via telefon.</p>
					</div>
				</div>
			<?php endif; ?>

			<?php if (get_field('bedomarutbildning_tillfallen_rep', 116)) { // Om det finns tillfällen inlagda på sidan Bedömarutbildning
							?>
				<span class="symple-button reg_bedomarutbildning">Registrera dig till bedömarkurs</span>

				<div id="reg_bedomarutbildning">
					<div id="bedomarutbildning_course">

						<form id="bedomarutbildning_form" class="form clear" name="bedomarutbildning_formName" method="post"
							action="/sender.php?bedomarutbildning">
							<div class="top">
								<div class="row full">

									<?php if (get_field('bedomarutbildning_tillfallen_rep', 116)): ?>
										<select class="req" name="bedomarutbildning_tillfalle" id="bedomarutbildning_tillfalle"
											required>
											<option value="" disabled selected>
												Välj datum *
											</option>
											<?php while (has_sub_field('bedomarutbildning_tillfallen_rep', 116)): ?>
												<option value="<?php echo get_sub_field('datum'); ?>">
													<?php echo get_sub_field('datum'); ?>
												</option>
											<?php endwhile; ?>
										</select>
									<?php endif; ?>
								</div>

							</div>
							<div class="middle">

								<div class="row full">
									<input type="text" class="inputbox req" name="diarienummer" id="diarienummer"
										placeholder="Diarienummer *" required>
									<i class="fa fa-check"></i>
									<i class="fa fa-times"></i>
								</div>

								<div class="row left">
									<input type="text" class="inputbox req" name="firstname" id="firstname" placeholder="Förnamn *"
										required>
									<i class="fa fa-check"></i>
									<i class="fa fa-times"></i>
								</div>
								<div class="row right">
									<input type="text" class="inputbox req" name="lastname" id="lastname" placeholder="Efternamn *"
										required>
									<i class="fa fa-check"></i>
									<i class="fa fa-times"></i>
								</div>
								<div class="row left">
									<input type="text" class="inputbox req" name="company" id="company" placeholder="Företag *"
										required>
									<i class="fa fa-check"></i>
									<i class="fa fa-times"></i>
								</div>
								<div class="row right">
									<input type="text" class="inputbox req" name="orgnr" id="orgnr"
										placeholder="Organisationsnummer *" required>
									<i class="fa fa-check"></i>
									<i class="fa fa-times"></i>
								</div>
								<div class="row left">
									<input type="text" class="inputbox req" name="postadress" id="postadress"
										placeholder="Postadress *" required>
									<i class="fa fa-check"></i>
									<i class="fa fa-times"></i>
								</div>
								<div class="row right">
									<input type="text" class="inputbox req" name="postnummer_ort" id="postnummer_ort"
										placeholder="Postnummer och ort *" required>
									<i class="fa fa-check"></i>
									<i class="fa fa-times"></i>
								</div>
								<div class="row left">
									<input type="text" class="inputbox req" name="faktura_adress" id="faktura_adress"
										placeholder="Fakturaadress *" required>
									<i class="fa fa-check"></i>
									<i class="fa fa-times"></i>
								</div>
								<div class="row right">
									<input type="text" class="inputbox req" name="faktura_postnummer_ort"
										id="faktura_postnummer_ort" placeholder="Postnummer och ort *" required>
									<i class="fa fa-check"></i>
									<i class="fa fa-times"></i>
								</div>
								<div class="row left">
									<input type="tel" class="inputbox req" name="phone" id="phone"
										onkeypress="return event.charCode >= 48 && event.charCode <= 57" placeholder="Telefon *"
										required>
									<i class="fa fa-check"></i>
									<i class="fa fa-times"></i>
								</div>
								<div class="row right">
									<input type="email" class="inputbox req" name="email" id="email" placeholder="E-postadress *"
										required>
									<i class="fa fa-check"></i>
									<i class="fa fa-times"></i>
								</div>
								<div class="row full">
									<input type="text" class="inputbox" name="ovriga_kontaktuppgifter" id="ovriga_kontaktuppgifter"
										placeholder="Övriga kontaktuppgifter (ref.nr/beställningsnr. etc)">
								</div>
								<div class="row full">
									<input type="text" class="inputbox req" name="send_material_to_email"
										id="send_material_to_email" placeholder="Jag vill ha kursmaterialet hit innan kursstart *"
										required>
									<i class="fa fa-check"></i>
									<i class="fa fa-times"></i>
								</div>
								<div class="row full">
									<label for="">Markera om du önskar gå fördjupningsdelen i något av dessa teknikområden:</label>

									<span><input type="checkbox" name="fordjupning_alt[]" value="Kontroll"> Kontroll</span>
									<span><input type="checkbox" name="fordjupning_alt[]" value="Medicinska laboratorier">
										Medicinska laboratorier</span>
									<span><input type="checkbox" name="fordjupning_alt[]"
											value="Organ som certifierar produkter, processer och tjänster"> Organ som certifierar
										produkter, processer och tjänster</span>
								</div>
								<div class="row full">
									<label for="">Kan du tänka dig att arbeta som bedömare åt Swedac?</label>
									<span><input type="radio" name="bedommare_arbeta" value="Ja" checked> Ja</span>
									<span><input type="radio" name="bedommare_arbeta" value="Nej"> Nej</span>
								</div>

								<div class="row full">
									<textarea name="ovriga_onskemal" id="" class="textarea req"
										placeholder="Eventuella övriga önskemål och upplysningar (t ex om kost)" rows="5"
										required></textarea>
									<i class="fa fa-check"></i>
									<i class="fa fa-times"></i>
								</div>
							</div>

							<div class="frc-captcha" data-sitekey="FCMHCLG9N0S4NF1M" data-lang="<?php echo strtolower(ICL_LANGUAGE_CODE); ?>"></div>

							<div class="bottom">
								<input class="submit-booking" type="submit" value="Skicka anmälan" disabled>
								<span>* Obligatioriska fält</span>
							</div>
							<input type="hidden" name="swedac_education" value="<?php the_title(); ?>">
							<input type="hidden" name="url" value="<?php echo strtok($_SERVER["REQUEST_URI"], '?'); ?>" />
						</form>
					</div>
				</div>
			<?php } ?>


		<?php } ?>
	<?php elseif (ICL_LANGUAGE_CODE == 'en'): ?>

	<?php endif; ?>

	<?php if (ICL_LANGUAGE_CODE == 'sv'): ?>
		<?php if (get_field('snabbval_subpage_sv')): ?>
			<div id="page_snabbval">
				<ul>
					<?php while (has_sub_field('snabbval_subpage_sv')): ?>
						<li>
							<a href="<?php echo get_sub_field('lankmal'); ?>" title="<?php echo get_sub_field('rubrik'); ?>">
								<div class="rubrik">
									<h2 class="title-cuter"><?php echo get_sub_field('rubrik'); ?></h2>
								</div>
							</a>
						</li>
					<?php endwhile; ?>
				</ul>
			</div>
		<?php endif; ?>

		<?php $posts = get_field('kontaktperson_relation');
		if ($posts): ?>
			<div class="kontaktperson_section">
				<h2>Kontaktperson</h2>
				<ul>
					<?php foreach ($posts as $post): // variable must be called $post (IMPORTANT) ?>
						<?php setup_postdata($post);
						$kontaktperson = get_field('kontaktperson_profilbild'); ?>
						<li>

							<?php if ($kontaktperson) { ?>
								<div class="left">
									<img data-src="<?php echo $kontaktperson['sizes']['medium']; ?>" alt="<?php the_title(); ?>" />
								</div>
								<div class="right">
									<span class="namn">
										<?php echo get_field('kontaktperson_namn'); ?>, <?php echo get_field('kontaktperson_roll'); ?>
									</span>
									<span class="epost">
										<a href="mailto:<?php echo antispambot(get_field('kontaktperson_epost')); ?>">Skicka e-post</a>
									</span>
									<?php if (get_field('kontaktperson_telefonnummer')) { ?>
										<span class="telefon">
											Telefon: <?php echo get_field('kontaktperson_telefonnummer'); ?>
										</span>
									<?php } ?>
								</div>
							<?php } else { ?>
								<div class="right">
									<span class="namn">
										<?php if (get_field('kontaktperson_roll')) { ?>

											<?php echo get_field('kontaktperson_namn'); ?>, <?php echo get_field('kontaktperson_roll'); ?>

										<?php } else { ?>

											<?php echo get_field('kontaktperson_namn'); ?>

										<?php } ?>
									</span>
									<?php if (get_field('kontaktperson_epost')) { ?>
										<span class="epost">
											<a href="mailto:<?php echo antispambot(get_field('kontaktperson_epost')); ?>">Skicka e-post</a>
										</span>
									<?php } ?>
									<?php if (get_field('kontaktperson_telefonnummer')) { ?>
										<span class="telefon">
											Telefon: <?php echo get_field('kontaktperson_telefonnummer'); ?>
										</span>
									<?php } ?>
								</div>
							<?php } ?>
						</li>
					<?php endforeach; ?>
				</ul>
			</div>
			<?php wp_reset_postdata(); // IMPORTANT - reset the $post object so the rest of the page works correctly ?>
		<?php endif; ?>

		<?php if (is_page_template('page-external-search.php')) { ?>

		<?php } else { ?>
			<?php if (ICL_LANGUAGE_CODE == 'sv'): ?>
				<?php // Hämta innehåll som är relaterat till den aktuella sidan men också skriva ut relatert information
							$postRelated = get_field('relaterad_information');
							if ($postRelated) { ?>
					<div class="relaterad_information_custom">
						<h2>Relaterat</h2>
						<ul>
							<?php foreach ($postRelated as $post): // variable must be called $post (IMPORTANT) ?>
								<?php setup_postdata($post); ?>
								<?php $postType = get_post_type_object(get_post_type());
								if ($postType) {
									$postIn = esc_html($postType->labels->singular_name);
									if ($postIn == 'Inlägg') {
										$postIn = 'Nyhet';
									}
								} ?>

								<li>
									<a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
									<span>(<?php echo $postIn; ?><?php if ($postIn == 'Nyhet') {
										   echo ' - ' . get_the_date('j F Y');
									   } ?>)</span>
								</li>
							<?php endforeach; ?>
						</ul>
					</div>
				<?php } else { ?>

				<?php } ?>
				<?php wp_reset_query(); ?>
			<?php elseif (ICL_LANGUAGE_CODE == 'en'): ?>
				<?php // Hämta innehåll som är relaterat till den aktuella sidan men också skriva ut relatert information
				
							$postRelated = get_field('relaterad_information_en');
							if ($postRelated) { ?>
					<div class="relaterad_information_custom">
						<h2>Related</h2>
						<ul>
							<?php foreach ($postRelated as $post): // variable must be called $post (IMPORTANT) ?>
								<?php setup_postdata($post); ?>
								<li>
									<a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
									<span>(<?php echo $postIn; ?><?php if ($postIn == 'News') {
										   echo ' - ' . get_the_date('j F Y');
									   } ?>)</span>
								</li>
							<?php endforeach; ?>
						</ul>
					</div>
				<?php } else { ?>

				<?php } ?>
				<?php wp_reset_query(); ?>
			<?php endif; ?>
		<?php } ?>




		<?php if (get_field('enskilda_dokument')): ?>
			<div class="dokument_section">
				<h2><?= __('Ladda ner dokument', 'certificates'); ?></h2>
				<ul>
					<?php while (has_sub_field('enskilda_dokument')): ?>

						<?php
						$attachment_id = get_sub_field('dokument');
						$url = wp_get_attachment_url($attachment_id); //Hämta filensurl
						$fullsize_path = get_attached_file($attachment_id); //Hämta filen
			
						$filename_only = '';
						$filesize = '0 KB';
						$path_info = '';
						$ext = 'Okänd filtyp';

						if (file_exists($fullsize_path)) {
							$filename_only = basename($fullsize_path); //Hämta namn
							$filesize = size_format(filesize($fullsize_path), 2); //Hämta filstorlek
							$path_info = pathinfo($fullsize_path); //Hämta filtypen
							$ext = pathinfo($url, PATHINFO_EXTENSION); //Hämta filtypen
						}
						?>
						<li>

							<div class="left">
								<img data-src="<?php echo get_template_directory_uri(); ?>/assets/images/icon-document-large.png"
									alt="Dokument" />
							</div>
							<div class="right">
								<span class="file"><a href="<?php echo $url; ?>" title=""
										download><?php echo get_sub_field('namn'); ?></a></span>
								<span class="file_type_size"><?php echo $ext; ?>, <?php echo $filesize; ?></span>
							</div>
						</li>
					<?php endwhile; ?>
				</ul>
			</div>
		<?php endif; ?>
	<?php elseif (ICL_LANGUAGE_CODE == 'en'): ?>
		<?php if (get_field('snabbval_subpage_en')): ?>
			<div id="page_snabbval">
				<ul>
					<?php while (has_sub_field('snabbval_subpage_en')): ?>
						<li>
							<a href="<?php echo get_sub_field('lankmal'); ?>" title="<?php echo get_sub_field('rubrik'); ?>">
								<div class="rubrik">
									<h2 class="title-cuter"><?php echo get_sub_field('rubrik'); ?></h2>
								</div>
							</a>
						</li>
					<?php endwhile; ?>
				</ul>
			</div>
		<?php endif; ?>

		<?php $posts = get_field('kontaktperson_relation_en');
		if ($posts): ?>
			<div class="kontaktperson_section">
				<h2>Contact person</h2>
				<ul>
					<?php foreach ($posts as $post): // variable must be called $post (IMPORTANT) ?>
						<?php setup_postdata($post);
						$kontaktperson = get_field('kontaktperson_profilbild_en'); ?>
						<li>

							<?php if ($kontaktperson) { ?>
								<div class="left">
									<img data-src="<?php echo $kontaktperson['sizes']['medium']; ?>" alt="<?php the_title(); ?>" />
								</div>
								<div class="right">
									<span class="namn">
										<?php echo get_field('kontaktperson_namn_en'); ?>,
										<?php echo get_field('kontaktperson_roll_en'); ?>
									</span>
									<span class="epost">
										<a href="mailto:<?php echo antispambot(get_field('kontaktperson_epost_en')); ?>">Send email</a>
									</span>
									<?php if (get_field('kontaktperson_telefonnummer_en')) { ?>
										<span class="telefon">
											Phone: <?php echo get_field('kontaktperson_telefonnummer_en'); ?>
										</span>
									<?php } ?>
								</div>
							<?php } else { ?>
								<div class="right">
									<span class="namn">
										<?php if (get_field('kontaktperson_roll_en')) { ?>

											<?php echo get_field('kontaktperson_namn_en'); ?>,
											<?php echo get_field('kontaktperson_roll_en'); ?>

										<?php } else { ?>

											<?php echo get_field('kontaktperson_namn_en'); ?>

										<?php } ?>
									</span>
									<?php if (get_field('kontaktperson_epost_en')) { ?>
										<span class="epost">
											<a href="mailto:<?php echo antispambot(get_field('kontaktperson_epost_en')); ?>">Send email</a>
										</span>
									<?php } ?>
									<?php if (get_field('kontaktperson_telefonnummer_en')) { ?>
										<span class="telefon">
											Phone: <?php echo get_field('kontaktperson_telefonnummer_en'); ?>
										</span>
									<?php } ?>
								</div>
							<?php } ?>
						</li>
					<?php endforeach; ?>
				</ul>
			</div>
			<?php wp_reset_postdata(); // IMPORTANT - reset the $post object so the rest of the page works correctly ?>
		<?php endif; ?>


		<?php if (get_field('enskilda_dokument_en')): ?>
			<div class="dokument_section">
				<h2>Download document</h2>
				<ul>
					<?php while (has_sub_field('enskilda_dokument_en')): ?>

						<?php
						$attachment_id = get_sub_field('dokument');
						$url = wp_get_attachment_url($attachment_id); //Hämta filensurl
						$fullsize_path = get_attached_file($attachment_id); //Hämta filen
			
						$filename_only = '';
						$filesize = '0 KB';
						$path_info = '';
						$ext = 'Unkown file';

						if (file_exists($fullsize_path)) {
							$filename_only = basename($fullsize_path); //Hämta namn
							$filesize = size_format(filesize($fullsize_path), 2); //Hämta filstorlek
							$path_info = pathinfo($fullsize_path); //Hämta filtypen
							$ext = pathinfo($url, PATHINFO_EXTENSION); //Hämta filtypen
						}
						?>


						<li>
							<div class="left">
								<img data-src="<?php echo get_template_directory_uri(); ?>/assets/images/icon-document-large.png"
									alt="Dokument" />
							</div>
							<div class="right">
								<span class="file"><a href="<?php echo $url; ?>" title=""
										download><?php echo get_sub_field('namn'); ?></a></span>
								<span class="file_type_size"><?php echo $ext; ?>, <?php echo $filesize; ?></span>
							</div>
						</li>
					<?php endwhile; ?>
				</ul>
			</div>
		<?php endif; ?>
	<?php endif; ?>

</div>

<script type="text/javascript">
	function addMoreAttachment() {
		jQuery('.attachment-row:last')
			.clone()
			.insertAfter('.attachment-row:last');
		jQuery('.attachment-row:last').find('input').val('');
	}
</script>