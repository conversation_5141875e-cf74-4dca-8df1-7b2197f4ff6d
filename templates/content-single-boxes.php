<?php if (have_rows('box_repeater_placeholder', 'option')) : ?>
    <div class="container-fluid bg-d-blue py-5 border-bottom mb-neg-5">
        <div class="wrapper px-0">
            <div class="container px-md-0">
                <div class="row mx-0">
                    <div class="col-12 px-0">
                        <?php $title = get_field('box_header'); ?>
                        <h2 class="text-white mb-4"><?php echo $title; ?></h2>
                    </div>
                </div>
                <div class="row mx-0">
                    <?php
                    if (have_rows('box_repeater')) :
                        while( have_rows('box_repeater') ) : the_row();
                            $page = get_sub_field('box_site');
                            $pageID = $page->ID;
                            $img = get_sub_field('box_img');
                            $pageTitle = get_sub_field('box_title');
                            $pageText = get_sub_field('box_text'); ?>
                            <a class="col-12 col-md-8 mx-md-auto col-lg-4 py-3 py-lg-0 text-decoration-none" id="card-box" href="<?php the_permalink($pageID); ?>">
                                <div class="col-12 col-lg-12 py-3 grow cardImg" style="<?php if ($img) : ?>background-image: url('<?php echo $img; ?>'); <?php else :?> background: green; <?php endif; ?>"></div>
                                <div class="text-white pt-3">
                                    <h3 class="text-white"><?php echo $pageTitle; ?></h3>
                                    <small><?php echo $pageText; ?></small>
                                </div>
                            </a>
                            <?php 
                        endwhile; 
                    else :
                        while( have_rows('box_repeater_placeholder', 'option') ) : the_row();
                            $page = get_sub_field('box_site_placeholder');
                            $pageID = $page->ID;
                            $img = get_sub_field('box_img_placeholder');
                            $pageTitle = get_sub_field('box_title_placeholder');
                            $pageText = get_sub_field('box_text_placeholder'); ?>
                            <div class="col-12 col-md-8 mx-md-auto col-lg-4 py-3 py-lg-0">
                                <a class="text-decoration-none" href="<?php the_permalink($pageID); ?>">
                                    <div class="col-12 col-lg-12 py-3 grow cardImg" style="<?php if ($img) : ?>background-image: url('<?php echo $img; ?>'); <?php else :?> background: green; <?php endif; ?>"></div>
                                    <div class="text-white pt-3">
                                        <h3 class="text-white"><?php echo $pageTitle; ?></h3>
                                        <small><?php echo $pageText; ?></small>
                                    </div>
                                </a>
                            </div>
                            <?php 
                        endwhile; 
                    endif;?>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>