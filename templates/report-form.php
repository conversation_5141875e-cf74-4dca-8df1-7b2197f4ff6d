<?php if (isset($_GET['success_repport'])): ?>
  <div class="messange_box">
    <div class="inner success">
      <h2><?php _e('Tack för din anmälan!', 'applyform') ?></h2>
      <p><?php _e('Din anmälan har skickats till oss på Swedac. Vi återkommer så snart vi kan.', 'applyform') ?></p>
    </div>
  </div>
<?php elseif (isset($_GET['human_validation_failed'])): ?>
  <div class="messange_box">
    <div class="inner failed">
      <h2><?php _e('Något gick fel!', 'applyform') ?></h2>
      <p><?php _e('Vi kunde inte verifiera att du är en människa. Var god försök igen.', 'applyform') ?></p>
      <p><?php _e('Skulle problemet kvarstå så är du välkommen att kontakta oss via telefon.', 'applyform') ?></p>
    </div>
  </div>
<?php elseif (isset($_GET['failed_repport'])): ?>
  <div class="messange_box">
    <div class="inner failed">
      <h2><?php _e('Din anmälan har inte skickats.', 'applyform') ?></h2>
      <p><?php _e('Det verkar som att något gick fel. Vänligen försök en gång till.', 'applyform') ?></p>
      <p>
        <?php _e('Skulle samma fel uppstå en gång till så är du välkommen att kontakta oss via telefon.', 'applyform') ?>
      </p>
    </div>
  </div>
<?php endif; ?>

<div class="container visselblasning_form_section px-0">
  <div class="row">
    <div class="col-12">

      <form id="visselblasning_form" class="form clear" name="visselblasning" method="post"
        action="/sender.php?visselblasning" autocomplete="true" enctype="multipart/form-data">
        <div class="row_section">

          <div class="pb-3">

            <span class="row_title">Kontaktuppgifter</span>

            <div class="form-row left">
              <input type="text" class="inputbox req" name="firstname" id="firstname"
                placeholder="<?php _e('Namn *', 'applyform') ?>" title="firstname" required>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>
            <div class="form-row right">
              <input type="text" class="inputbox req" name="lastname" id="lastname"
                placeholder="<?php _e('Efternamn *', 'applyform') ?>" title="lastname" required>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>

            <div class="form-row full">
              <input type="email" class="inputbox req" name="email" id="email"
                placeholder="<?php _e('E-postadres *', 'applyform') ?>" title="email" required>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>

            <div class="form-row full">
              <input type="tel" class="inputbox req" name="phone" id="phone"
                placeholder="<?php _e('Telefonnummer *', 'applyform') ?>" title="phone" required>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>

            <div class="form-row full">
              <input type="text" class="inputbox" name="adress" id="adress"
                placeholder="<?php _e('Postadress', 'applyform') ?>" title="adress">
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>

            <div class="form-row left">
              <input type="text" class="inputbox" name="postnummer" id="postnummer"
                placeholder="<?php _e('Postnummer', 'applyform') ?>" title="Postnummer">
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>
            <div class="form-row right">
              <input type="text" class="inputbox" name="postort" id="postort"
                placeholder="<?php _e('Postort', 'applyform') ?>" title="Postort">
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>

            <span class="row_title">Beskriv missförhållandet</span>
            <div class="form-row full">
              <textarea type="text" rows="3" class="inputbox" name="missförhållandet" id="missförhållandet"
                title="Beskriv missförhållandet"></textarea>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>

            <span class="row_title">Var har missförhållandena ägt rum?</span>
            <div class="form-row full">
              <textarea type="text" rows="3" class="inputbox" name="var_missforhallandet" id="var_missforhallandet"
                title="Var har missförhållandena ägt rum?"></textarea>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>

            <span class="row_title">När ägde missförhållandena rum?</span>
            <div class="form-row full">
              <textarea type="text" rows="3" class="inputbox" name="when_missforhallandet" id="when_missforhallandet"
                title="När ägde missförhållandena rum?"></textarea>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>

            <span class="row_title">Hur fick du kännedom om missförhållandet?</span>
            <div class="form-row full">
              <textarea type="text" rows="3" class="inputbox" name="how_missforhallandet" id="how_missforhallandet"
                title="Hur fick du kännedom om missförhållandet?"></textarea>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>

            <span class="row_title">Har du skälig anledning att anta att uppgifterna om missförhållandena är
              sanna?</span>
            <div class="form-row full">
              <textarea type="text" rows="3" class="inputbox" name="how_reason_missforhallandet"
                id="how_reason_missforhallandet"
                title="Har du skälig anledning att anta att uppgifterna om missförhållandena är sanna?"></textarea>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>

            <span class="row_title">Om missförhållandena inte redan har ägt rum, när kommer de att äga rum?</span>
            <div class="form-row full">
              <textarea type="text" rows="3" class="inputbox" name="not_when_missforhallandet"
                id="not_when_missforhallandet"
                title="Om missförhållandena inte redan har ägt rum, när kommer de att äga rum?"></textarea>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>

            <span class="row_title">Vem eller vilka personer är inblandade i missförhållandet?</span>
            <div class="form-row full">
              <textarea type="text" rows="3" class="inputbox" name="who_missforhallandet" id="who_missforhallandet"
                title="Vem eller vilka personer är inblandade i missförhållandet?"></textarea>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>

            <span class="row_title">I vilket arbetsrelaterat sammanhang fick du reda på informationen?</span>
            <div class="form-row full">
              <textarea type="text" rows="3" class="inputbox" name="work_related_missforhallandet"
                id="work_related_missforhallandet"
                title="I vilket arbetsrelaterat sammanhang fick du reda på informationen?"></textarea>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>

            <span class="row_title">På vilket sätt rör missförhållandet en överträdelse av unionsrätten?</span>
            <div class="form-row full">
              <textarea type="text" rows="3" class="inputbox" name="why_union_right_missforhallandet"
                id="why_union_right_missforhallandet"
                title="På vilket sätt rör missförhållandet en överträdelse av unionsrätten?"></textarea>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>

            <span class="row_title">Finns det något ytterligare du vill tillägga?</span>
            <div class="form-row full">
              <textarea type="text" rows="3" class="inputbox" name="extra_missforhallandet" id="extra_missforhallandet"
                title="Finns det något ytterligare du vill tillägga?"></textarea>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>

          </div>
          <div class="row_section pt-3 captcha">
            <input type="hidden" name="lang" value="<?php echo strtolower(ICL_LANGUAGE_CODE); ?>" />
            <input type="hidden" name="url" value="<?php echo strtok($_SERVER["REQUEST_URI"], '?'); ?>" />
            <div class="row px-0 mx-0">
              <div class="frc-captcha" data-sitekey="FCMHCLG9N0S4NF1M" data-lang="<?php echo strtolower(ICL_LANGUAGE_CODE); ?>"></div>
              <div class="col-md-4 col-12 pr-0"><input class="submit-button w-100" id="submit-button" type="submit"
                  value="<?php _e('Skicka', 'applyform') ?>" disabled></div>
            </div>
          </div>
        </div> <!-- End of row_section -->
      </form>
    </div>
  </div>
</div>