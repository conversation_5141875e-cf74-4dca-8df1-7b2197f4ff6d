<div id="page-header-parent">
	<div class="title">
		<div class="wrapper">
			<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
				<span class="title-page">Kalender</span>
			<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
				<span class="title-page">Calender</span>
			<?php endif;?>
			
		</div>
	</div>
		
	<div class="wrapper">
		<?php
		if (ICL_LANGUAGE_CODE=='sv') :
			cc_breadcrumb_renderer();
		elseif (ICL_LANGUAGE_CODE=='en') : 
			?>
			<nav aria-label="Breadcrumb" id="breadcrumbs"><span xmlns:v="http://rdf.data-vocabulary.org/#"><span typeof="v:Breadcrumb"><a href="https://www.swedac.se?lang=en" title="Return to Home" rel="v:url" property="v:title">Home</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="https://www.swedac.se/calender/?lang=en" title="Return to Calender" rel="v:url" property="v:title">Calender</a> / <span class="breadcrumb_last"><?php the_title();?></span></span></span></span></nav>
			<?php
		endif;
		?>
	</div>
</div>
 
<div class="wrapper">
	<div id="single_section_posts_kalender">
		<div class="main">
			<?php while (have_posts()) : the_post(); ?>
				<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
					<article <?php post_class('single-post-kalender'); ?>>
						<h1 class="entry-title r_toggle"><?php the_title(); ?></h1>
						<div class="kalender_date">
							<span id="start_date" class="start_date"><?php echo get_field('kal_startdatum_sv'); ?></span>
							<span class="start_year"><?php echo get_field('kal_manad_ar_sv'); ?></span>
						</div>
						<div class="kalender_info">
							<header>
								<table>
									<tr>
										<td class="first">DATUM:</td>
										<?php if(get_field('kal_startdatum_sv') ==  get_field('kal_slutdatum_sv')) { ?>
											<td class="last"><?php echo get_field('kal_startdatum_sv'); ?> <span>- <?php echo get_field('kal_slutdatum_sv'); ?></span> <?php echo get_field('kal_manad_ar_sv'); ?></td>
										<?php } else { ?>
											<td class="last"><?php echo get_field('kal_startdatum_sv'); ?> - <?php echo get_field('kal_slutdatum_sv'); ?> <?php echo get_field('kal_manad_ar_sv'); ?></td>
										<?php } ?>
									</tr>
									<tr>
										<td class="first">TID:</td>
										<td class="last">Kl. <?php echo get_field('kal_tid_sv'); ?> - <?php echo get_field('kal_tid_slut_sv'); ?></td>
									</tr>
									<tr>
										<td class="first">PLATS:</td>
										<td class="last"><?php echo get_field('kal_plats_sv'); ?></td>
									</tr>
								</table>
								<?php 
									$excerpt = get_the_content();
									$excerpt = strip_tags($excerpt);    
								?>
								
									<?php 
										$field_date_start = get_field('kal_startdatum_sv');

										$long_start = array('januari', 'februari', 'mars', 'april', 'maj', 'juni', 'juli', 'augusti', 'september', 'oktober', 'november', 'december');
										$short_start = array('01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12');

										$translated_start = str_replace($long_start, $short_start, $field_date_start);
										
										$month_start = substr($translated_start, 3,2); 
										$day_start = substr($translated_start, 0,2); 
										$done_start = $month_start .'-'. $day_start;

										$field_date_end = get_field('kal_slutdatum_sv');

										$long_end = array('januari', 'februari', 'mars', 'april', 'maj', 'juni', 'juli', 'augusti', 'september', 'oktober', 'november', 'december');
										$short_end = array('01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12');

										$translated_end = str_replace($long_end, $short_end, $field_date_end);
										
										$month_end = substr($translated_end, 3,2); 
										$day_end = substr($translated_end, 0,2); 
										$done_end = $month_end .'-'. $day_end;
									?>

								<span class="addtocalendar">
							        <var class="atc_event">
							            <var class="atc_date_start"><?php echo get_field('kal_manad_ar_sv'); ?>-<?php echo $done_start; ?> <?php echo get_field('kal_tid_sv'); ?>:00</var>
							            <var class="atc_date_end"><?php echo get_field('kal_manad_ar_sv'); ?>-<?php echo $done_end; ?> <?php echo get_field('kal_tid_slut_sv'); ?>:00</var>
							            <var class="atc_timezone">Europe/Stockholm</var>
							            <var class="atc_title"><?php the_title(); ?></var>
							            <var class="atc_description"><?php echo substr($excerpt, 0, 500) . '...'; ?></var>
							            <var class="atc_location"><?php echo get_field('kal_plats_sv'); ?></var>
							        </var>
							    </span>

							    <span>

							    </span>

							</header>
						</div>
						<div class="post-content">
							<?php the_content(); ?>
						</div>
						<footer class="post-footer">
							<div class="post_settings">
								<div class="publish_change">
									<span>Publicerad: <?php echo get_the_date('j F Y'); ?>, kl. <?php the_time('H:i') ?></span>  
								</div>
								<div class="share_post">
									<!-- FACEBOOK -->
									<a class="share facebook" onclick="window.open('http://www.facebook.com/sharer/sharer.php?u=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>', 'shareFacebook', 'width=650, height=270, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
										<i class="fa fa-facebook"></i> <span>Dela på Facebook</span>
									</a>
									<!-- FACEBOOK END -->

									<!-- LINKEDIN -->
										<a class="share linkedin" onclick="window.open('http://www.linkedin.com/shareArticle?mini=true&url=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>&source=[SOURCE/DOMAIN]', 'shareLinkedIn', 'width=610, height=480, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
											<i class="fa fa-linkedin-square"></i> <span>Dela på LinkedIn</span>
										</a>
									<!-- LINKEIND END -->


									<!-- MAIL -->
									<a class="share mail" href="mailto:?subject=<?php the_title(); ?>&body=Hej, jag tror att du skulle gilla detta. Kolla in detta <?php echo get_permalink(); ?>">
										<i class="fa fa-envelope" aria-hidden="true"></i> <span>Skicka som e-post</span>
									</a>
									<!-- MAIL END -->
								</div>
							</div>
						</footer>
					</article>
				<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
					<article <?php post_class('single-post-kalender'); ?>>
						<h1 class="entry-title r_toggle"><?php the_title(); ?></h1>
						<div class="kalender_date">
							<span id="start_date" class="start_date"><?php echo get_field('kal_startdatum_en'); ?></span>
							<span class="start_year"><?php echo get_field('kal_manad_ar_en'); ?></span>
						</div>
						<div class="kalender_info">
							<header>
								<table>
									<tr>
										<td class="first">DATE:</td>
										<?php if(get_field('kal_startdatum_en') ==  get_field('kal_slutdatum_en')) { ?>
											<td class="last"><?php echo get_field('kal_startdatum_en'); ?> <span>- <?php echo get_field('kal_slutdatum_en'); ?></span> <?php echo get_field('kal_manad_ar_en'); ?></td>
										<?php } else { ?>
											<td class="last"><?php echo get_field('kal_startdatum_en'); ?> - <?php echo get_field('kal_slutdatum_en'); ?> <?php echo get_field('kal_manad_ar_en'); ?></td>
										<?php } ?>
									</tr>
									<tr>
										<td class="first">TIME:</td>
										<td class="last">Kl. <?php echo get_field('kal_tid_en'); ?> - <?php echo get_field('kal_tid_slut_en'); ?></td>
									</tr>
									<tr>
										<td class="first">LOCATION:</td>
										<td class="last"><?php echo get_field('kal_plats_en'); ?></td>
									</tr>
								</table>
								<?php 
									$excerpt = get_the_content();
									$excerpt = strip_tags($excerpt);    
								?>
								
								<?php 
									$field_date_start = get_field('kal_startdatum_en');
									$long_start = array('January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December');
									$short_start = array('01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12');

									$translated_start = str_replace($long_start, $short_start, $field_date_start);

									$month_start = substr($translated_start, 3,2); 
									$day_start = substr($translated_start, 0,2); 
									$done_start = $month_start .'-'. $day_start;

									$field_date_end = get_field('kal_slutdatum_en');

									$long_end = array('January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December');
									$short_end = array('01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12');

									$translated_end = str_replace($long_end, $short_end, $field_date_end);
									
									$month_end = substr($translated_end, 3,2); 
									$day_end = substr($translated_end, 0,2); 
									$done_end = $month_end .'-'. $day_end;
								?>

								<span class="addtocalendar">
							        <var class="atc_event">
							            <var class="atc_date_start"><?php echo get_field('kal_manad_ar_en'); ?>-<?php echo $done_start; ?> <?php echo get_field('kal_tid_en'); ?>:00</var>
							            <var class="atc_date_end"><?php echo get_field('kal_manad_ar_en'); ?>-<?php echo $done_end; ?> <?php echo get_field('kal_tid_slut_en'); ?>:00</var>
							            <var class="atc_timezone">Europe/Stockholm</var>
							            <var class="atc_title"><?php the_title(); ?></var>
							            <var class="atc_description"><?php echo substr($excerpt, 0, 500) . '...'; ?></var>
							            <var class="atc_location"><?php echo get_field('kal_plats_en'); ?></var>
							        </var>
							    </span>
							</header>
						</div>
						<div class="post-content">
							<?php the_content(); ?>
						</div>
						<footer class="post-footer">
							<div class="post_settings">
			
								<div class="publish_change">
									<span>Published: <?php echo get_the_date('j F Y'); ?>, kl. <?php the_time('H:i') ?></span>  
								<div class="share_post">
									<!-- FACEBOOK -->
									<a class="share facebook" onclick="window.open('http://www.facebook.com/sharer/sharer.php?u=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>', 'shareFacebook', 'width=650, height=270, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
										<i class="fa fa-facebook"></i> <span>Share on Facebook</span>
									</a>
									<!-- FACEBOOK END -->

									<!-- LINKEDIN -->
										<a class="share linkedin" onclick="window.open('http://www.linkedin.com/shareArticle?mini=true&url=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>&source=[SOURCE/DOMAIN]', 'shareLinkedIn', 'width=610, height=480, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
											<i class="fa fa-linkedin-square"></i> <span>Share on LinkedIn</span>
										</a>
									<!-- LINKEIND END -->


									<!-- MAIL -->
									<a class="share mail" href="mailto:?subject=<?php the_title(); ?>&body=Hej, jag tror att du skulle gilla detta. Kolla in detta <?php echo get_permalink(); ?>">
										<i class="fa fa-envelope" aria-hidden="true"></i> <span>Send as email</span>
									</a>
									<!-- MAIL END -->
								</div>
							</div>
						</footer>
					</article>
				<?php endif;?>
			
		<?php endwhile; ?>
		</div>

		<div class="sidebar">
			<?php get_template_part('templates/sidebar', 'page'); ?>
		</div>
	</div>
</div>