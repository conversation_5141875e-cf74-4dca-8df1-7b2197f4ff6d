<div id="page-header-parent">
	<div class="title">
		<div class="wrapper">
			<span class="title-page"><?= __('Certifikat &amp; Godkännanden', 'certificates'); ?></span>
		</div>
	</div>

	<div class="wrapper">
		<?php 
		$home_link = get_site_url();
		$services_link = get_site_url() . '/tjanster/';
		$legal_metrology_link = get_site_url() . '/tjanster/reglerad-matteknik/';
		$certificates_link = get_site_url() . '/tjanster/reglerad-matteknik/certifikat-och-godkannanden/';
		if (ICL_LANGUAGE_CODE == 'en') {
			$home_link = get_site_url() . '?lang=en';
			$services_link = get_site_url() . '/services/?lang=en';
			$legal_metrology_link = get_site_url() . '/services/measurement-technology/?lang=en';
			$certificates_link = get_site_url() . '/services/measurement-technology/certifications-and-approvals/?lang=en';
		}
		?>
		<nav aria-label="<?= __('Brödsmulor', 'breadcrumbs'); ?>" id="breadcrumbs">
			<span xmlns:v="http://rdf.data-vocabulary.org/#"><span typeof="v:Breadcrumb"><a href="<?php echo $home_link; ?>" title="<?php esc_attr_e('Gå till Hem', 'certificates'); ?>" rel="v:url" property="v:title"><?= __('Hem', 'breadcrumbs'); ?></a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="<?php echo $services_link; ?>" title="<?php esc_attr_e('Gå till Tjänster', 'certificates'); ?>" rel="v:url" property="v:title"><?= __('Tjänster', 'breadcrumbs'); ?></a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="<?php echo $legal_metrology_link; ?>" title="<?php esc_attr_e('Gå till Reglerad mätteknik', 'certificates'); ?>" rel="v:url" property="v:title"><?= __('Reglerad mätteknik', 'breadcrumbs'); ?></a> /<span rel="v:child" typeof="v:Breadcrumb"><a href="<?php echo $certificates_link; ?>" title="<?php esc_attr_e('Gå till Certifikat och godkännanden', 'certificates'); ?>"> <?= __('Certifikat och Godkännanden', 'certificates'); ?></a> / <span class="breadcrumb_last"><?php the_title(); ?></span></span></span>
		</nav>
	</div>
</div>

<div id="single_section_post_dokument">
	<div class="main">
		<?php while (have_posts()) : the_post(); ?>
			<article <?php post_class('single-post-dokument'); ?>>
				<header class="post-header">
					<div class="wrapper">
						<?php if (isset($_GET['success_subscribing'])) { ?>
							<div class="header_message">
								<div class="wrapper">
									<div class="inner success">
										<span><?= __('Du bevakar nu', 'certificates') ?> <b><?php echo lcfirst(get_the_title()); ?></b>. <?= __('När någonting ändras här kommer du få ett mail.', 'certificates') ?></span>
									</div>
								</div>
							</div>
						<?php } ?>

						<?php if (isset($_GET['failed_subscribing'])) { ?>
							<div class="header_message">
								<div class="wrapper">
									<div class="inner failed">
										<span><?= __('Du bevakar redan detta certifikatet.', 'certificates') ?></span>
									</div>
								</div>
							</div>
						<?php } ?>

						<h1><?php echo get_field('crm_cert_dokumentbeteckning'); ?> - <?php the_title(); ?> - <?= get_field('crm_cert_instrument') ?></h1>

						<div class="dokument_section">
							<?php
							$url = get_field('crm_cert_doc');
							if ($url) {
								$parts = explode('/', $url);
								$value = $parts[count($parts) - 1];

								$search  = array('-', '_', '.pdf');
								$replace = array(' ', ':', '');
								$valueFormated = str_replace($search, $replace, $value);
							?>

								<ul>
									<li>
										<div class="left">
											<img data-src="<?php echo get_template_directory_uri(); ?>/assets/images/icon-document-large.png" alt="Dokument" />
										</div>
										<div class="right">
											<span class="file">
												<h3>
													<a href="<?php echo $url; ?>" title="" target="_blank"><?= __('Ladda ner dokument', 'certificates') ?></a>
												</h3>
											</span>
											<span class="file_name"><?php the_title(); ?> - <?php echo $valueFormated; ?></span>
											<span class="file_type_size"><?= __('PDF', 'certificates') ?></span>
										</div>
									</li>
								</ul>
							<?php } ?>
						</div>

					</div>
				</header>
				<div class="wrapper">
					<div class="post-content">
						<h3><?=__('Information','certificates')?></h3>
						<div class="allman_info">
							<table>
								<tbody>
									<?php if (get_field('crm_cert_issue_date')) { ?>
										<tr class="doc_description">
											<td class="a"><strong><?= __('Utfärdandedatum', 'certificates') ?></strong>:</td>
											<td class="b"><?= __(get_field('crm_cert_issue_date'), 'certificates'); ?></a></td>
										</tr>
									<?php } ?>
									<?php if (get_field('crm_cert_end_date')) { ?>
										<tr class="doc_description">
											<td class="a"><strong><?= __('Utgångsdatum', 'certificates') ?></strong>:</td>
											<td class="b"><?= __(get_field('crm_cert_end_date'), 'certificates'); ?></a></td>
										</tr>
									<?php } ?>
									<?php if (get_field('crm_cert_instrument')) { ?>
										<tr class="doc_description">
											<td class="a"><strong><?= __('Mätinstument', 'certificates') ?></strong>:</td>
											<td class="b"><?= __(get_field('crm_cert_instrument'), 'certificates'); ?></a></td>
										</tr>
									<?php } ?>
									<?php if (get_field('crm_cert_issue_to_company')) { ?>
										<tr class="doc_description">
											<td class="a"><strong><?= __('Utfärdat till företag', 'certificates') ?></strong>:</td>
											<td class="b"><?= __(get_field('crm_cert_issue_to_company'), 'certificates'); ?></a></td>
										</tr>
									<?php } ?>
									<?php if (get_field('crm_cert_description')) { ?>
										<tr class="doc_description">
											<td class="b"><?= __(get_field('crm_cert_description'), 'certificates'); ?></a></td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
						</div>
					</div>
					<div class="old_certificates">
						<h3><?=__('Utgångna certifikat','certificates')?></h3>
						<div class="table_containetr">
							<table>
								<thead>
									<tr>
										<th class="tg-0pky"><?=__('Certifikat','certificates')?>:</th>
										<th class="tg-0lax"><?=__('Utfärdandedatum','certificates')?>:</th>
										<th class="tg-0lax"><?=__('Utgångsdatum','certificates')?>:</th>
										<th class="tg-0lax"><?=__('Mätinstument','certificates')?>:</th>
										<th class="tg-0lax"><?=__('Utfärdat till företag','certificates')?>:</th>
									</tr>
								</thead>
								<tbody>
									<?php
										foreach(get_field('crm_cert_past_certifications') as $old_Cert){
											?>
											<tr>
												<td><a href="<?=$old_Cert['crm_cert_past_doc']?>"><?= __('Ladda ner','certificates')?> </a></td>
												<td><?=$old_Cert['crm_cert_past_issue_date']?></td>
												<td><?=$old_Cert['crm_cert_past_end_date']?></td>
												<td><?=$old_Cert['crm_cert_past_instrument']?></td>
												<td><?=$old_Cert['crm_cert_past_issue_to_company']?></td>
											</tr>
											<?php
										}
									?>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</article>
		<?php endwhile; ?>

		<div id="sok_certifikat">

			<div class="wrapper">
				<div class="title">
					<img alt="<?= __('Sök', 'certificates'); ?>" data-src="<?php echo get_template_directory_uri(); ?>/assets/images/icon-search.png">
					<span>
						<h2><?= __('Sök Certifikat och Godkännanden', 'certificates')?></h2>
					</span>
				</div>
				<form id="searchform_certifikat" action="<?= get_site_url();?>" method="get">
					<div class="row">
						<input type="search" class="search-field" placeholder="<?= __('Fritext', 'certificates'); ?>" value="" name="fritext" title="<?= __('Fritext', 'certificates')?>">
					</div>
					<div class="row">
						<input type="text" class="search-field" placeholder="<?= __('Certifikatnummer', 'certificates'); ?>" name="s" title="<? __('Certifikatnummer', 'certificates')?>">
					</div>
					<div class="row">
						<?php $all_omraden = array(
							'post_type' 		=> 'amnesomraden',
							'posts_per_page' 	=> -1,
							'orderby'			=> 'title',
							'order' 			=> 'ASC',
							'tax_query'         => array(
								array(
									'taxonomy' 	=> 'amnesomraden_categories',
									'field' 	=> 'slug',
									'terms' 	=> 'reglerad-matteknik'
								)
							)
						);

						$query_all = new WP_Query($all_omraden);
						if ($query_all->have_posts()) : ?>
							<label class="label_select" for="all_omraden_select"><?= __('Område', 'certificates') ?></label>
							<select id="all_omraden_select" name="omraden">
								<option selected disabled><?= __('Välj område', 'certificates')?></option>
								<?php while ($query_all->have_posts()) : $query_all->the_post(); ?>
									<option value="<?php the_ID(); ?>"><?php the_title(); ?></option>
								<?php endwhile; ?>
							</select>
						<?php endif;
						wp_reset_query(); ?>


					</div>
					<div class="row">
						<label class="label_select" for="typ_av_certifikat"><?= __('Certifikattyp', 'certificates'); ?></label>
						<select id="typ_av_certifikat" name="certifikat_type">
							<option value="" selected><?= __('Alla typer av certifikat', 'certificates'); ?></option>
							<option value="cottt"><?= __('Certifikat om tillsatsanordningar till taxametrar', 'certificates'); ?></option>
							<option value="cumtawg"><?= __('Certifikat utfärdade med stöd av WELMEC Guide 8.8', 'certificates'); ?></option>
							<option value="eoemk"><?= __('EU-typintyg och EU-intyg om konstruktionskontroll (MID 2014/32/EU)', 'certificates'); ?></option>
							<option value="gakmd"><?= __('Godkännanden av kvalitetssystem, modul D', 'certificates'); ?></option>
						</select>
					</div>
					<input name="site_section" type="hidden" value="certificate_pt" />
					<input type="hidden" name="lang" value="<?php echo ICL_LANGUAGE_CODE; ?>">
					<input type="hidden" name="post_type" value="certifikat" />
					<input type="submit" class="search-submit" value="<?= __('Sök', 'certificates'); ?>">
				</form>
			</div>
		</div>

		<div class="wrapper">
			<div class="senaste_dokument">
				<h3><?= __('Senaste publicerade dokument', 'certificates'); ?></h3>
				<?php
				$args = array(
					'post_status' 		=> 'publish',
					'post_type' 		=> 'certifikat',
					'paged' 			=> $paged,
					'posts_per_page' 	=> 10,
					'orderby' 			=> 'date',
					'meta_query' => array(
						array(
							'key' => 'crm_cert_doc',
							'value' => '.pdf',
							'compare' => 'LIKE'
						)
					)
				);

				$query = new WP_Query($args);
				if ($query->have_posts()) : ?>
					<div class="table_wrapper">
						<table>
							<thead>
								<tr>
									<th><?=  __('DOK.NR', 'certificates') ?></th>
									<th><?=  __('RUBRIK', 'certificates') ?></th>
									<th><?=  __('TYP AV DOKUMENT', 'certificates') ?></th>
									<th><?=  __('DATUM', 'certificates') ?></th>
								</tr>
							</thead>
							<tbody>
								<?php while ($query->have_posts()) : $query->the_post(); ?>
									<tr>
										<td><?php echo get_field('crm_cert_dokumentbeteckning'); ?></td>
										<td><a href="<?php the_permalink(); ?>" title="<?php the_title(); ?>"><?php the_title(); ?></a></td>
										<td><?php echo get_field('crm_cert_type'); ?></td>
										<td><?php the_time('Y-m-d'); ?></td>
									</tr>
								<?php endwhile; ?>
							</tbody>
						</table>
					</div>
				<?php endif;
				wp_reset_postdata(); ?>
			</div>
		</div>
	</div>
</div>