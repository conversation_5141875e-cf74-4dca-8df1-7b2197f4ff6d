<div class="page-content">

	<?php swedac_parse_the_content( get_field ( 'content_group' ) ); ?>
	

	<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
		<?php if(get_field('snabbval_subpage_sv')): ?>
			<div id="page_snabbval">
			<ul>
				<?php while(has_sub_field('snabbval_subpage_sv')): ?>
					<li>
						<a href="<?php echo get_sub_field('lankmal'); ?>" title="<?php echo get_sub_field('rubrik'); ?>">
							<div class="rubrik">
								<h2 class="title-cuter"><?php echo get_sub_field('rubrik'); ?></h2>
							</div>
						</a>
					</li>
				<?php endwhile; ?>
			</ul>
			</div>
		<?php endif; ?>

		<?php $posts = get_field('kontaktperson_relation');
		if( $posts ): ?>
			<div class="kontaktperson_section">
				<h2>Kontaktperson</h2>
			    <ul>
			    <?php foreach( $posts as $post): // variable must be called $post (IMPORTANT) ?>
			        <?php setup_postdata($post); $kontaktperson = get_field('kontaktperson_profilbild'); ?>
			        <li>

						<?php if ($kontaktperson) { ?>
							<div class="left">
								<img data-src="<?php echo $kontaktperson['sizes']['medium']; ?>" alt="<?php the_title(); ?>" />
							</div>
							<div class="right">
								<span class="namn">
									<?php echo get_field('kontaktperson_namn'); ?>, <?php echo get_field('kontaktperson_roll'); ?>
								</span>
								<span class="epost">
									<a href="mailto:<?php echo antispambot(get_field('kontaktperson_epost')); ?>">Skicka e-post</a>
								</span>
								<?php if(get_field('kontaktperson_telefonnummer')) { ?>
									<span class="telefon">
										Telefon: <?php echo get_field('kontaktperson_telefonnummer'); ?>
									</span>
								<?php } ?>
							</div>
						<?php } else { ?>
							<div class="right">
								<span class="namn">
									<?php if(get_field('kontaktperson_roll')) {?>

										<?php echo get_field('kontaktperson_namn'); ?>, <?php echo get_field('kontaktperson_roll'); ?>

									<?php } else { ?>

										<?php echo get_field('kontaktperson_namn'); ?>

									<?php } ?>
								</span>
								<?php if(get_field('kontaktperson_epost')) { ?>
									<span class="epost">
										<a href="mailto:<?php echo antispambot(get_field('kontaktperson_epost')); ?>">Skicka e-post</a>
									</span>
								<?php } ?>
								<?php if(get_field('kontaktperson_telefonnummer')) { ?>
									<span class="telefon">
										Telefon: <?php echo get_field('kontaktperson_telefonnummer'); ?>
									</span>
								<?php } ?>
							</div>
						<?php } ?>
			        </li>
			    <?php endforeach; ?>
			    </ul>
		    </div>
		    <?php wp_reset_postdata(); // IMPORTANT - reset the $post object so the rest of the page works correctly ?>
		<?php endif; ?>
		
			<?php // Hämta innehåll som är relaterat till den aktuella sidan men också skriva ut relatert information
				$postRelated = get_field('relaterad_information');
				if ($postRelated) { ?>
					<div class="relaterad_information_custom">
						<h2>Relaterat</h2>
						<ul>
							<?php foreach( $postRelated as $post): // variable must be called $post (IMPORTANT) ?>
								<?php setup_postdata($post); ?>
								<?php $postType = get_post_type_object(get_post_type());
								if ($postType) {
									$postIn = esc_html($postType->labels->singular_name);
									if($postIn == 'Inlägg') {
										$postIn = 'Nyhet';
									}
								} ?>

								<li>
									<a href="<?php the_permalink(); ?>"><?php the_title(); ?></a> <span>(<?php echo $postIn; ?><?php if($postIn == 'Nyhet') { echo ' - '.get_the_date( 'j F Y' );} ?>)</span>
								</li>
							<?php endforeach; ?>
						</ul>
					</div>
				<?php } else { ?>

				<?php } ?>
			<?php wp_reset_query();  ?>
	
	<?php endif;?>

</div>

<script type="text/javascript">
	function addMoreAttachment() {
		jQuery('.attachment-row:last')
			.clone()
			.insertAfter('.attachment-row:last');
		jQuery('.attachment-row:last').find('input').val('');
	}
</script>