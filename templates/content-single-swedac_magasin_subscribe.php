<div id="page-header-parent">
	<div class="title">
		<div class="wrapper">
			<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
				<span class="title-page">Swedacskolan</span>
			<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
				<span class="title-page">Swedac school</span>
			<?php endif;?>

		</div>
	</div>

	<?php
		$post_id = get_queried_object_id();
		if ( has_post_thumbnail( $post_id ) ) :
		    $image_array = wp_get_attachment_image_src( get_post_thumbnail_id( $post_id ), 'swedac_magasin_single' );
		    $image = $image_array[0];
		endif;
	?>

	<?php $swedacMagasinrektangulark = get_field('swedac_magasin_rektangular');
		$imageSwedacMagasin = $swedacMagasinrektangulark['sizes']['swedac_magasin_single']; ?>

	<div class="post_thumbnail">
		<?php if($imageSwedacMagasin) { ?>
			<div class="image" style="background-image: url(<?php echo $imageSwedacMagasin; ?>)"></div>
		<?php } else { ?>
			<div class="image" style="background-image: url(<?php echo $image; ?>)"></div>
		<?php } ?>
	</div>

</div>

<div class="wrapper">
	<div id="single_section_post_swedac_magasin">
		<div class="main">
			<?php while (have_posts()) : the_post(); ?>
				<article <?php post_class('single-post'); ?>>
					<header class="post-header">
						<h1><?php the_title(); ?></h1>
						<div class="ingress">
							<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
								<?php echo get_field('page_ingress_subpage_sv'); ?>
							<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
								<?php echo get_field('page_ingress_subpage_en'); ?>
							<?php endif;?>
						</div>
					</header>
					<div class="post-content">
						<?php the_content(); ?>
					</div>

					<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
						<?php // Hämta innehåll som är relaterat till den aktuella sidan men också skriva ut relatert information
						$inner_args = get_posts( array(
							'status'			=> 'publish',
							'post_type' 		=> array('news', 'page', 'swedac_magasin', 'amnesomraden'),
							'posts_per_page' 	=> 10,
							'orderby' 			=> 'date',
							'order' 			=> 'asc',
							'meta_query' => array(
								array(
									'key' => 'relaterad_information', // or whatever your custom post type field is called
									'value' => '"' . get_the_ID() . '"', // will work only if the main loop has started
									'compare' => 'LIKE'
								)
							)
						));
							$posts = get_field('relaterad_information');
							if(($inner_args) && ($posts) ) { ?>
								<div class="relaterad_information_custom">
									<h2>Relaterat</h2>
									<ul>
										<?php foreach($inner_args as $inner_arg): ?>
											<?php $postType = get_post_type_object(get_post_type($inner_arg->ID));
											if ($postType) {
												$postIn = esc_html($postType->labels->singular_name);
											} ?>
											<li>
												<a href="<?php echo get_the_permalink( $inner_arg->ID ); ?>"><?php echo get_the_title( $inner_arg->ID ); ?></a> <span>(<?php echo $postIn; ?>)</span>
											</li>
										<?php endforeach; ?>
										<?php foreach( $posts as $post): // variable must be called $post (IMPORTANT) ?>
											<?php setup_postdata($post); ?>
											<li>
												<a href="<?php the_permalink(); ?>"><?php the_title(); ?></a> <span>(<?php echo $postIn; ?>)</span>
											</li>
										<?php endforeach; ?>
									</ul>
								</div>
							<?php } elseif ($posts) { ?>
								<div class="relaterad_information_custom">
									<h2>Relaterat</h2>
									<ul>
										<?php foreach( $posts as $post): // variable must be called $post (IMPORTANT) ?>
											<?php setup_postdata($post); ?>
											<?php $postType = get_post_type_object(get_post_type());
											if ($postType) {
												$postIn = esc_html($postType->labels->singular_name);
											} ?>

											<li>
												<a href="<?php the_permalink(); ?>"><?php the_title(); ?></a> <span>(<?php echo $postIn; ?>)</span>
											</li>
										<?php endforeach; ?>
									</ul>
								</div>
							<?php } elseif ($inner_args) { ?>
								<div class="relaterad_information_custom">
									<h2>Relaterat</h2>
									<ul>
										<?php foreach($inner_args as $inner_arg): ?>
											<?php $postType = get_post_type_object(get_post_type($inner_arg->ID));
											if ($postType) {
												$postIn = esc_html($postType->labels->singular_name);
											} ?>
											<li>
												<a href="<?php echo get_the_permalink( $inner_arg->ID ); ?>"><?php echo get_the_title( $inner_arg->ID ); ?></a> <span>(<?php echo $postIn; ?>)</span>
											</li>
										<?php endforeach; ?>
									</ul>
								</div>
							<?php } else { ?>

							<?php } ?>
						<?php wp_reset_query();  ?>
					<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
						<?php // Hämta innehåll som är relaterat till den aktuella sidan men också skriva ut relatert information
						$inner_args = get_posts( array(
							'status'			=> 'publish',
							'post_type' 		=> array('news', 'page', 'swedac_magasin', 'amnesomraden'),
							'posts_per_page' 	=> 10,
							'orderby' 			=> 'date',
							'order' 			=> 'asc',
							'meta_query' => array(
								array(
									'key' => 'relaterad_information_en', // or whatever your custom post type field is called
									'value' => '"' . get_the_ID() . '"', // will work only if the main loop has started
									'compare' => 'LIKE'
								)
							)
						));
							$posts = get_field('relaterad_information_en');
							if(($inner_args) && ($posts) ) { ?>
								<div class="relaterad_information_custom">
									<h2>Related</h2>
									<ul>
										<?php foreach($inner_args as $inner_arg): ?>
											<?php $postType = get_post_type_object(get_post_type($inner_arg->ID));
											if ($postType) {
												$postIn = esc_html($postType->labels->singular_name);
											} ?>
											<li>
												<a href="<?php echo get_the_permalink( $inner_arg->ID ); ?>"><?php echo get_the_title( $inner_arg->ID ); ?></a> <span>(<?php echo $postIn; ?>)</span>
											</li>
										<?php endforeach; ?>
										<?php foreach( $posts as $post): // variable must be called $post (IMPORTANT) ?>
											<?php setup_postdata($post); ?>
											<li>
												<a href="<?php the_permalink(); ?>"><?php the_title(); ?></a> <span>(<?php echo $postIn; ?>)</span>
											</li>
										<?php endforeach; ?>
									</ul>
								</div>
							<?php } elseif ($posts) { ?>
								<div class="relaterad_information_custom">
									<h2>Related</h2>
									<ul>
										<?php foreach( $posts as $post): // variable must be called $post (IMPORTANT) ?>
											<?php setup_postdata($post); ?>
											<?php $postType = get_post_type_object(get_post_type());
											if ($postType) {
												$postIn = esc_html($postType->labels->singular_name);
											} ?>

											<li>
												<a href="<?php the_permalink(); ?>"><?php the_title(); ?></a> <span>(<?php echo $postIn; ?>)</span>
											</li>
										<?php endforeach; ?>
									</ul>
								</div>
							<?php } elseif ($inner_args) { ?>
								<div class="relaterad_information_custom">
									<h2>Related</h2>
									<ul>
										<?php foreach($inner_args as $inner_arg): ?>
											<?php $postType = get_post_type_object(get_post_type($inner_arg->ID));
											if ($postType) {
												$postIn = esc_html($postType->labels->singular_name);
											} ?>
											<li>
												<a href="<?php echo get_the_permalink( $inner_arg->ID ); ?>"><?php echo get_the_title( $inner_arg->ID ); ?></a> <span>(<?php echo $postIn; ?>)</span>
											</li>
										<?php endforeach; ?>
									</ul>
								</div>
							<?php } else { ?>

							<?php } ?>
						<?php wp_reset_query();  ?>
					<?php endif;?>

					<footer class="post-footer">
						<div class="post_settings">
							<?php if(ICL_LANGUAGE_CODE=='sv'): ?>

								<div class="share_post">
									<!-- FACEBOOK -->
									<a class="share facebook" onclick="window.open('http://www.facebook.com/sharer/sharer.php?u=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>', 'shareFacebook', 'width=650, height=270, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
										<i class="fa fa-facebook"></i> <span>Dela på Facebook</span>
									</a>
									<!-- FACEBOOK END -->

									<!-- LINKEDIN -->
										<a class="share linkedin" onclick="window.open('http://www.linkedin.com/shareArticle?mini=true&url=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>&source=[SOURCE/DOMAIN]', 'shareLinkedIn', 'width=610, height=480, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
											<i class="fa fa-linkedin-square"></i> <span>Dela på LinkedIn</span>
										</a>
									<!-- LINKEIND END -->


									<!-- MAIL -->
									<a class="share mail" href="mailto:?subject=<?php the_title(); ?>&body=Hej, jag tror att du skulle gilla detta. Kolla in detta <?php echo get_permalink(); ?>">
										<i class="fa fa-envelope" aria-hidden="true"></i> <span>Skicka som e-post</span>
									</a>
									<!-- MAIL END -->
								</div>
							<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>

								<div class="share_post">
									<!-- FACEBOOK -->
									<a class="share facebook" onclick="window.open('http://www.facebook.com/sharer/sharer.php?u=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>', 'shareFacebook', 'width=650, height=270, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
										<i class="fa fa-facebook"></i> <span>Share on Facebook</span>
									</a>
									<!-- FACEBOOK END -->

									<!-- LINKEDIN -->
										<a class="share linkedin" onclick="window.open('http://www.linkedin.com/shareArticle?mini=true&url=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>&source=[SOURCE/DOMAIN]', 'shareLinkedIn', 'width=610, height=480, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
											<i class="fa fa-linkedin-square"></i> <span>Share on LinkedIn</span>
										</a>
									<!-- LINKEIND END -->


									<!-- MAIL -->
									<a class="share mail" href="mailto:?subject=<?php the_title(); ?>&body=Hej, jag tror att du skulle gilla detta. Kolla in detta <?php echo get_permalink(); ?>">
										<i class="fa fa-envelope" aria-hidden="true"></i> <span>Send as email</span>
									</a>
									<!-- MAIL END -->
								</div>
							<?php endif;?>
						</div>
					</footer>
				</article>

			<?php endwhile; ?>


			<!-- //Hämta senaste från Swedac Magasin -->
			<div class="latest_swedac_magasin">
				<?php $args = array('post_type' => 'swedac_magasin', 'posts_per_page' => 5, 'orderby' => 'date');
				$query = new WP_Query($args);
				if($query->have_posts()): ?>
				    <ul>
						<?php while($query->have_posts()):$query->the_post(); ?>
							<?php $get_post_image = wp_get_attachment_image_src( get_post_thumbnail_id($post->ID), 'swedac_magasin' );
							$swedac_magasin = $get_post_image['0']; ?>
							<?php $swedacMagasinKvadratisk = get_field('swedac_magasin_kvadratisk');
								$imageSwedacMagasin = $swedacMagasinKvadratisk['sizes']['swedac_magasin']; ?>
						 	<li>
						 		<div class="content">
						 			<a href="<?php the_permalink(); ?>">
							 			<div class="cover"></div>
										<div class="post_image">
											<?php if($imageSwedacMagasin) { ?>
												<img data-src="<?php echo $imageSwedacMagasin; ?>" alt="<?php the_title(); ?>" />
											<?php } else { ?>
												<img data-src="<?php echo $swedac_magasin; ?>" alt="<?php the_title(); ?>" />
											<?php } ?>

										</div>
										<div class="info">
											<h2><?php the_title(); ?></h2>
											<div class="excerpt">
												<?php echo substr( get_the_excerpt(), 0, strrpos( substr( get_the_excerpt(), 0, 50), ' ' ) ); ?>
											</div>
										</div>
									</a>
						 		</div>
						 	</li>
						<?php endwhile; ?>
				    </ul>
				<?php endif; wp_reset_postdata(); ?>
			</div>
			<div id="swedac_magasin_latest">
				<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
					<div class="left">
						<div class="omslag">
							<?php $omslag = get_field('swedac_magasin_omslag', 988); ?>
							<img alt="<?php echo $omslag['alt']; ?>" data-src="<?php echo $omslag['sizes']['medium']; ?>" />
						</div>
					</div>
					<div class="right">
						<div class="nuvarande_info">
							<h2><?php echo get_field('swedac_magasin_rurbik', 988); ?></h2>
							<span><?php echo get_field('swedac_magasin_beskrivning', 988); ?></span>
							<ul>
								<li><a href="<?php echo get_field('swedac_magasin_oppna_e-tidning_url', 988); ?>" target="_blank"><?php echo get_field('swedac_magasin_oppna_e-tidning_rubrik', 988); ?></a></li>
								<li><a href="<?php echo get_field('swedac_magasin_tidningsarkiv_url', 988); ?>" target="_blank"><?php echo get_field('swedac_magasin_tidningsarkiv_rubrik', 988); ?></a></li>
							</ul>
						</div>

						<div class="prenumerera_magasin">
							<h2><?php echo get_field('swedac_magasin_rubrik_prenumerera', 988); ?></h2>
							<span><?php echo get_field('swedac_magasin_beskrivning_prenumerera', 988); ?></span>

							<div class="prenumerera_magasin_form">
								<form id="prenumerera_magasin_form" class="form clear" name="prenumerera_magasin_form" method="post" action="/sender.php?prenumerera_magasin" novalidate="novalidate" autocomplete="false">
									<div class="top">
										<div class="row namn">
											<input type="text" class="inputbox req" name="namn" id="namn" placeholder="Namn *" >
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>
										<div class="row postadress">
											<input type="text" class="inputbox req" name="postadress" id="postadress" placeholder="Postadress *" >
											<i class="fa fa-check"></i>
											<i class="fa fa-times"></i>
										</div>
										<div class="send">
											<input class="submit" type="submit" value="Skicka">
										</div>
										<span>* Obligatoriska fält</span>
									</div>
									<input type="hidden" name="url" value="<?php echo strtok($_SERVER["REQUEST_URI"],'?');?>"/>
								</form>
							</div>

						</div>
					</div>
				<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
					<div class="left">
						<div class="omslag">
							<?php $omslag = get_field('swedac_magasin_omslag_en', 3487); ?>
							<img data-src="<?php echo $omslag['sizes']['medium']; ?>" />
						</div>
					</div>
					<div class="right">
						<div class="nuvarande_info">
							<h2><?php echo get_field('swedac_magasin_rurbik_en', 3487); ?></h2>
							<span><?php echo get_field('swedac_magasin_beskrivning_en', 3487); ?></span>
							<ul>
								<li><a href="<?php echo get_field('swedac_magasin_oppna_e-tidning_url_en', 3487); ?>" target="_blank"><?php echo get_field('swedac_magasin_oppna_e-tidning_rubrik_en', 3487); ?></a></li>
								<li><a href="<?php echo get_field('swedac_magasin_tidningsarkiv_url_en', 3487); ?>" target="_blank"><?php echo get_field('swedac_magasin_tidningsarkiv_rubrik_en', 3487); ?></a></li>
							</ul>
						</div>

						<div class="prenumerera_magasin">
							<h2><?php echo get_field('swedac_magasin_rubrik_prenumerera_en', 3487); ?></h2>
							<span><?php echo get_field('swedac_magasin_beskrivning_prenumerera_en', 3487); ?></span>
							<ul>
								<li><a href="<?php echo get_field('swedac_magasin_starta_prenumeration_en', 3487); ?>" target="_blank">Subscribe now</a></li>
							</ul>
						</div>
					</div>
				<?php endif;?>
			</div>
		</div>
	</div>
</div>
