<?php
global $wp;

$headerAriaLabel = 'Sidhuvud';
$scShortCutText = 'Hop<PERSON> till innehållet';

if (ICL_LANGUAGE_CODE == 'en') {
	$headerAriaLabel = 'Header';
	$scShortCutText = 'Skip to content';
}
?>

<a class="screen-reader-shortcut" href="<?php echo home_url($wp->request . '#header'); ?>"><?php echo $scShortCutText; ?></a>
<div class="mobile-menu">
	<div class="logotype">
		<a href="<?php echo home_url(); ?>">
			<img data-src="<?php echo get_template_directory_uri(); ?>/assets/images/swedac-logotype.svg" alt="<?php echo (ICL_LANGUAGE_CODE == 'en') ? 'Logo for' : ((ICL_LANGUAGE_CODE == 'sv') ? 'Logotyp för' : ''); ?> <?php bloginfo('name'); ?>" class="logo-img">
		</a>
	</div>
	<div class="menu-toggle">
		<button class="open" aria-label="open Menu">
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="5 5 14 14" width="32" height="32">
				<path fill="none" d="M0 0h24v24H0z"/>
				<path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z"/>
			</svg>
		</button>
		<button class="close hidden" aria-label="Close Menu">
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="5 5 14 14" width="32" height="32">
    			<path fill="none" d="M0 0h24v24H0z"/>
    			<path d="M18.3 5.71a.996.996 0 0 0-1.41 0L12 10.59 7.11 5.7A.996.996 0 1 0 5.7 7.11L10.59 12 5.7 16.89a.996.996 0 1 0 1.41 1.41L12 13.41l4.89 4.89a.996.996 0 1 0 1.41-1.41L13.41 12l4.89-4.89c.38-.38.38-1.02 0-1.4z"/>
  			</svg>
		</button>
	</div>

</div>
<div class="header-wrapper">
	<header id="header" class="clear" aria-label="<?php echo $headerAriaLabel; ?>">
		<?php if (isset($_GET['unsubscription'])) { ?>
			<div class="header_message_unsub">
				<div class="inner success">
	
					<?php if (ICL_LANGUAGE_CODE == 'sv') : ?>
						<span><strong>Bevakning borttagen</strong> - Du har tagit bort dig från bevakningen för ämnesområdet.</span>
					<?php elseif (ICL_LANGUAGE_CODE == 'en') : ?>
						<span><strong>You have been removed</strong> - You have removed you from monitoring of the area.</span>
					<?php endif; ?>
				</div>
			</div>
		<?php } ?>
	
		<?php if (isset($_GET['unsubscription_doc'])) { ?>
			<div class="header_message_unsub">
				<div class="inner success">
	
					<?php if (ICL_LANGUAGE_CODE == 'sv') : ?>
						<span><b>Bevakning borttagen</b> - Du har tagit bort dig från bevakningen för dokumentet.</span>
					<?php elseif (ICL_LANGUAGE_CODE == 'en') : ?>
						<span><b>You have been removed</b> - You have removed you from monitoring of the document.</span>
					<?php endif; ?>
				</div>
			</div>
		<?php } ?>
	
		<?php if (isset($_GET['success_prenumerera_magasin'])) { ?>
			<div class="header_message_unsub">
				<div class="inner success">
					<?php if (ICL_LANGUAGE_CODE == 'sv') : ?>
						<span><strong>Prenumeration mottagen</strong> - Du prenumererar nu på Swedac Magasin.</span>
					<?php elseif (ICL_LANGUAGE_CODE == 'en') : ?>
	
					<?php endif; ?>
				</div>
			</div>
		<?php } ?>
	
		<?php if (isset($_GET['failed_prenumerera_magasin'])) { ?>
			<div class="header_message_unsub">
				<div class="inner failed">
					<?php if (ICL_LANGUAGE_CODE == 'sv') : ?>
						<span><strong>Något gick snett</strong> - Försök en gång till eller kontakta oss direkt.</span>
					<?php elseif (ICL_LANGUAGE_CODE == 'en') : ?>
	
					<?php endif; ?>
				</div>
			</div>
		<?php } ?>
		<div class="menu-wrapper">
			<div id="top_content">
				<div class="wrapper">
		
					<div class="socialmedia">
						<?php
						if (ICL_LANGUAGE_CODE == 'sv') {
							$socialMedia ='header_socialmedia_sv';
							$page = 10;
						} else {
							$socialMedia ='header_socialmedia_en';
							$page = 2652;
						}
						?>
						<?php if (get_field($socialMedia, $page)) : ?>
							<ul>
								<?php while (has_sub_field($socialMedia, $page)) : ?>
									<li>
										<span><a href="<?php echo get_sub_field('lankmal'); ?>" target="_blank" aria-label="<?php echo get_sub_field('namn'); ?>"><?php echo get_sub_field('icon'); ?></a></span>
									</li>
								<?php endwhile; ?>
							</ul>
						<?php endif; ?>
					</div>
				
					<nav id="main_navigation" aria-label="<?php echo (ICL_LANGUAGE_CODE == 'en') ? 'Main navigation' : 'Huvudnavigation'; ?>">
						<?php wp_nav_menu(['theme_location' => 'mainNav', 'container' => '', 'menu_class' => 'nav']); ?>
						<?php wp_nav_menu(['theme_location' => 'topNav_right','menu_id' => 'mobile_top_navigation_right', 'container' => '', 'menu_class' => 'top_top_right_level']); ?>

					</nav>
					<div class="top_toolbox">
						<nav id="top_navigation_right" aria-label="<?php echo (ICL_LANGUAGE_CODE == 'en') ? 'Right page navigation' : 'Höger sidnavigation'; ?> ">
							<?php wp_nav_menu(['theme_location' => 'topNav_right', 'container' => '', 'menu_class' => 'top_top_right_level']); ?>
						</nav> 
					</div>
				</div>
			</div>
			<div class="wrapper">
		
				<div id="logotype">
					<a href="<?php echo home_url(); ?>">
						<img data-src="<?php echo get_template_directory_uri(); ?>/assets/images/swedac-logotype.svg" alt="<?php echo (ICL_LANGUAGE_CODE == 'en') ? 'Logo for' : ((ICL_LANGUAGE_CODE == 'sv') ? 'Logotyp för' : ''); ?> <?php bloginfo('name'); ?>" class="logo-img">
					</a>
				</div>
				<div class="search-bar">
					<form action="<?= home_url() ?>">
						<input type="search" name="s" placeholder="<?php echo (ICL_LANGUAGE_CODE == 'en') ? 'Search entire site...' : 'Sök på webbplatsen...'; ?>" id="search-input" aria-label="<?= __('Sök på Swedac.se') ?>">
						<?php if(ICL_LANGUAGE_CODE !== 'sv') : ?>
							<input type="hidden" name="lang" value="<?php echo esc_attr(ICL_LANGUAGE_CODE); ?>">
						<?php endif; ?>
						<button type="submit"><svg fill="#fff" height="20" viewBox="0 0 461.516 461.516" width="20"><path d="m185.746 371.332c41.251.001 81.322-13.762 113.866-39.11l122.778 122.778c9.172 8.858 23.787 8.604 32.645-.568 8.641-8.947 8.641-23.131 0-32.077l-122.778-122.778c62.899-80.968 48.252-197.595-32.716-260.494s-197.594-48.252-260.493 32.716-48.252 197.595 32.716 260.494c32.597 25.323 72.704 39.06 113.982 39.039zm-98.651-284.273c54.484-54.485 142.82-54.486 197.305-.002s54.486 142.82.002 197.305-142.82 54.486-197.305.002c-.001-.001-.001-.001-.002-.002-54.484-54.087-54.805-142.101-.718-196.585.239-.24.478-.479.718-.718z"></path></svg><?php echo (ICL_LANGUAGE_CODE == 'en') ? 'Search' : 'Sök'; ?></button>
						<input class="alla_sokkallor" type="hidden" name="alla_sokkallor" id="alla_sokkallor_quick" value="on" checked>
						<input class="search_alt" type="hidden" name="swedac_se" id="swedac_se_quick" value="on" checked>
						<input class="search_alt" type="hidden" name="foreskrifter_dokument" id="foreskrifter_dokument_quick" value="on" checked>
						<input class="search_alt" type="hidden" name="namnstampelregistret" id="namnstampelregistret_quick" value="on" checked>
						<input class="search_alt" type="hidden" name="ackrediterande_organ"a id="ackrediterande_organ_quick" value="on" checked>
						<input class="search_alt" type="hidden" name="certifikat_godkannanden" id="certifikat_godkannanden_quick" value="on" checked>
					</form>
				</div>
			</div>
		</div>

	</header>
</div>

<script>
	document.addEventListener("DOMContentLoaded", function () {
		var menuItems = document.querySelectorAll("#main_navigation .nav > li button");
		const lang = document.querySelector('html').getAttribute('lang');
		menuItems.forEach(function (item) {

			lang === "sv-SE" ? item.setAttribute("aria-label", "Växla synligheten på undermenyn") : item.setAttribute("aria-label", "Toggle submenu");
			
		});

		var subMenuItems = document.querySelectorAll("#main_navigation .sub-menu > li button");

		subMenuItems.forEach(function (item) {
			lang === "sv-SE" ? item.setAttribute("aria-label", "Växla synligheten på undermenyn") : item.setAttribute("aria-label", "Toggle submenu");
		});
	});
</script>