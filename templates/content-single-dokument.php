<div id="page-header-parent">
	<div class="title">
		<div class="wrapper">
			<span class="title-page">Lag &amp; Rätt</span>
		</div>
	</div>

	<div class="wrapper">
		<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
			<nav aria-label="Brödsmulor" id="breadcrumbs">
				<span xmlns:v="http://rdf.data-vocabulary.org/#"><span typeof="v:Breadcrumb"><a href="https://www.swedac.se" title="Gå till Hem" rel="v:url" property="v:title">Hem</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="https://www.swedac.se/lag-ratt/" title="Gå till Lag & Rätt" rel="v:url" property="v:title">Lag &amp; Rätt</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="https://www.swedac.se/lag-ratt/swedacs-foreskrifter/" title="Gå till Swedacs föreskrifter">Swedacs föreskrifter</a> / <span class="breadcrumb_last"><?php the_title(); ?></span></span></span></span></span>
			</nav>
		<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
			<nav aria-label="Breadcrumb" id="breadcrumbs">
				<span xmlns:v="http://rdf.data-vocabulary.org/#"><span typeof="v:Breadcrumb"><a href="https://www.swedac.se" title="Return to Home" rel="v:url" property="v:title">Home</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="https://www.swedac.se/law-order?lang=en" title="Return to Law & Order" rel="v:url" property="v:title">Law &amp; Order</a> / <span rel="v:child" typeof="v:Breadcrumb"><a href="https://www.swedac.se/law-order/swedacs-regulations/" title="Return to Swedac’s regulations">Swedac’s regulations</a> / <span class="breadcrumb_last"><?php the_title(); ?></span></span></span></span></span>
			</nav>
		<?php endif;?>
	</div>
</div>

<div id="single_section_post_dokument">
		<div class="main">
			<?php while (have_posts()) : the_post(); ?>
			<article <?php post_class('single-post-dokument'); ?>>
				<header class="post-header">
					<div class="wrapper">
						<?php if( isset($_GET['success_subscribing']) ) { ?>
							<div class="header_message">
								<div class="wrapper">
									<div class="inner success">
										<span>Du bevakar nu <b><?php echo lcfirst(get_the_title()); ?></b>. När någonting ändras här kommer du få ett mail.</span>
									</div>
								</div>
							</div>
						<?php } ?>

						<?php if( isset($_GET['failed_subscribing']) ) { ?>
							<div class="header_message">
								<div class="wrapper">
									<div class="inner failed">
										<span>Du bevakar redan denna föreskrift.</span>
									</div>
								</div>
							</div>
						<?php } ?>

						<h1><?php echo get_field('crm_doc_dokumentbeteckning'); ?> - <?php the_title(); ?></h1>

						<div class="dokument_section">
							<?php
								$url = get_field('crm_doc_doc');
								if($url) {
									$parts = explode('/', $url);
									$value = $parts[count($parts) - 1];

									$search  = array('-', '_', '.pdf');
									$replace = array(' ', ':', '');
									$valueFormated = str_replace($search, $replace, $value);
								?>

								<ul>
									<li>
										<div class="left">
											<img data-src="<?php echo get_template_directory_uri(); ?>/assets/images/icon-document-large.png" alt="Dokument" />
										</div>
										<div class="right">
											<span class="file">
												<h3>
													<a href="<?php echo $url; ?>" title="" target="_blank"><?= __('Ladda ner dokument', 'certificates'); ?></a>
												</h3>
											</span>
											<span class="file_name"><?php the_title(); ?> - <?php echo $valueFormated; ?></span>
											<span class="file_type_size">PDF</span>
										</div>
									</li>
								</ul>
							<?php } ?>
						</div>

					</div>
				</header>
				<div class="wrapper">
					<div class="post-content">
						<h3>Information</h3>
						<div class="allman_info">
							<table>
								<tbody>
									<?php if(get_field('crm_doc_description')) { ?>
										<tr class="doc_description">
											<td class="a"><strong>Beskrivning</strong>:</td>
											<td class="b"><?php echo get_field('crm_doc_description'); ?></a></td>
										</tr>
									<?php } ?>
									<?php if(get_field('crm_doc_originalver')) {

										$url = get_field('crm_doc_originalver');
										$parts = explode('/', $url);
										$value = $parts[count($parts) - 1];

										$search  = array('-', '_konsol', '_', '.pdf');
										$replace = array(' ', '', ':', '');
										$valueFormated = str_replace($search, $replace, $value); ?>
										<tr>
											<td class="a"><strong>Grundförfattning</strong>:</td>
											<td class="b"><a href="<?php echo get_field('crm_doc_originalver'); ?>" target="_blank"><?php echo $valueFormated; ?></a></td>
										</tr>
									<?php } ?>
									<?php if(get_field('crm_doc_amendmentver')): ?>
										<tr>
											<td class="a"><strong>Ändringsförfattningar</strong>:</td>
											<td class="b">
												<?php while(has_sub_field('crm_doc_amendmentver')): ?>

													<?php if(get_sub_field('forfattning')) {
														$url = get_sub_field('forfattning');
														$parts = explode('/', $url);
														$value = $parts[count($parts) - 1];
														$search  = array('-', '_', '.pdf');
														$replace = array(' ', ':', '');
														$valueFormated = str_replace($search, $replace, $value);

													} ?>
													<span>
														<?php $args = array(
															'status'			=> 'publish',
															'post_type' 		=> 'dokument',
															'posts_per_page' 	=> 1,
															'meta_key'			=> 'crm_doc_dokumentbeteckning',
															'meta_value'		=> $valueFormated,
														);
														$query = new WP_Query($args);
														if($query->have_posts()): ?>
													    	<?php while($query->have_posts()):$query->the_post(); ?>
																<a href="<?php the_permalink(); ?>" title="<?php the_title(); ?>">
																	<?php echo get_field('crm_doc_dokumentbeteckning'); ?>
																</a>
													    	<?php endwhile; ?>
														<?php endif;
														wp_reset_postdata(); ?>
													</span>
												<?php endwhile; ?>
											</td>
										</tr>
									<?php endif; ?>
									<?php if(get_field('crm_doc_authorization')) { ?>
										<tr>
											<td class="a"><strong>Bemyndigande</strong>:</td>
											<td class="b"><?php echo get_field('crm_doc_authorization'); ?></td>
										</tr>
									<?php } ?>
									<?php if(get_field('crm_doc_celexnumber')) { ?>
										<tr>
											<td class="a"><strong>Celexnummer</strong>:</td>
											<td class="b"><?php echo get_field('crm_doc_celexnumber'); ?></td>
										</tr>

									<?php } ?>

								</tbody>
							</table>
						</div>

						<div class="relateade_omraden">
							<?php if(get_field('crm_doc_area')):
								//$rows = get_field('crm_doc_area');
								$doc_area = get_field('crm_doc_area');
								$doc_area = array_map(function ($omrade){
									return $omrade['omradesnamn'];
								}, $doc_area);

								// $doc_area = array_reduce($doc_area, function($carry, $item) {
								// 	if(empty($carry)) {
								// 		$carry .= $item;
								// 	} else {
								// 		$carry .= ", $item";
								// 	}
								// 	return $carry;
								// });
							endif;
							$findMatchedArea = array(
								'post_type' 		=> array('amnesomraden'),
								'posts_per_page' 	=> -1,
								'post_status' 		=> 'publish',
								'orderby' 			=> 'date',
								'order'				=> 'ASC'

							);

							$areaFoundQuery = new WP_Query($findMatchedArea);
							if($doc_area) {
								if($areaFoundQuery->have_posts()): ?>
									<h3>Relaterade ämnesområden</h3>
									<ul class="suggestion_posts">
									    <?php while($areaFoundQuery->have_posts()):$areaFoundQuery->the_post(); ?>

									        <?php $area_title = get_the_title(); ?>
									        <?php if (in_array($area_title, $doc_area)) { ?>
									        	<li <?php post_class(); ?>>
										        	<a href="<?php the_permalink(); ?>" title="Se <?php the_title(); ?>">
										        		<?php the_title(); ?>
										        	</a>
										     	</li>
									        <?php } ?>

									    <?php endwhile; ?>
								  	</ul>
								<?php endif; wp_reset_postdata();
							} ?>

						</div>
					</div>
				</div>
			</article>
		<?php endwhile; ?>

			<div id="sok_foreskriver_dokument">

				<div class="wrapper">
						<div class="title">
							<img alt="Sök" data-src="<?php echo get_template_directory_uri(); ?>/assets/images/icon-search.png">

							<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
								<span><h2>Sök föreskrifter & dokument</h2></span>
							<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
								<span><h2>Search regulations and documents</h2></span>
							<?php endif;?>
						</div>
						<form id="searchform_foreskrifter_dokument"action="<?= get_site_url();?>" method="get">
							<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
								<div class="row">
									<input type="search" class="search-field" placeholder="Fritext" value="" name="fritext" title="Fritext">
								</div>
								<div class="row">
									<input type="text" class="search-field" placeholder="Dokumentnummer" name="s" title="Dokumentnummer">
								</div>
							<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
								<div class="row">
									<input type="search" class="search-field" placeholder="Text search" value="" name="fritext" title="Text search">
								</div>
								<div class="row">
									<input type="text" class="search-field" placeholder="Document number" name="s" title="Document number">
								</div>
							<?php endif;?>
							<div class="row">
								<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
									<?php $all_omraden = array(
										'post_type' 		=> 'amnesomraden',
										'posts_per_page' 	=> -1,
										'orderby'			=> 'title',
										'order' 			=> 'ASC'
									);

									$query_all = new WP_Query($all_omraden);
									if($query_all->have_posts()): ?>
										<label class="label_select" for="all_omraden_select">Område</label>
										<select id="all_omraden_select" name="omraden">
										    <option selected disabled>Välj område</option>
										    <?php while($query_all->have_posts()):$query_all->the_post(); ?>
										       <option value="<?php the_title(); ?>"><?php the_title(); ?></option>
										    <?php endwhile; ?>
									  	</select>
									<?php endif; wp_reset_query(); ?>
								<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
									<?php $all_omraden = array(
										'post_type' 		=> 'amnesomraden',
										'posts_per_page' 	=> -1,
										'orderby'			=> 'title',
										'order' 			=> 'ASC'
									);

									$query_all = new WP_Query($all_omraden);
									if($query_all->have_posts()): ?>
										<label class="label_select" for="all_omraden_select">Area</label>
											<select id="all_omraden_select" name="omraden">
											    <option selected disabled>Choose area</option>
											    <?php while($query_all->have_posts()):$query_all->the_post(); ?>
											       <option value="<?php the_title(); ?>"><?php the_title(); ?></option>
											    <?php endwhile; ?>
										  	</select>
									<?php else:  ?>
										<label class="label_select">
											<select id="all_omraden_select" name="omraden">
											    <option selected disabled>No areas exists</option>
										  	</select>
									  	</label>
									<?php endif; wp_reset_query(); ?>
								<?php endif;?>

							</div>
							<div class="row">
									<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
										<label class="label_select" for="typ_av_dokument">Dokumenttyp</label>
										<select id="typ_av_dokument" name="document_type">
											<option value="" selected>Alla typer av dokument</option>
											<option value="stafs">STAFS</option>
											<option value="doc">DOC</option>
											<option value="rep">REP</option>
											<option value="info">INFO</option>
										</select>
									<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
										<label class="label_select" for="typ_av_dokument">Type of document</label>
										<select id="typ_av_dokument" name="document_type">
											<option value="" selected>All types of documents</option>
											<option value="stafs">STAFS</option>
											<option value="doc">DOC</option>
											<option value="rep">REP</option>
											<option value="info">INFO</option>
										</select>
									<?php endif;?>
							</div>


							<input name="site_section" type="hidden" value="document_pt" />
							<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
								<input type="hidden" name="lang" value="sv">
								<input type="hidden" name="post_type" value="dokument" />
								<input type="submit" class="search-submit" value="Sök">
							<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
								<input type="hidden" name="lang" value="en">
								<input type="hidden" name="post_type" value="document" />
								<input type="submit" class="search-submit" value="Search">
							<?php endif;?>
						</form>

					</div>

			</div>

			<?php 
			/** 
			 * @link https://mekshq.com/passing-variables-via-get_template_part-wordpress/
			 */
			include(locate_template('templates/monitor-content-form.php', false, false));
			?>

			<div class="wrapper">
				<div class="senaste_dokument">
					<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
						<h3>Senaste publicerade dokument</h3>
						<?php
						$args = array(
							'post_status' 		=> 'publish',
							'post_type' 		=> 'dokument',
							'paged' 			=> $paged,
							'posts_per_page' 	=> 10,
							'orderby' 			=> 'date',
							'meta_query' => array (
							    array(
							        'key' => 'crm_doc_doc',
							        'value' => '.pdf',
							        'compare' => 'LIKE'
							    )
							)
						);

						$query = new WP_Query($args);
						if($query->have_posts()): ?>
							<div class="table_wrapper">
								<table>
									<thead>
										<tr>
											<th>DOK.NR</th>
											<th>RUBRIK</th>
											<th>TYP AV DOKUMENT</th>
											<th>DATUM</th>
										</tr>
									</thead>
									<tbody>
								    <?php while($query->have_posts()):$query->the_post(); ?>
							      		<tr>
											<td><?php echo get_field('crm_doc_dokumentbeteckning'); ?></td>
											<td><a href="<?php the_permalink(); ?>" title="<?php the_title(); ?>"><?php the_title(); ?></a></td>
											<td><?php echo get_field('crm_doc_type'); ?></td>
											<td><?php the_time('Y-m-d'); ?></td>
							      		</tr>
								    <?php endwhile; ?>
							  		</tbody>
							  	</table>
						  	</div>
						<?php endif; wp_reset_postdata(); ?>
					<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
						<h3>Latest published documents</h3>
							<?php $paged = get_query_var( 'paged' ) ? get_query_var( 'paged' ) : 1;
							$args = array(
								'post_status' 		=> 'publish',
								'post_type' 		=> 'document_eng',
								'paged' 			=> $paged,
								'posts_per_page' 	=> 10,
								'orderby' 			=> 'date',
								'meta_query' => array (
								    array(
								        'key' => 'crm_doc_doc_en',
								        'value' => '.pdf',
								        'compare' => 'LIKE'
								    )
								)
							);

							$query = new WP_Query($args);
							if($query->have_posts()): ?>
								<div class="table_wrapper">
									<table>
										<thead>
											<tr>
												<th>DOC.NR</th>
												<th>TITLE</th>
												<th>TYPE OF DOCUMENT</th>
												<th>DATE</th>
											</tr>
										</thead>
										<tbody>
										   	<?php while( $query->have_posts() ) : $query->the_post(); ?>
								          		<?php if(get_field('crm_doc_doc_en')) { ?>
					          			      		<tr>
					          							<td><?php echo get_field('crm_doc_dokumentbeteckning_en'); ?></td>
					          							<td><a href="<?php the_permalink(); ?>" title="<?php the_title(); ?>"><?php the_title(); ?></a></td>
					          							<td><?php echo get_field('crm_doc_type_en'); ?></td>
					          							<td><?php the_time('Y-m-d'); ?></td>
					          			      		</tr>
								          		<?php } ?>
										    <?php endwhile; ?>
									   </tbody>
									</table>
							  	</div>
							<?php endif; wp_reset_postdata(); ?>
					<?php endif;?>
				</div>
			</div>

		</div>
</div>
