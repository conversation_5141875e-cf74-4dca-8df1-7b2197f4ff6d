<div id="page-header-parent">
	<div class="title">
		<div class="wrapper">
			<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
				<span class="title-page">Nyheter</span>
			<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
				<span class="title-page">News</span>
			<?php endif;?>
		</div>
	</div>

	<?php cc_breadcrumb_renderer(true); ?>
</div>

<div class="wrapper">
	<div id="single_section_post">
		<div class="main">
			<?php while (have_posts()) : the_post(); ?>
			<article <?php post_class('single-post'); ?>>
				<header class="post-header">
					<h1><?php the_title(); ?></h1>

					<div class="ingress">
						<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
							<?php echo get_field('page_ingress_subpage_sv'); ?>
						<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
							<?php echo get_field('page_ingress_subpage_en'); ?>
						<?php endif;?>
					</div>

					<?php if ( has_post_thumbnail()) : ?>
						<?php 
						$get_post_image = wp_get_attachment_image_src( get_post_thumbnail_id($post->ID), 'large' );
						$image_url = $get_post_image['0'];
						$img_id = get_post_thumbnail_id(get_the_ID());
						$alt_text = get_post_meta($img_id , '_wp_attachment_image_alt', true); 
						$thumbnail_image = get_posts(array('p' => $img_id, 'post_type' => 'attachment'));
						$caption = ($thumbnail_image && isset($thumbnail_image[0])) ? $thumbnail_image[0]->post_excerpt : false;
						?>
						<?php if ($caption) : ?>
							<figure class="page_thumbnail">
								<img data-src="<?php echo $image_url; ?>" alt="<?php echo $alt_text; ?>" />
								<figcaption class="wp-caption-text"><?php echo $caption; ?></figcaption>
							</figure>
						<?php else : ?>
							<div class="page_thumbnail">
								<img data-src="<?php echo $image_url; ?>" alt="<?php echo $alt_text; ?>" />
							</div>
						<?php endif; ?>
					<?php endif; ?>
				</header>
				<div class="post-content">
					<?php the_content(); ?>
				</div>


				<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
					<?php // Hämta innehåll som är relaterat till den aktuella sidan men också skriva ut relatert information
						$postRelated = get_field('relaterad_information');
						if ($postRelated) { ?>
							<div class="relaterad_information_custom">
								<h2>Relaterat</h2>
								<ul>
									<?php foreach( $postRelated as $post): // variable must be called $post (IMPORTANT) ?>
										<?php setup_postdata($post); ?>
										<?php $postType = get_post_type_object(get_post_type());
										if ($postType) {
											$postIn = esc_html($postType->labels->singular_name);
											if($postIn == 'Inlägg') {
												$postIn = 'Nyhet';
											}
										} ?>

										<li>
											<a href="<?php the_permalink(); ?>"><?php the_title(); ?></a> <span>(<?php echo $postIn; ?><?php if($postIn == 'Nyhet') { echo ' - '.get_the_date( 'j F Y' );} ?>)</span>
										</li>
									<?php endforeach; ?>
								</ul>
							</div>
						<?php } else { ?>

						<?php } ?>
					<?php wp_reset_query();  ?>
				<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
					<?php // Hämta innehåll som är relaterat till den aktuella sidan men också skriva ut relatert information

						$postRelated = get_field('relaterad_information_en');
						if($postRelated) { ?>
							<div class="relaterad_information_custom">
								<h2>Related</h2>
								<ul>
									<?php foreach( $postRelated as $post): // variable must be called $post (IMPORTANT) ?>
										<?php setup_postdata($post); ?>
										<li>
											<a href="<?php the_permalink(); ?>"><?php the_title(); ?></a> <span>(<?php echo $postIn; ?><?php if($postIn == 'News') { echo ' - '.get_the_date( 'j F Y' );} ?>)</span>
										</li>
									<?php endforeach; ?>
								</ul>
							</div>
						<?php } else { ?>

						<?php } ?>
					<?php wp_reset_query();  ?>
				<?php endif;?>




				<footer class="post-footer">
					<div class="post_settings">
						<?php if(ICL_LANGUAGE_CODE=='sv'): ?>

							<?php $show_author = get_field('hide_author_sv'); ?>
							<?php if($show_author == 1) { ?>
								<div class="author">
									<span><a href="mailto:<?php echo antispambot(get_the_author_meta('user_email')); ?>" title="Skicka ett mail till <?php the_author_meta('display_name'); ?>"><?php the_author_meta('display_name'); ?></a></span>
								</div>
							<?php } else { ?>

							<?php } ?>

							<div class="publish_change">
								<span>Publicerad: <?php echo get_the_date('j F Y'); ?>, kl. <?php the_time('H:i') ?></span>
								<span>Senast uppdaterad: <?php the_modified_date('j F Y'); ?>, kl. <?php the_modified_time('H:i') ?></span>
							</div>
							<div class="share_post">
								<!-- FACEBOOK -->
								<a class="share facebook" onclick="window.open('http://www.facebook.com/sharer/sharer.php?u=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>', 'shareFacebook', 'width=650, height=270, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
									<i class="fa fa-facebook"></i> <span>Dela på Facebook</span>
								</a>
								<!-- FACEBOOK END -->

								<!-- LINKEDIN -->
									<a class="share linkedin" onclick="window.open('http://www.linkedin.com/shareArticle?mini=true&url=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>&source=[SOURCE/DOMAIN]', 'shareLinkedIn', 'width=610, height=480, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
										<i class="fa fa-linkedin-square"></i> <span>Dela på LinkedIn</span>
									</a>
								<!-- LINKEIND END -->


								<!-- MAIL -->
								<a class="share mail" href="mailto:?subject=<?php the_title(); ?>&body=Hej, jag tror att du skulle gilla detta. Kolla in detta <?php echo get_permalink(); ?>">
									<i class="fa fa-envelope" aria-hidden="true"></i> <span>Skicka som e-post</span>
								</a>
								<!-- MAIL END -->
							</div>
						<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
							<?php $show_author = get_field('hide_author_en'); ?>
							<?php if($show_author == 1) { ?>
								<div class="author">
									<span><a href="mailto:<?php echo antispambot(get_the_author_meta('user_email')); ?>" title="Send email to <?php the_author_meta('display_name'); ?>"><?php the_author_meta('display_name'); ?></a></span>
								</div>
							<?php } else { ?>

							<?php } ?>

							<div class="publish_change">
								<span>Published: <?php echo get_the_date('j F Y'); ?>, kl. <?php the_time('H:i') ?></span>
								<span>Last updated: <?php the_modified_date('j F Y'); ?>, kl. <?php the_modified_time('H:i') ?></span>
							</div>
							<div class="share_post">
								<!-- FACEBOOK -->
								<a class="share facebook" onclick="window.open('http://www.facebook.com/sharer/sharer.php?u=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>', 'shareFacebook', 'width=650, height=270, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
									<i class="fa fa-facebook"></i> <span>Share on Facebook</span>
								</a>
								<!-- FACEBOOK END -->

								<!-- LINKEDIN -->
									<a class="share linkedin" onclick="window.open('http://www.linkedin.com/shareArticle?mini=true&url=<?php print(urlencode(get_permalink())); ?>&title=<?php print(urlencode(the_title())); ?>&source=[SOURCE/DOMAIN]', 'shareLinkedIn', 'width=610, height=480, resizable=0, toolbar=0, menubar=0, status=0, location=0, scrollbars=0'); return false;">
										<i class="fa fa-linkedin-square"></i> <span>Share on LinkedIn</span>
									</a>
								<!-- LINKEIND END -->


								<!-- MAIL -->
								<a class="share mail" href="mailto:?subject=<?php the_title(); ?>&body=Hej, jag tror att du skulle gilla detta. Kolla in detta <?php echo get_permalink(); ?>">
									<i class="fa fa-envelope" aria-hidden="true"></i> <span>Send as email</span>
								</a>
								<!-- MAIL END -->
							</div>
						<?php endif;?>
					</div>
				</footer>
			</article>
		<?php endwhile; ?>
		</div>

		<div class="sidebar">
			<?php get_template_part('templates/sidebar', 'page'); ?>
		</div>
	</div>
</div>
