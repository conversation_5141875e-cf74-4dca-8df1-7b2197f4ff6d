<?php
    $servername = "localhost";
    $username = "swedacuser";
    $password = "e9ymypara";
    $dbname = "swedac_live";

    // Create connection
    $conn = new mysqli($servername, $username, $password, $dbname);
    // Check connection
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    $search_term = utf8_decode($search_term);
    $url = utf8_decode($url);

    $sql_get = "SELECT `term` FROM `wp_serach_log` WHERE `term` = '$search_term'";
    $result = $conn->query($sql_get);

    if ($result->num_rows > 0) {
        $sql = "UPDATE `wp_serach_log` SET `antal_sokningar`=`antal_sokningar`+1 WHERE `term` = '$search_term'";
    } else {
        $sql = "INSERT INTO `wp_serach_log`(`term`, `antal_sokningar`, `url`) VALUES ('$search_term', '1', '$url')";
    }

    if ($conn->query($sql) === TRUE) { }
    $conn->close();
?>
