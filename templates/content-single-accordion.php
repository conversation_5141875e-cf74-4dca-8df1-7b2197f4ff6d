<?php if (have_rows('accordion_repeater')) : 
    $counter = 0; ?>
    <div class="wrapper py-3 py-md-5 px-0">
        <div class="container px-0">
            <div class="row mx-0">
                <?php while( have_rows('accordion_repeater') ) : the_row();
                    $title = get_sub_field('accordion_title');
                    $counter++; ?>
                    <div class="col-12 col-md-8 px-md-0">
                        <div id="accordion<?php echo $counter; ?>">
                            <div class="card">
                                <div class="card-header accordion collapsed" id="heading<?php echo $counter; ?>" data-toggle="collapse" data-target="#collapse<?php echo $counter; ?>" aria-expanded="true" aria-controls="collapse<?php echo $counter; ?>">
                                    <span><?php echo $title; ?></span>
                                </div>
                            </div>
                            <div id="collapse<?php echo $counter; ?>" class="collapse" aria-labelledby="heading<?php echo $counter; ?>" data-parent="#accordion<?php echo $counter; ?>">
                                <div class="card-body">
                                    <?php while( have_rows('accordion_link_repeater') ) : the_row(); 
                                        $linkName = get_sub_field('accordion_link');
                                        $link = get_sub_field('accordion_url'); ?>
                                        <p><a href="<?php echo $link; ?>"><?php echo $linkName; ?></a></p>
                                    <?php endwhile; ?>	
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endwhile; ?>	
            </div>
        </div>
    </div>
<?php endif; ?>  