<div class="certification-container">
	<?php
	$term = null;
	
	if (!empty(get_field('certifieringsordning_kategori'))) {
		$term = get_field('certifieringsordning_kategori');
	}

    $args = array(
		'status'			=> 'publish',
		'post_type' 		=> 'kravspecifikationer',
		'posts_per_page' 	=> -1,
		'orderby' 			=> 'title',
		'order' 			=> 'ASC',
		'tax_query' => array(
	        array(
	            'taxonomy' => 'kravspec_tax',
	            'field'    => 'term_id',
	            'terms'    => $term,
	        ),
	    ),
	);
	
    $query = new WP_Query($args);
	
	if ($query->have_posts()) :
		?>
		<ol>
			<?php
			while ($query->have_posts()) :	
				?>
				<li>
					<?php
					$query->the_post();
					$postName = $query->posts[$query->current_post]->post_name;

					$dock = false;

					if (isset($_GET['view']) && $_GET['view'] == $postName) {
						$dock = true;
					}
					?>
					
					<h2 id="<?php echo $postName; ?>" class="tgr-certifieringsorg-box <?php if ($dock) { echo 'active'; } ?>" data-id="post-id-<?php echo get_the_ID(); ?>">
						<i class="fa fa-caret-right" aria-hidden="true"></i>
						<span><?php the_title(); ?></span>
					</h2>
			
					<?php
					if (have_rows('kravspecifikationer')) :
						?>
						<ul class="required-specs-lists" data-id="post-id-<?php echo get_the_ID(); ?>" <?php if ($dock) { echo 'style="display: block;"'; } ?>>
							<?php
						    while (have_rows('kravspecifikationer')) : 
						    	the_row();
						    	?>
						    	<li><?php echo get_sub_field('beskrivning'); ?></li>
						    	<?php
						    endwhile;
						    ?>
					    </ul>
					    <?php
					endif;
					?>
				</li>
				<?php
			endwhile;
			?>
		</ol>
		<?php
		wp_reset_postdata();
	endif;
	?>
</div>
