<?php
$title = get_field('form-info-title');
$text = get_field('form-info-text');
$attachment_info = get_field('form-info-attachments');
$doc_info = get_field('form-doc-information');
?>

<?php if ($title || $text || $attachment_info || $doc_info) : ?>

  <?php if (isset($_GET['success_ackreditering'])): ?>
  <div class="messange_box">
    <div class="inner success">
      <h2><?php _e('Tack för din ansökan!', 'applyform') ?></h2>
      <p><?php _e('<PERSON> ansökan har skickats till oss på Swedac. Vi återkommer så snart vi kan.', 'applyform') ?></p>
    </div>
  </div>
  <?php elseif (isset($_GET['failed_ackreditering'])): ?>
  <div class="messange_box">
    <div class="inner failed">
      <h2><?php _e('<PERSON> ansökan har inte skickats.', 'applyform') ?></h2>
      <p><?php _e('Det verkar som att något gick fel. Vänligen försök en gång till.', 'applyform') ?></p>
      <p><?php _e('Skulle samma fel uppstå en gång till så är du välkommen att kontakta oss via telefon.', 'applyform') ?></p>
    </div>
  </div>
  <?php elseif (isset($_GET['human_validation_failed'])) : ?>
  <div class="messange_box">
    <div class="inner failed">
      <h2><?php _e('Något gick fel!', 'applyform') ?></h2>
      <p><?php _e('Vi kunde inte verifiera att du är en människa. Var god försök igen.', 'applyform') ?></p>
      <p><?php _e('Skulle problemet kvarstå så är du välkommen att kontakta oss via telefon.', 'applyform') ?></p>
    </div>
  </div>
  <?php elseif (isset($_GET['ogiltigt_format'])): ?>
  <div class="messange_box">
    <div class="inner failed">
      <h2><?php _e('Din ansökan har inte skickats.', 'applyform') ?></h2>
      <p><?php _e('Fel filformat har använts vid uppladdningen av dokument.', 'applyform') ?></p>
    </div>
  </div>
  <?php endif;?>

  <div class="container ackreditering_form_section px-0">
    <div class="row">
      <div class="col-12">

        <h2><?php echo $title; ?></h2>
        <p><?php echo $text; ?></p>

        <form id="ansok_om_ackreditering_form" class="form clear" name="ansok_om_ackreditering" method="post" action="/sender.php?ansok_om_ackreditering" autocomplete="on" enctype="multipart/form-data">
          <fieldset class="py-3">
            <legend><?php _e('Typ av ackreditering', 'applyform') ?></legend>
            <div>
              <input type="radio" id="avser_nyackreditering" name="typ_av_ackreditering" value="Avser nyackreditering">
              <label class="radio-label" for="avser_nyackreditering"><?php _e('Avser nyackreditering', 'applyform') ?></label>
            </div>
            <div>
              <input type="radio" id="avser_utokad_ackreditering" name="typ_av_ackreditering" value="Avser utökad ackreditering för ackrediteringsnummer: ">
              <label class="radio-label" for="avser_utokad_ackreditering"><?php _e('Avser ändrad ackreditering', 'applyform') ?></label>
            </div>
            <div class="pt-3">
              <label for="ack_nummer"><?php _e('Ange ackrediteringsnummer', 'applyform') ?></label>
              <input type="text" class="inputbox w-100" name="ack_nummer" id="ack_nummer" title="Ange Ackrediteringsnummer">
            </div>
          </fieldset>
          <div class="row_section last-child pb-3">
            <span class="row_title"><?php _e('Ackreditering önskas mot följande standard', 'applyform') ?></span>
            <div class="form-row full">
              <select name="ack_standard" id="ack_standard" required>
                <option value="" disabled selected><?php _e('Välj standard', 'applyform') ?></option>
                <option value="SS-EN ISO 14065:2013">SS-EN ISO 14065:2013</option>
                <option value="SS-EN ISO 15189:2022">SS-EN ISO 15189:2022</option>
                <option value="SS-EN ISO/IEC 17020:2012">SS-EN ISO/IEC 17020:2012</option>
                <option value="SS-EN ISO/IEC 17021-1:2015">SS-EN ISO/IEC 17021-1:2015</option>
                <option value="SS-EN ISO/IEC 17024:2012">SS-EN ISO/IEC 17024:2012</option>
                <option value="SS-EN ISO/IEC 17025:2018">SS-EN ISO/IEC 17025:2018</option>
                <option value="SS-EN ISO/IEC 17029:2019">SS-EN ISO/IEC 17029:2019</option>
                <option value="SS-EN ISO 17034:2016">SS-EN ISO 17034:2016</option>
                <option value="SS-EN ISO/IEC 17043:2023">SS-EN ISO/IEC 17043:2023</option>
                <option value="SS-EN ISO/IEC 17065:2012">SS-EN ISO/IEC 17065:2012</option>
                <option value="(EG) nr 1221/2009 EMAS">(EG) nr 1221/2009 EMAS</option>
              </select>
            </div>
          </div>

          <div class="row_section last-child">
            <?php if ($attachment_info): ?>
              <p><?php echo $attachment_info; ?></p>
            <?php endif;?>
            <div class="row mx-0 attachment-row pb-3">
              <label class="visuallyhidden" for="attachments"><?php _e('Bilagor', 'applyform') ?></label>
              <input type="file" name="attachment[]" id="attachments" onchange="fileType(this)">
              <img onclick="addMoreAttachment()" src="<?php echo get_template_directory_uri() ?>/assets/images/plus-black.png" alt="<?php _e('Lägg till fler filer', 'applyform') ?>">
            </div>
          </div>

          <div class="row_section">
            <div class="pb-3">
              <h3><?php _e('Namn och adressuppgifter', 'applyform') ?></h3>
              <div class="form-row left">
                <label for="ack_foretagsnamn"><?php _e('Företagsnamn *', 'applyform') ?></label>
                <input type="text" class="inputbox req" name="ack_foretagsnamn" id="ack_foretagsnamn" title="Företagsnamn" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
              </div>
              <div class="form-row right">
                <label for="ack_avdelning"><?php _e('Avdelning *', 'applyform') ?></label>
                <input type="text" class="inputbox req" name="ack_avdelning" id="ack_avdelning" title="Avdelning" required>
                  <i class="fa fa-check"></i>
                  <i class="fa fa-times"></i>
              </div>
              <div class="form-row full">
                <label for="ack_postadress"><?php _e('Postadress *', 'applyform') ?></label>
                <input type="text" class="inputbox req" name="ack_postadress" id="ack_postadress" title="Postadress" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
              </div>
              <div class="form-row left">
                <label for="ack_postnummer"><?php _e('Postnummer *', 'applyform') ?></label>
                <input type="text" class="inputbox req" name="ack_postnummer" id="ack_postnummer" title="Postnummer" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
              </div>
              <div class="form-row right">
                <label for="ack_ort"><?php _e('Ort *', 'applyform') ?></label>
                <input type="text" class="inputbox req" name="ack_ort" id="ack_ort" title="Ort" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
              </div>
              <div class="form-row full">
                <label for="ack_besoksadress"><?php _e('Besöksadress *', 'applyform') ?></label>
                <input type="text" class="inputbox req" name="ack_besoksadress" id="ack_besoksadress" title="Besöksadress" required>
                  <i class="fa fa-check"></i>
                  <i class="fa fa-times"></i>
              </div>
              <div class="form-row full">
                <label for="ack_orgnummer"><?php _e('Organisationsnummer *', 'applyform') ?></label>
                <input type="text" class="inputbox req" name="ack_orgnummer" id="ack_orgnummer" title="Organisationsnummer" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
              </div>
              <div class="form-row left">
                <label for="ack_telefonnummer"><?php _e('Telefonnummer *', 'applyform') ?></label>
                <input type="text" class="inputbox req" name="ack_telefonnummer" id="ack_telefonnummer" title="Telefonnummer" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
              </div>
              <div class="form-row right">
                <label for="ack_webbplats"><?php _e('Webbplats *', 'applyform') ?></label>
                <input type="text" class="inputbox req" name="ack_webbplats" id="ack_webbplats" title="Webbplats" required>
                  <i class="fa fa-check"></i>
                  <i class="fa fa-times"></i>
              </div>
            </div>
          </div> <!-- End of row_section -->

          <div class="row_section">
            <div class="pb-3">
              <h3><?php _e('Fakturaadress', 'applyform') ?></h3>
              <div class="form-row full">
                <label for="ack_fak_postadress"><?php _e('Postadress *', 'applyform') ?></label>
                <input type="text" class="inputbox req" name="ack_fak_postadress" id="ack_fak_postadress" title="Postadress" required>
                  <i class="fa fa-check"></i>
                  <i class="fa fa-times"></i>
              </div>

              <div class="form-row left">
                <label for="ack_fak_postnummer"><?php _e('Postnummer *', 'applyform') ?></label>
                <input type="text" class="inputbox req" name="ack_fak_postnummer" id="ack_fak_postnummer" title="Postnummer" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
              </div>
              <div class="form-row right">
                <label for="ack_fak_ort"><?php _e('Ort *', 'applyform') ?></label>
                <input type="text" class="inputbox req" name="ack_fak_ort" id="ack_fak_ort" title="Ort" required>
                  <i class="fa fa-check"></i>
                  <i class="fa fa-times"></i>
              </div>
              <div class="form-row full">
                <label for="ack_fak_fakref_bestallnr"><?php _e('Fakturareferens eller Beställningsnummer *', 'applyform') ?></label>
                <input type="text" class="inputbox req" name="ack_fak_fakref_bestallnr" id="ack_fak_fakref_bestallnr" title="Fakturareferens eller Beställningsnummer" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
              </div>
              <div class="form-row full mb-0"><small><?php _e('Om möjligheten finns att ta emot elektronisk faktura, fyll i ett eller båda fälten nedan', 'form') ?></small></div>
              <div class="form-row left">
                <label for="ack_fak_peppol_id"><?php _e('PEPPOL-ID', 'applyform') ?></label>
                <input type="text" class="inputbox" name="ack_fak_peppol_id" id="ack_fak_peppol_id" title="peppol-id">
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
              </div>
              <div class="form-row right">
                <label for="ack_fak_epost_for_faktura"><?php _e('E-postadress för faktura', 'applyform') ?></label>
                <input type="text" class="inputbox" name="ack_fak_epost_for_faktura" id="ack_fak_epost_for_faktura" title="E-postadress för faktura">
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
              </div>
            </div>
          </div> <!-- End of row_section -->

          <div class="row_section">
            <span class="row_title"><?php _e('Arbetsställe/ort där det ackrediterade arbetet kommer att utföras (om annan än ovan)', 'applyform') ?></span>
            <div class="form-row full">
              <label for="ack_avdelning_annan"><?php _e('Avdelning *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_avdelning_annan" id="ack_avdelning_annan" title="Avdelning" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
            </div>
            <div class="form-row full">
              <label for="ack_postadress_annan"><?php _e('Postadress *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_postadress_annan" id="ack_postadress_annan" title="Postadress" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
            </div>
            <div class="form-row left">
              <label for="ack_postnummer_annan"><?php _e('Postnummer *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_postnummer_annan" id="ack_postnummer_annan" title="Postnummer" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
            </div>
            <div class="form-row right">
              <label for="ack_ort_annan"><?php _e('Ort *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_ort_annan" id="ack_ort_annan" title="Ort"
                required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
            </div>
            <div class="form-row full">
              <label for="ack_besoksadress_annan"><?php _e('Besöksadress *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_besoksadress_annan" id="ack_besoksadress_annan" title="Besöksadress" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
            </div>
            <div class="form-row left">
              <label for="ack_telefonnummer_annan"><?php _e('Telefonnummer *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_telefonnummer_annan" id="ack_telefonnummer_annan" title="Telefonnummer" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
            </div>
            <div class="form-row right">
              <label for="ack_webbplats_annan"><?php _e('Webbplats *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_webbplats_annan" id="ack_webbplats_annan" title="Webbplats" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
            </div>
          </div> <!-- End of row_section -->

          <div class="row_section">
            <span class="row_title"><?php _e('Behörig firmatecknare/verksamhetsansvarig för den ackrediterade verksamheten', 'applyform') ?></span>
            <div class="form-row left">
              <label for="ack_for_efternamn_first"><?php _e('För- och efternamn *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_for_efternamn_first" id="ack_for_efternamn_first" title="För- och efternamn" required>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>
            <div class="form-row right">
              <label for="ack_befattning_first"><?php _e('Befattning *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_befattning_first" id="ack_befattning_first" title="Befattning" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
            </div>
            <div class="form-row left">
              <label for="ack_telefon_first"><?php _e('Telefon *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_telefon_first" id="ack_telefon_first"title="Telefon" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
            </div>
            <div class="form-row right">
              <label for="ack_epost_first"><?php _e('Epost *', 'applyform') ?></label>
              <input type="email" class="inputbox req" name="ack_epost_first" id="ack_epost_first" title="Epost" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
            </div>
          </div> <!-- End of row_section -->

          <div class="row_section">
            <span class="row_title"><?php _e('Person som har tekniskt ansvar för den ackrediterade verksamheten ', 'applyform') ?></span>
            <div class="form-row left">
              <label for="ack_for_efternamn_second"><?php _e('För- och efternamn *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_for_efternamn_second" id="ack_for_efternamn_second" title="För- och efternamn" required>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>
            <div class="form-row right">
              <label for="ack_befattning_second"><?php _e('Befattning *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_befattning_second" id="ack_befattning_second" title="Befattning" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
            </div>
            <div class="form-row left">
              <label for="ack_telefon_second"><?php _e('Telefon *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_telefon_second" id="ack_telefon_second" title="Telefon" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
            </div>
            <div class="form-row right">
              <label for="ack_epost_second"><?php _e('Epost *', 'applyform') ?></label>
              <input type="email" class="inputbox req" name="ack_epost_second" id="ack_epost_second" title="Epost" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
            </div>
          </div> <!-- End of row_section -->

          <div class="row_section">
            <span class="row_title"><?php _e('Person som ansvarar för upprätthållande av kvalitetssystem för den ackrediterade verksamheten (t.ex. kvalitetsansvarig eller kvalitetschef)', 'applyform') ?></span>
            <div class="form-row left">
              <label for="ack_for_efternamn_third"><?php _e('För- och efternamn *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_for_efternamn_third" id="ack_for_efternamn_third" title="För- och efternamn" required>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>
            <div class="form-row right">
            <label for="ack_befattning_third"><?php _e('Befattning *', 'applyform') ?></label>
                <input type="text" class="inputbox req" name="ack_befattning_third" id="ack_befattning_third" title="Befattning" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
            </div>
            <div class="form-row left">
              <label for="ack_telefon_third"><?php _e('Telefon *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_telefon_third" id="ack_telefon_third" title="Telefon" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
            </div>
            <div class="form-row right">
              <label for="ack_epost_third"><?php _e('Epost *', 'applyform') ?></label>
              <input type="email" class="inputbox req" name="ack_epost_third" id="ack_epost_third" title="Epost" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
            </div>
          </div> <!-- End of row_section -->

          <div class="row_section">
            <span class="row_title"><?php _e('Kontaktperson mot Swedac i frågor rörande ackreditering ', 'applyform') ?></span>
            <div class="form-row left">
              <label for="ack_for_efternamn_fourth"><?php _e('För- och efternamn *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_for_efternamn_fourth" id="ack_for_efternamn_fourth" title="För- och efternamn" required>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>
            <div class="form-row right">
              <label for="ack_befattning_fourth"><?php _e('Befattning *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_befattning_fourth" id="ack_befattning_fourth" title="Befattning" required>
                <i class="fa fa-check"></i>
                <i class="fa fa-times"></i>
            </div>
            <div class="form-row left">
              <label for="ack_telefon_fourth"><?php _e('Telefon *', 'applyform') ?></label>
              <input type="text" class="inputbox req" name="ack_telefon_fourth" id="ack_telefon_fourth" title="Telefon" required>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>
            <div class="form-row right">
              <label for="ack_epost_fourth"><?php _e('Epost *', 'applyform') ?></label>
              <input type="email" class="inputbox req" name="ack_epost_fourth" id="ack_epost_fourth" title="Epost" required>
              <i class="fa fa-check"></i>
              <i class="fa fa-times"></i>
            </div>
          </div> <!-- End of row_section -->

          <div class="row_section">
            <span class="row_title"><?php _e('Övrig information', 'applyform') ?></span>
            <label class="visuallyhidden" for="other_information"><?php _e('Övrig information', 'applyform') ?></label>
            <textarea class="w-100" id="other_information" name="other_information" rows="8" cols="50"></textarea>
          </div> <!-- End of row_section -->

          <div class="row mx-0 pb-3">
            <?php get_template_part('templates/content-single-organs');?>
          </div>

          <div class="row_section pt-3">
            <input type="hidden" name="user-lang" value="<?php echo strtolower(ICL_LANGUAGE_CODE); ?>" />
            <input type="hidden" name="url" value="<?php echo strtok($_SERVER["REQUEST_URI"], '?'); ?>" />
            <div class="row px-0 mx-0">
            <div class="frc-captcha" data-sitekey="FCMHCLG9N0S4NF1M" data-lang="<?php echo strtolower(ICL_LANGUAGE_CODE); ?>"></div>
              <div class="col-md-4 col-12 pr-0"><input class="submit-button w-100" id="submit-button" type="submit" value="<?php _e('Skicka', 'applyform') ?>" disabled></div>
            </div>
          </div>

        </form>
      </div>
    </div>
  </div>

<?php endif;?>