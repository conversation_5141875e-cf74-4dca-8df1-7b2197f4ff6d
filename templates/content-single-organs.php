
<?php if ( have_rows('form-doc-repeater') ) : 
    while( have_rows('form-doc-repeater') ) : the_row();
        $title = get_sub_field('form-title'); 
    ?>
        <h2 class="py-3"><?php echo $title; ?></h2>
        <?php if ( have_rows('form-row-repeater') ) : 
            $count = 1; ?>
                <?php while( have_rows('form-row-repeater') ) : the_row();
                    $count++;
                    $doc = get_sub_field('form-doc');
                    $doc_url = get_sub_field('form-doc-url');
                    $text = get_sub_field('form-doc-text');
                    $bg = ( $count % 2 == 0 ) ? '#e6e6e6;' : '#ffffff;'; ?>
                    
                    <dl class="row mx-0 px-0 py-3" style="background: <?php echo $bg; ?>">
                        <div class="col-2">
                            <dt> <a href="<?php echo $doc_url; ?>"><?php echo $doc; ?></a></dt>
                        </div>
                        <div class="col-10">
                            <dd class="m-0"><?php echo $text; ?></dd>
                        </div>
                    </dl>
                <?php endwhile; ?>
        <?php endif; ?>
    <?php endwhile; ?>
<?php endif; ?>
