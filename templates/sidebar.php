<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
	<?php if(is_singular('kurser')) { ?>
		<h2 class="sidebar_title">Fler kurser</h2>
		<?php $currentID = $post->ID; ?>

		<?php $args = array(
			'post_type' => 'kurser',
			'post__not_in' => array( $currentID ),
			'posts_per_page' => 8,
			'orderby' => 'date'
		);

		$query = new WP_Query($args);
		if($query->have_posts()): ?>
		    <ul>
			    <?php while($query->have_posts()):$query->the_post(); ?>
			        <li <?php post_class(); ?>>
			        	<h2><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
			        </li>
			    <?php endwhile; ?>
		    	<a class="arkiv_link" href="/tjanster/swedac-academy/kurser/"><PERSON><PERSON> kurser</a>
		  	</ul>
		<?php endif; wp_reset_postdata(); ?>

	<?php } elseif (is_singular('kalender')) { ?>
		<h2 class="sidebar_title">Fler händelser</h2>
			<?php $currentID = $post->ID; ?>

			<?php $args = array(
				'post_type' 		=> 'kalender',
				'post_status' 		=> 'publish',
				'post__not_in' 		=> array( $currentID ),
				'posts_per_page'	=> 8,
				'orderby' 			=> 'date'
				);
			$query = new WP_Query($args);
			if($query->have_posts()): ?>
			    <ul style="display: flex; flex-direction: column;">
				    <?php while($query->have_posts()):$query->the_post(); ?>
				        <li <?php post_class(); ?>>
				        	<div class="meta">
				        		<span><?php echo get_field('kal_startdatum_sv'); ?> - <?php echo get_field('kal_slutdatum_sv'); ?> <?php echo get_field('kal_manad_ar_sv'); ?></span>
				        	</div>
				        	<h2><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
				        </li>
				    <?php endwhile; ?>
			    	<a class="arkiv_link" href="/kalender/">Alla händelser</a>
			  	</ul>

			<?php else: ?>

			<em>Inga andra händelser finns i kalendern.</em>
			<?php endif; wp_reset_postdata(); ?>

	<?php } elseif(is_singular('medarbetare')) { ?>

		<h2 class="sidebar_title">Andra medarbetare</h2>
		<?php $currentID = $post->ID; ?>

		<?php $args = array(
			'post_type' 		=> 'medarbetare',
			'post_status' 		=> 'publish',
			'post__not_in' 		=> array( $currentID ),
			'posts_per_page'	=> 4,
			'order' 			=> 'desc',
			'orderby' 			=> 'date',
			);
		$query = new WP_Query($args);
		if($query->have_posts()): ?>
		    <ul>
			    <?php while($query->have_posts()):$query->the_post(); ?>
			        <li <?php post_class(); ?>>

			        	<h2><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>

						<div class="ingress">
							<?php echo get_field('medarbetare_ingress_sv'); ?>
						</div>
			        </li>
			    <?php endwhile; ?>


		  	</ul>
		<?php else: ?>
			<em>Inga övriga medarbetare finns publicerade.</em>
		<?php endif; wp_reset_postdata(); ?>

	<?php } elseif(is_single()) { ?>
		<h2 class="sidebar_title">Fler nyheter</h2>
		<?php $currentID = $post->ID; ?>

		<?php $args = array(
			'post_type' 		=> 'news',
			'post_status' 		=> 'publish',
			'post__not_in' 		=> array( $currentID ),
			'posts_per_page'	=> 8,
			'orderby' 			=> 'date'
			);
		$query = new WP_Query($args);
		if($query->have_posts()): ?>
		    <ul>
			    <?php while($query->have_posts()):$query->the_post(); ?>
			        <li <?php post_class(); ?>>
			        	<div class="meta">
			        		<?php get_template_part('templates/entry-meta'); ?>
			        	</div>
			        	<h2><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
			        </li>
			    <?php endwhile; ?>

		    	<a class="arkiv_link" href="/nyheter/">Till nyhetsarkivet</a>
		  	</ul>
		<?php endif; wp_reset_postdata(); ?>

	<?php } elseif (is_page_template('page-template-7-bloggen.php')) { ?>
		<div class="om-gd-bloggen">
			<div class="profilbild">
				<?php $profilbild_gd_bloggen = get_field('gd_bloggen_profilbild', 1022); ?>
				<?php if($profilbild_gd_bloggen) { ?>
					<img data-src="<?php echo $profilbild_gd_bloggen['sizes']['medium']; ?>" alt="" />
				<?php } ?>
			</div>
			<div class="content">
				<?php if (!empty(get_field('gd_bloggen_rubrik', 1022))): ?>
					<h3><?php echo get_field('gd_bloggen_rubrik', 1022); ?></h3>
				<?php endif; ?>
				<div class="info">
					<?php echo get_field('gd_bloggen_beskrivning', 1022); ?>

					<?php if(get_field('gd_bloggen_epost')) { ?>
						<a href="mailto:<?php echo antispambot(get_field('gd_bloggen_epost', 1022)); ?>">Maila mig</a>
					<?php } ?>
				</div>
			</div>
		</div>
	<?php } else { ?>
		<div id="sidebar_sub_menu">
			<!-- Skriv ut sidonavigation -->
			<?php
			    if ($post->post_parent) {
			        $ancestors = get_post_ancestors($post->ID);
			        $parent = $ancestors[count($ancestors) - 1];
			    } else {
			        $parent = $post->ID;
			    }

			$args = [
				'child_of' => $post->ID,
				'depth' => 1,
				'title_li' => null,
				'echo' => false,
				'sort_column' => 'menu_order'
			];
			$children = wp_list_pages($args);

		    if (!empty($children)) : ?>
				<ul class="pt-3">
					<?php echo $children; ?>
				</ul>
			<?php else: ?>
				<?php wp_nav_menu(['theme_location' => 'mainNav', 'container' => '', 'menu_class' => 'submenu', 'start_depth' => 1]); ?>
			<?php endif; ?>
		</div>

		<?php
		if (is_page(137)) :
			?>
			<div id="sidebar_subscribe">
				<h2 class="sidebar_title">Innehåll</h2>
				<ul>
					<li>
						<div class="images">
	    					<img src="<?php echo home_url('wp-content/uploads/2016/04/icon-follow-blue.svg'); ?>">
	    				</div>
	    				<a href="#monitor_content_for_updates">Bevaka innehåll</a>
	    			</li>
				</ul>
			</div>
			<?php
		endif;
		?>		
	<?php } ?>
<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
	<?php if(is_singular('kurser')) { ?>
		<h2 class="sidebar_title">More courses</h2>
		<?php $currentID = $post->ID; ?>

		<?php $args = array(
			'post_type' => 'kurser',
			'post__not_in' => array( $currentID ),
			'posts_per_page' => 8,
			'orderby' => 'date'
		);

		$query = new WP_Query($args);
		if($query->have_posts()): ?>
		    <ul>
			    <?php while($query->have_posts()):$query->the_post(); ?>
			        <li <?php post_class(); ?>>
			        	<h2><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
			        </li>
			    <?php endwhile; ?>
		    	<a class="arkiv_link" href="/services/swedac-academy/courses-2/?lang=en">All courses</a>
		  	</ul>
		<?php endif; wp_reset_postdata(); ?>

	<?php } elseif (is_singular('kalender')) { ?>
		<h2 class="sidebar_title">More events</h2>
			<?php $currentID = $post->ID; ?>

			<?php $args = array(
				'post_type' 		=> 'kalender',
				'post_status' 		=> 'publish',
				'post__not_in' 		=> array( $currentID ),
				'posts_per_page'	=> 8,
				'orderby' 			=> 'date'
				);
			$query = new WP_Query($args);
			if($query->have_posts()): ?>
			    <ul>
				    <?php while($query->have_posts()):$query->the_post(); ?>
				        <li <?php post_class(); ?>>
				        	<div class="meta">
				        		<span><?php echo get_field('kal_startdatum_sv'); ?> - <?php echo get_field('kal_slutdatum_sv'); ?> <?php echo get_field('kal_manad_ar_sv'); ?></span>
				        	</div>
				        	<h2><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
				        </li>
				    <?php endwhile; ?>
			    	<a class="arkiv_link" href="/calender/?lang=en">All events</a>
			  	</ul>

			<?php else: ?>

			<em>No other events are on the calendar.</em>
			<?php endif; wp_reset_postdata(); ?>
	<?php } elseif(is_singular('medarbetare')) { ?>

	<h2 class="sidebar_title">Other employees</h2>
	<?php $currentID = $post->ID; ?>

	<?php $args = array(
		'post_type' 		=> 'medarbetare',
		'post_status' 		=> 'publish',
		'post__not_in' 		=> array( $currentID ),
		'posts_per_page'	=> 4,
		'order' 			=> 'desc',
		'orderby' 			=> 'date',
		);
	$query = new WP_Query($args);
	if($query->have_posts()): ?>
	    <ul>
		    <?php while($query->have_posts()):$query->the_post(); ?>
		        <li <?php post_class(); ?>>
		        	<h2><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>

					<div class="ingress">
						<?php echo get_field('medarbetare_ingress_no'); ?>
					</div>
		        </li>
		    <?php endwhile; ?>


	  	</ul>
	<?php else: ?>
		<em>No other employees are published.</em>
	<?php endif; wp_reset_postdata(); ?>

	<?php } elseif(is_single()) { ?>
		<h2 class="sidebar_title">More news</h2>
		<?php $currentID = $post->ID; ?>

		<?php $args = array(
			'post_type' 		=> 'news',
			'post_status' 		=> 'publish',
			'post__not_in' 		=> array( $currentID ),
			'posts_per_page'	=> 8,
			'orderby' 			=> 'date'
			);
		$query = new WP_Query($args);
		if($query->have_posts()): ?>
		    <ul>
			    <?php while($query->have_posts()):$query->the_post(); ?>
			        <li <?php post_class(); ?>>
			        	<div class="meta">
			        		<?php get_template_part('templates/entry-meta'); ?>
			        	</div>
			        	<h2><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
			        </li>
			    <?php endwhile; ?>

		    	<a class="arkiv_link" href="/news/?lang=en">To news archive</a>
		  	</ul>
		<?php endif; wp_reset_postdata(); ?>

	<?php } elseif (is_page_template('page-template-7-bloggen.php')) { ?>
		<div class="om-gd-bloggen">
			<div class="profilbild">
				<?php $profilbild_gd_bloggen = get_field('gd_bloggen_profilbild_en', 9827); ?>
				<?php if($profilbild_gd_bloggen) { ?>
					<img data-src="<?php echo $profilbild_gd_bloggen['sizes']['medium']; ?>" alt="" />
				<?php } ?>
			</div>
			<div class="content">
				<?php if (!empty(get_field('gd_bloggen_rubrik_en', 9827))): ?>
					<h3><?php echo get_field('gd_bloggen_rubrik_en', 9827); ?></h3>
				<?php endif; ?>
				<div class="info">
					<?php echo get_field('gd_bloggen_beskrivning_en', 9827); ?>

					<?php if(get_field('gd_bloggen_epost_en')) { ?>
						<a href="mailto:<?php echo antispambot(get_field('gd_bloggen_epost_en', 9827)); ?>">Send me a email</a>
					<?php } else { ?>

					<?php } ?>
				</div>
			</div>
		</div>
	<?php } else { ?>
		<div id="sidebar_sub_menu">
			<!-- Skriv ut sidonavigation -->
			<?php
			    if ($post->post_parent) {
			        $ancestors = get_post_ancestors($post->ID);
			        $parent = $ancestors[count($ancestors) - 1];
			    } else {
			        $parent = $post->ID;
			    }

		    $children = wp_list_pages("title_li=&child_of=" . $parent . "&echo=0");
		    if ($children) { ?>

		    	<h2><?php 
				$post_ancestors = get_post_ancestors($post->ID);
				$parent_post_id = (is_array($post_ancestors) && !empty($post_ancestors)) ? array_pop($post_ancestors) : false;
				if ($parent_post_id) {
					echo get_page($parent_post_id)->post_title;
				}
				?></h2>

		    <?php } ?>


			<?php wp_nav_menu(['theme_location' => 'mainNav', 'container' => '', 'menu_class' => 'submenu', 'start_depth' => 1]); ?>
		</div>
	<?php } ?>
<?php endif;?>
