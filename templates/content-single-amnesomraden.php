<?php $workingAreaID = get_the_ID();?>
<?php
	$list = [];
	foreach(get_the_terms($workingAreaID, 'amnesomraden_categories') as $term){
		$list[] = $term->name;
	}
	if(!empty($list)):
		?>
		<script type="application/ld+json">
			{
				"@context": "https://schema.org",
				"@type": "Article",
				"keywords": "<?php echo implode(',', $list)?>"
			}
		</script>
		<?php
	endif
?>
<div id="page-header-parent">
	<div class="title">
		<div class="wrapper">
			<?php if (ICL_LANGUAGE_CODE == 'sv'): ?>
				<span class="title-page">Ämnesområden</span>
			<?php elseif (ICL_LANGUAGE_CODE == 'en'): ?>
				<span class="title-page">Working areas</span>
			<?php endif;?>
		</div>
	</div>
	<div class="wrapper">
		<?php if (ICL_LANGUAGE_CODE == 'sv'): ?>
			<nav aria-label="Brödsmulor" id="breadcrumbs"><span xmlns:v="http://rdf.data-vocabulary.org/#">
				<span typeof="v:Breadcrumb">
					<a href="/" title="Gå till Hem" rel="v:url" property="v:title">Hem</a> /
						<span rel="v:child" typeof="v:Breadcrumb"><a href="/amnesomraden/" title="Gå till Ämnesområden" rel="v:url" property="v:title">Ämnesområden</a> /
						<span class="breadcrumb_last"><?php the_title();?></span>
					</span>
				</span>
			</nav>
		<?php elseif (ICL_LANGUAGE_CODE == 'en'): ?>
			<nav aria-label="Breadcrumb" id="breadcrumbs"><span xmlns:v="http://rdf.data-vocabulary.org/#">
				<span typeof="v:Breadcrumb"><a href="/?lang=en" title="Return to Home" rel="v:url" property="v:title">Home</a> /
					<span rel="v:child" typeof="v:Breadcrumb">
						<a href="/working-areas/?lang=en" title="Return to Working Areas" rel="v:url" property="v:title">Working Areas</a> /
						<span class="breadcrumb_last"><?php the_title();?></span>
					</span>
				</span>
			</nav>
		<?php endif;?>
	</div>
</div>

<?php if (ICL_LANGUAGE_CODE == 'sv'): ?>
	<div class="wrapper">
		<div class="single_section_post_amnesomrade">
			<div class="sidebar">
				<?php get_template_part('templates/sidebar-amnesomraden', 'page');?>
			</div>
			<div class="main">
				<?php while (have_posts()): the_post();?>
					<?php global $wp_query;
					$head_postid = get_the_ID();
					$bevaka_innehall = get_field('bevaka_innehall', $head_postid);
					$head_title = get_the_title($head_postid); ?>

					<article <?php post_class('single-post');?> aria-label="<?php echo get_the_title(); ?>">
						<header class="post-header">
							<?php if (isset($_GET['success_subscribing'])) { ?>
								<div class="header_message">
									<div class="wrapper">
										<div class="inner success">
											<span>Du bevakar nu ämnesområdet, <strong><?php the_title();?></strong>. När någonting
												ändras här kommer du få ett mail.</span>
										</div>
									</div>
								</div>
							<?php }?>
							<?php if (isset($_GET['failed_subscribing'])) { ?>
								<div class="header_message">
									<div class="wrapper">
										<div class="inner failed">
											<span>Du bevakar redan detta ämnesområdet.</span>
										</div>
									</div>
								</div>
							<?php }?>
							<h1><?php the_title();?></h1>
							<div class="ingress">
								<?php echo get_field('page_ingress_subpage_sv');?>
							</div>
							<?php if (get_field('page_ankarlankar')): ?>
								<div class="page_ankarlankar">
									<h2 class="nav-header">Snabbnavigation</h2>
									<ul>
										<?php while (has_sub_field('page_ankarlankar')): ?>
										<li>
											<a href="<?php echo get_sub_field('lank');?>" title="Gå till <?php echo get_sub_field('namn');?>">
												<?php echo get_sub_field('namn');?>
											</a>
										</li>
										<?php endwhile;?>
									</ul>
								</div>
							<?php endif;?>
							<?php if (has_post_thumbnail()): ?>
								<?php $get_post_image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'large');
								$image_url = $get_post_image['0'];						
								$img_id = get_post_thumbnail_id(get_the_ID());
								$alt_text = get_post_meta($img_id , '_wp_attachment_image_alt', true); 
								$thumbnail_image = get_posts(array('p' => $img_id, 'post_type' => 'attachment'));
								$caption = ($thumbnail_image && isset($thumbnail_image[0])) ? $thumbnail_image[0]->post_excerpt : false;
								?>
								<?php if ($caption) : ?>
									<figure class="page_thumbnail">
										<img data-src="<?php echo $image_url; ?>" alt="<?php echo $alt_text; ?>" />
										<figcaption class="wp-caption-text"><?php echo $caption; ?></figcaption>
									</figure>
								<?php else : ?>
									<div class="page_thumbnail">
										<img data-src="<?php echo $image_url; ?>" alt="<?php echo $alt_text; ?>" />
									</div>
								<?php endif; ?>
							<?php endif;
							wp_reset_query();?>
						</header>

						<?php if (ICL_LANGUAGE_CODE == 'sv'): ?>
							<?php $posts = get_field('berorda-amnesomraden_sv');
							if ($posts): ?>
								<div class="ledningssystem-section">
									<ul>
										<?php foreach ($posts as $post): // variable must be called $post (IMPORTANT) ?>
											<?php setup_postdata($post);?>
											<li>
												<a href="<?php the_permalink();?>"><?php the_title();?></a>
											</li>
										<?php endforeach;?>
									</ul>
								</div>
								<?php wp_reset_postdata(); // IMPORTANT - reset the $post object so the rest of the page works correctly ?>
							<?php endif;?>
						<?php elseif (ICL_LANGUAGE_CODE == 'en'): ?>
							<?php $posts = get_field('berorda-amnesomraden_en');
							if ($posts): ?>
								<div class="ledningssystem-section">
									<ul>
										<?php foreach ($posts as $post): // variable must be called $post (IMPORTANT) ?>
										<?php setup_postdata($post);?>
										<li>
											<a href="<?php the_permalink();?>"><?php the_title();?></a>
										</li>
										<?php endforeach;?>
									</ul>
								</div>
								<?php wp_reset_postdata(); // IMPORTANT - reset the $post object so the rest of the page works correctly ?>
							<?php endif;?>
						<?php endif;?>

						<div class="post-content">
							<?php the_content();?>
						</div>

						<footer class="post-footer">
							<?php $ids = get_field('show_links_on_amnesomrade', false, false);

							if (!empty($ids)) {
								$args = array(
									'post_type' => 'intern_lankningar',
									'posts_per_page' => -1,
									'post__in' => $ids,
									'orderby' => 'date',
									'order' => 'asc',
								);

								$query = new WP_Query($args);
								$firstpost = true; ?>
								<ul id="spec_links">
									<?php
									if ($query->have_posts()):
										$i = 1;
										$t = 1;
										while ($query->have_posts()):
											$query->the_post(); ?>
											<li id="target_<?php echo $t; ?>">
												<div class="head paragraph_start card-link d-block position-relative text-dark collapsible-link-white border-0"
													aria-expanded="false" data-paragraph="<?php echo $i; ?>"
													data-toggle="<?php echo $i; ?>">
													<div class="images">
														<img alt="" data-src="<?php echo get_field('lankar_ikon_white');?>">
													</div>
													<h2><?php the_title();?></h2>
												</div>
												<div class="hidden_content" data-more="<?php echo $i; ?>">
													<!-- Dokument -->
													<?php if ($post->ID == 887) { ?>
														<div class="document_crm w-100">
															<?php
															// Get ids
															$idsQuery = "SELECT post_id FROM wp_postmeta WHERE meta_key LIKE 'crm_doc_webCategories_%_webCategories_area' AND meta_value = '{$head_title}'";
															$idsResult = $wpdb->get_results($idsQuery, ARRAY_N);
															$ids = array_map(function ($i) { return intval($i[0]); }, $idsResult);

															// Get documents
															$args = array(
																'post__in' => $ids,
																'status' => 'publish',
																'post_type' => 'dokument',
																'posts_per_page' => -1,
																'meta_key' => 'crm_doc_dokumentbeteckning',
																'orderby' => 'meta_value',
																'order' => 'ASC'
															);

															$documents = get_posts($args);
															uksort($documents, "strnatcmp");
															$docArray = array();

															// Get unique control form titles
															$titles = [];
															$webCategories = [];
															$heading_titles = [];
															$acc_orders = [];
															$control_form_titles = [];

															// Loop through documents and append acf-fields
															foreach ($documents as $document) {
																$wc = get_field('crm_doc_webCategories', $document->ID);
																$webCategories[] = $wc;
																$document->webCategories = $wc;
															}

															// Check if accOrder is matching with current area
															foreach ($documents as $doc) :
																foreach ($doc->webCategories as $wc) {
																	// Checks if current area is equal to webcategorys area - if so then add to array
																	if ($head_title == $wc['webCategories_area'] && $head_title != $wc['webCategories_title']) :
																		if ($wc['webCategories_acc_order'] != null) :
																			$titles[] = $wc['webCategories_acc_order'];
																		else :
																			$titles[] = $wc['webCategories_area'];
																		endif;
																	elseif ($head_title == $wc['webCategories_area'] && $head_title == $wc['webCategories_title']) :
																		$titles[] = $wc['webCategories_title'];
																	endif;
																}
															endforeach;

															// Merge to one array each
															$newWebCategories = [];
															foreach ($webCategories as $array) {
																foreach($array as $inner) {
																	$newWebCategories[] = $inner;
																}
															}

															// Get all values from headings
															$heading_titles = array_column($newWebCategories, "webCategories_headings");

															$newHeadingTitles = [];
															foreach ($heading_titles as $array) {
																foreach($array as $inner) {
																	$newHeadingTitles[] = $inner;
																}
															}

															//Delete duplicates in array
															$titles = array_unique(array_filter($titles));
															$acc_orders = array_unique(array_filter(array_column($newWebCategories, "webCategories_acc_order")));
															$control_form_titles = array_unique(array_filter(array_column($newWebCategories, "webCategories_title")));
															$newHeadingTitles = array_unique(array_filter(array_column($newHeadingTitles, "heading")));

															//Sort arrays
															sort($control_form_titles);
															rsort($newHeadingTitles);
															sort($titles);
															rsort($acc_orders);

															// Filter
															function my_posts_where( $where ) {
																$where = str_replace("meta_key = 'crm_doc_area_$", "meta_key LIKE 'crm_doc_area_%", $where);
																return $where;
															}

															add_filter('posts_where', 'my_posts_where');

															foreach ($titles as $title) :
																$id = 'id' . rand(); ?>
																<div id="accordion" class="py-1">
																	<div class="card">
																		<a class="text-decoration-none card-link d-block position-relative text-dark collapsible-link-black border-0" aria-expanded="false" data-toggle="collapse" data-target="#<?php echo esc_html($id); ?>" href="#">
																			<div class="card-header accordion-icon">
																				<span class="accordion-text"><?php echo esc_html($title); ?></span>
																			</div>
																		</a>
																		<div id="<?php echo esc_html($id); ?>" class="collapse" data-parent="#accordion">
																			<ul class="area_docs" style="padding-block: 15px;">
																				<?php if (!in_array($title, $control_form_titles)) : ?>
																					<li class="pt-1 m-0">
																						<span class="doc_header"><?php _e('Specifika dokument för ackrediteringsordningen'); ?></span>
																					</li>
																				<?php endif;
																				$acceptedDocs = array();

																				foreach ($newHeadingTitles as $header) :
																					$firstHeader = true;
																					foreach ($documents as $document) :
																						$firstDoc = true;
																						// Array that contains the areas acc orders
																						$document_acc_orders = [];
																						$document_titles = [];
																						$document_headings = [];
																						$document_areas = [];

																						// Insert accorders and headers to array
																						foreach ($document->webCategories as $wc) {
																							$document_acc_orders[] = $wc['webCategories_acc_order'];
																							$document_titles[] = $wc['webCategories_title'];
																							$document_areas[] = $wc['webCategories_area'];
																							foreach ($wc['webCategories_headings'] as $h) {
																								array_push($document_headings, $h['heading']);
																							}
																						}

																						$document_acc_orders = array_unique(array_filter($document_acc_orders));
																						$document_titles = array_unique(array_filter($document_titles));
																						$document_areas = array_unique(array_filter($document_areas));
																						$document_headings = array_unique(array_filter($document_headings));

																						// Count quantity of unique headers
																						$qty = count($document_headings);

																						// Checks if there is a match - continue if not.
																						if (!in_array($title, $document_acc_orders) && !in_array($title, $document_titles)) :
																							continue;
																						endif;

																						foreach ($document->webCategories as $wcQty) :
																							// Check if header matches and if there is the current area.
																							if ($title == $wcQty['webCategories_title'] || $title == $wcQty['webCategories_acc_order'] || $title == $wcQty['webCategories_area']) :
																								// If there is the first header
																								for ($i = 0; $i < $qty; $i++) :
																									if ($wcQty['webCategories_headings'][$i]['heading'] == $header && $wcQty['webCategories_area'] == $head_title) :
																										if ($firstHeader) : ?>
																											<li class="py-1 pr-4 m-0">
																												<span class="header_title"><?php echo esc_html($header); ?></span>
																											</li>
																											<?php $firstHeader = false;
																										endif;
																										if ($firstDoc) : ?>
																											<li class="py-1 px-4">
																												<span class="docs">
																													<?php if ($document->crm_doc_doc) : ?>
																														<a class="doc_link" href="<?php echo esc_url($document->crm_doc_doc); ?>">
																													<?php endif;
																													echo esc_html($document->crm_doc_dokumentbeteckning);
																													if ($document->crm_doc_doc) : ?>
																														</a>
																													<?php endif; ?>
																													&nbsp;|&nbsp;&nbsp;<?php echo esc_html($document->post_title); ?>
																												</span>
																											</li>
																											<?php
																											$acceptedDocs[] = $document; // Add all accepted docs to each accOrder to array
																											$firstDoc = false;
																										endif;
																									endif;
																								endfor;
																							endif;
																						endforeach;
																					endforeach;
																				endforeach; ?>
																			</ul>
																			<?php if (!in_array($title, $control_form_titles)) :
																				foreach ($acceptedDocs as $doc) :
																					// Array that contains the current accorder's controlforms
																					$generalControlForms = [];
																					foreach ($doc->webCategories as $wc) {
																						// Checks if current accorder is equal to controlform-field title - if so then add to array
																						if ($title == $wc['webCategories_acc_order'] || $title == $wc['webCategories_area']) :
																							$generalControlForms[] = $wc['webCategories_title'];
																						endif;
																					}
																				endforeach;

																				// Args for general docs with area of controlform
																				$general_args = array(
																					'posts_per_page'	=> -1,
																					'post_type'		=> 'dokument',
																					'meta_key' => 'crm_doc_dokumentbeteckning',
																					'orderby' => 'meta_value',
																					'post__not_in' => $do_not_duplicate,
																					'order' => 'ASC',
																					'meta_query'	=> array(
																						'relation'		=> 'OR',
																						array(
																							'key'		=> 'crm_doc_area_$_omradesnamn',
																							'compare'	=> 'IN',
																							// Check if accOrder contains any controlforms
																							'value'		=> $generalControlForms,
																						),
																					)
																				);

																				// Get posts from general documents
																				$general_documents = get_posts($general_args);
																				$general_headings = [];
																				$general_webCategories = [];

																				// Loop through documents and append acf-fields
																				foreach ($general_documents as $general_document) {
																					$wc = get_field('crm_doc_webCategories', $general_document->ID);
																					$general_webCategories[] = $wc;
																					$general_document->webCategories = $wc;
																				}

																				$new_generalWebCategories = [];
																				foreach ($general_webCategories as $array) {
																					foreach($array as $inner) {
																						$new_generalWebCategories[] = $inner;
																					}
																				}

																				$general_headings = array_column($new_generalWebCategories, "webCategories_headings");

																				$new_generalHeadingTitles = [];
																				foreach ($general_headings as $array) {
																					foreach($array as $inner) {
																						$new_generalHeadingTitles[] = $inner;
																					}
																				}

																				$new_generalHeadingTitles = array_unique(array_filter(array_column($new_generalHeadingTitles, "heading")));
																				rsort($new_generalHeadingTitles); ?>

																				<ul class="general_docs">
																					<li class="pt-1 m-0">
																						<span class="doc_header"><?php _e('Generella dokument för kontrollformen'); ?></span>
																					</li>
																					<?php foreach ($new_generalHeadingTitles as $general_header) :
																						$firstHeader = true;
																						foreach ($general_documents as $general_document) :
																							$firstDoc = true;
																							// Array that contains the areas acc orders
																							$general_acc_orders = [];
																							$general_titles = [];
																							$general_headings = [];

																							// Insert accorders and headers to array
																							foreach ($general_document->webCategories as $wc) {
																								$general_acc_orders[] = $wc['webCategories_acc_order'];
																								$general_titles[] = $wc['webCategories_title'];
																								foreach ($wc['webCategories_headings'] as $h) {
																									array_push($general_headings, $h['heading']);
																								}
																							}

																							// Make headers unique
																							$general_headings = array_unique($general_headings);
																							// Count quantity of unique headers
																							$general_qty = count($general_headings);

																							foreach ($general_document->webCategories as $general_wcQty) :
																								// Checks if there is a match - continue if not.
																								for ($i = 0; $i < $general_qty; $i++) :
																									if (in_array($general_wcQty['webCategories_title'], $generalControlForms)) :
																										if ($general_wcQty['webCategories_headings'][$i]['heading'] == $general_header) :
																											if ($firstHeader) : ?>
																												<li class="py-1 pr-4">
																													<span class="header_title py-1"><?php echo esc_html($general_header); ?></span>
																												</li>
																												<?php $firstHeader = false;
																											endif;
																											if ($firstDoc) : ?>
																												<li class="px-4 py-1">
																													<span class="docs">
																														<?php if ($general_document->crm_doc_doc) : ?>
																															<a class="doc_link" href="<?php echo esc_url($general_document->crm_doc_doc); ?>">
																														<?php endif;
																														echo esc_html($general_document->crm_doc_dokumentbeteckning);
																														if ($general_document->crm_doc_doc) : ?>
																															</a>
																														<?php endif; ?>
																														&nbsp;|&nbsp;&nbsp;<?php echo esc_html($general_document->post_title); ?>
																													</span>
																												</li>
																												<?php $firstDoc = false;
																											endif;
																										endif;
																									endif;
																								endfor;
																							endforeach;
																						endforeach;
																					endforeach; ?>
																				</ul>
																			<?php endif; ?>
																		</div>
																	</div>
																</div>
																<?php // Clears arrays
																$document_acc_orders = array();
																$document_titles = array();
																$document_headings = array();
																$general_documents = array();
																$general_headings = array();
																$general_acc_orders = array();
																$generalControlForms = array();
															endforeach; ?>
														</div>
													<?php }?>

													<!-- Externa länkar -->
													<?php if ($post->ID == 888) {?>
														<div class="externa_lankar">
															<?php if (get_field('externa_lankar', $head_postid)): ?>
																<ul>
																	<?php while (has_sub_field('externa_lankar', $head_postid)): ?>
																		<li>
																			<a href="<?php echo get_sub_field('lankmal');?>"
																				target="_blank"><?php echo get_sub_field('lanknamn');?></a>
																		</li>
																	<?php endwhile;?>
																</ul>
															<?php endif;?>
														</div>
													<?php }?>

													<?php if ($post->ID == 889) {?>
														<div id="relaterad_information" class="relaterad_information">
															<?php $pairedPosts = get_field('relaterad_information', $workingAreaID);
															if ($pairedPosts): ?>
																<ul>
																	<?php foreach ($pairedPosts as $pairedPost): ?>
																	<li>
																		<a
																			href="<?=get_the_permalink($pairedPost->ID);?>"><?=get_the_title($pairedPost->ID);?></a>
																	</li>
																	<?php endforeach;?>
																</ul>
															<?php endif;?>
															<?php wp_reset_query();?>
														</div>
													<?php }?>
													<!-- Bevaka innehåll -->
													<?php if ($post->ID == 890) { ?>
														<?php if ($bevaka_innehall == true) {
														$docArrayPar = (isset($docArray) && is_array($docArray) && !empty($docArray)) ? wp_json_encode($docArray) : '';	
														?>
															<div class="starta_bevakning">
																<span>När innehållet eller krav- och vägledningsdokumenten uppdateras på denna
																	sida
																	skickas en notifikation till angiven e-postadress. Genom att anmäla dig till
																	denna tjänst samtycker du till att vi lagrar din e-postadress. Du kan alltid
																	kontakta Swedac om du vill att din e-postadress ska tas bort ur
																	registret</span>
																<form id="monitor_content" class="form clear" name="monitor_content" method="post"
																	action="/subscribers.php?add_subscriber" novalidate="novalidate"
																	autocomplete="off">
																	<div class="row name" style="margin-inline: 0 !important">
																		<input class="req" type="text" name="name" placeholder="Namn *"
																			title="Namn">
																		<i class="fa fa-check"></i>
																		<i class="fa fa-times"></i>
																		<p class="error-text  m-0" style="display: none;"></p>
																	</div>
																	<div class="row email" style="margin-inline: 0 !important">
																		<input class="req" type="email" name="email" placeholder="E-postadress *"
																			title="E-postadress">
																		<i class="fa fa-check"></i>
																		<i class="fa fa-times"></i>
																		<p class="error-text m-0" style="display: none;"></p>
																	</div>
																	<input type="hidden" name="doc_bet"
																		value='<?php echo $docArrayPar; ?>'>

																	<input type="hidden" value="<?php echo $head_postid; ?>" name="post_id" />
																	<input type="hidden" name="url"
																		value="<?php echo strtok($_SERVER["REQUEST_URI"], '?'); ?>" />
																	<input type="submit" class="submit_bevakning" value="Bevaka" title="Bevaka">
																</form>
															</div>
														<?php }?>
													<?php }?>
												</div>
											</li>
										<?php
										$i++;
										$t++;
										
										endwhile;
									endif;
									wp_reset_postdata();?>
									<li id="target_5">
										<a href="/print-content.php?print=<?php echo $head_postid; ?>" target="_blank">
											<div class="head">
												<div class="images">
													<img alt=""
														data-src="https://www.swedac.se/wp-content/uploads/2016/04/icon-print-white.svg">
												</div>
												<h2>Skriv ut material</h2>
											</div>
										</a>
									</li>
								</ul>
							<?php
							} else {
							} ?>
						</footer>
					</article>
				<?php endwhile;?>
			</div>

			<?php if (have_rows('working_area_tabel', $head_postid)): ?>
				<div id="working_area_tabel">
					<?php while (have_rows('working_area_tabel', $head_postid)): the_row();?>
						<div class="working_info">
							<?php echo get_sub_field('beskrivning');?>
						</div>
						<div class="table_wrapper">
							<table>
								<thead>
									<tr>
										<th><?php echo get_sub_field('foretag');?></th>
										<th><?php echo get_sub_field('adress');?></th>
										<th><?php echo get_sub_field('omraden');?></th>
										<th><?php echo get_sub_field('verksamt');?></th>
										<th><?php echo get_sub_field('giltigt');?></th>
										<th><?php echo get_sub_field('anm');?></th>
									</tr>
								</thead>
								<?php if (have_rows('tabell_rader')): ?>
									<tbody>
										<?php while (have_rows('tabell_rader')): the_row();?>
											<tr>
												<td><?php echo get_sub_field('foretag');?></td>
												<td><?php echo get_sub_field('adress');?></td>
												<td><a href="<?php echo get_sub_field('lagg_till_omfattningsbilaga');?>"
														title="Se omfattningsbilaga för <?php echo wp_strip_all_tags(get_sub_field('foretag')); ?>"
														target="_blank">Se omfattningsbilaga</a></td>
												<td><?php echo get_sub_field('verksamt_i');?></td>
												<td><?php echo get_sub_field('giltig');?></td>
												<td><?php echo get_sub_field('anm');?></td>
											</tr>
										<?php endwhile;?>
									</tbody>
								<?php endif;?>
							</table>
						</div>
					<?php endwhile;?>
				</div>
			<?php endif;?>
		</div>
	</div>
<?php elseif (ICL_LANGUAGE_CODE == 'en'): ?>
	<div class="wrapper">
		<div class="single_section_post_amnesomrade">
			<div class="sidebar">
				<?php get_template_part('templates/sidebar-amnesomraden', 'page');?>
			</div>
			<div class="main">
				<?php while (have_posts()): the_post();
					global $wp_query;
					$head_postid = get_the_ID();
					$bevaka_innehall = get_field('bevaka_innehall_en', $head_postid);
					$head_title = get_the_title($head_postid); ?>

					<article <?php post_class('single-post');?> aria-label="<?php echo get_the_title(); ?>">
						<header class="post-header">
							<?php if (isset($_GET['success_subscribing'])) {?>
								<div class="header_message">
									<div class="wrapper">
										<div class="inner success">
											<span>Now you are on the list for monitoring of,
												<strong><?php the_title();?></strong>. When something changes here, you will
												receive an email.</span>
										</div>
									</div>
								</div>
							<?php }?>
							<?php if (isset($_GET['failed_subscribing'])) {?>
								<div class="header_message">
									<div class="wrapper">
										<div class="inner failed">
											<span>Your are already on the list for monitoring of this area.</span>
										</div>
									</div>
								</div>
							<?php }?>
							<h1><?php the_title();?></h1>
							<div class="ingress">
								<?php echo get_field('page_ingress_subpage_en');?>
							</div>
							<?php if (get_field('page_ankarlankar_en')): ?>
								<div class="page_ankarlankar">
									<h2 class="nav-header">Quick Navigation</h2>
									<ul>
										<?php while (has_sub_field('page_ankarlankar_en')): ?>
										<li>
											<a href="<?php echo get_sub_field('lank');?>"
												title="Go to <?php echo get_sub_field('namn');?>">
												<?php echo get_sub_field('namn');?>
											</a>
										</li>
										<?php endwhile;?>
									</ul>
								</div>
							<?php endif;?>
							<?php if (has_post_thumbnail()):
								$get_post_image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'large');
								$image_url = $get_post_image['0'];
								$img_id = get_post_thumbnail_id(get_the_ID());
								$alt_text = get_post_meta($img_id , '_wp_attachment_image_alt', true); ?>
								<div class="page_thumbnail">
									<img data-src="<?php echo $image_url; ?>" alt="<?php echo $alt_text; ?>" />
								</div>
							<?php endif;
							wp_reset_query();?>
						</header>

						<div class="post-content">
							<?php the_content();?>
						</div>

						<footer class="post-footer">
							<?php $ids = get_field('show_links_on_amnesomrade_en', false, false);

							if (!empty($ids)) {
								$args = array(
									'post_type' => 'intern_lankningar',
									'posts_per_page' => 8,
									'post__in' => $ids,
									'orderby' => 'date',
									'order' => 'asc',
								);

								$query = new WP_Query($args);
								$firstpost = true;
								if ($query->have_posts()): ?>
									<ul id="spec_links">
										<?php
										$i = 1;
										$t = 1;
										$t = 1;
										while ($query->have_posts()): $query->the_post();
											if(get_the_title() !== 'Bevaka innehåll'){
												$post_slug = $post->post_name;
												$rel_postid = get_the_ID();?>
												<li id="target_<?php echo $t; ?>">
													<div class="head paragraph_start" data-paragraph="<?php echo $i; ?>"
														data-toggle="<?php echo $i; ?>">
														<div class="images">
															<img alt="" data-src="<?php echo get_field('lankar_ikon_white_en');?>">
														</div>
														<h2><?php the_title();?></h2>
													</div>
													<!-- // Om rel_postid = utskrift / Annars -->
													<?php if ($rel_postid == 9849) {?>
													<?php } else { ?>
														<span class="r_more" data-toggle="<?php echo $i; ?>">
															<div class="plus"><img alt=""
																data-src="<?php echo get_template_directory_uri(); ?>/assets/images/svg/plus-white.svg">
															</div>
															<div class="minus"><img alt=""
																data-src="<?php echo get_template_directory_uri(); ?>/assets/images/svg/minus-white.svg">
															</div>
														</span>
													<?php }?>

													<div class="hidden_content" data-more="<?php echo $i; ?>">
														<!-- Dokument -->
														<?php if ($rel_postid == 9845) {?>
															<div id="document_crm"></div>
														<?php }?>

														<!-- Externa länkar -->
														<?php if ($rel_postid == 9846) {?>
															<div class="externa_lankar_en">
																<?php if (get_field('externa_lankar_en', $head_postid)): ?>
																	<ul>
																		<?php while (has_sub_field('externa_lankar_en', $head_postid)): ?>
																		<li>
																			<a href="<?php echo get_sub_field('lankmal');?>" target="_blank"><?php echo get_sub_field('lanknamn');?></a>
																		</li>
																		<?php endwhile;?>
																	</ul>
																<?php endif;?>
															</div>
														<?php }?>

														<!-- Relaterad information -->
														<?php if ($rel_postid == 9847) {?>
															<div id="relaterad_information" class="relaterad_information">
																<?php $pairedPosts = get_field('relaterad_information_en', $workingAreaID);
																if ($pairedPosts): ?>
																	<ul>
																		<?php foreach ($pairedPosts as $pairedPost): ?>
																			<li>
																				<a href="<?=get_the_permalink($pairedPost->ID);?>"><?=get_the_title($pairedPost->ID);?></a>
																			</li>
																		<?php endforeach;?>
																	</ul>
																<?php endif;?>
																<?php wp_reset_query();?>
															</div>
														<?php }?>

														<!-- Bevaka innehåll -->
														<?php if ($rel_postid == 9848) {
															if ($bevaka_innehall == true) { ?>
																<div class="starta_bevakning">
																	<span>
																		When we update the content on this page you will receive a notification
																		sent to the email address you entered.
																	</span>
																	<form id="monitor_content" class="form clear" name="monitor_content" method="post" action="/subscribers.php?add_subscriber" novalidate="novalidate" autocomplete="false">
																		<div class="row name" style="margin-inline: 0 !important">
																			<input class="req" type="text" name="name" placeholder="Name *" title="Name">
																			<i class="fa fa-check"></i>
																			<i class="fa fa-times"></i>
																		</div>
																		<div class="row email" style="margin-inline: 0 !important">
																			<input class="req" type="email" name="email" placeholder="Email *" title="Email">
																			<i class="fa fa-check"></i>
																			<i class="fa fa-times"></i>
																		</div>
																		<input type="hidden" value="<?php echo $head_postid; ?>" name="post_id" />
																		<input type="hidden" name="url" value="<?php echo strtok($_SERVER["REQUEST_URI"], '?'); ?>" />
																		<input type="submit" class="submit_bevakning" value="Monitor" title="Monitor">
																	</form>
																</div>
															<?php }
														} ?>
													</div>
												</li>
											<?php
											$i++;
											$t++;
											}
										endwhile;

										wp_reset_postdata(); ?>
										<li id="target_5">
											<a href="/print-content.php?print=<?php echo $head_postid; ?>" target="_blank">
												<div class="head">
													<div class="images">
														<img alt=""
															data-src="https://www.swedac.se/wp-content/uploads/2016/04/icon-print-white.svg">
													</div>
													<h2>Print</h2>
												</div>
											</a>
										</li>
									</ul>
								<?php endif;
							} else { ?>
								<ul id="spec_links">
									<li id="target_5">
										<a href="/print-content.php?print=<?php echo $head_postid; ?>" target="_blank">
											<div class="head">
												<div class="images">
													<img alt=""
														data-src="https://www.swedac.se/wp-content/uploads/2016/04/icon-print-white.svg">
												</div>
												<h2>Print</h2>
											</div>
										</a>
									</li>
								</ul>
							<?php } ?>
						</footer>
					</article>
				<?php endwhile;?>
				<div class="relaterad_information">
					<?php $inner_args = get_posts(array(
						'status' => 'publish',
						'post_type' => 'news',
						'posts_per_page' => 10,
						'orderby' => 'date',
						'order' => 'asc',
						'meta_query' => array(
							array(
								'key' => 'koppling_till_amnesomrade_en', // or whatever your custom post type field is called
								'value' => '"' . get_the_ID() . '"', // will work only if the main loop has started
								'compare' => 'LIKE',
							),
						),
					));
					if ($inner_args): ?>
						<ul>
							<?php foreach ($inner_args as $inner_arg): ?>
								<li>
									<a href="<?php echo get_the_permalink($inner_arg->ID); ?>"><?php echo get_the_title($inner_arg->ID); ?></a>
								</li>
							<?php endforeach;?>
						</ul>
					<?php endif;?>
					<?php wp_reset_query();?>
				</div>

				<div class="document_crm">
					<?php function my_posts_where($where) {
						global $wpdb;
						$where = str_replace("meta_key = 'crm_doc_area_en_%", "meta_key LIKE 'crm_doc_area_en_%", $wpdb->remove_placeholder_escape($where));
						return $where;
					}
					add_filter('posts_where', 'my_posts_where');

					$rows_array = array();

					$args = array(
						'status' => 'publish',
						'post_type' => 'document_eng',
						'posts_per_page' => -1,
						'orderby' => 'date',
						'order' => 'asc',
						'meta_query' => array(
							// 'relation' => 'OR', //Om fler fält ska användas
							array(
								'key' => 'crm_doc_area_en_%_omradesnamn',
								'value' => $head_title,
								'compare' => 'LIKE',
							),
						),
					);

					$posts = get_posts($args);

					//Skapar tommar arrayer för att använda senare
					$array_dokument = array();
					$array_rubriker = array();

					// Skapar en array med alla rubriker från CRM
					$rows = get_field('rubriker_fran_crm_en', 'option');
					if(!empty($rows)){
						for ($i = 0; $i < count($rows); $i++) {
							$array_rubriker[] = $rows[$i]['rubrik'];
						}
					}

					foreach ($array_rubriker as $crm_rubrik) { //För varje rubrik så kör vi en Foreach
						$print = false;
						echo "<div class='doc_section'><ul>";

						foreach ($posts as $post) { //För dokument så gör vi en koll efter matchande rubriker
							if (get_field('crm_doc_area_en', $post->ID)): // Hämtar alla områden
								while (has_sub_field('crm_doc_area_en', $post->ID)):
									$heading = get_sub_field('heading', $post->ID);
									$area = get_sub_field('omradesnamn', $post->ID);
									if ($heading == $crm_rubrik && $area == $head_title) { ?>
										<li>
											<?php if ($print == false) { ?>
												<strong><?php echo $crm_rubrik; ?></strong>
												<?php $print = true;
											}
											if (get_field('crm_doc_doc_en', $post->ID)) { ?>
												<a href="<?php the_permalink();?>">
													<?php echo get_field('crm_doc_dokumentbeteckning_en', $post->ID); ?>,
													<?php the_title();?>
												</a>
											<?php } else {?>
												<span>
													<?php echo get_field('crm_doc_dokumentbeteckning_en', $post->ID);?>,
													<?php the_title();?>
												</span>
											<?php }?>
										</li>
										<?php break;}
								endwhile;
							endif;
						}
						echo "</ul></div>";
					} ?>
				</div>
			</div>
			<?php if (have_rows('working_area_tabel_en', $head_postid)): ?>
				<div id="working_area_tabel">
					<?php while (have_rows('working_area_tabel_en', $head_postid)): the_row();?>
						<div class="working_info">
							<?php echo get_sub_field('beskrivning');?>
						</div>
						<div class="table_wrapper">
							<table>
								<thead>
									<tr>
										<th><?php echo get_sub_field('foretag');?></th>
										<th><?php echo get_sub_field('adress');?></th>
										<th><?php echo get_sub_field('omraden');?></th>
										<th><?php echo get_sub_field('verksamt_i');?></th>
										<th><?php echo get_sub_field('giltigt');?></th>
										<th><?php echo get_sub_field('anm');?></th>
									</tr>
								</thead>
								<?php if (have_rows('tabell_rader_en')): ?>
									<tbody>
										<?php while (have_rows('tabell_rader_en')): the_row();?>
										<tr>
											<td><?php echo get_sub_field('foretag');?></td>
											<td><?php echo get_sub_field('adress');?></td>
											<td><a href="<?php echo get_sub_field('lagg_till_omfattningsbilaga');?>"
													title="See accreditation scope appendix for <?php echo wp_strip_all_tags(get_sub_field('foretag')); ?>"
													target="_blank">See accreditation scope appendix</a></td>
											<td><?php echo get_sub_field('verksamt_i');?></td>
											<td><?php echo get_sub_field('giltig');?></td>
											<td><?php echo get_sub_field('anm');?></td>
										</tr>
										<?php endwhile;?>
									</tbody>
								<?php endif;?>
							</table>
						</div>
					<?php endwhile;?>
				</div>
			<?php endif;?>
		</div>
	</div>
<?php endif;?>
