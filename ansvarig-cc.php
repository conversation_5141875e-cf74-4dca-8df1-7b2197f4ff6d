<?php
include("wp-load.php");

// CYBERCOM 2021
$responsible_post = get_posts(
  array(
    'numberposts' => -1,
    'post_type'   => 'ansvarigperson',
    'post_status' => array('published'),
  )
 );

 $name  = '';
 $email = '';
 $responsible_url = '';

foreach( $responsible_post as $person ) {

	$person_id = $person->ID;

  $name  = get_field('namn_ansvarig', $person_id);
  $email = get_field('e_post_ansvarig', $person_id);
  $urls  = get_field('ansvarig_for_sida', $person_id);

  foreach ($urls as $url) {

		$link = $url;

		$url = apply_filters( 'url_to_postid', $url );
    $url_split = explode( '?page_id=', $url );
		$url = $url_split[1];
		$page_resps = get_the_title( $url );

		echo $responsible_url .= '<li><a href="'. esc_url( $link ) .'" title="'. esc_html( $page_title ) .'">'. esc_html( $page_resps  ) .'</a></li>';

	}
  // CYBERCOM 2021

  //Skapar mailet
	//********************************************************

	$site_url = '/ansvarig-cc.php';
	//BOUNDARY (DON'T TOUCH)
	$boundary_ansvarig = uniqid('np');


	if ( $site_url ) {
		//-----------------------------------------
		//  Mail till ansvarig
		//-----------------------------------------
		//SET AN ID FOR FORM

		//TITLE IN MAIL
		$sub_ansvarig = "Kontrollera sidor | swedac.se";

		//SHORT EXCERPT VIEWED IN EG. GMAIL
		$excerpt = 'Dags att kontrollera innehåll på swedac.se';

		//MAIN MESSAGE IN MAIL
		$message = '
			<p>Hej! <br/>
			Nu är det dags att gå igenom innehållet på följande sidor och kontrollera att allt stämmer.</p>

			<b>Du ansvarar för följande sidor:</b>
			<ul style="margin:5px 0 15px 25px;">
			'.$correct_url.'
			</ul>

			<p>Om något behöver uppdateras så kontaktar du <a href="mailto:<EMAIL>"><EMAIL></a><br>
			Återkoppla till <a href="mailto:<EMAIL>"><EMAIL></a> oavsett om innehållet är korrekt eller behöver uppdateras.</p>

			<small>Om dina sidor har en engelsk version ska denna kontrolleras också.</small>

			<small>Du får detta meddelande eftersom du är satt som ansvarig för dessa sidor.</small>
		';

		//SENDERS EMAIL
		$from = '<EMAIL>';

		//SENDERS NAME
    $from_name = 'Swedac';

		//Till ansvariga §email
    wp_mail($email, subject_ansvarig($sub_ansvarig), html_mail_ansvarig($sub_ansvarig,$message,$excerpt));
		$responsible_url = ''; // empty the array with urls after each mail sent
	}

}

function subject_ansvarig($sub_ansvarig){

	$subject_ansvarig = mb_encode_mimeheader(utf8_decode($sub_ansvarig), 'UTF-8', 'B');

	return $subject_ansvarig;
}

function headers_ansvarig($from, $from_name){

	global $boundary_ansvarig;

	$headers_ansvarig = "MIME-Version: 1.0\r\n";
	$headers_ansvarig .= "From: ".$from_name." <" . $from . ">\r\n";
	// $headers_ansvarig .= "Errors-To: ". $from . "\r\n";
	// $headers_ansvarig .= "Return-Path: ". $from . "\r\n";
	// $headers_ansvarig .= "Message-ID: <" . time() . "." . $from . ">\r\n";
  // $headers_ansvarig .= "Content-Type: multipart/alternative;boundary=" . $boundary_ansvarig . "\r\n";
  // $headers_ansvarig .= "This is a multi-part message in MIME format.\r\n";

	return $headers_ansvarig;
}


function html_mail_ansvarig($sub_ansvarig,$message,$excerpt) {

	global $boundary_ansvarig;

	$plain_text = strip_tags($message);

	$html_mail_ansvarig .= '<!DOCTYPE html>
	<html lang="en">
	<head>
	  <meta charset="utf-8"> <!-- utf-8 works for most cases -->
		<meta name="viewport" content="width=device-width"> <!-- Forcing initial-scale shouldnt be necessary -->
		<meta http-equiv="X-UA-Compatible" content="IE=edge"> <!-- Use the latest (edge) version of IE rendering engine -->
	  <title>'.$sub.'</title> <!-- the <title> tag shows on email notifications on Android 4.4. -->
	</head>
	<body leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" bgcolor="#f4f4f4" style="margin:0; padding:0; -webkit-text-size-adjust:none; -ms-text-size-adjust:none;">
	<table cellpadding="0" cellspacing="0" border="0" height="100%" width="100%" bgcolor="#f4f4f4" id="bodyTable" style="border-collapse: collapse;table-layout: fixed;margin:0 auto;"><tr><td>

		<!-- Hidden Preheader Text : BEGIN -->
		<div style="display:none; visibility:hidden; opacity:0; color:transparent; height:0; width:0;line-height:0; overflow:hidden;mso-hide: all;">
			'.$excerpt.'
		</div>
		<!-- Hidden Preheader Text : END -->

	  <!-- Outlook and Lotus Notes dont support max-width but are always on desktop, so we can enforce a wide, fixed width view. -->
	  <!-- Beginning of Outlook-specific wrapper : BEGIN -->
		<!--[if (gte mso 9)|(IE)]>
	  <table width="800px" align="center" cellpadding="0" cellspacing="0" border="0">
	    <tr>
	      <td>
	  <![endif]-->
	  <!-- Beginning of Outlook-specific wrapper : END -->

	  <!-- Email wrapper : BEGIN -->
	  <table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="max-width:800px;margin: auto;" class="email-container">
	  	<tr>
	      <td style="text-align: center;padding: 40px 0;font-family: sans-serif; font-size: 16px; line-height: 24px;color: #888888;">
	        <strong>'.$sub.'</strong><br>
	      </td>
	    </tr>
	    <tr>
	    	<td>

	        <table border="0" width="100%" cellpadding="0" cellspacing="0" bgcolor="#ffffff">

	          <!-- Full Width, Fluid Column : BEGIN -->
	          <tr>
	            <td style="padding: 40px; font-family: sans-serif; font-size: 16px; line-height: 24px; color: #666666;">
	              '.$message.'
	            </td>
	          </tr>


				</td>
	      	</tr>
	          <!-- Full Width, Fluid Column : END -->

	        </table>
	      </td>
			</tr>

	    <!-- Footer : BEGIN -->
	    <tr>
	      <td style="text-align: center;padding: 40px 0;font-family: sans-serif; font-size: 11px; line-height: 18px;color: #888888;">
	        <img style="width:200px; height:37px;" src="https://www.swedac.se/wp-content/themes/swedac_theme/assets/images/swedac-logotype.png" alt="Swedac Logotyp" />


	      </td>
	    </tr>
	    <!-- Footer : END -->

	  </table>
	  <!-- Email wrapper : END -->

	  <!-- End of Outlook-specific wrapper : BEGIN -->
		<!--[if (gte mso 9)|(IE)]>
	      </td>
	    </tr>
	  </table>
	  <![endif]-->
	  <!-- End of Outlook-specific wrapper : END -->

	</td></tr></table>
	</body>
	</html>';

	return $html_mail_ansvarig;

}
