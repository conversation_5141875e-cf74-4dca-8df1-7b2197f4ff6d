<?php

/**
 * Template Name: Template 4 - Sökningen
 */

$searchUrl = 'https://' . $_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI'];
$searchUrlResults = 'https://' . $_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI'];
$search_term = get_search_query();
$search_term_form = get_search_query();
$search_term_form = preg_replace('/\s+/', '+', $search_term_form);
	if (strpos($searchUrl,'&site_section=document_pt') !== false) {
		$fritext = isset($_GET['fritext']) ? $_GET['fritext'] : '';
		$doc_nr = isset($_GET['s']) ? $_GET['s'] : '';
		$omraden = isset($_GET['omraden']) ? $_GET['omraden'] : '';
		$doc_type = isset($_GET['document_type']) ? $_GET['document_type'] : '';

		// Hämta in dokumentsökningen
		include('searchFiles/documentSearch.php');
	}elseif(strpos($searchUrl,'&site_section=certificate_pt') !== false){
		include('searchFiles/certificateSearch.php');
	}
	else {?>

		<input id="search_term_result" type="hidden" value="<?php echo get_search_query(); ?>">


		<div id="toggleToResults">
			<div class="wrapper">
				<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
					<?php if(strpos($searchUrl,'swedac_se=on') !== false) { ?>
						<div class="toggleButton">

							<a href="#swedacResults">
								<span id="getPagePostResults"></span>
								Swedac.se
							</a>
						</div>
					<?php } ?>
					<?php if(strpos($searchUrl,'foreskrifter_dokument=on') !== false) { ?>
						<div class="toggleButton">
							<a href="#documentResults">
								<span id="getDocumentResults"></span>
								Föreskrifter & Dokument
							</a>
						</div>
					<?php } ?>
					<?php if(strpos($searchUrl,'namnstampelregistret=on') !== false) { ?>
						<div class="toggleButton">
							<a href="#namnstampelregistretResults">
								<span id="getNamnstampResults"></span>
								Namnstämpelregistret
							</a>
						</div>
					<?php } ?>
					<?php if(strpos($searchUrl,'ackrediterande_organ=on') !== false) { ?>
						<div class="toggleButton">
							<a href="#ackrediteradeorganResults">
								<span id="getAckrediteradeOrganResults"></span>
								Ackrediterade organ
							</a>
						</div>
					<?php } ?>
					<?php if(strpos($searchUrl,'certifikat_godkannanden=on') !== false) { ?>
						<div class="toggleButton">
							<a href="#certificateResult">
								<span id="getCertificateResults"></span>
								<?php _e('Certifikat och Godkännanden', 'certificates');?>
							</a>
						</div>
					<?php } ?>
				<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
					<?php if(strpos($searchUrl,'swedac_se=on') !== false) { ?>
						<div class="toggleButton">

							<a href="#swedacResults">
								<span id="getPagePostResults"></span>
								Swedac.se
							</a>
						</div>
					<?php } ?>
					<?php if(strpos($searchUrl,'foreskrifter_dokument=on') !== false) { ?>
						<div class="toggleButton">
							<a href="#documentResults">
								<span id="getDocumentResults"></span>
								Regulations & Documents
							</a>
						</div>
					<?php }
					if(strpos($searchUrl,'namnstampelregistret=on') !== false) { ?>
						<div class="toggleButton">
							<a href="#namnstampelregistretResults">
								<span id="getNamnstampResults"></span>
								Stamps register
							</a>
						</div>
					<?php }
						if(strpos($searchUrl,'ackrediterande_organ=on') !== false) { ?>
						<div class="toggleButton">
							<a href="#ackrediteradeorganResults">
								<span id="getAckrediteradeOrganResults"></span>
								Accreditation register
							</a>
						</div>
					<?php } ?>
				<?php endif; ?>


			</div>
		</div>

		<?php // Hämta in söklog från databas
			include('searchFiles/searchlog-get-db.php');
		?>

		<?php
		if (strpos($searchUrl,'swedac_se=on') !== false) {
			// Sökning på Swedac.se
			$searchTemplate = 'searchTemplates/sv_swedac.php';
			if (ICL_LANGUAGE_CODE == 'en') {
				$searchTemplate = 'searchTemplates/en_swedac.php';
			}
			include $searchTemplate;
		}
		if (strpos($searchUrl,'foreskrifter_dokument=on') !== false) {
			// Sökning på Dokument -->
			$documentTemplate = 'searchTemplates/sv_light_document.php';
		  	if (ICL_LANGUAGE_CODE == 'en') {
				$documentTemplate = 'searchTemplates/en_light_document.php';
		  	}
		  	include $documentTemplate;
		}
		if (strpos($searchUrl,'certifikat_godkannanden=on') !== false) {
			// Sökning på Certifikat -->
			$certificateTemplate = 'searchTemplates/light_certificates.php';
		  	include $certificateTemplate;
		}
		if ((strpos($searchUrl,'post_type=empty') !== false) && (strpos($searchUrl,'ackrediterande_organ=on') !== false) && (strpos($searchUrl,'namnstampelregistret=on') !== false)) { ?>

		<?php } elseif ((strpos($searchUrl,'post_type=empty') == true) && (strpos($searchUrl,'ackrediterande_organ=on') == false) && (strpos($searchUrl,'namnstampelregistret=on') == false) && (strpos($searchUrl,'foreskrifter_dokument=on') == false)) { ?>

				<div class="wrapper">
					<div class="alert alert-warning">

						<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
							<h2>Inget hittades</h2>
							<em><?php echo sprintf( __( 'Sökordet '), $wp_query->found_posts ); echo '<span class="term">' . get_search_query() .'</span> gav dessvärre ingen träff pga att ingen sökkälla valdes vid sökningen.'; ?></em>
							<p>Välj en sökkälla och gör ett nytt försök.</p>
						<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
							<h2>Nothing found</h2>
							<em><?php echo sprintf( __( 'The keyword '), $wp_query->found_posts ); echo '<span class="term">' . get_search_query() .'</span> gave unfortunately no hit because no search source was selected at the search.'; ?></em>
							<p>Välj en sökkälla och gör ett nytt försök.</p>
						<?php endif;?>
					</div>
				</div>

		<?php } else { ?>

		<?php } ?>

	<?php } ?>