<?php

use Roots\Sage\Setup;
use Roots\Sage\Wrapper;

?>

<!doctype html>
<html <?php language_attributes(); ?>>
    <?php get_template_part('templates/head'); ?>
    <body <?php body_class(); ?>>
    <!--[if IE]>
        <div class="alert-ie-appeared">
            <?php _e('Du använder <strong>Internet Explorer</strong> som webbläsare. Internet Explorer har från och med januari 2016 slutat få säkerhetsuppdateringar utav Microsoft Corporation. Så för att uppnå den bästa upplevelsen av vår webbplats, var god uppdatera till en annan <a href="http://browsehappy.com/" target="_blank">webbläsare</a>.'); ?>
        </div>
    <![endif]-->
    <div id="main_wrapper">
        <div class="" role="document">
            <?php //do_action('get_header'); get_template_part('templates/header');?>

			<?php  $url = 'https://' . $_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI'];?>

			<?php if (strpos($url,'&site_section=document_pt') !== false || strpos($url,'&site_section=certificate_pt') !== false) { ?>

	            <div id="page-header-parent">
					<div class="title">
						<div class="wrapper">
							<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
								<span class="title-page">Sökresultat</span>
							<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
								<span class="title-page">Search result</span>
							<?php endif;?>
						</div>
					</div>
				</div>


	            <div class="content row">
	                <main class="main">
	                    <?php include('page-template-4-search.php'); ?>
	                </main>

	                <?php if (Setup\display_sidebar()) : ?>
	                    <aside class="sidebar">
	                        <?php include Wrapper\sidebar_path(); ?>
	                    </aside>
	                <?php endif; ?>
	            </div>

			<?php } else { ?>
	            <div id="page-header-parent">
					<div class="title">
						<div class="wrapper">
							<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
								<span class="title-page">Sökresultat</span>
							<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
								<span class="title-page">Search result</span>
							<?php endif;?>
						</div>
					</div>
					<div id="search_form">

						<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
							<div class="wrapper">
								<form action="<?php echo get_site_url(); ?>" method="get" id="search-form" accept-charset="UTF-8">
									<div class="main_serach">
										<input pattern=".{3,}" required title="En sökning bör innehålla minst 3 tecken" type="search" class="search-field" placeholder="<?php if(get_search_query()) { echo sprintf( __( 'Du sökte på '), $wp_query->found_posts ); echo get_search_query(); } else { echo 'Sök...'; } ?>" value="" name="s" id="search-page-input">
										<button type="submit" class="search-submit"><svg fill="#fff" height="20" viewBox="0 0 461.516 461.516" width="20"><path d="m185.746 371.332c41.251.001 81.322-13.762 113.866-39.11l122.778 122.778c9.172 8.858 23.787 8.604 32.645-.568 8.641-8.947 8.641-23.131 0-32.077l-122.778-122.778c62.899-80.968 48.252-197.595-32.716-260.494s-197.594-48.252-260.493 32.716-48.252 197.595 32.716 260.494c32.597 25.323 72.704 39.06 113.982 39.039zm-98.651-284.273c54.484-54.485 142.82-54.486 197.305-.002s54.486 142.82.002 197.305-142.82 54.486-197.305.002c-.001-.001-.001-.001-.002-.002-54.484-54.087-54.805-142.101-.718-196.585.239-.24.478-.479.718-.718z"></path></svg>Sök</button>
									</div>

									<div class="adv_search">
										<ul id="mainmenu">
											<li><span class="toggle-adv">Avancerad sökning <i class="fa fa-angle-down" aria-hidden="true"></i></span>
												<ul id="submenu">
													<li><a href="https://www.swedac.se/lag-ratt/swedacs-foreskrifter/sok-foreskrifter-och-dokument/" target="_blank">Föreskrifter & Dokument</a></li>
													<li><a href="https://swedac.se/tjanster/reglerad-matteknik/sok-certifikat-och-godkannanden/" target="_blank"><?php _e('Certifikat och Godkännanden', 'certificates');?></a></li>
													<li><a href="http://search.swedac.se/sv/ackrediteringar" target="_blank">Ackrediterade organ</a></li>
													<li><a href="http://search.swedac.se/sv/namnstamplar" target="_blank">Namnstämpelregistret</a></li>
												</ul>
											</li>
										</ul>
									</div>
									<div id="sokkallor">
										<label for="alla_sokkallor" class="alt">
											<input class="alla_sokkallor" type="checkbox" name="alla_sokkallor" id="alla_sokkallor">
											Alla sökkällor
										</label>
										<label for="swedac_se" class="alt">
											<input class="search_alt" type="checkbox" name="swedac_se" id="swedac_se">
											<span>Swedac.se</span>
										</label>
										<label for="foreskrifter_dokument" class="alt">
											<input class="search_alt" type="checkbox" name="foreskrifter_dokument" id="foreskrifter_dokument">
											<span>Föreskrifter & Dokument</span>
										</label>
										<label for="namnstampelregistret" class="alt">
											<input class="search_alt" type="checkbox" name="namnstampelregistret" id="namnstampelregistret">
											<span>Namnstämpelregistret</span>
										</label>
										<label for="ackrediterande_organ" class="alt">
											<input class="search_alt" type="checkbox" name="ackrediterande_organ" id="ackrediterande_organ">
											<span>Ackrediterade organ</span>
										</label>
										<label for="certifikat_godkannanden" class="alt">
											<input class="search_alt" type="checkbox" name="certifikat_godkannanden" id="certifikat_godkannanden">
											<span><?php _e('Certifikat och Godkännanden', 'certificates');?></span>
										</label>
									</div>
									<input type="hidden" name="lang" value="sv">
									<input class="intern_sok_empty" type="hidden" value="empty" name="post_type" id="post_type" disabled/>

									<input class="intern_sok" type="hidden" value="post" name="post_type_post" id="post_type_post" />
									<input class="intern_sok" type="hidden" value="amnesomraden" name="post_type_amnesomraden" id="post_type_amnesomraden" />
									<input class="intern_sok" type="hidden" value="kalender" name="post_type_kalender" id="post_type_kalender" />
									<input class="intern_sok" type="hidden" value="kurser" name="post_type_kurser" id="post_type_kurser" />
									<input class="intern_sok" type="hidden" value="attachment" name="post_type_attachment" id="post_type_attachment" />
									<input class="intern_sok" type="hidden" value="swedac_magasin" name="post_type_swedac_magasin" id="post_type_swedac_magasin" />
									<input class="intern_sok" type="hidden" value="page" name="post_type_page" id="post_type_page" />

									<input class="intern_sok_dokument" type="hidden" value="dokument" name="post_type_dokument" id="post_type_dokument" />
									<input class="intern_sok_certifikat" type="hidden" value="certifikat" name="post_type_certifikat" id="post_type_certifikat" />

									<input class="intern_sok_dokument_only" type="hidden" value="dokument" name="post_type" id="post_type_dokument_only" disabled/>
								</form>

							</div>
						</div>
				</div>
						<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
							<div class="wrapper">
								<form action="<?php echo get_site_url(); ?>/?lang=en" method="get" id="search-form" accept-charset="UTF-8">
									<div class="main_serach">
										<input pattern=".{3,}" required title="A search should contain at least 3 characters" type="search" class="search-field" placeholder="<?php if(get_search_query()) { echo sprintf( __( 'You search on '), $wp_query->found_posts ); echo get_search_query(); } else { echo 'Search...'; } ?>" value="" name="s" id="search-page-input">
										<button type="submit" class="search-submit"><svg fill="#fff" height="20" viewBox="0 0 461.516 461.516" width="20"><path d="m185.746 371.332c41.251.001 81.322-13.762 113.866-39.11l122.778 122.778c9.172 8.858 23.787 8.604 32.645-.568 8.641-8.947 8.641-23.131 0-32.077l-122.778-122.778c62.899-80.968 48.252-197.595-32.716-260.494s-197.594-48.252-260.493 32.716-48.252 197.595 32.716 260.494c32.597 25.323 72.704 39.06 113.982 39.039zm-98.651-284.273c54.484-54.485 142.82-54.486 197.305-.002s54.486 142.82.002 197.305-142.82 54.486-197.305.002c-.001-.001-.001-.001-.002-.002-54.484-54.087-54.805-142.101-.718-196.585.239-.24.478-.479.718-.718z"></path></svg>Search</button>
									</div>

									<div class="adv_search">
										<ul id="mainmenu">
											<li><span class="toggle-adv">Advanced search <i class="fa fa-angle-down" aria-hidden="true"></i></span>
												<ul id="submenu">
													<li><a href="https://www.swedac.se/law-order/swedacs-regulations/search-regulations-documents/?lang=en" target="_blank">Regulations & Document</a></li>
													<li><a href="http://search.swedac.se/sv/ackrediteringar" target="_blank">Accreditation register</a></li>
													<li><a href="http://search.swedac.se/sv/namnstamplar" target="_blank">Stamps register</a></li>
												</ul>
											</li>
										</ul>
									</div>
    								<div id="sokkallor">
    									<label for="alla_sokkallor" class="alt">
    										<input class="alla_sokkallor" type="checkbox" name="alla_sokkallor" id="alla_sokkallor" >
    										All sources
    									</label>
    									<label for="swedac_se" class="alt">
    										<input class="search_alt" type="checkbox" name="swedac_se" id="swedac_se" >
    										<span>Swedac.se</span>
    									</label>
    									<label for="foreskrifter_dokument" class="alt">
    										<input class="search_alt" type="checkbox" name="foreskrifter_dokument" id="foreskrifter_dokument" >
    										<span>Regulations & Documents</span>
    									</label>
    									<label for="namnstampelregistret" class="alt">
    										<input class="search_alt" type="checkbox" name="namnstampelregistret" id="namnstampelregistret" >
    										<span>Stamps register</span>
    									</label>
    									<label for="ackrediterande_organ" class="alt">
    										<input class="search_alt" type="checkbox" name="ackrediterande_organ" id="ackrediterande_organ" >
    										<span>Accreditation register</span>
    									</label>
    								</div>
    								<input type="hidden" name="lang" value="en">
    								<input class="intern_sok_empty" type="hidden" value="empty" name="post_type" id="post_type" disabled/>

    								<input class="intern_sok" type="hidden" value="post" name="post_type_post" id="post_type_post" />
    								<input class="intern_sok" type="hidden" value="amnesomraden" name="post_type_amnesomraden" id="post_type_amnesomraden" />
    								<input class="intern_sok" type="hidden" value="kalender" name="post_type_kalender" id="post_type_kalender" />
    								<input class="intern_sok" type="hidden" value="kurser" name="post_type_kurser" id="post_type_kurser" />
                                    <input class="intern_sok" type="hidden" value="attachment" name="post_type_attachment" id="post_type_attachment" />
    								<input class="intern_sok" type="hidden" value="swedac_magasin" name="post_type_swedac_magasin" id="post_type_swedac_magasin" />
    								<input class="intern_sok" type="hidden" value="page" name="post_type_page" id="post_type_page" />

    								<input class="intern_sok_dokument" type="hidden" value="dokument" name="post_type_dokument" id="post_type_dokument" />
									<input class="intern_sok_certifikat" type="hidden" value="certifikat" name="post_type_certifikat" id="post_type_certifikat" />

    								<input class="intern_sok_dokument_only" type="hidden" value="dockument_eng" name="post_type" id="post_type_dokument_only" disabled/>
    							</form>

    						</div>
    					</div>
    			        </div>
						<?php endif;?>
				            <div class="content row">
				                <main class="main">
				                    <?php include('page-template-4-search.php'); ?>

				                    <?php
				                         // Swedac integration include, inkluderar diverse klasser.
				                         require_once("wp-content/themes/swedac_theme/integration/search_integrate.php");


				                    	//ankallar integrationen för SÖK
				                    	try {
				                    		// stamps
				                    		$search_stamps = new search_stamps();
				                    		$stamps = $search_stamps->initiateStamps();
				                    		//accredations
				                    		$searchAcc = new search_integrate();
				                    		$result = $searchAcc->ackrediterande_organ();


				                    	}

				                    	catch(Exception $error) {
				                    		echo($error);
				                    	}
				                    ?>
				                </main>

				                <?php if (Setup\display_sidebar()) : ?>
				                    <aside class="sidebar">
				                        <?php include Wrapper\sidebar_path(); ?>
				                    </aside>
				                <?php endif; ?>
				            </div>
			<?php } ?>

        <br class="clear">
        </div><!-- /.wrapper -->
    </div> <!-- /#main_wrapper -->
    	<input id="valueOfSkip" type="hidden" name="skip" value="20"/>
    	<input id="valueOfSkipStamp" type="hidden" name="skip" value="10"/>
    	<input id="searchString" type="hidden" name="skip" value="<?php
    	if(isset($_GET["s"])){


    	 echo rawurlencode($_GET["s"]);
    	 	}

    	  ?>"/>
    	  <script type="text/javascript" src="wp-content/themes/swedac_theme/integration/ajax.js"> </script>

    	   <?php $url = 'https://' . $_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI'];?>

    	   <?php if (strpos($url,'&site_section=document_pt') !== false || strpos($url,'&site_section=certificate_pt') !== false) { ?>

    	   <?php } else { ?>
    	   		   <script type="text/javascript">
    	   	        jQuery(function() {
    	   	            var mapList = <?php echo $searchAcc->getMapCoords().";"; ?>
                        if(mapList) {
                            loadMap(mapList);
                        }
    	   	            try {
    	   	                ga('send', 'search', 'search-tab1', 'bil', 457);
    	   	            } catch (e) { }
    	   	        });
    	   	    </script>
    	   <?php } ?>
    </body>
</html>
