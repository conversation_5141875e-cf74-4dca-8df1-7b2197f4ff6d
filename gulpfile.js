require('dotenv').config();

// Debug env vars
// console.log('BROWSERSYNC_PROXY:', process.env.BROWSERSYNC_PROXY);
// console.log('BROWSERSYNC_HOST:', process.env.BROWSERSYNC_HOST);
// console.log('BROWSERSYNC_NOTIFY:', process.env.BROWSERSYNC_NOTIFY);
// console.log('BROWSERSYNC_OPEN:', process.env.BROWSERSYNC_OPEN);
// console.log('BROWSERSYNC_HTTPS:', process.env.BROWSERSYNC_HTTPS);
// console.log('BROWSERSYNC_KEY:', process.env.BROWSERSYNC_KEY);
// console.log('BROWSERSYNC_CERT:', process.env.BROWSERSYNC_CERT);

// UTVECKLING SKER LOKALT? TRUE || FALSE
const LOCAL_DEVELOPMENT = true;

// Imports
const gulp          = require('gulp');
const autoprefixer  = require('gulp-autoprefixer');
const babel         = require('gulp-babel');
const cleanCSS      = require('gulp-clean-css');
const concat        = require('gulp-concat');
const minify        = require('gulp-minify');
const plumber       = require('gulp-plumber');
const rename        = require('gulp-rename');
const sass          = require('gulp-sass')(require('sass'));
const sourcemaps    = require('gulp-sourcemaps');
const browserSync   = require('browser-sync').create();
const streamqueue   = require('streamqueue');

const cssDebug = true;
const cssCompatibility = 'ie8';
const watchDependencies = ['build'];

const paths = {
    scripts: {
        in: 'assets/scripts',
        out: 'dist/js',
    },
    styles: {
        in: 'assets/styles',
        out: 'dist/css',
    },
};

// Starta BrowserSync
function initBrowserSync(done) {
    browserSync.init({
        proxy: process.env.BROWSERSYNC_PROXY,
        host: process.env.BROWSERSYNC_HOST,
        notify: process.env.BROWSERSYNC_NOTIFY === 'true', 
        open: process.env.BROWSERSYNC_OPEN,
        https: process.env.BROWSERSYNC_HTTPS === 'true' ? {
            key: process.env.BROWSERSYNC_KEY,
            cert: process.env.BROWSERSYNC_CERT
        } : false
    });

    done();
};

let reloadBrowserSync = (done) => {
    done();
};

// Kör SCSS flödet på angiven källa
function doSassTasks(source, filename) {
    let destination = paths.styles.out;

    const lastSlash = source.lastIndexOf('/');
    if (lastSlash > -1) {
        const destPart = source.substring(0, lastSlash);
        destination += `/${destPart}`;        
    }

    let task = gulp.src(`${paths.styles.in}/${source}`)
        .pipe(sass()).on('error', sass.logError)
        .pipe(rename(filename))
        .pipe(autoprefixer({
            cascade: false
        }))
        .pipe(sourcemaps.init())
        .pipe(cleanCSS({
            compatibility: cssCompatibility,
            debug: cssDebug,
            rebase: false,
        }, function (details) {
            if (!cssDebug) {
                return;
            }

            console.log('/*************************/');
            console.log(`  ${details.name}: ${details.stats.originalSize}`);
            console.log(`  ${details.name}: ${details.stats.minifiedSize}`);
            console.log('/*************************/');
        }))
        .pipe(sourcemaps.write('.'))
        .pipe(gulp.dest(destination));

    if (LOCAL_DEVELOPMENT) {
        task = task.pipe(browserSync.reload({
            stream: true
        }));
    }

    return task;
}

// Slår ihop all CSS
function css() {
    //                 input      , output
    return doSassTasks('main.scss', 'structure.css');
};

// Slår ihop admin CSS
function cssAdmin() {
    return doSassTasks('admin/admin.scss', 'admin.css');
};

// Kör Javascript flödet på källfilerna i  files och vendors.
function doJSTasks(files, vendors, filename, dest = '') {
    let destination = paths.scripts.out;
    if (dest !== '') {
        destination += `/${dest}`;
    }
    // Ifall vendors är tom vill vi inte hantera den arrayen
    let vendorsTask = null;
    if (vendors.length > 0) {
        const fixedVendorPaths = vendors.map((e) => `${paths.scripts.in}/${e}`);
        vendorsTask = gulp.src(fixedVendorPaths);
    }

    const fixedFilePaths = files.map((e) => `${paths.scripts.in}/${e}`);
    const filesTask = gulp.src(fixedFilePaths)
        .pipe(plumber())
        .pipe(babel())
        .pipe(plumber.stop());

    let task = filesTask;
    if (vendorsTask !== null) {
        task = streamqueue({ objectMode: true }, vendorsTask, filesTask);
    }

    task = task
        .pipe(sourcemaps.init())
        .pipe(concat(filename))
        .pipe(minify({
            ext: {
                src: '-debug.js',
                min: '-dist.js'
            },
            noSource: true,
            exclude: ['tasks'],
            ignoreFiles: ['.combo.js', '-min.js']
        }))
        .pipe(sourcemaps.write('.'))
        .pipe(gulp.dest(destination));

    if (LOCAL_DEVELOPMENT) {
        task = task.pipe(browserSync.reload({
            stream: true
        }));
    }

    return task;
}

// Slår ihop alla script
function scripts() {
    // Vendors, t.ex. jQuery plugins, Modernizr etc.
    const vendors = [
        'bootstrap.bundle.min.js',
        'modernizr/modernizr.min.js',
        'plugins/salvattore/salvattore.min.js',
        'plugins/addtocalendar/atc.min.js',
        'plugins/clipboardjs/clipboard.min.js',
        'plugins/highlight/jquery.highlight-5.js',
        'plugins/owl-slider/owl.carousel.min.js',
        'plugins/validate/jquery.validate.js',
    ];
    // Våra kodfiler.
    const files = [
        'main.js',
    ];

    //               files, vendors, output
    return doJSTasks(files, vendors, 'main.js');
};

// Slår ihop alla admin script
function scriptsAdmin() {
    // Vendors, t.ex. jQuery plugins, Modernizr etc.
    const vendors = [];
    // Våra kodfiler.
    const files = [
        'admin/admin.js'
    ];

    //               files, vendors, output    , outputmapp
    return doJSTasks(files, vendors, 'admin.js', 'admin');
};

gulp.task('build', gulp.parallel(css, cssAdmin, scripts, scriptsAdmin));

if (LOCAL_DEVELOPMENT) {
    exports.initBrowserSync = initBrowserSync;
    watchDependencies.push(initBrowserSync);

    reloadBrowserSync = (done) => {
        browserSync.reload();
        done();
    };
}

// Sätter upp watch commandot (gulp watch) för att automatiskt kompilera 
gulp.task('watch', gulp.series(...watchDependencies, function () {
    gulp.watch('assets/styles/**/*.scss', css);

    gulp.watch('assets/styles/admin/admin.scss', cssAdmin);

    gulp.watch('assets/scripts/main.js', gulp.series(scripts, reloadBrowserSync));
    gulp.watch('assets/scripts/admin/admin.js', gulp.series(scriptsAdmin, reloadBrowserSync));

    if (LOCAL_DEVELOPMENT) {
        gulp.watch("./**/*.php").on('change', browserSync.reload);
    }
}));

exports.css = css;
exports.cssAdmin = cssAdmin;
exports.scripts = scripts;
exports.scriptsAdmin = scriptsAdmin;