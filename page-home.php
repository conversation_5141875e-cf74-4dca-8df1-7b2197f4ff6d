<?php

/**
 * Template Name: Startsida
 */
?>

<div id="slider">
	<?php
	if (ICL_LANGUAGE_CODE == 'sv') {
		$slides = get_field('frontpage_slider_sv');
	} else {
		$slides = get_field('frontpage_slider_en');
	}
	$slideNumber = 1;
	?>
	<div id="sliderControlls" class="carousel slide" data-ride="false">
		<div class="carousel-inner">
			<?php
			foreach ($slides as $slide) {
			?>
			<div class="carousel-item <?= $slideNumber == 1 ? "active" : "" ?> ">
				<div class="slideCover"></div>
				<!-- <img class="d-block w-100" src="<?= $slide['bild']['sizes']['frontpage-slider-size']; ?>" alt=""> -->
				<img class="d-block w-100 <?php if( isset( $slide[ 'bild_mobil' ] ) && !empty($slide['bild_mobil'])){echo "desktop";}?>" src="<?= $slide['bild']['sizes']['frontpage-slider-size']; ?>" alt="">
				<?php if( isset( $slide['bild_mobil'] ) && !empty( $slide['bild_mobil'] ) ): ?>
					<img class="d-block w-100 mobile" src="<?= $slide['bild_mobil']['sizes']['frontpage-slider-size']; ?>" alt="<?= $slide['bild']['alt']; ?>">
				<?php endif; ?>
				<div class="wrapper">
					<div class="info">
						<h2><?= $slide['rubrik']; ?></h2>

						<div class="content">
							<?= $slide['beskrivning']; ?>
							<?php
								if((isset($slide['knapp_lank']) && isset($slide['knapp_text'])) && (!empty($slide['knapp_lank']) && !empty($slide['knapp_text']))){
									?>
									<br>
									<div class="link">
										<a href="<?php echo $slide['knapp_lank'] ?>" class="button" aria-hidden="true"><?php echo $slide['knapp_text'] ?></a>
									</div>
									<?php
								}
							?>
						</div>
					</div>
				</div>
				<?php if(isset($slide['copyright']) && !empty($slide['copyright'])) { ?>
					<div class="carousel-copyright">
						<div><?php echo wp_kses_post($slide['copyright']); ?></div>
					</div>
				<?php }	?>
			</div>
			<?php
			$slideNumber++;
			}
			?>
		</div>
		<a class="carousel-control-prev" href="#sliderControlls" role="button" data-slide="prev">
			<span class="carousel-control-prev-icon" aria-hidden="true"></span>
			<span class="sr-only">Previous</span>
		</a>
		<a class="carousel-control-next" href="#sliderControlls" role="button" data-slide="next">
			<span class="carousel-control-next-icon" aria-hidden="true"></span>
			<span class="sr-only">Next</span>
		</a>
	</div>
</div>
</div>
<div id="info-block">
	<div class="wrapper">
		<div class="blocks">
			<?php
			if (ICL_LANGUAGE_CODE == 'sv') {
				$columns = get_field('info_columns');
			} else {
				$columns = get_field('info_columns_en');
			}
			?>
			<div class="block" style="max-width: 45ch; margin-inline:auto">
				<?php
				if (isset($columns['kolumn_1']['image']['url'])) {
				?>
					<img src="<?= $columns['kolumn_1']['image']['url'] ?>" alt="<?= $columns['kolumn_1']['image']['alt'] ?>">
				<?php
				}

				if (isset($columns['kolumn_1']['title'])) {
				?>
					<div class="title">
						<?= $columns['kolumn_1']['title'] ?>
					</div>
				<?php
				}
				if (isset($columns['kolumn_1']['text'])) {
				?>
					<div class="text">
						<?= $columns['kolumn_1']['text'] ?>
					</div>
				<?php
				}

				if (!empty($columns['kolumn_1']['link'])) {
				?>
					<div class="link">
						<a href="<?= $columns['kolumn_1']['link']['url'] ?>" target="<?= $columns['kolumn_1']['link']['target'] ?>"><?= $columns['kolumn_1']['link']['title'] ?></a>
					</div>
				<?php
				}
				?>
			</div>
			<div class="block" style="max-width: 45ch; margin-inline:auto">
				<?php
				if (isset($columns['kolumn_2']['image']['url'])) {
				?>
					<img src="<?= $columns['kolumn_2']['image']['url'] ?>" alt="<?= $columns['kolumn_2']['image']['alt'] ?>">
				<?php
				}

				if (isset($columns['kolumn_2']['title'])) {
				?>
					<div class="title">
						<?= $columns['kolumn_2']['title'] ?>
					</div>
				<?php
				}
				if (isset($columns['kolumn_2']['text'])) {
				?>
					<div class="text">
						<?= $columns['kolumn_2']['text'] ?>
					</div>
				<?php
				}

				if (!empty($columns['kolumn_2']['link'])) {
				?>
					<div class="link">
						<a href="<?= $columns['kolumn_2']['link']['url'] ?>" target="<?= $columns['kolumn_2']['link']['target'] ?>"><?= $columns['kolumn_2']['link']['title'] ?></a>
					</div>
				<?php
				}
				?>
			</div>
			<div class="block third" style="max-width: 45ch; margin-inline:auto">
				<?php
				if (isset($columns['kolumn_3']['image']['url'])) {
				?>
					<img src="<?= $columns['kolumn_3']['image']['url'] ?>" alt="<?= $columns['kolumn_3']['image']['alt'] ?>">
				<?php
				}

				if (isset($columns['kolumn_3']['title'])) {
				?>
					<div class="title">
						<?= $columns['kolumn_3']['title'] ?>
					</div>
				<?php
				}
				if (isset($columns['kolumn_3']['text'])) {
				?>
					<div class="text">
						<?= $columns['kolumn_3']['text'] ?>
					</div>
				<?php
				}

				if (!empty($columns['kolumn_3']['link'])) {
				?>
					<div class="link">
						<a href="<?= $columns['kolumn_3']['link']['url'] ?>" target="<?= $columns['kolumn_3']['link']['target'] ?>"><?= $columns['kolumn_3']['link']['title'] ?></a>
					</div>
				<?php
				}
				?>
			</div>

		</div>
	</div>
</div>

<div class="wrapper">
	<ul id="quick_links">
		<?php
		$locations = get_nav_menu_locations();
		$object = wp_get_nav_menu_object($locations["quickLinks"]);
		$menu_items = wp_get_nav_menu_items($object->name, $args);
		foreach ($menu_items as $item) {
		?>
		<li>
			<a href="<?= $item->url ?>">
				<div class="link"><?= $item->title ?></div>
			</a>
		</li>

		<?php
		}
		?>
	</ul>
</div>
<?php if (ICL_LANGUAGE_CODE == 'sv') : ?>
	<div id="frontpage_puffar">
		<div class="wrapper">

			<div class="tag">
				<a href="<?= home_url('/nyheter') ?>">Aktuellt</a>
			</div>
			<ul class="owl-carousel" id='owl-slider'>
				<?php
				$slider_args = array( 
					'posts_per_page' => 6, 
					'post_type' => 'news', 
					'post_status' => 'publish' 
				);
				wp_reset_postdata();
				$my_slider_query = new WP_Query( $slider_args );
				?>
				<?php if ($my_slider_query->have_posts()) : ?>
					<?php while ( $my_slider_query->have_posts()) : $my_slider_query->the_post() ?>
						<li class="content item lookAtMe">
							<div class="image">
								<img src="<?php echo get_the_post_thumbnail_url() ? get_the_post_thumbnail_url() : get_stylesheet_directory_uri().'/assets/images/document.jpg' ?>" alt="<?php echo get_post_meta( get_post_thumbnail_id(), '_wp_attachment_image_alt', true ); ?>">
							</div>
							<div class="timestamp">
								<?php echo get_the_date(); ?>
							</div>
							<div class="info">
								<a href="<?php the_permalink(); ?>" title="Läs mer om <?php the_title(); ?>">
									<h2 class="title-cuter"><?php the_title(); ?></h2>
								</a>
								<div class="excerpt">
									<?php $value = strip_tags(get_field('page_ingress_subpage_sv')); ?>
									<?php 
									if (mb_strlen($value, 'UTF-8') > 150) {
										$value = mb_substr($value, 0, 150);
										echo "<p>".$value . '...</p>';
									} else {
										echo "<p>".$value."</p>";
									} ?>
								</div>
							</div>
								</li>
					<?php endwhile; ?>
				<?php endif; ?>
				<?php wp_reset_postdata(); ?>
			</ul>
		</div>
	</div>

	<script>
    document.addEventListener("DOMContentLoaded", function () {
        setTimeout(function () {
            var owlNextButtonNext = document.querySelector(".owl-next span");
			var owlNextButtonPrev = document.querySelector(".owl-prev span");


            if (owlNextButtonNext) {
                owlNextButtonNext.setAttribute("aria-label", "Nästa");
            }

            if (owlNextButtonPrev) {
                owlNextButtonPrev.setAttribute("aria-label", "Föregående");
            }

        }, 100);
    });

	</script>

<?php elseif (ICL_LANGUAGE_CODE == 'en') : ?>

<?php endif; ?>

<div id="frontpage_news">
	<div class="wrapper">
		<div class="left">
			<ul>
				<?php 
				if (ICL_LANGUAGE_CODE == 'sv') {
					$image_url = get_field('coworker_main_image', 10)['sizes']['swedac_magasin']; 
					$image_alt = get_field('coworker_main_image', 10)['alt']; 
					$post_link = get_field('coworker_main_link', 10)['url']; 
					$title = get_field('coworker_main_title', 10); 
					$text = get_field('coworker_main_text', 10); 
				} else {
					$image_url = get_field('coworker_main_image_en', 2652)['sizes']['swedac_magasin']; 
					$image_alt = get_field('coworker_main_image_en', 2652)['alt']; 
					$post_link = get_field('coworker_main_link_en', 2652)['url']; 
					$title = get_field('coworker_main_title_en', 2652); 
					$text = get_field('coworker_main_text_en', 2652); 
				} 
				?>

				<li <?php post_class('front-news-post'); ?>>
					<div class="image">
						<img data-src="<?php echo $image_url; ?>" alt="<?php echo $image_alt ?>" />
					</div>
					<div class="info">
						<a href="<?php echo $post_link  ?>">	
							<h2><?php echo $title; ?></h2>
						</a>
						<div class="excerpt">
							<?php echo $text; ?>
						</div>
					</div>
				</li>
			</ul>
		</div>
		<div class="right">
			<?php

			if (ICL_LANGUAGE_CODE == 'sv') {
				$side_image_url = get_field('coworker_side_image', 10)['sizes']['swedac_magasin'];
				$side_image_alt = get_field('coworker_side_image', 10)['alt'];
				$side_link = get_field('coworker_side_link', 10);
				$side_title = get_field('coworker_side_title', 10);
				$side_text = get_field('coworker_side_text', 10);
			} else {
				$side_image_url = get_field('coworker_side_image_en', 2652)['sizes']['swedac_magasin'];
				$side_image_alt = get_field('coworker_side_image_en', 2652)['alt'];
				$side_link = get_field('coworker_side_link_en', 2652);
				$side_title = get_field('coworker_side_title_en', 2652);
				$side_text = get_field('coworker_side_text_en', 2652);
			}
			
			?>
			<img src=" <?php echo $side_image_url; ?>" alt="<?php echo $side_image_alt ?>">
			<div class="info">
				<h2><?php echo $side_title; ?></h2>
				<div class="excerpt">
					<p><?php echo $side_text; ?></p>
				</div>
				<a href="<?php echo $side_link['url']; ?>"><?php echo $side_link['title']; ?></a>
			</div>
		</div>
	</div>
</div>
<?php if (ICL_LANGUAGE_CODE == 'sv') : ?>
	<div id="swedac_magasin">
		<div class="wrapper">
			<h2 class="title">
				<a href="/swedac-magasin/">
					Uppdrag tillit
				</a>
			</h2>

			<?php $args = array(
				'post_type' => 'swedac_magasin',
				'posts_per_page' => 5,
				'orderby' => 'date'
			);
			$query = new WP_Query($args);
			if ($query->have_posts()) : ?>
				<ul>
					<?php while ($query->have_posts()) : $query->the_post(); ?>
						<?php $get_post_image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'swedac_magasin');
						if (isset($get_post_image['0'])) {
							$swedac_magasin = $get_post_image['0'];
						} else {
							$swedac_magasin = false;
						}

						$swedacMagasinKvadratisk = get_field('swedac_magasin_kvadratisk');
						$imageSwedacMagasin = $swedacMagasinKvadratisk['sizes']['swedac_magasin'];
						?>

						<li>
							<div class="content">
								<a href="<?php the_permalink(); ?>">
									<div class="cover"></div>
									<div class="post_image">
										<?php if ($imageSwedacMagasin) { ?>
											<img data-src="<?php echo $imageSwedacMagasin; ?>" alt="" />
										<?php } else { ?>
											<img data-src="<?php echo $swedac_magasin; ?>" alt="" />
										<?php } ?>
									</div>
									<div class="info">
										<h2><?php the_title(); ?></h2>
										<div class="excerpt">

											<?php the_excerpt(); ?>

											<!-- <?php echo substr(get_the_excerpt(), 0, strrpos(substr(get_the_excerpt(), 0, 50), ' ')); ?> -->
										</div>
									</div>
								</a>
							</div>
						</li>

					<?php endwhile; ?>
				</ul>
		</div>
	</div>
<?php endif;
			wp_reset_postdata(); ?>
<?php elseif (ICL_LANGUAGE_CODE == 'en') : ?>

<?php endif; ?>


<div id="fordonsbesiktning">
	<div class="wrapper">
		<div class="left">
			<div class="box">
				<img data-src="<?php echo get_template_directory_uri(); ?>/assets/images/icon-car.png" alt="" />
			</div>
		</div>

		<div class="right">
			<?php if (ICL_LANGUAGE_CODE == 'sv') : ?>
				<h2><?php echo get_field('frontpage_fordon_verkstader_rubrik_sv'); ?></h2>
				<div class="text">
					<?php echo get_field('frontpage_fordon_verkstader_intro_sv'); ?>
				</div>

				<div class="lankar">
					<?php if (get_field('frontpage_fordon_verkstader_rep_sv')) : ?>
						<ul>
							<?php while (has_sub_field('frontpage_fordon_verkstader_rep_sv')) : ?>
								<li>
									<a href="<?php echo get_sub_field('lankmal'); ?>" title="<?php echo get_sub_field('namn'); ?>"><?php echo get_sub_field('namn'); ?></a>
								</li>
							<?php endwhile; ?>
						</ul>
					<?php endif; ?>
				</div>
			<?php elseif (ICL_LANGUAGE_CODE == 'en') : ?>
				<h2><?php echo get_field('frontpage_fordon_verkstader_rubrik_en'); ?></h2>
				<div class="text">
					<?php echo get_field('frontpage_fordon_verkstader_intro_en'); ?>
				</div>

				<div class="lankar">
					<?php if (get_field('frontpage_fordon_verkstader_rep_en')) : ?>
						<ul>
							<?php while (has_sub_field('frontpage_fordon_verkstader_rep_en')) : ?>
								<li>
									<a href="<?php echo get_sub_field('lankmal'); ?>" title="<?php echo get_sub_field('namn'); ?>"><?php echo get_sub_field('namn'); ?></a>
								</li>
							<?php endwhile; ?>
						</ul>
					<?php endif; ?>
				</div>
			<?php endif; ?>

		</div>
	</div>
</div>