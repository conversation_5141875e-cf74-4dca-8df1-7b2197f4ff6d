<?php
/**
 * Template Name: Template 6 - Swedac <PERSON>
 */
?>
<?php while (have_posts()) : the_post(); ?>
	<?php get_template_part('templates/page', 'header-custom'); ?>
	<div class="wrapper">
		<?php get_template_part('templates/content', 'page'); ?>
	</div>
<?php endwhile; ?>
<div class="wrapper">
	<div id="page-magasin-content">
		<?php if(ICL_LANGUAGE_CODE=='sv'):
			$args = array(
				'post_type' => 'swedac_magasin',
				'posts_per_page' => 10,
				'orderby' => 'date'
			);
		elseif(ICL_LANGUAGE_CODE=='en'):
			$args = array(
				'post_type' => 'swedac_magasin_en',
				'posts_per_page' => 10,
				'orderby' => 'date'
			);
		endif;

		$query = new WP_Query($args);
		if($query->have_posts()): ?>
		    <ul>
		    	<?php $i = 1; ?>
				<?php while($query->have_posts()):$query->the_post(); ?>

					<?php $get_post_image = wp_get_attachment_image_src( get_post_thumbnail_id($post->ID), 'swedac_magasin' );
					$swedac_magasin = $get_post_image['0']; ?>

					<?php $swedacMagasinKvadratisk = get_field('swedac_magasin_kvadratisk');
						$imageSwedacMagasin = $swedacMagasinKvadratisk['sizes']['swedac_magasin']; ?>


				 	<li class="article_<?php echo $i; ?>">
				 		<div class="content">
				 			<a href="<?php the_permalink(); ?>">
					 			<div class="cover"></div>
								<div class="post_image">
									<?php if($imageSwedacMagasin) { ?>
										<img data-src="<?php echo $imageSwedacMagasin; ?>" alt="<?php the_title(); ?>" />
									<?php } else { ?>
										<img data-src="<?php echo $swedac_magasin; ?>" alt="<?php the_title(); ?>" />
									<?php } ?>
								</div>
								<div class="info">
									<h2><?php the_title(); ?></h2>
									<div class="excerpt">
										<?php the_excerpt(); ?>

										<!-- <?php echo substr( get_the_excerpt(), 0, strrpos( substr( get_the_excerpt(), 0, 50), ' ' ) ); ?> -->
									</div>
								</div>
							</a>
				 		</div>
				 	</li>

				<?php $i++; endwhile; ?>
		    </ul>
		<?php endif; wp_reset_postdata(); ?>
	</div>
<!--
	<div id="swedac_magasin_latest">
		<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
			<div class="left">
				<div class="omslag">
					<?php $omslag = get_field('swedac_magasin_omslag', 988); ?>
					<img data-src="<?php echo $omslag['sizes']['medium']; ?>" />
				</div>
			</div>
			<div class="right">
				<div class="nuvarande_info">
					<h2><?php echo get_field('swedac_magasin_rurbik', 988); ?></h2>
					<span><?php echo get_field('swedac_magasin_beskrivning', 988); ?></span>
					<ul>
						<li><a href="<?php echo get_field('swedac_magasin_oppna_e-tidning_url', 988); ?>" target="_blank"><?php echo get_field('swedac_magasin_oppna_e-tidning_rubrik', 988); ?></a></li>
						<li><a href="<?php echo get_field('swedac_magasin_tidningsarkiv_url', 988); ?>" target="_blank"><?php echo get_field('swedac_magasin_tidningsarkiv_rubrik', 988); ?></a></li>
					</ul>
				</div>

				<div class="prenumerera_magasin">
					<h2><?php echo get_field('swedac_magasin_rubrik_prenumerera', 988); ?></h2>
					<span><?php echo get_field('swedac_magasin_beskrivning_prenumerera', 988); ?></span>
					<div class="prenumerera_magasin_form">
						<form id="prenumerera_magasin_form" class="form clear" name="prenumerera_magasin_form" method="post" action="/sender.php?prenumerera_magasin" novalidate="novalidate" autocomplete="false">
							<div class="top">
								<div class="row namn">
									<input type="text" class="inputbox req" name="namn" id="namn" placeholder="Namn *" >
									<i class="fa fa-check"></i>
									<i class="fa fa-times"></i>
								</div>
								<div class="row postadress">
									<input type="text" class="inputbox req" name="postadress" id="postadress" placeholder="Postadress *" >
									<i class="fa fa-check"></i>
									<i class="fa fa-times"></i>
								</div>
								<div class="send">
									<input class="submit" type="submit" value="Skicka">
								</div>
								<span>* Obligatoriska fält</span>
							</div>
							<input type="hidden" name="url" value="<?php echo strtok($_SERVER["REQUEST_URI"],'?');?>"/>
						</form>
					</div>
				</div>
			</div>
		<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
			<div class="left">
				<div class="omslag">
					<?php $omslag = get_field('swedac_magasin_omslag_en', 3487); ?>
					<img data-src="<?php echo $omslag['sizes']['medium']; ?>" />
				</div>
			</div>
			<div class="right">
				<div class="nuvarande_info">
					<h2><?php echo get_field('swedac_magasin_rurbik_en', 3487); ?></h2>
					<span><?php echo get_field('swedac_magasin_beskrivning_en', 3487); ?></span>
					<ul>
						<li><a href="<?php echo get_field('swedac_magasin_oppna_e-tidning_url_en', 3487); ?>" target="_blank"><?php echo get_field('swedac_magasin_oppna_e-tidning_rubrik_en', 3487); ?></a></li>
						<li><a href="<?php echo get_field('swedac_magasin_tidningsarkiv_url_en', 3487); ?>" target="_blank"><?php echo get_field('swedac_magasin_tidningsarkiv_rubrik_en', 3487); ?></a></li>
					</ul>
				</div>

				<div class="prenumerera_magasin">
					<h2><?php echo get_field('swedac_magasin_rubrik_prenumerera_en', 3487); ?></h2>
					<span><?php echo get_field('swedac_magasin_beskrivning_prenumerera_en', 3487); ?></span>
					<ul>
						<li><a href="<?php echo get_field('swedac_magasin_starta_prenumeration_en', 3487); ?>" target="_blank">Subscribe now</a></li>
					</ul>
				</div>
			</div>
		<?php endif;?>
	</div>-->
</div>
