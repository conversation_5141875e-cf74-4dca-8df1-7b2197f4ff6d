{"key": "group_5f80148f28233", "title": "Swedac Utvalda", "fields": [{"key": "field_5f80151f4cf8c", "label": "<PERSON><PERSON><PERSON><PERSON> poster", "name": "utvalda_repeater", "type": "repeater", "instructions": "<PERSON><PERSON><PERSON> till ett nytt segment som inkluderar bilder, video och inlägg mm.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "block", "button_label": "Lägg till nytt segment", "sub_fields": [{"key": "field_5f801ff55ad77", "label": "Inledande bild", "name": "ut<PERSON><PERSON>_hero", "type": "image", "instructions": "Ange bilden som ska visas i rektangulär form.\r\nT.ex 1920 x 500. Kommer att beskäras automatiskt. Så beskär innan uppladdning.", "required": 1, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5f804258f0b9c", "label": "<PERSON><PERSON><PERSON>", "name": "ut<PERSON><PERSON>_hero_rubrik", "type": "text", "instructions": "Ange rubrik på inledande bild.", "required": 1, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5f80431f64ff3", "label": "Underrubrik", "name": "utvalda_underrubrik", "type": "text", "instructions": "Ange underrubrik som introducerar ämnet.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5f80434b64ff4", "label": "Text", "name": "utvalda_text", "type": "text", "instructions": "<PERSON>e inledande text om ämnet.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5f80220e94c1c", "label": "Utvalda inlägg", "name": "utvalda_repeater_inlagg", "type": "repeater", "instructions": "<PERSON><PERSON>gg till 4-6 inlägg som ska visas (minst 4 och max 6st).", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 4, "max": 6, "layout": "table", "button_label": "", "sub_fields": [{"key": "field_5f8028a18c407", "label": "Inlägg", "name": "utvalda_inlagg", "type": "post_object", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "1000", "class": "", "id": ""}, "post_type": ["swed<PERSON>_magasin"], "taxonomy": "", "allow_null": 0, "multiple": 0, "return_format": "object", "ui": 1}]}, {"key": "field_5f8447d27b9e8", "label": "Video", "name": "utvalda_video", "type": "oembed", "instructions": "Ange URL för video", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "width": "", "height": ""}, {"key": "field_5f8447fc7b9e9", "label": "Text", "name": "utvalda_video_text", "type": "text", "instructions": "<PERSON><PERSON> text om video.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5f8448983ee99", "label": "Bild", "name": "utvalda_video_bild", "type": "image", "instructions": "Posterbild till video. Ange bilden som ska visas i rektangulär form.\r\nT.ex 1920 x 500. Kommer att beskäras automatiskt. Så beskär innan uppladdning.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5f84496a31087", "label": "<PERSON><PERSON><PERSON>", "name": "utvalda_video_rubrik", "type": "text", "instructions": "<PERSON><PERSON> rubrik till video.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}]}], "location": [[{"param": "post_type", "operator": "==", "value": "swedac_ut<PERSON>da"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": ["permalink", "the_content", "excerpt", "discussion", "comments", "slug", "author", "format", "featured_image", "categories", "tags", "send-trackbacks"], "active": true, "description": "", "modified": 1648126181}