{"key": "group_5fdc5b5f91927", "title": "Tre boxar", "fields": [{"key": "field_5fca3a2ff8371", "label": "Tre boxar: rubrik", "name": "box_header", "aria-label": "", "type": "text", "instructions": "Ange rubrik som visas ovanför de tre boxarna.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "<PERSON> <PERSON><PERSON>", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5fca0517e7be9", "label": "Tre boxar", "name": "box_repeater", "aria-label": "", "type": "repeater", "instructions": "Välj tre sidor som ska visas i rutorna", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 3, "layout": "table", "button_label": "Lägg till rad", "sub_fields": [{"key": "field_5fca0588e7bea", "label": "<PERSON><PERSON><PERSON> till sida", "name": "box_site", "aria-label": "", "type": "post_object", "instructions": "<PERSON><PERSON><PERSON><PERSON> vilken sida som ska visas i en av tre rutor.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["page"], "taxonomy": "", "allow_null": 0, "multiple": 0, "return_format": "object", "ui": 1, "bidirectional_target": [], "parent_repeater": "field_5fca0517e7be9"}, {"key": "field_5fca0bad060f3", "label": "<PERSON><PERSON><PERSON>", "name": "box_title", "aria-label": "", "type": "text", "instructions": "<PERSON>e rubrik", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_5fca0517e7be9"}, {"key": "field_5fca0c92060f4", "label": "Text", "name": "box_text", "aria-label": "", "type": "textarea", "instructions": "Ange text", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "new_lines": "", "maxlength": "", "placeholder": "", "rows": "", "parent_repeater": "field_5fca0517e7be9"}, {"key": "field_5fe0b59d899e8", "label": "Bild", "name": "box_img", "aria-label": "", "type": "image", "instructions": "<PERSON><PERSON><PERSON> till en bild.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "url", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "parent_repeater": "field_5fca0517e7be9"}], "rows_per_page": 20}], "location": [[{"param": "post_template", "operator": "==", "value": "page-template-hr.php"}], [{"param": "post_template", "operator": "==", "value": "page-template-ledigatjanster.php"}], [{"param": "post_template", "operator": "==", "value": "page-template-avdelningssidor.php"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1713820563}