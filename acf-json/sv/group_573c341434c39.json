{"key": "group_573c341434c39", "title": "Kontaktinformation", "fields": [{"key": "field_573c34f5f2f86", "label": "<PERSON><PERSON><PERSON>", "name": "kontaktuppgifter_kontor", "type": "repeater", "instructions": "<PERSON>ä<PERSON> till ett kontor med tillhörande kontaktuppgifter", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Lägg till kontor", "sub_fields": [{"key": "field_573c3515f2f87", "label": "Typ av kontor", "name": "typ_av_kontor", "type": "text", "instructions": "Ange typ av kontor. <br/>\r\nT.ex \"Huvudförvaltning\", \"Stockholmskontor\".", "required": 0, "conditional_logic": 0, "wrapper": {"width": 25, "class": "", "id": ""}, "default_value": "", "placeholder": "Typ av kontor", "prepend": "", "append": "", "maxlength": "", "readonly": 0, "disabled": 0}, {"key": "field_573c3566f2f88", "label": "Besöksadress", "name": "besoks<PERSON><PERSON>", "type": "textarea", "instructions": "<PERSON><PERSON>.", "required": 0, "conditional_logic": 0, "wrapper": {"width": 20, "class": "", "id": ""}, "default_value": "", "placeholder": "Besöksadress", "maxlength": "", "rows": 3, "new_lines": "wpautop", "readonly": 0, "disabled": 0}, {"key": "field_573c3597f2f89", "label": "<PERSON><PERSON><PERSON>", "name": "postadress", "type": "textarea", "instructions": "<PERSON><PERSON>.", "required": 0, "conditional_logic": 0, "wrapper": {"width": 20, "class": "", "id": ""}, "default_value": "", "placeholder": "<PERSON><PERSON><PERSON>", "maxlength": "", "rows": 3, "new_lines": "wpautop", "readonly": 0, "disabled": 0}, {"key": "field_573c35b0f2f8a", "label": "Karta", "name": "karta", "type": "image", "instructions": "Ladda upp kartbild", "required": 0, "conditional_logic": 0, "wrapper": {"width": "35", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}]}], "location": [[{"param": "page", "operator": "==", "value": "46"}]], "menu_order": 0, "position": "acf_after_title", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "modified": 1648124158}