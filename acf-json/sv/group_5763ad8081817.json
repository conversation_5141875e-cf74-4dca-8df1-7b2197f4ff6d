{"key": "group_5763ad8081817", "title": "Visa nyhetsförfattare", "fields": [{"key": "field_5763ae70b60e5", "label": "Visa författare", "name": "hide_author_sv", "type": "true_false", "instructions": "Ange ifall författaren ska visas.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "ui": 0, "ui_on_text": "", "ui_off_text": ""}], "location": [[{"param": "post_type", "operator": "==", "value": "post"}], [{"param": "post_type", "operator": "==", "value": "news"}]], "menu_order": -5, "position": "acf_after_title", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "modified": 1695902621}