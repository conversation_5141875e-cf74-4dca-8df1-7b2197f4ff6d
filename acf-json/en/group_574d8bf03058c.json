{"key": "group_574d8bf03058c", "title": "Allmäninformation", "fields": [{"key": "field_574d8cd8d7708", "label": "Bildspel / Slider", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "placement": "top", "endpoint": 0}, {"key": "field_574d8ce8d7709", "label": "Bildspel", "name": "frontpage_slider_en", "type": "repeater", "instructions": "Bildspel på startsidan", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Lägg till bild", "rows_per_page": 20, "sub_fields": [{"key": "field_574d8cf7d770a", "label": "Bild", "name": "bild", "type": "image", "instructions": "<PERSON>e en bild", "required": 0, "conditional_logic": 0, "wrapper": {"width": 20, "class": "", "id": ""}, "aria-label": "", "return_format": "array", "preview_size": "thumbnail", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "parent_repeater": "field_574d8ce8d7709"}, {"key": "field_574d8d06d770b", "label": "<PERSON><PERSON><PERSON>", "name": "rubrik", "type": "text", "instructions": "Ange en passande rubrik", "required": 0, "conditional_logic": 0, "wrapper": {"width": 25, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "<PERSON><PERSON><PERSON>", "prepend": "", "append": "", "maxlength": "", "readonly": 0, "disabled": 0, "parent_repeater": "field_574d8ce8d7709"}, {"key": "field_574d8d21d770c", "label": "Beskrivning", "name": "beskrivning", "type": "textarea", "instructions": "Ange en beskrivning.", "required": 0, "conditional_logic": 0, "wrapper": {"width": 55, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "Beskrivning", "maxlength": "", "rows": 4, "new_lines": "wpautop", "readonly": 0, "disabled": 0, "parent_repeater": "field_574d8ce8d7709"}, {"key": "field_6512b5f0a1a0c", "label": "Knapp Text", "name": "knapp_text", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_574d8ce8d7709"}, {"key": "field_6512b5fca1a0d", "label": "<PERSON><PERSON><PERSON>", "name": "knapp_lank", "type": "url", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "", "parent_repeater": "field_574d8ce8d7709"}]}, {"key": "field_641d5db12c043", "label": "Startinformation", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "placement": "top", "endpoint": 0}, {"key": "field_641d5dc92c044", "label": "<PERSON><PERSON><PERSON>", "name": "info_columns_en", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "layout": "block", "sub_fields": [{"key": "field_641d5de92c045", "label": "Kolumn 1", "name": "kolumn_1", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "layout": "block", "sub_fields": [{"key": "field_641d5e0a1c148", "label": "Bild", "name": "image", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_641d5e201c149", "label": "Titel", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_641d5e2a1c14a", "label": "Text", "name": "text", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_641d5e311c14b", "label": "<PERSON><PERSON><PERSON>", "name": "link", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "return_format": ""}]}, {"key": "field_641d5e571c14c", "label": "Kolumn 2", "name": "kolumn_2", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "layout": "block", "sub_fields": [{"key": "field_641d5e571c14d", "label": "Bild", "name": "image", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_641d5e571c14e", "label": "Titel", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_641d5e571c14f", "label": "Text", "name": "text", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_641d5e571c150", "label": "<PERSON><PERSON><PERSON>", "name": "link", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "return_format": ""}]}, {"key": "field_641d5e581c151", "label": "Kolumn 3", "name": "kolumn_3", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "layout": "block", "sub_fields": [{"key": "field_641d5e581c152", "label": "Bild", "name": "image", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_641d5e581c153", "label": "Titel", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_641d5e581c154", "label": "Text", "name": "text", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_641d5e581c155", "label": "<PERSON><PERSON><PERSON>", "name": "link", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "return_format": "array"}]}]}, {"key": "field_643e6f5522eb7", "label": "Medarbetarporträtt", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "placement": "top", "endpoint": 0}, {"key": "field_643e6f6f22eb8", "label": "Huvudbild", "name": "coworker_main_image_en", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_643e6f7822eb9", "label": "huvudtitel", "name": "coworker_main_title_en", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_643e6_f7922eba", "label": "huvudtext", "name": "coworker_main_text_en", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 3, "new_lines": "br"}, {"key": "field_643e6f7b22ebb", "label": "Huvudlänk", "name": "coworker_main_link_en", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "return_format": "array"}, {"key": "field_643e6f7c22ebc", "label": "Sidobild", "name": "coworker_side_image_en", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_643e6fbd22ebd", "label": "sidotitel", "name": "coworker_side_title_en", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_643e6fbe22ebe", "label": "sidotext", "name": "coworker_side_text_en", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "", "maxlength": "", "rows": 3, "new_lines": "br"}, {"key": "field_643e6ffc22ebf", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "coworker_side_link_en", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "return_format": "array"}, {"key": "field_574d8e58d771a", "label": "Swedac Academy", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "placement": "top", "endpoint": 0}, {"key": "field_574d8e5cd771b", "label": "Bild", "name": "swedac_academy_bild_en", "type": "image", "instructions": "<PERSON>e en bild.", "required": 0, "conditional_logic": 0, "wrapper": {"width": 15, "class": "", "id": ""}, "aria-label": "", "return_format": "array", "preview_size": "thumbnail", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_574d8e9ed771c", "label": "<PERSON><PERSON><PERSON>", "name": "swedac_academy_rubrik_en", "type": "text", "instructions": "<PERSON>e en rubrik. <br>\r\nT.ex Swedac Academy", "required": 0, "conditional_logic": 0, "wrapper": {"width": 15, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "<PERSON><PERSON><PERSON>", "prepend": "", "append": "", "maxlength": "", "readonly": 0, "disabled": 0}, {"key": "field_574d8eb3d771d", "label": "Beskrivning", "name": "swedac_academy_beskrivning_en", "type": "textarea", "instructions": "Ange en passande beskrivning. <br>\r\nT.ex Något som förklarar vad Swedac Academy är för något.", "required": 0, "conditional_logic": 0, "wrapper": {"width": 25, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "Beskrivning", "maxlength": "", "rows": 3, "new_lines": "wpautop", "readonly": 0, "disabled": 0}, {"key": "field_574d8f7ad771e", "label": "Länkar", "name": "swedac_academy_lankar_en", "type": "repeater", "instructions": "<PERSON>e ett par länkar som t.ex \"Aktuella kurser\" & \"Om Swedac Acedemy\"", "required": 0, "conditional_logic": 0, "wrapper": {"width": 45, "class": "", "id": ""}, "aria-label": "", "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Lägg till rad", "rows_per_page": 20, "sub_fields": [{"key": "field_574d8f89d771f", "label": "<PERSON><PERSON>", "name": "namn", "type": "text", "instructions": "Ange ett länknamn", "required": 1, "conditional_logic": 0, "wrapper": {"width": 50, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "<PERSON><PERSON>", "prepend": "", "append": "", "maxlength": "", "readonly": 0, "disabled": 0, "parent_repeater": "field_574d8f7ad771e"}, {"key": "field_574d8f99d7720", "label": "Länkmål", "name": "lankmal", "type": "page_link", "instructions": "Ange ett länkmål", "required": 1, "conditional_logic": 0, "wrapper": {"width": 50, "class": "", "id": ""}, "aria-label": "", "post_type": ["page"], "taxonomy": [], "allow_null": 0, "multiple": 0, "allow_archives": 1, "parent_repeater": "field_574d8f7ad771e"}]}, {"key": "field_574d8fdcd7721", "label": "Fordon & Verkstäder", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "placement": "top", "endpoint": 0}, {"key": "field_574d8fe8d7722", "label": "<PERSON><PERSON><PERSON>", "name": "frontpage_fordon_verkstader_rubrik_en", "type": "text", "instructions": "Ange en passande rubrik. <br>\r\nT.<PERSON> Fordonsbesktning och ackrediterade bilverkstäder", "required": 0, "conditional_logic": 0, "wrapper": {"width": 25, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "<PERSON><PERSON><PERSON>", "prepend": "", "append": "", "maxlength": "", "readonly": 0, "disabled": 0}, {"key": "field_574d8ffed7723", "label": "Introduktion", "name": "frontpage_for<PERSON>_verks<PERSON>er_intro_en", "type": "textarea", "instructions": "Ange en passande introduktionstext", "required": 0, "conditional_logic": 0, "wrapper": {"width": 25, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "Introduktion", "maxlength": "", "rows": 3, "new_lines": "wpautop", "readonly": 0, "disabled": 0}, {"key": "field_574d9013d7724", "label": "Länkar för fordon & verkstäder", "name": "frontpage_for<PERSON>_verks<PERSON>er_rep_en", "type": "repeater", "instructions": "Ange information om fordonsbeskrining och ackrediterade.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Lägg till länk", "rows_per_page": 20, "sub_fields": [{"key": "field_574d901fd7725", "label": "<PERSON><PERSON>", "name": "namn", "type": "text", "instructions": "Ange ett länknamn.", "required": 1, "conditional_logic": 0, "wrapper": {"width": 25, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "<PERSON><PERSON>", "prepend": "", "append": "", "maxlength": "", "readonly": 0, "disabled": 0, "parent_repeater": "field_574d9013d7724"}, {"key": "field_574d902fd7726", "label": "Länkmål", "name": "lankmal", "type": "url", "instructions": "Ange ett länkmål", "required": 1, "conditional_logic": 0, "wrapper": {"width": 75, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "Url", "parent_repeater": "field_574d9013d7724"}]}, {"key": "field_6512c584f0f0a", "label": "Header", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_574d91c5d772f", "label": "Sociala medier", "name": "header_socialmedia_en", "type": "repeater", "instructions": "<PERSON><PERSON><PERSON> till era sociala medier genom att ange en ikon samt en länk till rätt media.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Lägg till rad", "sub_fields": [{"key": "field_6512c57ef0f09", "label": "<PERSON><PERSON>", "name": "namn", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_574d91d4d7730", "label": "<PERSON><PERSON>", "name": "icon", "type": "font-awesome", "instructions": "Ange rätt ikon för rätt media.", "required": 1, "conditional_logic": 0, "wrapper": {"width": 25, "class": "", "id": ""}, "aria-label": "", "default_value": "fa-facebook", "save_format": "element", "allow_null": 0, "enqueue_fa": 0, "fa_live_preview": "", "choices": {"null": "- Select -", "fa-500px": "&#xf26e; fa-500px", "fa-adjust": "&#xf042; fa-adjust", "fa-adn": "&#xf170; fa-adn", "fa-align-center": "&#xf037; fa-align-center", "fa-align-justify": "&#xf039; fa-align-justify", "fa-align-left": "&#xf036; fa-align-left", "fa-align-right": "&#xf038; fa-align-right", "fa-amazon": "&#xf270; fa-amazon", "fa-ambulance": "&#xf0f9; fa-ambulance", "fa-american-sign-language-interpreting": "&#xf2a3; fa-american-sign-language-interpreting", "fa-anchor": "&#xf13d; fa-anchor", "fa-android": "&#xf17b; fa-android", "fa-angellist": "&#xf209; fa-angellist", "fa-angle-double-down": "&#xf103; fa-angle-double-down", "fa-angle-double-left": "&#xf100; fa-angle-double-left", "fa-angle-double-right": "&#xf101; fa-angle-double-right", "fa-angle-double-up": "&#xf102; fa-angle-double-up", "fa-angle-down": "&#xf107; fa-angle-down", "fa-angle-left": "&#xf104; fa-angle-left", "fa-angle-right": "&#xf105; fa-angle-right", "fa-angle-up": "&#xf106; fa-angle-up", "fa-apple": "&#xf179; fa-apple", "fa-archive": "&#xf187; fa-archive", "fa-area-chart": "&#xf1fe; fa-area-chart", "fa-arrow-circle-down": "&#xf0ab; fa-arrow-circle-down", "fa-arrow-circle-left": "&#xf0a8; fa-arrow-circle-left", "fa-arrow-circle-o-down": "&#xf01a; fa-arrow-circle-o-down", "fa-arrow-circle-o-left": "&#xf190; fa-arrow-circle-o-left", "fa-arrow-circle-o-right": "&#xf18e; fa-arrow-circle-o-right", "fa-arrow-circle-o-up": "&#xf01b; fa-arrow-circle-o-up", "fa-arrow-circle-right": "&#xf0a9; fa-arrow-circle-right", "fa-arrow-circle-up": "&#xf0aa; fa-arrow-circle-up", "fa-arrow-down": "&#xf063; fa-arrow-down", "fa-arrow-left": "&#xf060; fa-arrow-left", "fa-arrow-right": "&#xf061; fa-arrow-right", "fa-arrow-up": "&#xf062; fa-arrow-up", "fa-arrows": "&#xf047; fa-arrows", "fa-arrows-alt": "&#xf0b2; fa-arrows-alt", "fa-arrows-h": "&#xf07e; fa-arrows-h", "fa-arrows-v": "&#xf07d; fa-arrows-v", "fa-assistive-listening-systems": "&#xf2a2; fa-assistive-listening-systems", "fa-asterisk": "&#xf069; fa-asterisk", "fa-at": "&#xf1fa; fa-at", "fa-audio-description": "&#xf29e; fa-audio-description", "fa-backward": "&#xf04a; fa-backward", "fa-balance-scale": "&#xf24e; fa-balance-scale", "fa-ban": "&#xf05e; fa-ban", "fa-bar-chart": "&#xf080; fa-bar-chart", "fa-barcode": "&#xf02a; fa-barcode", "fa-bars": "&#xf0c9; fa-bars", "fa-battery-empty": "&#xf244; fa-battery-empty", "fa-battery-full": "&#xf240; fa-battery-full", "fa-battery-half": "&#xf242; fa-battery-half", "fa-battery-quarter": "&#xf243; fa-battery-quarter", "fa-battery-three-quarters": "&#xf241; fa-battery-three-quarters", "fa-bed": "&#xf236; fa-bed", "fa-beer": "&#xf0fc; fa-beer", "fa-behance": "&#xf1b4; fa-behance", "fa-behance-square": "&#xf1b5; fa-behance-square", "fa-bell": "&#xf0f3; fa-bell", "fa-bell-o": "&#xf0a2; fa-bell-o", "fa-bell-slash": "&#xf1f6; fa-bell-slash", "fa-bell-slash-o": "&#xf1f7; fa-bell-slash-o", "fa-bicycle": "&#xf206; fa-bicycle", "fa-binoculars": "&#xf1e5; fa-binoculars", "fa-birthday-cake": "&#xf1fd; fa-birthday-cake", "fa-bitbucket": "&#xf171; fa-bitbucket", "fa-bitbucket-square": "&#xf172; fa-bitbucket-square", "fa-black-tie": "&#xf27e; fa-black-tie", "fa-blind": "&#xf29d; fa-blind", "fa-bluetooth": "&#xf293; fa-bluetooth", "fa-bluetooth-b": "&#xf294; fa-bluetooth-b", "fa-bold": "&#xf032; fa-bold", "fa-bolt": "&#xf0e7; fa-bolt", "fa-bomb": "&#xf1e2; fa-bomb", "fa-book": "&#xf02d; fa-book", "fa-bookmark": "&#xf02e; fa-bookmark", "fa-bookmark-o": "&#xf097; fa-bookmark-o", "fa-braille": "&#xf2a1; fa-braille", "fa-briefcase": "&#xf0b1; fa-briefcase", "fa-btc": "&#xf15a; fa-btc", "fa-bug": "&#xf188; fa-bug", "fa-building": "&#xf1ad; fa-building", "fa-building-o": "&#xf0f7; fa-building-o", "fa-bullhorn": "&#xf0a1; fa-bullhorn", "fa-bullseye": "&#xf140; fa-bullseye", "fa-bus": "&#xf207; fa-bus", "fa-buysellads": "&#xf20d; fa-buysellads", "fa-calculator": "&#xf1ec; fa-calculator", "fa-calendar": "&#xf073; fa-calendar", "fa-calendar-check-o": "&#xf274; fa-calendar-check-o", "fa-calendar-minus-o": "&#xf272; fa-calendar-minus-o", "fa-calendar-o": "&#xf133; fa-calendar-o", "fa-calendar-plus-o": "&#xf271; fa-calendar-plus-o", "fa-calendar-times-o": "&#xf273; fa-calendar-times-o", "fa-camera": "&#xf030; fa-camera", "fa-camera-retro": "&#xf083; fa-camera-retro", "fa-car": "&#xf1b9; fa-car", "fa-caret-down": "&#xf0d7; fa-caret-down", "fa-caret-left": "&#xf0d9; fa-caret-left", "fa-caret-right": "&#xf0da; fa-caret-right", "fa-caret-square-o-down": "&#xf150; fa-caret-square-o-down", "fa-caret-square-o-left": "&#xf191; fa-caret-square-o-left", "fa-caret-square-o-right": "&#xf152; fa-caret-square-o-right", "fa-caret-square-o-up": "&#xf151; fa-caret-square-o-up", "fa-caret-up": "&#xf0d8; fa-caret-up", "fa-cart-arrow-down": "&#xf218; fa-cart-arrow-down", "fa-cart-plus": "&#xf217; fa-cart-plus", "fa-cc": "&#xf20a; fa-cc", "fa-cc-amex": "&#xf1f3; fa-cc-amex", "fa-cc-diners-club": "&#xf24c; fa-cc-diners-club", "fa-cc-discover": "&#xf1f2; fa-cc-discover", "fa-cc-jcb": "&#xf24b; fa-cc-jcb", "fa-cc-mastercard": "&#xf1f1; fa-cc-mastercard", "fa-cc-paypal": "&#xf1f4; fa-cc-paypal", "fa-cc-stripe": "&#xf1f5; fa-cc-stripe", "fa-cc-visa": "&#xf1f0; fa-cc-visa", "fa-certificate": "&#xf0a3; fa-certificate", "fa-chain-broken": "&#xf127; fa-chain-broken", "fa-check": "&#xf00c; fa-check", "fa-check-circle": "&#xf058; fa-check-circle", "fa-check-circle-o": "&#xf05d; fa-check-circle-o", "fa-check-square": "&#xf14a; fa-check-square", "fa-check-square-o": "&#xf046; fa-check-square-o", "fa-chevron-circle-down": "&#xf13a; fa-chevron-circle-down", "fa-chevron-circle-left": "&#xf137; fa-chevron-circle-left", "fa-chevron-circle-right": "&#xf138; fa-chevron-circle-right", "fa-chevron-circle-up": "&#xf139; fa-chevron-circle-up", "fa-chevron-down": "&#xf078; fa-chevron-down", "fa-chevron-left": "&#xf053; fa-chevron-left", "fa-chevron-right": "&#xf054; fa-chevron-right", "fa-chevron-up": "&#xf077; fa-chevron-up", "fa-child": "&#xf1ae; fa-child", "fa-chrome": "&#xf268; fa-chrome", "fa-circle": "&#xf111; fa-circle", "fa-circle-o": "&#xf10c; fa-circle-o", "fa-circle-o-notch": "&#xf1ce; fa-circle-o-notch", "fa-circle-thin": "&#xf1db; fa-circle-thin", "fa-clipboard": "&#xf0ea; fa-clipboard", "fa-clock-o": "&#xf017; fa-clock-o", "fa-clone": "&#xf24d; fa-clone", "fa-cloud": "&#xf0c2; fa-cloud", "fa-cloud-download": "&#xf0ed; fa-cloud-download", "fa-cloud-upload": "&#xf0ee; fa-cloud-upload", "fa-code": "&#xf121; fa-code", "fa-code-fork": "&#xf126; fa-code-fork", "fa-codepen": "&#xf1cb; fa-codepen", "fa-codiepie": "&#xf284; fa-codiepie", "fa-coffee": "&#xf0f4; fa-coffee", "fa-cog": "&#xf013; fa-cog", "fa-cogs": "&#xf085; fa-cogs", "fa-columns": "&#xf0db; fa-columns", "fa-comment": "&#xf075; fa-comment", "fa-comment-o": "&#xf0e5; fa-comment-o", "fa-commenting": "&#xf27a; fa-commenting", "fa-commenting-o": "&#xf27b; fa-commenting-o", "fa-comments": "&#xf086; fa-comments", "fa-comments-o": "&#xf0e6; fa-comments-o", "fa-compass": "&#xf14e; fa-compass", "fa-compress": "&#xf066; fa-compress", "fa-connectdevelop": "&#xf20e; fa-connectdevelop", "fa-contao": "&#xf26d; fa-contao", "fa-copyright": "&#xf1f9; fa-copyright", "fa-creative-commons": "&#xf25e; fa-creative-commons", "fa-credit-card": "&#xf09d; fa-credit-card", "fa-credit-card-alt": "&#xf283; fa-credit-card-alt", "fa-crop": "&#xf125; fa-crop", "fa-crosshairs": "&#xf05b; fa-crosshairs", "fa-css3": "&#xf13c; fa-css3", "fa-cube": "&#xf1b2; fa-cube", "fa-cubes": "&#xf1b3; fa-cubes", "fa-cutlery": "&#xf0f5; fa-cutlery", "fa-dashcube": "&#xf210; fa-dashcube", "fa-database": "&#xf1c0; fa-database", "fa-deaf": "&#xf2a4; fa-deaf", "fa-delicious": "&#xf1a5; fa-delicious", "fa-desktop": "&#xf108; fa-desktop", "fa-deviantart": "&#xf1bd; fa-deviantart", "fa-diamond": "&#xf219; fa-diamond", "fa-digg": "&#xf1a6; fa-digg", "fa-dot-circle-o": "&#xf192; fa-dot-circle-o", "fa-download": "&#xf019; fa-download", "fa-dribbble": "&#xf17d; fa-dribbble", "fa-dropbox": "&#xf16b; fa-dropbox", "fa-drupal": "&#xf1a9; fa-drupal", "fa-edge": "&#xf282; fa-edge", "fa-eject": "&#xf052; fa-eject", "fa-ellipsis-h": "&#xf141; fa-ellipsis-h", "fa-ellipsis-v": "&#xf142; fa-ellipsis-v", "fa-empire": "&#xf1d1; fa-empire", "fa-envelope": "&#xf0e0; fa-envelope", "fa-envelope-o": "&#xf003; fa-envelope-o", "fa-envelope-square": "&#xf199; fa-envelope-square", "fa-envira": "&#xf299; fa-envira", "fa-eraser": "&#xf12d; fa-eraser", "fa-eur": "&#xf153; fa-eur", "fa-exchange": "&#xf0ec; fa-exchange", "fa-exclamation": "&#xf12a; fa-exclamation", "fa-exclamation-circle": "&#xf06a; fa-exclamation-circle", "fa-exclamation-triangle": "&#xf071; fa-exclamation-triangle", "fa-expand": "&#xf065; fa-expand", "fa-expeditedssl": "&#xf23e; fa-expeditedssl", "fa-external-link": "&#xf08e; fa-external-link", "fa-external-link-square": "&#xf14c; fa-external-link-square", "fa-eye": "&#xf06e; fa-eye", "fa-eye-slash": "&#xf070; fa-eye-slash", "fa-eyedropper": "&#xf1fb; fa-eyedropper", "fa-facebook": "&#xf09a; fa-facebook", "fa-facebook-official": "&#xf230; fa-facebook-official", "fa-facebook-square": "&#xf082; fa-facebook-square", "fa-fast-backward": "&#xf049; fa-fast-backward", "fa-fast-forward": "&#xf050; fa-fast-forward", "fa-fax": "&#xf1ac; fa-fax", "fa-female": "&#xf182; fa-female", "fa-fighter-jet": "&#xf0fb; fa-fighter-jet", "fa-file": "&#xf15b; fa-file", "fa-file-archive-o": "&#xf1c6; fa-file-archive-o", "fa-file-audio-o": "&#xf1c7; fa-file-audio-o", "fa-file-code-o": "&#xf1c9; fa-file-code-o", "fa-file-excel-o": "&#xf1c3; fa-file-excel-o", "fa-file-image-o": "&#xf1c5; fa-file-image-o", "fa-file-o": "&#xf016; fa-file-o", "fa-file-pdf-o": "&#xf1c1; fa-file-pdf-o", "fa-file-powerpoint-o": "&#xf1c4; fa-file-powerpoint-o", "fa-file-text": "&#xf15c; fa-file-text", "fa-file-text-o": "&#xf0f6; fa-file-text-o", "fa-file-video-o": "&#xf1c8; fa-file-video-o", "fa-file-word-o": "&#xf1c2; fa-file-word-o", "fa-files-o": "&#xf0c5; fa-files-o", "fa-film": "&#xf008; fa-film", "fa-filter": "&#xf0b0; fa-filter", "fa-fire": "&#xf06d; fa-fire", "fa-fire-extinguisher": "&#xf134; fa-fire-extinguisher", "fa-firefox": "&#xf269; fa-firefox", "fa-first-order": "&#xf2b0; fa-first-order", "fa-flag": "&#xf024; fa-flag", "fa-flag-checkered": "&#xf11e; fa-flag-checkered", "fa-flag-o": "&#xf11d; fa-flag-o", "fa-flask": "&#xf0c3; fa-flask", "fa-flickr": "&#xf16e; fa-flickr", "fa-floppy-o": "&#xf0c7; fa-floppy-o", "fa-folder": "&#xf07b; fa-folder", "fa-folder-o": "&#xf114; fa-folder-o", "fa-folder-open": "&#xf07c; fa-folder-open", "fa-folder-open-o": "&#xf115; fa-folder-open-o", "fa-font": "&#xf031; fa-font", "fa-font-awesome": "&#xf2b4; fa-font-awesome", "fa-fonticons": "&#xf280; fa-fonticons", "fa-fort-awesome": "&#xf286; fa-fort-awesome", "fa-forumbee": "&#xf211; fa-forumbee", "fa-forward": "&#xf04e; fa-forward", "fa-foursquare": "&#xf180; fa-foursquare", "fa-frown-o": "&#xf119; fa-frown-o", "fa-futbol-o": "&#xf1e3; fa-futbol-o", "fa-gamepad": "&#xf11b; fa-gamepad", "fa-gavel": "&#xf0e3; fa-gavel", "fa-gbp": "&#xf154; fa-gbp", "fa-genderless": "&#xf22d; fa-genderless", "fa-get-pocket": "&#xf265; fa-get-pocket", "fa-gg": "&#xf260; fa-gg", "fa-gg-circle": "&#xf261; fa-gg-circle", "fa-gift": "&#xf06b; fa-gift", "fa-git": "&#xf1d3; fa-git", "fa-git-square": "&#xf1d2; fa-git-square", "fa-github": "&#xf09b; fa-github", "fa-github-alt": "&#xf113; fa-github-alt", "fa-github-square": "&#xf092; fa-github-square", "fa-gitlab": "&#xf296; fa-gitlab", "fa-glass": "&#xf000; fa-glass", "fa-glide": "&#xf2a5; fa-glide", "fa-glide-g": "&#xf2a6; fa-glide-g", "fa-globe": "&#xf0ac; fa-globe", "fa-google": "&#xf1a0; fa-google", "fa-google-plus": "&#xf0d5; fa-google-plus", "fa-google-plus-official": "&#xf2b3; fa-google-plus-official", "fa-google-plus-square": "&#xf0d4; fa-google-plus-square", "fa-google-wallet": "&#xf1ee; fa-google-wallet", "fa-graduation-cap": "&#xf19d; fa-graduation-cap", "fa-gratipay": "&#xf184; fa-gratipay", "fa-h-square": "&#xf0fd; fa-h-square", "fa-hacker-news": "&#xf1d4; fa-hacker-news", "fa-hand-lizard-o": "&#xf258; fa-hand-lizard-o", "fa-hand-o-down": "&#xf0a7; fa-hand-o-down", "fa-hand-o-left": "&#xf0a5; fa-hand-o-left", "fa-hand-o-right": "&#xf0a4; fa-hand-o-right", "fa-hand-o-up": "&#xf0a6; fa-hand-o-up", "fa-hand-paper-o": "&#xf256; fa-hand-paper-o", "fa-hand-peace-o": "&#xf25b; fa-hand-peace-o", "fa-hand-pointer-o": "&#xf25a; fa-hand-pointer-o", "fa-hand-rock-o": "&#xf255; fa-hand-rock-o", "fa-hand-scissors-o": "&#xf257; fa-hand-scissors-o", "fa-hand-spock-o": "&#xf259; fa-hand-spock-o", "fa-hashtag": "&#xf292; fa-hashtag", "fa-hdd-o": "&#xf0a0; fa-hdd-o", "fa-header": "&#xf1dc; fa-header", "fa-headphones": "&#xf025; fa-headphones", "fa-heart": "&#xf004; fa-heart", "fa-heart-o": "&#xf08a; fa-heart-o", "fa-heartbeat": "&#xf21e; fa-heartbeat", "fa-history": "&#xf1da; fa-history", "fa-home": "&#xf015; fa-home", "fa-hospital-o": "&#xf0f8; fa-hospital-o", "fa-hourglass": "&#xf254; fa-hourglass", "fa-hourglass-end": "&#xf253; fa-hourglass-end", "fa-hourglass-half": "&#xf252; fa-hourglass-half", "fa-hourglass-o": "&#xf250; fa-hourglass-o", "fa-hourglass-start": "&#xf251; fa-hourglass-start", "fa-houzz": "&#xf27c; fa-houzz", "fa-html5": "&#xf13b; fa-html5", "fa-i-cursor": "&#xf246; fa-i-cursor", "fa-ils": "&#xf20b; fa-ils", "fa-inbox": "&#xf01c; fa-inbox", "fa-indent": "&#xf03c; fa-indent", "fa-industry": "&#xf275; fa-industry", "fa-info": "&#xf129; fa-info", "fa-info-circle": "&#xf05a; fa-info-circle", "fa-inr": "&#xf156; fa-inr", "fa-instagram": "&#xf16d; fa-instagram", "fa-internet-explorer": "&#xf26b; fa-internet-explorer", "fa-ioxhost": "&#xf208; fa-ioxhost", "fa-italic": "&#xf033; fa-italic", "fa-joomla": "&#xf1aa; fa-j<PERSON>la", "fa-jpy": "&#xf157; fa-jpy", "fa-jsfiddle": "&#xf1cc; fa-jsfiddle", "fa-key": "&#xf084; fa-key", "fa-keyboard-o": "&#xf11c; fa-keyboard-o", "fa-krw": "&#xf159; fa-krw", "fa-language": "&#xf1ab; fa-language", "fa-laptop": "&#xf109; fa-laptop", "fa-lastfm": "&#xf202; fa-lastfm", "fa-lastfm-square": "&#xf203; fa-lastfm-square", "fa-leaf": "&#xf06c; fa-leaf", "fa-leanpub": "&#xf212; fa-leanpub", "fa-lemon-o": "&#xf094; fa-lemon-o", "fa-level-down": "&#xf149; fa-level-down", "fa-level-up": "&#xf148; fa-level-up", "fa-life-ring": "&#xf1cd; fa-life-ring", "fa-lightbulb-o": "&#xf0eb; fa-lightbulb-o", "fa-line-chart": "&#xf201; fa-line-chart", "fa-link": "&#xf0c1; fa-link", "fa-linkedin": "&#xf0e1; fa-linkedin", "fa-linkedin-square": "&#xf08c; fa-linkedin-square", "fa-linux": "&#xf17c; fa-linux", "fa-list": "&#xf03a; fa-list", "fa-list-alt": "&#xf022; fa-list-alt", "fa-list-ol": "&#xf0cb; fa-list-ol", "fa-list-ul": "&#xf0ca; fa-list-ul", "fa-location-arrow": "&#xf124; fa-location-arrow", "fa-lock": "&#xf023; fa-lock", "fa-long-arrow-down": "&#xf175; fa-long-arrow-down", "fa-long-arrow-left": "&#xf177; fa-long-arrow-left", "fa-long-arrow-right": "&#xf178; fa-long-arrow-right", "fa-long-arrow-up": "&#xf176; fa-long-arrow-up", "fa-low-vision": "&#xf2a8; fa-low-vision", "fa-magic": "&#xf0d0; fa-magic", "fa-magnet": "&#xf076; fa-magnet", "fa-male": "&#xf183; fa-male", "fa-map": "&#xf279; fa-map", "fa-map-marker": "&#xf041; fa-map-marker", "fa-map-o": "&#xf278; fa-map-o", "fa-map-pin": "&#xf276; fa-map-pin", "fa-map-signs": "&#xf277; fa-map-signs", "fa-mars": "&#xf222; fa-mars", "fa-mars-double": "&#xf227; fa-mars-double", "fa-mars-stroke": "&#xf229; fa-mars-stroke", "fa-mars-stroke-h": "&#xf22b; fa-mars-stroke-h", "fa-mars-stroke-v": "&#xf22a; fa-mars-stroke-v", "fa-maxcdn": "&#xf136; fa-maxcdn", "fa-meanpath": "&#xf20c; fa-meanpath", "fa-medium": "&#xf23a; fa-medium", "fa-medkit": "&#xf0fa; fa-medkit", "fa-meh-o": "&#xf11a; fa-meh-o", "fa-mercury": "&#xf223; fa-mercury", "fa-microphone": "&#xf130; fa-microphone", "fa-microphone-slash": "&#xf131; fa-microphone-slash", "fa-minus": "&#xf068; fa-minus", "fa-minus-circle": "&#xf056; fa-minus-circle", "fa-minus-square": "&#xf146; fa-minus-square", "fa-minus-square-o": "&#xf147; fa-minus-square-o", "fa-mixcloud": "&#xf289; fa-mixcloud", "fa-mobile": "&#xf10b; fa-mobile", "fa-modx": "&#xf285; fa-modx", "fa-money": "&#xf0d6; fa-money", "fa-moon-o": "&#xf186; fa-moon-o", "fa-motorcycle": "&#xf21c; fa-motorcycle", "fa-mouse-pointer": "&#xf245; fa-mouse-pointer", "fa-music": "&#xf001; fa-music", "fa-neuter": "&#xf22c; fa-neuter", "fa-newspaper-o": "&#xf1ea; fa-newspaper-o", "fa-object-group": "&#xf247; fa-object-group", "fa-object-ungroup": "&#xf248; fa-object-ungroup", "fa-odnoklassniki": "&#xf263; fa-odnoklassniki", "fa-odnoklassniki-square": "&#xf264; fa-odnoklassniki-square", "fa-opencart": "&#xf23d; fa-opencart", "fa-openid": "&#xf19b; fa-openid", "fa-opera": "&#xf26a; fa-opera", "fa-optin-monster": "&#xf23c; fa-optin-monster", "fa-outdent": "&#xf03b; fa-outdent", "fa-pagelines": "&#xf18c; fa-pagelines", "fa-paint-brush": "&#xf1fc; fa-paint-brush", "fa-paper-plane": "&#xf1d8; fa-paper-plane", "fa-paper-plane-o": "&#xf1d9; fa-paper-plane-o", "fa-paperclip": "&#xf0c6; fa-paperclip", "fa-paragraph": "&#xf1dd; fa-paragraph", "fa-pause": "&#xf04c; fa-pause", "fa-pause-circle": "&#xf28b; fa-pause-circle", "fa-pause-circle-o": "&#xf28c; fa-pause-circle-o", "fa-paw": "&#xf1b0; fa-paw", "fa-paypal": "&#xf1ed; fa-paypal", "fa-pencil": "&#xf040; fa-pencil", "fa-pencil-square": "&#xf14b; fa-pencil-square", "fa-pencil-square-o": "&#xf044; fa-pencil-square-o", "fa-percent": "&#xf295; fa-percent", "fa-phone": "&#xf095; fa-phone", "fa-phone-square": "&#xf098; fa-phone-square", "fa-picture-o": "&#xf03e; fa-picture-o", "fa-pie-chart": "&#xf200; fa-pie-chart", "fa-pied-piper": "&#xf2ae; fa-pied-piper", "fa-pied-piper-alt": "&#xf1a8; fa-pied-piper-alt", "fa-pied-piper-pp": "&#xf1a7; fa-pied-piper-pp", "fa-pinterest": "&#xf0d2; fa-pinterest", "fa-pinterest-p": "&#xf231; fa-pinterest-p", "fa-pinterest-square": "&#xf0d3; fa-pinterest-square", "fa-plane": "&#xf072; fa-plane", "fa-play": "&#xf04b; fa-play", "fa-play-circle": "&#xf144; fa-play-circle", "fa-play-circle-o": "&#xf01d; fa-play-circle-o", "fa-plug": "&#xf1e6; fa-plug", "fa-plus": "&#xf067; fa-plus", "fa-plus-circle": "&#xf055; fa-plus-circle", "fa-plus-square": "&#xf0fe; fa-plus-square", "fa-plus-square-o": "&#xf196; fa-plus-square-o", "fa-power-off": "&#xf011; fa-power-off", "fa-print": "&#xf02f; fa-print", "fa-product-hunt": "&#xf288; fa-product-hunt", "fa-puzzle-piece": "&#xf12e; fa-puzzle-piece", "fa-qq": "&#xf1d6; fa-qq", "fa-qrcode": "&#xf029; fa-qrcode", "fa-question": "&#xf128; fa-question", "fa-question-circle": "&#xf059; fa-question-circle", "fa-question-circle-o": "&#xf29c; fa-question-circle-o", "fa-quote-left": "&#xf10d; fa-quote-left", "fa-quote-right": "&#xf10e; fa-quote-right", "fa-random": "&#xf074; fa-random", "fa-rebel": "&#xf1d0; fa-rebel", "fa-recycle": "&#xf1b8; fa-recycle", "fa-reddit": "&#xf1a1; fa-reddit", "fa-reddit-alien": "&#xf281; fa-reddit-alien", "fa-reddit-square": "&#xf1a2; fa-reddit-square", "fa-refresh": "&#xf021; fa-refresh", "fa-registered": "&#xf25d; fa-registered", "fa-renren": "&#xf18b; fa-renren", "fa-repeat": "&#xf01e; fa-repeat", "fa-reply": "&#xf112; fa-reply", "fa-reply-all": "&#xf122; fa-reply-all", "fa-retweet": "&#xf079; fa-retweet", "fa-road": "&#xf018; fa-road", "fa-rocket": "&#xf135; fa-rocket", "fa-rss": "&#xf09e; fa-rss", "fa-rss-square": "&#xf143; fa-rss-square", "fa-rub": "&#xf158; fa-rub", "fa-safari": "&#xf267; fa-safari", "fa-scissors": "&#xf0c4; fa-scissors", "fa-scribd": "&#xf28a; fa-scribd", "fa-search": "&#xf002; fa-search", "fa-search-minus": "&#xf010; fa-search-minus", "fa-search-plus": "&#xf00e; fa-search-plus", "fa-sellsy": "&#xf213; fa-sellsy", "fa-server": "&#xf233; fa-server", "fa-share": "&#xf064; fa-share", "fa-share-alt": "&#xf1e0; fa-share-alt", "fa-share-alt-square": "&#xf1e1; fa-share-alt-square", "fa-share-square": "&#xf14d; fa-share-square", "fa-share-square-o": "&#xf045; fa-share-square-o", "fa-shield": "&#xf132; fa-shield", "fa-ship": "&#xf21a; fa-ship", "fa-shirtsinbulk": "&#xf214; fa-shirtsinbulk", "fa-shopping-bag": "&#xf290; fa-shopping-bag", "fa-shopping-basket": "&#xf291; fa-shopping-basket", "fa-shopping-cart": "&#xf07a; fa-shopping-cart", "fa-sign-in": "&#xf090; fa-sign-in", "fa-sign-language": "&#xf2a7; fa-sign-language", "fa-sign-out": "&#xf08b; fa-sign-out", "fa-signal": "&#xf012; fa-signal", "fa-simplybuilt": "&#xf215; fa-simplybuilt", "fa-sitemap": "&#xf0e8; fa-sitemap", "fa-skyatlas": "&#xf216; fa-skyatlas", "fa-skype": "&#xf17e; fa-skype", "fa-slack": "&#xf198; fa-slack", "fa-sliders": "&#xf1de; fa-sliders", "fa-slideshare": "&#xf1e7; fa-slideshare", "fa-smile-o": "&#xf118; fa-smile-o", "fa-snapchat": "&#xf2ab; fa-snapchat", "fa-snapchat-ghost": "&#xf2ac; fa-snapchat-ghost", "fa-snapchat-square": "&#xf2ad; fa-snapchat-square", "fa-sort": "&#xf0dc; fa-sort", "fa-sort-alpha-asc": "&#xf15d; fa-sort-alpha-asc", "fa-sort-alpha-desc": "&#xf15e; fa-sort-alpha-desc", "fa-sort-amount-asc": "&#xf160; fa-sort-amount-asc", "fa-sort-amount-desc": "&#xf161; fa-sort-amount-desc", "fa-sort-asc": "&#xf0de; fa-sort-asc", "fa-sort-desc": "&#xf0dd; fa-sort-desc", "fa-sort-numeric-asc": "&#xf162; fa-sort-numeric-asc", "fa-sort-numeric-desc": "&#xf163; fa-sort-numeric-desc", "fa-soundcloud": "&#xf1be; fa-soundcloud", "fa-space-shuttle": "&#xf197; fa-space-shuttle", "fa-spinner": "&#xf110; fa-spinner", "fa-spoon": "&#xf1b1; fa-spoon", "fa-spotify": "&#xf1bc; fa-spotify", "fa-square": "&#xf0c8; fa-square", "fa-square-o": "&#xf096; fa-square-o", "fa-stack-exchange": "&#xf18d; fa-stack-exchange", "fa-stack-overflow": "&#xf16c; fa-stack-overflow", "fa-star": "&#xf005; fa-star", "fa-star-half": "&#xf089; fa-star-half", "fa-star-half-o": "&#xf123; fa-star-half-o", "fa-star-o": "&#xf006; fa-star-o", "fa-steam": "&#xf1b6; fa-steam", "fa-steam-square": "&#xf1b7; fa-steam-square", "fa-step-backward": "&#xf048; fa-step-backward", "fa-step-forward": "&#xf051; fa-step-forward", "fa-stethoscope": "&#xf0f1; fa-stethoscope", "fa-sticky-note": "&#xf249; fa-sticky-note", "fa-sticky-note-o": "&#xf24a; fa-sticky-note-o", "fa-stop": "&#xf04d; fa-stop", "fa-stop-circle": "&#xf28d; fa-stop-circle", "fa-stop-circle-o": "&#xf28e; fa-stop-circle-o", "fa-street-view": "&#xf21d; fa-street-view", "fa-strikethrough": "&#xf0cc; fa-strikethrough", "fa-stumbleupon": "&#xf1a4; fa-stumbleupon", "fa-stumbleupon-circle": "&#xf1a3; fa-stumbleupon-circle", "fa-subscript": "&#xf12c; fa-subscript", "fa-subway": "&#xf239; fa-subway", "fa-suitcase": "&#xf0f2; fa-suitcase", "fa-sun-o": "&#xf185; fa-sun-o", "fa-superscript": "&#xf12b; fa-superscript", "fa-table": "&#xf0ce; fa-table", "fa-tablet": "&#xf10a; fa-tablet", "fa-tachometer": "&#xf0e4; fa-tachometer", "fa-tag": "&#xf02b; fa-tag", "fa-tags": "&#xf02c; fa-tags", "fa-tasks": "&#xf0ae; fa-tasks", "fa-taxi": "&#xf1ba; fa-taxi", "fa-television": "&#xf26c; fa-television", "fa-tencent-weibo": "&#xf1d5; fa-tencent-weibo", "fa-terminal": "&#xf120; fa-terminal", "fa-text-height": "&#xf034; fa-text-height", "fa-text-width": "&#xf035; fa-text-width", "fa-th": "&#xf00a; fa-th", "fa-th-large": "&#xf009; fa-th-large", "fa-th-list": "&#xf00b; fa-th-list", "fa-themeisle": "&#xf2b2; fa-themeisle", "fa-thumb-tack": "&#xf08d; fa-thumb-tack", "fa-thumbs-down": "&#xf165; fa-thumbs-down", "fa-thumbs-o-down": "&#xf088; fa-thumbs-o-down", "fa-thumbs-o-up": "&#xf087; fa-thumbs-o-up", "fa-thumbs-up": "&#xf164; fa-thumbs-up", "fa-ticket": "&#xf145; fa-ticket", "fa-times": "&#xf00d; fa-times", "fa-times-circle": "&#xf057; fa-times-circle", "fa-times-circle-o": "&#xf05c; fa-times-circle-o", "fa-tint": "&#xf043; fa-tint", "fa-toggle-off": "&#xf204; fa-toggle-off", "fa-toggle-on": "&#xf205; fa-toggle-on", "fa-trademark": "&#xf25c; fa-trademark", "fa-train": "&#xf238; fa-train", "fa-transgender": "&#xf224; fa-transgender", "fa-transgender-alt": "&#xf225; fa-transgender-alt", "fa-trash": "&#xf1f8; fa-trash", "fa-trash-o": "&#xf014; fa-trash-o", "fa-tree": "&#xf1bb; fa-tree", "fa-trello": "&#xf181; fa-trello", "fa-tripadvisor": "&#xf262; fa-tripadvisor", "fa-trophy": "&#xf091; fa-trophy", "fa-truck": "&#xf0d1; fa-truck", "fa-try": "&#xf195; fa-try", "fa-tty": "&#xf1e4; fa-tty", "fa-tumblr": "&#xf173; fa-tumblr", "fa-tumblr-square": "&#xf174; fa-tumblr-square", "fa-twitch": "&#xf1e8; fa-twitch", "fa-umbrella": "&#xf0e9; fa-umbrella", "fa-underline": "&#xf0cd; fa-underline", "fa-undo": "&#xf0e2; fa-undo", "fa-universal-access": "&#xf29a; fa-universal-access", "fa-university": "&#xf19c; fa-university", "fa-unlock": "&#xf09c; fa-unlock", "fa-unlock-alt": "&#xf13e; fa-unlock-alt", "fa-upload": "&#xf093; fa-upload", "fa-usb": "&#xf287; fa-usb", "fa-usd": "&#xf155; fa-usd", "fa-user": "&#xf007; fa-user", "fa-user-md": "&#xf0f0; fa-user-md", "fa-user-plus": "&#xf234; fa-user-plus", "fa-user-secret": "&#xf21b; fa-user-secret", "fa-user-times": "&#xf235; fa-user-times", "fa-users": "&#xf0c0; fa-users", "fa-venus": "&#xf221; fa-venus", "fa-venus-double": "&#xf226; fa-venus-double", "fa-venus-mars": "&#xf228; fa-venus-mars", "fa-viacoin": "&#xf237; fa-viacoin", "fa-viadeo": "&#xf2a9; fa-viadeo", "fa-viadeo-square": "&#xf2aa; fa-viadeo-square", "fa-video-camera": "&#xf03d; fa-video-camera", "fa-vimeo": "&#xf27d; fa-vimeo", "fa-vimeo-square": "&#xf194; fa-vimeo-square", "fa-vine": "&#xf1ca; fa-vine", "fa-vk": "&#xf189; fa-vk", "fa-volume-control-phone": "&#xf2a0; fa-volume-control-phone", "fa-volume-down": "&#xf027; fa-volume-down", "fa-volume-off": "&#xf026; fa-volume-off", "fa-volume-up": "&#xf028; fa-volume-up", "fa-weibo": "&#xf18a; fa-weibo", "fa-weixin": "&#xf1d7; fa-weixin", "fa-whatsapp": "&#xf232; fa-whatsapp", "fa-wheelchair": "&#xf193; fa-wheelchair", "fa-wheelchair-alt": "&#xf29b; fa-wheelchair-alt", "fa-wifi": "&#xf1eb; fa-wifi", "fa-wikipedia-w": "&#xf266; fa-wikipedia-w", "fa-windows": "&#xf17a; fa-windows", "fa-wordpress": "&#xf19a; fa-wordpress", "fa-wpbeginner": "&#xf297; fa-wpbeginner", "fa-wpforms": "&#xf298; fa-wpforms", "fa-wrench": "&#xf0ad; fa-wrench", "fa-xing": "&#xf168; fa-xing", "fa-xing-square": "&#xf169; fa-xing-square", "fa-y-combinator": "&#xf23b; fa-y-combinator", "fa-yahoo": "&#xf19e; fa-yahoo", "fa-yelp": "&#xf1e9; fa-yelp", "fa-yoast": "&#xf2b1; fa-yoast", "fa-youtube": "&#xf167; fa-youtube", "fa-youtube-play": "&#xf16a; fa-youtube-play", "fa-youtube-square": "&#xf166; fa-youtube-square"}, "show_preview": 1, "default_label": "", "parent_repeater": "field_574d91c5d772f"}, {"key": "field_574d9207d7731", "label": "Länkmål", "name": "lankmal", "type": "text", "instructions": "Ange länken till rätt sociala media.", "required": 0, "conditional_logic": 0, "wrapper": {"width": 75, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "Url", "prepend": "", "append": "", "maxlength": "", "readonly": 0, "disabled": 0, "parent_repeater": "field_574d91c5d772f"}]}, {"key": "field_574d9059d7727", "label": "Sidfot", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "placement": "top", "endpoint": 0}, {"key": "field_574d905ed7728", "label": "<PERSON><PERSON>", "name": "footer_about_en", "type": "textarea", "instructions": "Ange en kort beskrivning om vad Swedac är för något.", "required": 1, "conditional_logic": 0, "wrapper": {"width": 100, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "<PERSON><PERSON>", "maxlength": "", "rows": 3, "new_lines": "wpautop", "readonly": 0, "disabled": 0}, {"key": "field_574d907dd7729", "label": "Telefonnummer", "name": "footer_phonenumber_en", "type": "text", "instructions": "Ang<PERSON> ett telefonnummer", "required": 1, "conditional_logic": 0, "wrapper": {"width": 50, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "Telefonnummer", "prepend": "", "append": "", "maxlength": "", "readonly": 0, "disabled": 0}, {"key": "field_574d908cd772a", "label": "Epostadress", "name": "footer_epost_en", "type": "email", "instructions": "<PERSON><PERSON> en epostadress", "required": 1, "conditional_logic": 0, "wrapper": {"width": 50, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "Epost", "prepend": "", "append": ""}, {"key": "field_574d90a3d772b", "label": "<PERSON><PERSON><PERSON>", "name": "footer_adress_en", "type": "repeater", "instructions": "Ange era adresser.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "<PERSON><PERSON><PERSON> till adress", "rows_per_page": 20, "sub_fields": [{"key": "field_574d90f2d772d", "label": "<PERSON><PERSON>r<PERSON><PERSON>", "name": "omrade", "type": "text", "instructions": "<PERSON>e ett passande namn för området </br>\r\nT.ex Stockholmskontoret", "required": 1, "conditional_logic": 0, "wrapper": {"width": 33, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "<PERSON><PERSON>r<PERSON><PERSON>", "prepend": "", "append": "", "maxlength": "", "readonly": 0, "disabled": 0, "parent_repeater": "field_574d90a3d772b"}, {"key": "field_574d9120d772e", "label": "<PERSON>ress", "name": "adress", "type": "textarea", "instructions": "<PERSON><PERSON> ad<PERSON>.", "required": 1, "conditional_logic": 0, "wrapper": {"width": 66, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "", "maxlength": "", "rows": 3, "new_lines": "wpautop", "readonly": 0, "disabled": 0, "parent_repeater": "field_574d90a3d772b"}]}, {"key": "field_574d9229d7732", "label": "Länkar", "name": "footer_lankar_en", "type": "repeater", "instructions": "Ange länkar som ska visas i sidfoten med en rubrik, text samt ett länkmål. <br>\r\nMax 3 st", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "collapsed": "", "min": 0, "max": 4, "layout": "table", "button_label": "Lägg till länk", "rows_per_page": 20, "sub_fields": [{"key": "field_574d923ad7733", "label": "<PERSON><PERSON><PERSON>", "name": "rubrik", "type": "text", "instructions": "Ange en passande rubrik.", "required": 1, "conditional_logic": 0, "wrapper": {"width": 25, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "<PERSON><PERSON><PERSON>", "prepend": "", "append": "", "maxlength": "", "readonly": 0, "disabled": 0, "parent_repeater": "field_574d9229d7732"}, {"key": "field_574d9249d7734", "label": "Beskrivning", "name": "beskrivning", "type": "textarea", "instructions": "Ange en passande text.", "required": 1, "conditional_logic": 0, "wrapper": {"width": 25, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "", "maxlength": "", "rows": 4, "new_lines": "wpautop", "readonly": 0, "disabled": 0, "parent_repeater": "field_574d9229d7732"}, {"key": "field_574d925cd7735", "label": "Länkmål", "name": "lankmal", "type": "page_link", "instructions": "Ange ett länkmål.", "required": 0, "conditional_logic": 0, "wrapper": {"width": 25, "class": "", "id": ""}, "aria-label": "", "post_type": ["page"], "taxonomy": [], "allow_null": 0, "multiple": 0, "allow_archives": 1, "parent_repeater": "field_574d9229d7732"}, {"key": "field_574d9274d7736", "label": "Externt länkmål", "name": "externt_lankmal", "type": "url", "instructions": "Länka till externt länkmål.", "required": 0, "conditional_logic": 0, "wrapper": {"width": 25, "class": "", "id": ""}, "aria-label": "", "default_value": "", "placeholder": "Url", "parent_repeater": "field_574d9229d7732"}]}, {"key": "field_588b1c717a7c7", "label": "", "name": "", "type": "message", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "aria-label": "", "message": "Dessa två nedanstående fält är länken som visas allra längst ner på sidan \"Om local.swedac.se\".", "esc_html": 0, "new_lines": "wpautop"}, {"key": "field_588b1c7e7a7c8", "label": "<PERSON><PERSON>", "name": "om_swedac_namn_footer_en", "type": "text", "instructions": "<PERSON><PERSON> namn", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "aria-label": "", "default_value": "Om Swedac.se", "maxlength": "", "placeholder": "Om Swedac.se", "prepend": "", "append": ""}, {"key": "field_588b1c9f7a7c9", "label": "<PERSON><PERSON> (länk)", "name": "om_swed<PERSON>_lank_footer_en", "type": "page_link", "instructions": "<PERSON><PERSON> länkmå<PERSON>.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "aria-label": "", "post_type": ["page"], "taxonomy": [], "allow_null": 0, "multiple": 0, "allow_archives": 1}], "location": [[{"param": "page_template", "operator": "==", "value": "page-home.php"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "modified": 1695730062}