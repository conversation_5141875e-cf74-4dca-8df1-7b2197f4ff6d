<?php
/**
 * Template Name: Template 5 - Nyheter
 */
?>
<div class="wrapper">
	<div id="page-news-content">
		
		<?php $paged = get_query_var( 'paged' ) ? get_query_var( 'paged' ) : 1;
		$args = array(
			'post_status' 		=> 'publish',
			'post_type' 		=> 'news',
			'paged' 			=> $paged,
			'posts_per_page' 	=> 10, 
			'orderby' 			=> 'date'

		);

		$wp_query = new WP_Query($args);
		if($wp_query->have_posts()): ?>
		    <ul>
		    <?php while($wp_query->have_posts()):$wp_query->the_post(); ?>
		    	<?php $get_post_image = wp_get_attachment_image_src( get_post_thumbnail_id($post->ID), 'medium' );
		    	$image_url = $get_post_image['0']; ?>

		        <li>
		        	<div class="left">
		        		<div class="meta">
		        			<?php get_template_part('templates/entry-meta') ?>
		        		</div>
			        	<h2><a href="<?php the_permalink(); ?>" alt="<?php the_title(); ?>"><?php the_title(); ?></a></h2>
			        	<div class="excerpt">
			        		<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
								<?php if(get_field('page_ingress_subpage_sv')) {
									echo get_field('page_ingress_subpage_sv');
								} else {
									the_excerpt();
								} ?>
			        		<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
			        			<?php if(get_field('page_ingress_subpage_en')) {
									echo get_field('page_ingress_subpage_en');
								} else {
									the_excerpt();
								} ?>
			        		<?php endif;?>
			        	</div>
					</div>
					<div class="right">
						<?php if ( has_post_thumbnail() ) { ?>
							<a href="<?php the_permalink(); ?>" alt="<?php the_title(); ?>">
								<img data-src="<?php echo $image_url; ?>" alt="<?php the_title(); ?>" />
							</a>
						<?php } ?>
					</div>
		     	</li>
				
		    <?php endwhile; ?>
		  	</ul>
		  	
		  	<?php echo easy_wp_pagenavigation( $wp_query ); ?>
		  	
		<?php endif; wp_reset_postdata(); ?>

	</div>
</div>