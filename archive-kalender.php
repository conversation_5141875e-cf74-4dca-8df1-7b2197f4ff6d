<div id="page-header-parent">
	<div class="title">
		<div class="wrapper">
			
			<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
				<?php 
					$obj = get_post_type_object( 'kalender' );
					echo '<h1 class="title-page">Kalender</h1>';
				?>
			<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
				<?php 
					$obj = get_post_type_object( 'kalender' );
					echo '<h1 class="title-page">Calender</h1>';
				?>
			<?php endif;?>
		</div>
	</div>
	<div class="wrapper">
		<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
			<div id="breadcrumbs"><span xmlns:v="http://rdf.data-vocabulary.org/#"><span typeof="v:Breadcrumb"><a href="https://www.swedac.se" rel="v:url" property="v:title">Hem</a> / <span class="breadcrumb_last">Kalender</span></span></span></div>
		<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
			<div id="breadcrumbs"><span xmlns:v="http://rdf.data-vocabulary.org/#"><span typeof="v:Breadcrumb"><a href="https://www.swedac.se/?lang=en" rel="v:url" property="v:title">Home</a> / <span class="breadcrumb_last">Calender</span></span></span></div>
		<?php endif;?>
	</div>
</div>

<div class="wrapper"> 
	<div id="single_section_posts_kalender">
		<?php $paged = get_query_var( 'paged' ) ? get_query_var( 'paged' ) : 1;
		if(ICL_LANGUAGE_CODE=='sv'):
			$args = array(
				'post_type' 		=> 'kalender',
				'posts_per_page' 	=> 25,
				'paged' 			=> $paged,
				'post_status' 		=> 'publish',
				'order'     		=> 'ASC',
				'meta_key' 			=> 'kal_startdatum_sv',
				'orderby'   		=> 'meta_value'
			);
		elseif(ICL_LANGUAGE_CODE=='en'):
			$args = array(
				'post_type' 		=> 'kalender',
				'posts_per_page' 	=> 25, 
				'paged' 			=> $paged,
				'post_status' 		=> 'publish',
				'order'     		=> 'ASC',
				'meta_key' 			=> 'kal_startdatum_en',
				'orderby'   		=> 'meta_value'
			);
		endif;

		$query = new WP_Query($args);
		if($query->have_posts()):
		$i = 1; 

		while($query->have_posts()):$query->the_post(); ?>
		  	<article <?php post_class('single_section_post_kalender paragraph_start'); ?> data-paragraph="<?php echo $i; ?>">
		  		<?php include 'templates/content-kalender.php'; ?>
		  	</article>
		<?php $i++; endwhile; ?>
		<?php echo easy_wp_pagenavigation( $query ); ?>
		<?php else: ?>
			
			<div class="wrapper">
				<div class="alert alert-warning">
					<?php if(ICL_LANGUAGE_CODE=='sv'): ?>
						<strong>Kalendern är tom</strong>
						<em>För tillfället finns ingenting inlagt i kalendern.</em>
					<?php elseif(ICL_LANGUAGE_CODE=='en'): ?>
						<strong>Calender is empty</strong>
						<strong>Our calender is empty</strong>
					<?php endif;?>
				</div>
			</div>

		<?php endif; ?>
	</div>
</div>